import { PermissionCheck, PermissionResult, ResourceType, ActionType } from "./rbac-types";
import { createPolicyEngine } from "./policy-engine";

export class AccessControl {
  private static instance: AccessControl;
  private policyEngines: Map<string, any> = new Map();

  private constructor() {}

  static getInstance(): AccessControl {
    if (!AccessControl.instance) {
      AccessControl.instance = new AccessControl();
    }
    return AccessControl.instance;
  }

  // Get or create policy engine for tenant
  private getPolicyEngine(tenantId: string) {
    if (!this.policyEngines.has(tenantId)) {
      this.policyEngines.set(tenantId, createPolicyEngine(tenantId));
    }
    return this.policyEngines.get(tenantId);
  }

  // Main access control check
  async can(
    userId: string,
    action: ActionType,
    resource: ResourceType,
    context?: {
      tenantId?: string;
      workspaceId?: string;
      teamId?: string;
      resourceId?: string;
      requestData?: Record<string, any>;
    }
  ): Promise<boolean> {
    if (!context?.tenantId) {
      throw new Error("Tenant ID is required for access control checks");
    }

    const policyEngine = this.getPolicyEngine(context.tenantId);
    
    const check: PermissionCheck = {
      userId,
      resource,
      action,
      resourceId: context.resourceId,
      tenantId: context.tenantId,
      workspaceId: context.workspaceId,
      teamId: context.teamId,
      context: context.requestData,
    };

    const result = await policyEngine.checkPermission(check);
    return result.granted;
  }

  // Detailed access control check with result
  async check(
    userId: string,
    action: ActionType,
    resource: ResourceType,
    context?: {
      tenantId?: string;
      workspaceId?: string;
      teamId?: string;
      resourceId?: string;
      requestData?: Record<string, any>;
    }
  ): Promise<PermissionResult> {
    if (!context?.tenantId) {
      throw new Error("Tenant ID is required for access control checks");
    }

    const policyEngine = this.getPolicyEngine(context.tenantId);
    
    const check: PermissionCheck = {
      userId,
      resource,
      action,
      resourceId: context.resourceId,
      tenantId: context.tenantId,
      workspaceId: context.workspaceId,
      teamId: context.teamId,
      context: context.requestData,
    };

    return policyEngine.checkPermission(check);
  }

  // Batch permission checks
  async canMultiple(
    userId: string,
    permissions: Array<{
      action: ActionType;
      resource: ResourceType;
      resourceId?: string;
    }>,
    context?: {
      tenantId?: string;
      workspaceId?: string;
      teamId?: string;
      requestData?: Record<string, any>;
    }
  ): Promise<boolean[]> {
    if (!context?.tenantId) {
      throw new Error("Tenant ID is required for access control checks");
    }

    const policyEngine = this.getPolicyEngine(context.tenantId);
    
    const checks: PermissionCheck[] = permissions.map(perm => ({
      userId,
      resource: perm.resource,
      action: perm.action,
      resourceId: perm.resourceId,
      tenantId: context.tenantId!,
      workspaceId: context.workspaceId,
      teamId: context.teamId,
      context: context.requestData,
    }));

    const results = await policyEngine.checkMultiplePermissions(checks);
    return results.map(result => result.granted);
  }

  // Check if user has any of the specified permissions
  async canAny(
    userId: string,
    permissions: Array<{
      action: ActionType;
      resource: ResourceType;
      resourceId?: string;
    }>,
    context?: {
      tenantId?: string;
      workspaceId?: string;
      teamId?: string;
      requestData?: Record<string, any>;
    }
  ): Promise<boolean> {
    const results = await this.canMultiple(userId, permissions, context);
    return results.some(result => result);
  }

  // Check if user has all of the specified permissions
  async canAll(
    userId: string,
    permissions: Array<{
      action: ActionType;
      resource: ResourceType;
      resourceId?: string;
    }>,
    context?: {
      tenantId?: string;
      workspaceId?: string;
      teamId?: string;
      requestData?: Record<string, any>;
    }
  ): Promise<boolean> {
    const results = await this.canMultiple(userId, permissions, context);
    return results.every(result => result);
  }

  // Convenience methods for common checks
  async canRead(userId: string, resource: ResourceType, resourceId?: string, context?: any): Promise<boolean> {
    return this.can(userId, "read", resource, { ...context, resourceId });
  }

  async canWrite(userId: string, resource: ResourceType, resourceId?: string, context?: any): Promise<boolean> {
    return this.can(userId, "update", resource, { ...context, resourceId });
  }

  async canCreate(userId: string, resource: ResourceType, context?: any): Promise<boolean> {
    return this.can(userId, "create", resource, context);
  }

  async canDelete(userId: string, resource: ResourceType, resourceId?: string, context?: any): Promise<boolean> {
    return this.can(userId, "delete", resource, { ...context, resourceId });
  }

  async canManage(userId: string, resource: ResourceType, resourceId?: string, context?: any): Promise<boolean> {
    return this.can(userId, "manage", resource, { ...context, resourceId });
  }

  // Resource ownership checks
  async isOwner(userId: string, resource: ResourceType, resourceId: string, context?: any): Promise<boolean> {
    // This would typically check the resource's owner field
    // For now, we'll use a simple check
    return resourceId === userId;
  }

  // Team membership checks
  async isTeamMember(userId: string, teamId: string, context?: any): Promise<boolean> {
    return this.can(userId, "read", "team", { ...context, resourceId: teamId });
  }

  // Workspace membership checks
  async isWorkspaceMember(userId: string, workspaceId: string, context?: any): Promise<boolean> {
    return this.can(userId, "read", "workspace", { ...context, resourceId: workspaceId });
  }

  // Organization membership checks
  async isOrganizationMember(userId: string, context?: any): Promise<boolean> {
    return this.can(userId, "read", "organization", context);
  }

  // Clear cache for tenant (useful for testing or when roles change)
  clearCache(tenantId: string): void {
    this.policyEngines.delete(tenantId);
  }

  // Clear all caches
  clearAllCaches(): void {
    this.policyEngines.clear();
  }
}

// Export singleton instance
export const accessControl = AccessControl.getInstance();

// Convenience function exports
export const can = accessControl.can.bind(accessControl);
export const check = accessControl.check.bind(accessControl);
export const canRead = accessControl.canRead.bind(accessControl);
export const canWrite = accessControl.canWrite.bind(accessControl);
export const canCreate = accessControl.canCreate.bind(accessControl);
export const canDelete = accessControl.canDelete.bind(accessControl);
export const canManage = accessControl.canManage.bind(accessControl);
