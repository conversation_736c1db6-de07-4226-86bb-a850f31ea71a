# Performance Analytics Implementation

## Feature Overview

Implement comprehensive performance analytics for the NEXUS SaaS Starter using Web Vitals, Real User Monitoring (RUM), and advanced analytics to track user experience, application performance, and business metrics with multi-tenant isolation and privacy compliance.

## Context & Research

### Current Implementation Analysis
Based on codebase analysis, existing analytics patterns include:
- **Basic Analytics**: Some user tracking and event collection
- **Performance Monitoring**: Basic server-side performance metrics
- **Error Tracking**: Basic error logging and reporting
- **Business Metrics**: Revenue and subscription tracking

### Technology Stack Verification (Context7)

**Analytics Library Patterns**:
- Lightweight analytics abstraction layer for tracking page views and custom events
- Plugin-based architecture supporting multiple analytics providers
- User identification and trait tracking
- Event tracking with custom properties and context

**Performance Monitoring Strategies**:
- Web Vitals tracking (LCP, FID, CLS, TTFB)
- Real User Monitoring (RUM) for actual user experience
- Performance API integration for detailed metrics
- User activity and engagement tracking

**Privacy and Compliance**:
- Do Not Track (DNT) support and respect
- GDPR compliance with consent management
- Data anonymization and user privacy protection
- Conditional loading based on user consent

## Implementation Blueprint

### Data Models and Structure

```typescript
// Enhanced performance analytics configuration
interface PerformanceAnalyticsConfig {
  providers: {
    primary: AnalyticsProvider;
    secondary: AnalyticsProvider[];
    performance: PerformanceProvider[];
  };
  tracking: {
    webVitals: WebVitalsConfig;
    userActivity: UserActivityConfig;
    businessMetrics: BusinessMetricsConfig;
    errors: ErrorTrackingConfig;
  };
  privacy: {
    respectDNT: boolean;
    gdprCompliant: boolean;
    consentRequired: boolean;
    dataRetention: number;
  };
  multiTenant: {
    isolation: 'strict' | 'shared';
    customDomains: boolean;
    tenantSpecificMetrics: boolean;
  };
}

interface WebVitalsConfig {
  lcp: boolean; // Largest Contentful Paint
  fid: boolean; // First Input Delay
  cls: boolean; // Cumulative Layout Shift
  ttfb: boolean; // Time to First Byte
  fcp: boolean; // First Contentful Paint
}

interface UserActivityConfig {
  pageViews: boolean;
  sessionTracking: boolean;
  scrollDepth: boolean;
  timeOnPage: boolean;
  clickTracking: boolean;
}
```

### Task Breakdown

**Phase 1: Core Analytics Infrastructure (3-4 hours)**

1. **Analytics Library Setup**
   - File: `lib/analytics/core.ts`
   - Implement multi-provider analytics abstraction
   - Add plugin-based architecture for extensibility
   - Configure privacy-compliant tracking
   - Pattern: Follow analytics library best practices

2. **Web Vitals Integration**
   - File: `lib/analytics/web-vitals.ts`
   - Implement Core Web Vitals tracking
   - Add performance API integration
   - Configure real user monitoring
   - Pattern: Web performance measurement standards

**Phase 2: Advanced Performance Tracking (3-4 hours)**

3. **Real User Monitoring (RUM)**
   - File: `lib/analytics/rum.ts`
   - Implement comprehensive RUM tracking
   - Add user journey and flow analysis
   - Configure performance regression detection
   - Pattern: User experience optimization

4. **Business Metrics Analytics**
   - File: `lib/analytics/business-metrics.ts`
   - Track conversion funnels and KPIs
   - Implement cohort analysis and retention
   - Add revenue and subscription analytics
   - Pattern: Product analytics best practices

**Phase 3: Privacy and Compliance (2-3 hours)**

5. **Privacy-Compliant Tracking**
   - File: `lib/analytics/privacy.ts`
   - Implement GDPR compliance features
   - Add consent management integration
   - Configure data anonymization
   - Pattern: Privacy-by-design principles

6. **Multi-Tenant Analytics Isolation**
   - File: `lib/analytics/tenant-isolation.ts`
   - Implement tenant-specific analytics
   - Add cross-tenant analytics aggregation
   - Configure custom domain tracking
   - Pattern: Multi-tenant SaaS analytics

### Integration Points

**Frontend Integration**:
- Next.js App Router integration with analytics
- React component tracking and user interactions
- Client-side performance monitoring
- Progressive Web App (PWA) analytics

**Backend Integration**:
- Server-side event tracking and validation
- API performance monitoring
- Database query performance analytics
- Background job and queue analytics

**Business Integration**:
- Subscription lifecycle tracking
- Revenue attribution and analysis
- Customer success metrics
- Product usage analytics

## Validation Gates

### Level 1: Syntax & Style
```bash
npm run lint                    # ESLint checks
npx tsc --noEmit               # TypeScript validation
npm run test:unit              # Unit tests for analytics
```

### Level 2: Analytics Integration Tests
```bash
npm run dev                    # Start development server

# Test analytics initialization
curl http://localhost:3000/api/analytics/health

# Test event tracking
curl -X POST http://localhost:3000/api/analytics/track \
  -H "Content-Type: application/json" \
  -d '{"event": "test_event", "properties": {"test": true}}'

# Test web vitals collection
npm run test:web-vitals
```

### Level 3: Performance Analytics Tests
```bash
# Test RUM data collection
npm run test:rum

# Test business metrics tracking
npm run test:business-metrics

# Test privacy compliance
npm run test:privacy-compliance
```

### Level 4: Production Analytics Validation
```bash
# Validate analytics data flow
npm run analytics:validate

# Test multi-tenant isolation
npm run test:tenant-analytics

# Performance regression testing
npm run test:performance-regression
```

## Quality Standards Checklist

- [ ] Web Vitals tracking for all critical pages
- [ ] Real User Monitoring (RUM) implementation
- [ ] Business metrics and KPI tracking
- [ ] Privacy-compliant data collection
- [ ] Multi-tenant analytics isolation
- [ ] Performance regression detection
- [ ] User journey and funnel analysis
- [ ] Error tracking and alerting
- [ ] GDPR compliance and consent management
- [ ] Analytics data validation and quality

## Security Considerations

- **Data Privacy**: PII scrubbing and anonymization
- **Consent Management**: GDPR/CCPA compliance
- **Data Security**: Encrypted data transmission and storage
- **Access Control**: Role-based analytics access
- **Audit Logging**: Analytics access and modification tracking

## Performance Targets

- **Analytics Overhead**: <2% performance impact
- **Data Collection**: 99.9% event capture reliability
- **Real-time Processing**: <5s latency for critical events
- **Dashboard Performance**: <3s load time for analytics dashboards
- **Data Retention**: Configurable retention policies per tenant

---

**Implementation Priority**: HIGH - Critical for product optimization
**Estimated Effort**: 10-15 hours
**Dependencies**: Analytics providers, performance monitoring tools
**Success Metrics**: User experience insights, performance optimization, business growth

## Detailed Implementation

### 1. Core Analytics Infrastructure

```typescript
// lib/analytics/core.ts
import Analytics from 'analytics';
import googleAnalytics from '@analytics/google-analytics';
import mixpanelPlugin from '@analytics/mixpanel';
import segmentPlugin from '@analytics/segment';
import { doNotTrackEnabled } from 'analytics-plugin-do-not-track';
import { getTenantContext } from '@/lib/auth/tenant-context';
import { logger } from '@/lib/logger';

interface AnalyticsConfig {
  app: string;
  version: string;
  debug: boolean;
  plugins: any[];
}

interface TrackingEvent {
  event: string;
  properties?: Record<string, any>;
  context?: {
    tenantId?: string;
    userId?: string;
    sessionId?: string;
    page?: {
      url: string;
      title: string;
      referrer: string;
    };
  };
}

class NexusAnalytics {
  private analytics: any;
  private isInitialized = false;
  private eventQueue: TrackingEvent[] = [];
  private sessionId: string;
  private userId?: string;
  private tenantId?: string;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.initializeAnalytics();
  }

  private async initializeAnalytics(): Promise<void> {
    try {
      // Check if user has opted out
      const isDNTEnabled = doNotTrackEnabled();
      const hasConsent = await this.checkUserConsent();

      if (isDNTEnabled || !hasConsent) {
        logger.info('Analytics disabled due to DNT or lack of consent');
        return;
      }

      // Get tenant context for configuration
      this.tenantId = await getTenantContext();

      const config = await this.getAnalyticsConfig();

      this.analytics = Analytics({
        app: config.app,
        version: config.version,
        debug: config.debug,
        plugins: config.plugins
      });

      this.isInitialized = true;

      // Process queued events
      await this.processEventQueue();

      logger.info('Analytics initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize analytics', { error });
    }
  }

  private async getAnalyticsConfig(): Promise<AnalyticsConfig> {
    const plugins = [];

    // Google Analytics
    if (process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID) {
      plugins.push(
        googleAnalytics({
          measurementIds: [process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID],
          gtagConfig: {
            custom_map: {
              tenant_id: 'tenant_id',
              user_tier: 'user_tier'
            }
          }
        })
      );
    }

    // Mixpanel
    if (process.env.NEXT_PUBLIC_MIXPANEL_TOKEN) {
      plugins.push(
        mixpanelPlugin({
          token: process.env.NEXT_PUBLIC_MIXPANEL_TOKEN,
          options: {
            track_pageview: false, // We'll handle this manually
            persistence: 'localStorage',
            property_blacklist: ['$current_url', '$initial_referrer']
          }
        })
      );
    }

    // Segment
    if (process.env.NEXT_PUBLIC_SEGMENT_WRITE_KEY) {
      plugins.push(
        segmentPlugin({
          writeKey: process.env.NEXT_PUBLIC_SEGMENT_WRITE_KEY
        })
      );
    }

    return {
      app: 'nexus-saas',
      version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
      debug: process.env.NODE_ENV === 'development',
      plugins
    };
  }

  private async checkUserConsent(): Promise<boolean> {
    // Check for stored consent
    if (typeof window !== 'undefined') {
      const consent = localStorage.getItem('analytics-consent');
      return consent === 'granted';
    }
    return false;
  }

  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private async processEventQueue(): Promise<void> {
    if (!this.isInitialized || this.eventQueue.length === 0) return;

    for (const event of this.eventQueue) {
      await this.trackEvent(event);
    }

    this.eventQueue = [];
  }

  // Public API methods
  async page(properties?: Record<string, any>): Promise<void> {
    const event: TrackingEvent = {
      event: 'page_view',
      properties: {
        ...properties,
        timestamp: Date.now(),
        session_id: this.sessionId,
        tenant_id: this.tenantId,
        user_id: this.userId
      },
      context: await this.getPageContext()
    };

    if (this.isInitialized) {
      this.analytics.page(event.properties);
    } else {
      this.eventQueue.push(event);
    }
  }

  async track(event: string, properties?: Record<string, any>): Promise<void> {
    const trackingEvent: TrackingEvent = {
      event,
      properties: {
        ...properties,
        timestamp: Date.now(),
        session_id: this.sessionId,
        tenant_id: this.tenantId,
        user_id: this.userId
      },
      context: await this.getPageContext()
    };

    if (this.isInitialized) {
      this.analytics.track(event, trackingEvent.properties);
    } else {
      this.eventQueue.push(trackingEvent);
    }
  }

  async identify(userId: string, traits?: Record<string, any>): Promise<void> {
    this.userId = userId;

    const identifyData = {
      ...traits,
      tenant_id: this.tenantId,
      session_id: this.sessionId,
      identified_at: Date.now()
    };

    if (this.isInitialized) {
      this.analytics.identify(userId, identifyData);
    } else {
      // Store for later identification
      this.eventQueue.push({
        event: 'identify',
        properties: { userId, traits: identifyData }
      });
    }
  }

  private async getPageContext() {
    if (typeof window === 'undefined') return {};

    return {
      tenantId: this.tenantId,
      userId: this.userId,
      sessionId: this.sessionId,
      page: {
        url: window.location.href,
        title: document.title,
        referrer: document.referrer,
        path: window.location.pathname,
        search: window.location.search,
        hash: window.location.hash
      },
      screen: {
        width: window.screen.width,
        height: window.screen.height,
        density: window.devicePixelRatio || 1
      },
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      userAgent: navigator.userAgent,
      language: navigator.language,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    };
  }

  // Consent management
  async grantConsent(): Promise<void> {
    if (typeof window !== 'undefined') {
      localStorage.setItem('analytics-consent', 'granted');
      await this.initializeAnalytics();
    }
  }

  async revokeConsent(): Promise<void> {
    if (typeof window !== 'undefined') {
      localStorage.setItem('analytics-consent', 'denied');
      this.isInitialized = false;
      this.eventQueue = [];
    }
  }

  // Utility methods
  getSessionId(): string {
    return this.sessionId;
  }

  getUserId(): string | undefined {
    return this.userId;
  }

  getTenantId(): string | undefined {
    return this.tenantId;
  }

  isReady(): boolean {
    return this.isInitialized;
  }
}

// Singleton instance
export const analytics = new NexusAnalytics();

// React hook for analytics
export function useAnalytics() {
  const [isReady, setIsReady] = React.useState(analytics.isReady());

  React.useEffect(() => {
    const checkReady = () => setIsReady(analytics.isReady());
    const interval = setInterval(checkReady, 100);

    return () => clearInterval(interval);
  }, []);

  return {
    analytics,
    isReady,
    track: analytics.track.bind(analytics),
    page: analytics.page.bind(analytics),
    identify: analytics.identify.bind(analytics),
    grantConsent: analytics.grantConsent.bind(analytics),
    revokeConsent: analytics.revokeConsent.bind(analytics)
  };
}
```

### 2. Web Vitals Integration

```typescript
// lib/analytics/web-vitals.ts
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';
import { analytics } from './core';
import { logger } from '@/lib/logger';

interface WebVitalMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
  navigationType: string;
}

interface PerformanceMetrics {
  webVitals: Record<string, WebVitalMetric>;
  customMetrics: Record<string, number>;
  navigationTiming: PerformanceTiming | null;
  resourceTiming: PerformanceResourceTiming[];
}

class WebVitalsTracker {
  private metrics: PerformanceMetrics = {
    webVitals: {},
    customMetrics: {},
    navigationTiming: null,
    resourceTiming: []
  };

  private thresholds = {
    LCP: { good: 2500, poor: 4000 },
    FID: { good: 100, poor: 300 },
    CLS: { good: 0.1, poor: 0.25 },
    FCP: { good: 1800, poor: 3000 },
    TTFB: { good: 800, poor: 1800 }
  };

  constructor() {
    this.initializeWebVitals();
    this.initializeCustomMetrics();
  }

  private initializeWebVitals(): void {
    if (typeof window === 'undefined') return;

    // Largest Contentful Paint
    getLCP((metric) => {
      this.recordWebVital('LCP', metric);
    });

    // First Input Delay
    getFID((metric) => {
      this.recordWebVital('FID', metric);
    });

    // Cumulative Layout Shift
    getCLS((metric) => {
      this.recordWebVital('CLS', metric);
    });

    // First Contentful Paint
    getFCP((metric) => {
      this.recordWebVital('FCP', metric);
    });

    // Time to First Byte
    getTTFB((metric) => {
      this.recordWebVital('TTFB', metric);
    });
  }

  private recordWebVital(name: string, metric: any): void {
    const rating = this.getRating(name, metric.value);

    const webVital: WebVitalMetric = {
      name,
      value: metric.value,
      rating,
      delta: metric.delta,
      id: metric.id,
      navigationType: metric.navigationType || 'unknown'
    };

    this.metrics.webVitals[name] = webVital;

    // Track in analytics
    analytics.track('web_vital_measured', {
      metric_name: name,
      metric_value: metric.value,
      metric_rating: rating,
      metric_delta: metric.delta,
      navigation_type: metric.navigationType,
      page_url: window.location.href,
      page_title: document.title
    });

    logger.info(`Web Vital ${name} measured`, {
      value: metric.value,
      rating,
      delta: metric.delta
    });
  }

  private getRating(metricName: string, value: number): 'good' | 'needs-improvement' | 'poor' {
    const threshold = this.thresholds[metricName as keyof typeof this.thresholds];
    if (!threshold) return 'good';

    if (value <= threshold.good) return 'good';
    if (value <= threshold.poor) return 'needs-improvement';
    return 'poor';
  }

  private initializeCustomMetrics(): void {
    if (typeof window === 'undefined') return;

    // Track page load time
    window.addEventListener('load', () => {
      const loadTime = performance.now();
      this.recordCustomMetric('page_load_time', loadTime);
    });

    // Track DOM content loaded
    document.addEventListener('DOMContentLoaded', () => {
      const domLoadTime = performance.now();
      this.recordCustomMetric('dom_content_loaded', domLoadTime);
    });

    // Track resource loading
    this.trackResourceTiming();

    // Track user interactions
    this.trackUserInteractions();
  }

  private recordCustomMetric(name: string, value: number): void {
    this.metrics.customMetrics[name] = value;

    analytics.track('custom_performance_metric', {
      metric_name: name,
      metric_value: value,
      page_url: window.location.href,
      timestamp: Date.now()
    });
  }

  private trackResourceTiming(): void {
    if (typeof window === 'undefined' || !window.performance) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries() as PerformanceResourceTiming[];

      entries.forEach((entry) => {
        this.metrics.resourceTiming.push(entry);

        // Track slow resources
        if (entry.duration > 1000) {
          analytics.track('slow_resource_detected', {
            resource_name: entry.name,
            resource_type: entry.initiatorType,
            duration: entry.duration,
            size: entry.transferSize || 0,
            page_url: window.location.href
          });
        }
      });
    });

    observer.observe({ entryTypes: ['resource'] });
  }

  private trackUserInteractions(): void {
    if (typeof window === 'undefined') return;

    let interactionCount = 0;
    let firstInteractionTime: number | null = null;

    const trackInteraction = (event: Event) => {
      if (firstInteractionTime === null) {
        firstInteractionTime = performance.now();
        this.recordCustomMetric('time_to_first_interaction', firstInteractionTime);
      }

      interactionCount++;

      if (interactionCount % 10 === 0) {
        analytics.track('user_interaction_milestone', {
          interaction_count: interactionCount,
          time_since_load: performance.now(),
          page_url: window.location.href
        });
      }
    };

    ['click', 'keydown', 'scroll', 'touchstart'].forEach(eventType => {
      document.addEventListener(eventType, trackInteraction, { passive: true });
    });
  }

  // Public API
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  getWebVitalsSummary(): Record<string, any> {
    const summary: Record<string, any> = {};

    Object.entries(this.metrics.webVitals).forEach(([name, metric]) => {
      summary[name] = {
        value: metric.value,
        rating: metric.rating,
        threshold: this.thresholds[name as keyof typeof this.thresholds]
      };
    });

    return summary;
  }

  // Performance score calculation
  calculatePerformanceScore(): number {
    const vitals = this.metrics.webVitals;
    let score = 100;

    // Deduct points based on poor ratings
    Object.values(vitals).forEach((vital) => {
      if (vital.rating === 'poor') {
        score -= 20;
      } else if (vital.rating === 'needs-improvement') {
        score -= 10;
      }
    });

    return Math.max(0, score);
  }

  // Export metrics for reporting
  exportMetrics(): string {
    return JSON.stringify({
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      metrics: this.metrics,
      performanceScore: this.calculatePerformanceScore()
    }, null, 2);
  }
}

export const webVitalsTracker = new WebVitalsTracker();

// React hook for web vitals
export function useWebVitals() {
  const [metrics, setMetrics] = React.useState(webVitalsTracker.getMetrics());
  const [score, setScore] = React.useState(webVitalsTracker.calculatePerformanceScore());

  React.useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(webVitalsTracker.getMetrics());
      setScore(webVitalsTracker.calculatePerformanceScore());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  return {
    metrics,
    score,
    summary: webVitalsTracker.getWebVitalsSummary(),
    export: webVitalsTracker.exportMetrics.bind(webVitalsTracker)
  };
}
```

### 3. Real User Monitoring (RUM) Implementation

```typescript
// lib/analytics/rum.ts
import { analytics } from './core';
import { webVitalsTracker } from './web-vitals';
import { logger } from '@/lib/logger';

interface UserSession {
  sessionId: string;
  userId?: string;
  tenantId?: string;
  startTime: number;
  lastActivity: number;
  pageViews: number;
  interactions: number;
  errors: number;
  device: DeviceInfo;
  location: LocationInfo;
}

interface DeviceInfo {
  type: 'mobile' | 'tablet' | 'desktop';
  os: string;
  browser: string;
  screenResolution: string;
  viewportSize: string;
  pixelRatio: number;
  touchSupport: boolean;
}

interface LocationInfo {
  country?: string;
  region?: string;
  city?: string;
  timezone: string;
  language: string;
}

interface UserJourney {
  steps: JourneyStep[];
  funnels: FunnelAnalysis[];
  dropOffPoints: DropOffPoint[];
}

interface JourneyStep {
  timestamp: number;
  page: string;
  action: string;
  duration: number;
  metadata?: Record<string, any>;
}

class RealUserMonitoring {
  private session: UserSession;
  private journey: UserJourney;
  private performanceObserver?: PerformanceObserver;
  private errorCount = 0;
  private isActive = true;

  constructor() {
    this.session = this.initializeSession();
    this.journey = { steps: [], funnels: [], dropOffPoints: [] };
    this.setupMonitoring();
  }

  private initializeSession(): UserSession {
    const sessionId = this.generateSessionId();
    const now = Date.now();

    return {
      sessionId,
      startTime: now,
      lastActivity: now,
      pageViews: 0,
      interactions: 0,
      errors: 0,
      device: this.getDeviceInfo(),
      location: this.getLocationInfo()
    };
  }

  private generateSessionId(): string {
    return `rum_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getDeviceInfo(): DeviceInfo {
    if (typeof window === 'undefined') {
      return {
        type: 'desktop',
        os: 'unknown',
        browser: 'unknown',
        screenResolution: '0x0',
        viewportSize: '0x0',
        pixelRatio: 1,
        touchSupport: false
      };
    }

    const userAgent = navigator.userAgent;
    const isMobile = /Mobile|Android|iPhone|iPad/.test(userAgent);
    const isTablet = /iPad|Tablet/.test(userAgent);

    return {
      type: isMobile ? 'mobile' : isTablet ? 'tablet' : 'desktop',
      os: this.detectOS(userAgent),
      browser: this.detectBrowser(userAgent),
      screenResolution: `${screen.width}x${screen.height}`,
      viewportSize: `${window.innerWidth}x${window.innerHeight}`,
      pixelRatio: window.devicePixelRatio || 1,
      touchSupport: 'ontouchstart' in window
    };
  }

  private detectOS(userAgent: string): string {
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    return 'Unknown';
  }

  private detectBrowser(userAgent: string): string {
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  }

  private getLocationInfo(): LocationInfo {
    return {
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: navigator.language || 'unknown'
    };
  }

  private setupMonitoring(): void {
    if (typeof window === 'undefined') return;

    // Track page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.trackEvent('page_hidden');
      } else {
        this.trackEvent('page_visible');
        this.updateActivity();
      }
    });

    // Track user interactions
    this.setupInteractionTracking();

    // Track errors
    this.setupErrorTracking();

    // Track performance issues
    this.setupPerformanceTracking();

    // Track page unload
    window.addEventListener('beforeunload', () => {
      this.endSession();
    });

    // Periodic session updates
    setInterval(() => {
      this.updateSession();
    }, 30000); // Every 30 seconds
  }

  private setupInteractionTracking(): void {
    const interactionEvents = ['click', 'scroll', 'keydown', 'touchstart'];

    interactionEvents.forEach(eventType => {
      document.addEventListener(eventType, (event) => {
        this.trackInteraction(eventType, event);
      }, { passive: true });
    });
  }

  private setupErrorTracking(): void {
    // JavaScript errors
    window.addEventListener('error', (event) => {
      this.trackError('javascript_error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack
      });
    });

    // Promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.trackError('unhandled_promise_rejection', {
        reason: event.reason,
        promise: event.promise
      });
    });

    // Resource loading errors
    document.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.trackError('resource_error', {
          element: (event.target as Element).tagName,
          source: (event.target as any).src || (event.target as any).href,
          message: 'Failed to load resource'
        });
      }
    }, true);
  }

  private setupPerformanceTracking(): void {
    if (!window.PerformanceObserver) return;

    // Track long tasks
    try {
      this.performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'longtask') {
            this.trackPerformanceIssue('long_task', {
              duration: entry.duration,
              startTime: entry.startTime,
              name: entry.name
            });
          }
        });
      });

      this.performanceObserver.observe({ entryTypes: ['longtask'] });
    } catch (error) {
      logger.warn('Long task observer not supported', { error });
    }
  }

  private trackEvent(action: string, metadata?: Record<string, any>): void {
    const step: JourneyStep = {
      timestamp: Date.now(),
      page: window.location.pathname,
      action,
      duration: Date.now() - this.session.lastActivity,
      metadata
    };

    this.journey.steps.push(step);
    this.updateActivity();

    analytics.track('rum_event', {
      session_id: this.session.sessionId,
      action,
      page: window.location.pathname,
      duration: step.duration,
      metadata
    });
  }

  private trackInteraction(type: string, event: Event): void {
    this.session.interactions++;

    const target = event.target as Element;
    const metadata = {
      element: target.tagName,
      id: target.id,
      className: target.className,
      text: target.textContent?.slice(0, 100)
    };

    this.trackEvent(`interaction_${type}`, metadata);
  }

  private trackError(type: string, details: Record<string, any>): void {
    this.session.errors++;
    this.errorCount++;

    analytics.track('rum_error', {
      session_id: this.session.sessionId,
      error_type: type,
      error_details: details,
      page: window.location.pathname,
      user_agent: navigator.userAgent,
      timestamp: Date.now()
    });

    logger.error(`RUM Error: ${type}`, details);
  }

  private trackPerformanceIssue(type: string, details: Record<string, any>): void {
    analytics.track('rum_performance_issue', {
      session_id: this.session.sessionId,
      issue_type: type,
      issue_details: details,
      page: window.location.pathname,
      timestamp: Date.now()
    });
  }

  private updateActivity(): void {
    this.session.lastActivity = Date.now();
    this.isActive = true;
  }

  private updateSession(): void {
    if (!this.isActive) return;

    const sessionDuration = Date.now() - this.session.startTime;
    const webVitals = webVitalsTracker.getWebVitalsSummary();

    analytics.track('rum_session_update', {
      session_id: this.session.sessionId,
      session_duration: sessionDuration,
      page_views: this.session.pageViews,
      interactions: this.session.interactions,
      errors: this.session.errors,
      web_vitals: webVitals,
      device_info: this.session.device,
      location_info: this.session.location
    });
  }

  // Public API
  trackPageView(page?: string): void {
    this.session.pageViews++;
    this.trackEvent('page_view', { page: page || window.location.pathname });
  }

  trackCustomEvent(event: string, properties?: Record<string, any>): void {
    this.trackEvent(event, properties);
  }

  setUserId(userId: string): void {
    this.session.userId = userId;
  }

  setTenantId(tenantId: string): void {
    this.session.tenantId = tenantId;
  }

  getSessionInfo(): UserSession {
    return { ...this.session };
  }

  getJourney(): UserJourney {
    return { ...this.journey };
  }

  endSession(): void {
    const sessionDuration = Date.now() - this.session.startTime;

    analytics.track('rum_session_end', {
      session_id: this.session.sessionId,
      session_duration: sessionDuration,
      total_page_views: this.session.pageViews,
      total_interactions: this.session.interactions,
      total_errors: this.session.errors,
      final_web_vitals: webVitalsTracker.getWebVitalsSummary(),
      journey_steps: this.journey.steps.length
    });

    this.isActive = false;

    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }
  }
}

export const rumTracker = new RealUserMonitoring();

// React hook for RUM
export function useRUM() {
  const [sessionInfo, setSessionInfo] = React.useState(rumTracker.getSessionInfo());
  const [journey, setJourney] = React.useState(rumTracker.getJourney());

  React.useEffect(() => {
    const interval = setInterval(() => {
      setSessionInfo(rumTracker.getSessionInfo());
      setJourney(rumTracker.getJourney());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return {
    sessionInfo,
    journey,
    trackPageView: rumTracker.trackPageView.bind(rumTracker),
    trackCustomEvent: rumTracker.trackCustomEvent.bind(rumTracker),
    setUserId: rumTracker.setUserId.bind(rumTracker),
    setTenantId: rumTracker.setTenantId.bind(rumTracker)
  };
}
```

### 4. Business Metrics Analytics

```typescript
// lib/analytics/business-metrics.ts
import { analytics } from './core';
import { prisma } from '@/lib/database/prisma';
import { logger } from '@/lib/logger';

interface ConversionFunnel {
  name: string;
  steps: FunnelStep[];
  conversionRate: number;
  dropOffPoints: DropOffAnalysis[];
}

interface FunnelStep {
  name: string;
  event: string;
  users: number;
  conversionFromPrevious: number;
  averageTimeToNext: number;
}

interface DropOffAnalysis {
  fromStep: string;
  toStep: string;
  dropOffRate: number;
  commonReasons: string[];
}

interface CohortAnalysis {
  cohortDate: string;
  cohortSize: number;
  retentionRates: Record<string, number>;
  revenuePerUser: Record<string, number>;
}

interface BusinessKPI {
  name: string;
  value: number;
  target: number;
  trend: 'up' | 'down' | 'stable';
  period: 'daily' | 'weekly' | 'monthly';
}

class BusinessMetricsTracker {
  private funnels: Map<string, ConversionFunnel> = new Map();
  private kpis: Map<string, BusinessKPI> = new Map();

  constructor() {
    this.initializeDefaultFunnels();
    this.initializeDefaultKPIs();
  }

  private initializeDefaultFunnels(): void {
    // Signup funnel
    this.defineFunnel('signup', [
      { name: 'Landing Page Visit', event: 'page_view_landing' },
      { name: 'Signup Form View', event: 'signup_form_view' },
      { name: 'Signup Form Submit', event: 'signup_form_submit' },
      { name: 'Email Verification', event: 'email_verified' },
      { name: 'Onboarding Complete', event: 'onboarding_complete' }
    ]);

    // Subscription funnel
    this.defineFunnel('subscription', [
      { name: 'Pricing Page View', event: 'pricing_page_view' },
      { name: 'Plan Selection', event: 'plan_selected' },
      { name: 'Checkout Started', event: 'checkout_started' },
      { name: 'Payment Info Entered', event: 'payment_info_entered' },
      { name: 'Subscription Created', event: 'subscription_created' }
    ]);

    // Feature adoption funnel
    this.defineFunnel('feature_adoption', [
      { name: 'Feature Discovered', event: 'feature_discovered' },
      { name: 'Feature Clicked', event: 'feature_clicked' },
      { name: 'Feature Used', event: 'feature_used' },
      { name: 'Feature Mastered', event: 'feature_mastered' }
    ]);
  }

  private initializeDefaultKPIs(): void {
    this.defineKPI('monthly_recurring_revenue', 0, 100000, 'monthly');
    this.defineKPI('customer_acquisition_cost', 0, 50, 'monthly');
    this.defineKPI('customer_lifetime_value', 0, 1000, 'monthly');
    this.defineKPI('churn_rate', 0, 5, 'monthly');
    this.defineKPI('net_promoter_score', 0, 50, 'monthly');
    this.defineKPI('daily_active_users', 0, 10000, 'daily');
    this.defineKPI('feature_adoption_rate', 0, 80, 'weekly');
  }

  // Funnel Management
  defineFunnel(name: string, steps: Array<{ name: string; event: string }>): void {
    const funnel: ConversionFunnel = {
      name,
      steps: steps.map(step => ({
        name: step.name,
        event: step.event,
        users: 0,
        conversionFromPrevious: 0,
        averageTimeToNext: 0
      })),
      conversionRate: 0,
      dropOffPoints: []
    };

    this.funnels.set(name, funnel);
  }

  async trackFunnelEvent(funnelName: string, event: string, userId: string, properties?: Record<string, any>): Promise<void> {
    const funnel = this.funnels.get(funnelName);
    if (!funnel) return;

    const stepIndex = funnel.steps.findIndex(step => step.event === event);
    if (stepIndex === -1) return;

    // Track the event
    analytics.track(event, {
      funnel_name: funnelName,
      funnel_step: stepIndex,
      step_name: funnel.steps[stepIndex].name,
      user_id: userId,
      ...properties
    });

    // Update funnel analytics
    await this.updateFunnelAnalytics(funnelName);
  }

  private async updateFunnelAnalytics(funnelName: string): Promise<void> {
    const funnel = this.funnels.get(funnelName);
    if (!funnel) return;

    try {
      // This would typically query your analytics database
      // For now, we'll simulate the calculation
      const funnelData = await this.calculateFunnelMetrics(funnelName);

      funnel.steps = funnelData.steps;
      funnel.conversionRate = funnelData.conversionRate;
      funnel.dropOffPoints = funnelData.dropOffPoints;

      // Track funnel performance
      analytics.track('funnel_analysis_updated', {
        funnel_name: funnelName,
        conversion_rate: funnel.conversionRate,
        total_steps: funnel.steps.length,
        biggest_drop_off: funnelData.dropOffPoints[0]?.dropOffRate || 0
      });

    } catch (error) {
      logger.error('Failed to update funnel analytics', { funnelName, error });
    }
  }

  private async calculateFunnelMetrics(funnelName: string): Promise<{
    steps: FunnelStep[];
    conversionRate: number;
    dropOffPoints: DropOffAnalysis[];
  }> {
    // This would typically query your analytics database
    // Simulated calculation for demonstration
    const funnel = this.funnels.get(funnelName)!;
    const steps = [...funnel.steps];

    // Simulate user counts (in real implementation, query from database)
    let previousUsers = 1000;
    steps.forEach((step, index) => {
      const dropOffRate = Math.random() * 0.3; // 0-30% drop-off
      step.users = Math.floor(previousUsers * (1 - dropOffRate));
      step.conversionFromPrevious = index === 0 ? 100 : (step.users / previousUsers) * 100;
      step.averageTimeToNext = Math.random() * 3600; // 0-1 hour
      previousUsers = step.users;
    });

    const conversionRate = steps.length > 0 ? (steps[steps.length - 1].users / steps[0].users) * 100 : 0;

    const dropOffPoints: DropOffAnalysis[] = [];
    for (let i = 0; i < steps.length - 1; i++) {
      const dropOffRate = ((steps[i].users - steps[i + 1].users) / steps[i].users) * 100;
      dropOffPoints.push({
        fromStep: steps[i].name,
        toStep: steps[i + 1].name,
        dropOffRate,
        commonReasons: ['User confusion', 'Technical issues', 'Lack of motivation']
      });
    }

    return { steps, conversionRate, dropOffPoints };
  }

  // KPI Management
  defineKPI(name: string, value: number, target: number, period: 'daily' | 'weekly' | 'monthly'): void {
    this.kpis.set(name, {
      name,
      value,
      target,
      trend: 'stable',
      period
    });
  }

  async updateKPI(name: string, value: number): Promise<void> {
    const kpi = this.kpis.get(name);
    if (!kpi) return;

    const previousValue = kpi.value;
    kpi.value = value;

    // Determine trend
    if (value > previousValue * 1.05) {
      kpi.trend = 'up';
    } else if (value < previousValue * 0.95) {
      kpi.trend = 'down';
    } else {
      kpi.trend = 'stable';
    }

    // Track KPI update
    analytics.track('kpi_updated', {
      kpi_name: name,
      kpi_value: value,
      kpi_target: kpi.target,
      kpi_trend: kpi.trend,
      achievement_rate: (value / kpi.target) * 100,
      period: kpi.period
    });
  }

  // Revenue Tracking
  async trackRevenue(amount: number, currency: string, source: string, userId?: string, tenantId?: string): Promise<void> {
    analytics.track('revenue_generated', {
      amount,
      currency,
      source,
      user_id: userId,
      tenant_id: tenantId,
      timestamp: Date.now()
    });

    // Update MRR if it's a subscription
    if (source.includes('subscription')) {
      await this.updateKPI('monthly_recurring_revenue', amount);
    }
  }

  // Cohort Analysis
  async generateCohortAnalysis(startDate: Date, endDate: Date): Promise<CohortAnalysis[]> {
    const cohorts: CohortAnalysis[] = [];

    // This would typically query your user database
    // Simulated cohort analysis for demonstration
    const monthsBetween = this.getMonthsBetween(startDate, endDate);

    for (let i = 0; i < monthsBetween; i++) {
      const cohortDate = new Date(startDate);
      cohortDate.setMonth(cohortDate.getMonth() + i);

      const cohort: CohortAnalysis = {
        cohortDate: cohortDate.toISOString().slice(0, 7), // YYYY-MM format
        cohortSize: Math.floor(Math.random() * 1000) + 100,
        retentionRates: {},
        revenuePerUser: {}
      };

      // Calculate retention rates for each month
      for (let month = 0; month < 12; month++) {
        const retentionRate = Math.max(0, 100 - (month * 10) - Math.random() * 20);
        cohort.retentionRates[`month_${month}`] = retentionRate;
        cohort.revenuePerUser[`month_${month}`] = retentionRate * 0.5; // Simulated revenue
      }

      cohorts.push(cohort);
    }

    // Track cohort analysis generation
    analytics.track('cohort_analysis_generated', {
      start_date: startDate.toISOString(),
      end_date: endDate.toISOString(),
      cohorts_analyzed: cohorts.length
    });

    return cohorts;
  }

  private getMonthsBetween(startDate: Date, endDate: Date): number {
    return (endDate.getFullYear() - startDate.getFullYear()) * 12 +
           (endDate.getMonth() - startDate.getMonth()) + 1;
  }

  // Public API
  getFunnel(name: string): ConversionFunnel | undefined {
    return this.funnels.get(name);
  }

  getAllFunnels(): ConversionFunnel[] {
    return Array.from(this.funnels.values());
  }

  getKPI(name: string): BusinessKPI | undefined {
    return this.kpis.get(name);
  }

  getAllKPIs(): BusinessKPI[] {
    return Array.from(this.kpis.values());
  }

  // Dashboard data
  getDashboardData(): {
    funnels: ConversionFunnel[];
    kpis: BusinessKPI[];
    summary: Record<string, any>;
  } {
    const funnels = this.getAllFunnels();
    const kpis = this.getAllKPIs();

    const summary = {
      totalFunnels: funnels.length,
      averageConversionRate: funnels.reduce((sum, f) => sum + f.conversionRate, 0) / funnels.length,
      kpisOnTarget: kpis.filter(k => k.value >= k.target).length,
      kpisTotal: kpis.length
    };

    return { funnels, kpis, summary };
  }
}

export const businessMetrics = new BusinessMetricsTracker();

// React hook for business metrics
export function useBusinessMetrics() {
  const [dashboardData, setDashboardData] = React.useState(businessMetrics.getDashboardData());

  React.useEffect(() => {
    const interval = setInterval(() => {
      setDashboardData(businessMetrics.getDashboardData());
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  return {
    ...dashboardData,
    trackFunnelEvent: businessMetrics.trackFunnelEvent.bind(businessMetrics),
    updateKPI: businessMetrics.updateKPI.bind(businessMetrics),
    trackRevenue: businessMetrics.trackRevenue.bind(businessMetrics),
    generateCohortAnalysis: businessMetrics.generateCohortAnalysis.bind(businessMetrics)
  };
}
```

*Built with ❤️ by Nexus-Master Agent*
*Where 125 Senior Developers Meet AI Excellence*
