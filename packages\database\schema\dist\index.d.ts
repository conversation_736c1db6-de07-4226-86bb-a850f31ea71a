export * from "@prisma/client";
export type { Session, SubscriptionPlan, Tenant, TenantStatus, User, UserRole, UserStatus, Workspace } from "@prisma/client";
export declare const DATABASE_CONFIG: {
    readonly maxConnections: number;
    readonly connectionTimeout: number;
    readonly idleTimeout: number;
    readonly logLevel: any;
};
export declare const TENANT_CONTEXT_KEY = "app.current_tenant_id";
export interface TenantContext {
    tenantId: string;
    userId?: string;
}
export declare const getDatabaseUrl: () => string;
export declare const setTenantContext: (tenantId: string) => string;
export declare const getTenantContext: () => string;
export declare const MIGRATION_SCRIPTS: {
    readonly enableRLS: "\n    -- Enable Row Level Security on tenant-isolated tables\n    ALTER TABLE users ENABLE ROW LEVEL SECURITY;\n    ALTER TABLE workspaces ENABLE ROW LEVEL SECURITY;\n    ALTER TABLE sessions ENABLE ROW LEVEL SECURITY;\n  ";
    readonly createRLSPolicies: "\n    -- Create RLS policies for tenant isolation\n    CREATE POLICY tenant_isolation_users ON users\n      FOR ALL TO authenticated\n      USING (tenant_id = current_setting('app.current_tenant_id')::text);\n\n    CREATE POLICY tenant_isolation_workspaces ON workspaces\n      FOR ALL TO authenticated\n      USING (tenant_id = current_setting('app.current_tenant_id')::text);\n\n    CREATE POLICY tenant_isolation_sessions ON sessions\n      FOR ALL TO authenticated\n      USING (tenant_id = current_setting('app.current_tenant_id')::text);\n  ";
    readonly createIndexes: "\n    -- Create performance indexes\n    CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);\n    CREATE INDEX IF NOT EXISTS idx_workspaces_tenant_id ON workspaces(tenant_id);\n    CREATE INDEX IF NOT EXISTS idx_sessions_tenant_id ON sessions(tenant_id);\n    CREATE INDEX IF NOT EXISTS idx_tenants_slug ON tenants(slug);\n    CREATE INDEX IF NOT EXISTS idx_tenants_domain ON tenants(domain);\n    CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions(token);\n    CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);\n  ";
};
//# sourceMappingURL=index.d.ts.map