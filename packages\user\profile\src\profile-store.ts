import { create } from "zustand";
import { UserProfile, ProfileStats, NotificationSettings } from "./profile-types";

interface ProfileStore {
  // State
  profile: UserProfile | null;
  stats: ProfileStats | null;
  notificationSettings: NotificationSettings | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setProfile: (profile: UserProfile | null) => void;
  updateProfile: (updates: Partial<UserProfile>) => void;
  setStats: (stats: ProfileStats | null) => void;
  setNotificationSettings: (settings: NotificationSettings | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
  
  // Computed
  getProfileCompletion: () => number;
  getMissingFields: () => string[];
  getDisplayName: () => string;
}

export const useProfileStore = create<ProfileStore>((set, get) => ({
  // Initial state
  profile: null,
  stats: null,
  notificationSettings: null,
  isLoading: false,
  error: null,
  
  // Actions
  setProfile: (profile) => set({ profile, error: null }),
  
  updateProfile: (updates) => set((state) => ({
    profile: state.profile ? { ...state.profile, ...updates } : null,
  })),
  
  setStats: (stats) => set({ stats }),
  
  setNotificationSettings: (notificationSettings) => set({ notificationSettings }),
  
  setLoading: (isLoading) => set({ isLoading }),
  
  setError: (error) => set({ error, isLoading: false }),
  
  reset: () => set({
    profile: null,
    stats: null,
    notificationSettings: null,
    isLoading: false,
    error: null,
  }),
  
  // Computed
  getProfileCompletion: () => {
    const { profile } = get();
    if (!profile) return 0;
    
    const fields = [
      profile.firstName,
      profile.lastName,
      profile.bio,
      profile.location,
      profile.company,
      profile.jobTitle,
      profile.image,
    ];
    
    const completedFields = fields.filter(field => field && field.trim() !== "").length;
    return Math.round((completedFields / fields.length) * 100);
  },
  
  getMissingFields: () => {
    const { profile } = get();
    if (!profile) return [];
    
    const fieldMap = {
      firstName: profile.firstName,
      lastName: profile.lastName,
      bio: profile.bio,
      location: profile.location,
      company: profile.company,
      jobTitle: profile.jobTitle,
      image: profile.image,
    };
    
    return Object.entries(fieldMap)
      .filter(([_, value]) => !value || value.trim() === "")
      .map(([field]) => field);
  },
  
  getDisplayName: () => {
    const { profile } = get();
    if (!profile) return "";
    
    if (profile.firstName && profile.lastName) {
      return `${profile.firstName} ${profile.lastName}`;
    }
    
    if (profile.name) {
      return profile.name;
    }
    
    if (profile.firstName) {
      return profile.firstName;
    }
    
    return profile.email.split("@")[0];
  },
}));

// Selectors
export const selectProfile = (state: ProfileStore) => state.profile;
export const selectStats = (state: ProfileStore) => state.stats;
export const selectNotificationSettings = (state: ProfileStore) => state.notificationSettings;
export const selectIsLoading = (state: ProfileStore) => state.isLoading;
export const selectError = (state: ProfileStore) => state.error;
export const selectProfileCompletion = (state: ProfileStore) => state.getProfileCompletion();
export const selectMissingFields = (state: ProfileStore) => state.getMissingFields();
export const selectDisplayName = (state: ProfileStore) => state.getDisplayName();
