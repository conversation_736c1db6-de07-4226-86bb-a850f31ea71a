import { create } from "zustand";
import { Role, UserRole, Permission, PermissionResult } from "./rbac-types";

interface RBACStore {
  // State
  roles: Role[];
  userRoles: UserRole[];
  permissions: Permission[];
  permissionCache: Map<string, PermissionResult>;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setRoles: (roles: Role[]) => void;
  addRole: (role: Role) => void;
  updateRole: (roleId: string, updates: Partial<Role>) => void;
  removeRole: (roleId: string) => void;
  
  setUserRoles: (userRoles: UserRole[]) => void;
  addUserRole: (userRole: UserRole) => void;
  removeUserRole: (userRoleId: string) => void;
  
  setPermissions: (permissions: Permission[]) => void;
  
  cachePermissionResult: (key: string, result: PermissionResult) => void;
  getCachedPermissionResult: (key: string) => PermissionResult | undefined;
  clearPermissionCache: () => void;
  
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
  
  // Computed
  getUserRolesByUserId: (userId: string) => UserRole[];
  getRoleById: (roleId: string) => Role | undefined;
  getUserPermissions: (userId: string) => Permission[];
  hasRole: (userId: string, roleSlug: string) => boolean;
}

export const useRBACStore = create<RBACStore>((set, get) => ({
  // Initial state
  roles: [],
  userRoles: [],
  permissions: [],
  permissionCache: new Map(),
  isLoading: false,
  error: null,
  
  // Actions
  setRoles: (roles) => set({ roles }),
  
  addRole: (role) => set((state) => ({
    roles: [...state.roles, role],
  })),
  
  updateRole: (roleId, updates) => set((state) => ({
    roles: state.roles.map((role) =>
      role.id === roleId ? { ...role, ...updates } : role
    ),
  })),
  
  removeRole: (roleId) => set((state) => ({
    roles: state.roles.filter((role) => role.id !== roleId),
    userRoles: state.userRoles.filter((userRole) => userRole.roleId !== roleId),
  })),
  
  setUserRoles: (userRoles) => set({ userRoles }),
  
  addUserRole: (userRole) => set((state) => ({
    userRoles: [...state.userRoles, userRole],
  })),
  
  removeUserRole: (userRoleId) => set((state) => ({
    userRoles: state.userRoles.filter((userRole) => userRole.id !== userRoleId),
  })),
  
  setPermissions: (permissions) => set({ permissions }),
  
  cachePermissionResult: (key, result) => set((state) => {
    const newCache = new Map(state.permissionCache);
    newCache.set(key, result);
    return { permissionCache: newCache };
  }),
  
  getCachedPermissionResult: (key) => {
    const { permissionCache } = get();
    return permissionCache.get(key);
  },
  
  clearPermissionCache: () => set({ permissionCache: new Map() }),
  
  setLoading: (isLoading) => set({ isLoading }),
  
  setError: (error) => set({ error }),
  
  reset: () => set({
    roles: [],
    userRoles: [],
    permissions: [],
    permissionCache: new Map(),
    isLoading: false,
    error: null,
  }),
  
  // Computed
  getUserRolesByUserId: (userId) => {
    const { userRoles } = get();
    return userRoles.filter((userRole) => userRole.userId === userId && userRole.isActive);
  },
  
  getRoleById: (roleId) => {
    const { roles } = get();
    return roles.find((role) => role.id === roleId);
  },
  
  getUserPermissions: (userId) => {
    const { userRoles, roles } = get();
    const userRoleIds = userRoles
      .filter((userRole) => userRole.userId === userId && userRole.isActive)
      .map((userRole) => userRole.roleId);
    
    const permissions: Permission[] = [];
    
    userRoleIds.forEach((roleId) => {
      const role = roles.find((r) => r.id === roleId);
      if (role) {
        permissions.push(...role.permissions);
      }
    });
    
    // Remove duplicates
    const uniquePermissions = permissions.filter(
      (permission, index, self) =>
        index === self.findIndex((p) => p.id === permission.id)
    );
    
    return uniquePermissions;
  },
  
  hasRole: (userId, roleSlug) => {
    const { userRoles, roles } = get();
    const userRoleIds = userRoles
      .filter((userRole) => userRole.userId === userId && userRole.isActive)
      .map((userRole) => userRole.roleId);
    
    return roles.some((role) => 
      role.slug === roleSlug && userRoleIds.includes(role.id)
    );
  },
}));

// Selectors
export const selectRoles = (state: RBACStore) => state.roles;
export const selectUserRoles = (state: RBACStore) => state.userRoles;
export const selectPermissions = (state: RBACStore) => state.permissions;
export const selectIsLoading = (state: RBACStore) => state.isLoading;
export const selectError = (state: RBACStore) => state.error;

// Utility functions for permission cache keys
export const createPermissionCacheKey = (
  userId: string,
  resource: string,
  action: string,
  resourceId?: string,
  context?: Record<string, any>
): string => {
  const parts = [userId, resource, action];
  if (resourceId) parts.push(resourceId);
  if (context) parts.push(JSON.stringify(context));
  return parts.join(":");
};
