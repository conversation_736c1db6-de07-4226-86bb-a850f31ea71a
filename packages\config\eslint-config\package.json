{"name": "@nexus/eslint-config", "version": "0.1.0", "description": "Shared ESLint configuration for Nexus SaaS", "main": "index.js", "files": ["index.js", "next.js", "react.js"], "scripts": {"lint": "eslint .", "clean": "rm -rf node_modules"}, "dependencies": {"@eslint/eslintrc": "3.3.1", "eslint-config-next": "15.4.2"}, "peerDependencies": {"eslint": "9.31.0", "typescript": "5.8.3"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "8.37.0", "@typescript-eslint/parser": "8.37.0", "eslint-config-prettier": "10.1.8", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0"}}