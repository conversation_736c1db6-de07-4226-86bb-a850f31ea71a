# Create PRP Prompt for NEXUS SaaS Starter

## Feature Implementation Request

Generate a complete PRP (Product Requirement Prompt) for feature implementation with deep and thorough research. Ensure rich context is passed to the AI through the PRP to enable one-pass implementation success through self-validation and iterative refinement.

The AI agent only gets the context you are appending to the PRP and its own training data. Assume the AI agent has access to the codebase and current web search capabilities, so it's important that your research findings are included or referenced in the PRP.

## Research Process

> During the research process, think deeply and plan your approach. The deeper research we do here, the better the PRP will be. We optimize for chance of success and not for speed.

1. **Codebase Analysis In Depth**
   - Search the codebase for similar features/patterns
   - Identify all necessary files to reference in the PRP
   - Note all existing conventions to follow
   - Check existing test patterns for validation approach
   - Analyze current architecture and technology stack

2. **External Research At Scale**
   - Research similar features/patterns online and include URLs to documentation and examples
   - Library documentation (include specific URLs)
   - For critical pieces of documentation, reference specific sections with clear reasoning
   - Implementation examples (GitHub/StackOverflow/blogs)
   - Best practices and common pitfalls found during research
   - Current technology trends and updates (especially Next.js 15.4+, React 19, TypeScript 5.8+)

3. **Multi-Tenant Architecture Considerations**
   - Research multi-tenant patterns and best practices
   - Security implications for multi-tenant applications
   - Database design patterns for tenant isolation
   - Performance considerations for multi-tenant systems
   - Compliance requirements (SOC 2, GDPR, HIPAA)

4. **Technology Stack Verification**
   - Verify current versions and compatibility
   - Check for breaking changes in dependencies
   - Research best practices for the specific technology stack
   - Identify any performance optimizations

## PRP Generation

Using the template structure from `PRPs/nexus-saas-starter-base.md`, create a comprehensive PRP with the following sections:

### Critical Context to Include (Minimum Requirements)

- **Documentation**: URLs with specific sections relevant to the feature
- **Code Examples**: Real snippets from the current codebase showing patterns to follow
- **Gotchas**: Library quirks, version issues, multi-tenant considerations
- **Patterns**: Existing approaches to follow from the codebase
- **Best Practices**: Common pitfalls found during research
- **Multi-Tenant Requirements**: Security, isolation, and performance considerations
- **Technology Stack**: Current versions and compatibility requirements

### Implementation Blueprint Structure

1. **Data Models and Structure**
   - Database schema changes (Prisma models)
   - TypeScript interfaces and types
   - Validation schemas (using Zod v4)
   - Multi-tenant data isolation patterns

2. **Task Breakdown** (Information-dense with keywords)
   - Start with pseudocode showing approach
   - Reference real files for patterns
   - Include error handling strategy
   - List tasks in completion order
   - Use specific file paths and function names
   - Include multi-tenant context for each task

3. **Integration Points**
   - Database changes and migrations
   - API endpoint modifications
   - Frontend component updates
   - Authentication and authorization
   - Billing and subscription impacts
   - Middleware and security considerations

### Validation Gates (Must be Executable)

```bash
# Level 1: Syntax & Style
npm run lint                    # ESLint checks
npx tsc --noEmit               # TypeScript type checking

# Level 2: Unit Tests
npm test                       # Jest/React Testing Library tests

# Level 3: Integration Tests
npm run dev                    # Start development server
# Include specific curl commands for testing endpoints

# Level 4: End-to-End Tests
npm run build                  # Production build validation
npm run start                  # Production server testing
```

**Include creative validation methods specific to the feature:**
- Performance testing with specific metrics
- Security testing for multi-tenant isolation
- Accessibility testing where applicable
- Load testing for scalability
- Database migration validation
- Stripe webhook testing (if billing-related)

## Quality Standards

The PRP must include:
- [ ] All necessary context for one-pass implementation
- [ ] Validation gates that are executable by AI
- [ ] References to existing codebase patterns
- [ ] Clear implementation path with specific tasks
- [ ] Error handling and edge cases documented
- [ ] Multi-tenant architecture considerations
- [ ] Security and compliance requirements
- [ ] Performance and scalability considerations
- [ ] Integration testing scenarios
- [ ] Documentation and deployment steps

## Output Format

Save the PRP as: `PRPs/features/{phase-folder}/{feature-name}-implementation.md`

### File Organization Structure:
```
PRPs/
├── nexus-saas-starter-base.md      # Base template
├── create-prp-prompt.md            # PRP generation system
├── prp-prompt-commands.md          # Task roadmap
└── features/                       # Feature-specific PRPs
    ├── 01-foundation/
    │   ├── project-infrastructure-setup-implementation.md
    │   ├── multi-tenant-database-architecture-implementation.md
    │   ├── better-auth-integration-implementation.md
    │   ├── oauth-provider-integration-implementation.md
    │   ├── multi-factor-authentication-implementation.md
    │   └── tenant-context-system-implementation.md
    ├── 02-core/
    │   ├── stripe-integration-implementation.md
    │   ├── subscription-management-implementation.md
    │   ├── user-management-system-implementation.md
    │   ├── analytics-dashboard-implementation.md
    │   └── rbac-system-implementation.md
    ├── 03-enterprise/
    │   ├── soc2-compliance-implementation.md
    │   ├── gdpr-compliance-implementation.md
    │   ├── webhook-system-implementation.md
    │   └── custom-branding-implementation.md
    └── 04-optimization/
        ├── performance-optimization-implementation.md
        ├── developer-portal-implementation.md
        └── plugin-architecture-implementation.md
```

### Phase Classification:
- **01-foundation**: Core architecture, authentication, multi-tenancy
- **02-core**: Billing, user management, analytics, core features
- **03-enterprise**: Security, compliance, integrations, advanced features
- **04-optimization**: Performance, developer experience, ecosystem

## Example Usage

```
Create a PRP for implementing Better-Auth Integration with multi-tenant workspace support
```

This would generate a comprehensive PRP saved as:
`PRPs/features/01-foundation/better-auth-integration-implementation.md`

The PRP would include:
- Multi-tenant authentication flows
- Workspace-aware session management
- Role-based access control
- Security best practices
- Integration with existing UI components
- Database schema modifications
- Complete validation testing

### Implementation Process:
1. **Identify Phase**: Foundation, Core, Enterprise, or Optimization
2. **Generate PRP**: Use this prompt system with specific feature name
3. **Save Properly**: Use correct subfolder and naming convention
4. **Update Task List**: Link completed feature to generated PRP file

## Quality Scoring

Score the PRP on a scale of 1-10 based on:
- **Context Completeness** (1-3): All necessary documentation and examples
- **Implementation Clarity** (1-3): Clear, actionable tasks with specific details
- **Validation Coverage** (1-2): Comprehensive, executable validation gates
- **Multi-Tenant Readiness** (1-2): Proper consideration of enterprise requirements

**Target Score: 8-10** for production-ready implementation success.

## Remember

The goal is **one-pass implementation success** through comprehensive context engineering. Every PRP should enable an AI agent to implement the feature correctly on the first attempt with minimal debugging cycles.

---

**Framework**: NEXUS SaaS Starter Multi-Tenant Architecture  
**Technology Stack**: Next.js 15.4+ / React 19 / TypeScript 5.8+ / Prisma / better-auth / Stripe  
**Optimization**: Production-ready, enterprise-grade, multi-tenant SaaS applications
