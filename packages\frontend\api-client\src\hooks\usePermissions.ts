import { useCallback, useMemo } from "react";
import { useAuth } from "./useAuth";
import { useCurrentWorkspace } from "./useWorkspace";
import { User, Permission } from "../types";

interface PermissionContext {
  tenantId?: string;
  workspaceId?: string;
  teamId?: string;
  resourceId?: string;
  ownerId?: string;
}

export function usePermissions() {
  const { user, hasRole, hasAnyRole, isAdmin } = useAuth();
  const { workspaceId } = useCurrentWorkspace();

  // Check if user has specific permission
  const hasPermission = useCallback(
    (resource: string, action: string, context?: PermissionContext): boolean => {
      if (!user) return false;

      // System admins have all permissions
      if (isAdmin()) return true;

      // Check if user owns the resource
      if (context?.ownerId && context.ownerId === user.id) {
        return true;
      }

      // TODO: Implement actual permission checking with RBAC
      // This would typically check against user's roles and permissions
      // For now, we'll use basic role-based checks

      // Basic permission mapping
      const permissionMap: Record<string, string[]> = {
        "read:user": ["admin", "manager", "member"],
        "create:user": ["admin", "manager"],
        "update:user": ["admin", "manager"],
        "delete:user": ["admin"],
        
        "read:workspace": ["admin", "manager", "member"],
        "create:workspace": ["admin", "owner"],
        "update:workspace": ["admin", "owner", "manager"],
        "delete:workspace": ["admin", "owner"],
        
        "read:team": ["admin", "manager", "member"],
        "create:team": ["admin", "manager"],
        "update:team": ["admin", "manager", "team_lead"],
        "delete:team": ["admin", "manager"],
        
        "read:project": ["admin", "manager", "member"],
        "create:project": ["admin", "manager", "member"],
        "update:project": ["admin", "manager", "project_owner"],
        "delete:project": ["admin", "manager", "project_owner"],
        
        "read:file": ["admin", "manager", "member"],
        "create:file": ["admin", "manager", "member"],
        "update:file": ["admin", "manager", "file_owner"],
        "delete:file": ["admin", "manager", "file_owner"],
        
        "read:role": ["admin", "manager"],
        "create:role": ["admin"],
        "update:role": ["admin"],
        "delete:role": ["admin"],
        
        "read:analytics": ["admin", "manager"],
        "read:billing": ["admin", "owner"],
        "manage:billing": ["admin", "owner"],
      };

      const permissionKey = `${action}:${resource}`;
      const requiredRoles = permissionMap[permissionKey];

      if (!requiredRoles) {
        // If permission not defined, deny by default
        return false;
      }

      return hasAnyRole(requiredRoles);
    },
    [user, isAdmin, hasAnyRole]
  );

  // Check multiple permissions (AND logic)
  const hasAllPermissions = useCallback(
    (permissions: Array<{ resource: string; action: string }>, context?: PermissionContext): boolean => {
      return permissions.every(({ resource, action }) => 
        hasPermission(resource, action, context)
      );
    },
    [hasPermission]
  );

  // Check multiple permissions (OR logic)
  const hasAnyPermission = useCallback(
    (permissions: Array<{ resource: string; action: string }>, context?: PermissionContext): boolean => {
      return permissions.some(({ resource, action }) => 
        hasPermission(resource, action, context)
      );
    },
    [hasPermission]
  );

  // Resource-specific permission helpers
  const canReadUser = useCallback(
    (userId?: string) => hasPermission("user", "read", { resourceId: userId }),
    [hasPermission]
  );

  const canCreateUser = useCallback(
    () => hasPermission("user", "create"),
    [hasPermission]
  );

  const canUpdateUser = useCallback(
    (userId?: string) => hasPermission("user", "update", { resourceId: userId }),
    [hasPermission]
  );

  const canDeleteUser = useCallback(
    (userId?: string) => hasPermission("user", "delete", { resourceId: userId }),
    [hasPermission]
  );

  const canReadWorkspace = useCallback(
    (workspaceIdParam?: string) => hasPermission("workspace", "read", { 
      workspaceId: workspaceIdParam || workspaceId 
    }),
    [hasPermission, workspaceId]
  );

  const canCreateWorkspace = useCallback(
    () => hasPermission("workspace", "create"),
    [hasPermission]
  );

  const canUpdateWorkspace = useCallback(
    (workspaceIdParam?: string, ownerId?: string) => hasPermission("workspace", "update", { 
      workspaceId: workspaceIdParam || workspaceId,
      ownerId 
    }),
    [hasPermission, workspaceId]
  );

  const canDeleteWorkspace = useCallback(
    (workspaceIdParam?: string, ownerId?: string) => hasPermission("workspace", "delete", { 
      workspaceId: workspaceIdParam || workspaceId,
      ownerId 
    }),
    [hasPermission, workspaceId]
  );

  const canManageTeam = useCallback(
    (teamId?: string) => hasPermission("team", "update", { teamId }),
    [hasPermission]
  );

  const canManageProject = useCallback(
    (projectId?: string, ownerId?: string) => hasPermission("project", "update", { 
      resourceId: projectId,
      ownerId 
    }),
    [hasPermission]
  );

  const canManageFile = useCallback(
    (fileId?: string, ownerId?: string) => hasPermission("file", "update", { 
      resourceId: fileId,
      ownerId 
    }),
    [hasPermission]
  );

  const canViewAnalytics = useCallback(
    () => hasPermission("analytics", "read"),
    [hasPermission]
  );

  const canManageBilling = useCallback(
    () => hasPermission("billing", "manage"),
    [hasPermission]
  );

  const canViewBilling = useCallback(
    () => hasPermission("billing", "read"),
    [hasPermission]
  );

  // Get user's effective permissions
  const userPermissions = useMemo(() => {
    if (!user) return [];

    // TODO: This would typically come from the backend
    // For now, we'll derive from roles
    const permissions: string[] = [];

    if (hasRole("admin")) {
      permissions.push("*:*"); // Admin has all permissions
    }

    if (hasRole("manager")) {
      permissions.push(
        "read:*",
        "create:user",
        "update:user",
        "create:team",
        "update:team",
        "create:project",
        "update:project"
      );
    }

    if (hasRole("member")) {
      permissions.push(
        "read:user",
        "read:workspace",
        "read:team",
        "read:project",
        "read:file",
        "create:file",
        "update:file"
      );
    }

    return permissions;
  }, [user, hasRole]);

  return {
    // Core permission checking
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,

    // User permissions
    canReadUser,
    canCreateUser,
    canUpdateUser,
    canDeleteUser,

    // Workspace permissions
    canReadWorkspace,
    canCreateWorkspace,
    canUpdateWorkspace,
    canDeleteWorkspace,

    // Resource management permissions
    canManageTeam,
    canManageProject,
    canManageFile,

    // Feature permissions
    canViewAnalytics,
    canManageBilling,
    canViewBilling,

    // Computed values
    userPermissions,
    isResourceOwner: (ownerId?: string) => ownerId === user?.id,
    canAccessAdmin: isAdmin(),
  };
}

// Hook for permission-based component rendering
export function usePermissionGuard() {
  const { hasPermission, hasAllPermissions, hasAnyPermission } = usePermissions();

  const PermissionGuard = useCallback(
    ({ 
      resource, 
      action, 
      context, 
      fallback = null, 
      children 
    }: {
      resource: string;
      action: string;
      context?: PermissionContext;
      fallback?: React.ReactNode;
      children: React.ReactNode;
    }) => {
      const allowed = hasPermission(resource, action, context);
      return allowed ? children : fallback;
    },
    [hasPermission]
  );

  const MultiPermissionGuard = useCallback(
    ({ 
      permissions, 
      context, 
      mode = "all", 
      fallback = null, 
      children 
    }: {
      permissions: Array<{ resource: string; action: string }>;
      context?: PermissionContext;
      mode?: "all" | "any";
      fallback?: React.ReactNode;
      children: React.ReactNode;
    }) => {
      const allowed = mode === "all" 
        ? hasAllPermissions(permissions, context)
        : hasAnyPermission(permissions, context);
      
      return allowed ? children : fallback;
    },
    [hasAllPermissions, hasAnyPermission]
  );

  return {
    PermissionGuard,
    MultiPermissionGuard,
  };
}
