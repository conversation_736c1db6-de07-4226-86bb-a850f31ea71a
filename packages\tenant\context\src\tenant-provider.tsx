"use client";

import React, { useEffect } from "react";
import { TenantProvider as BaseTenantProvider, useTenantContext } from "./tenant-context";
import { useAuth } from "@nexus/auth-client";

// Enhanced provider with auto-initialization
function TenantInitializer({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const { refreshTenant, setLoading, setError } = useTenantContext();

  useEffect(() => {
    if (user?.tenantId) {
      refreshTenant();
    } else {
      setLoading(false);
      setError(null);
    }
  }, [user?.tenantId, refreshTenant, setLoading, setError]);

  return <>{children}</>;
}

// Main provider that combines auth and tenant context
export function TenantProvider({ children }: { children: React.ReactNode }) {
  return (
    <BaseTenantProvider>
      <TenantInitializer>
        {children}
      </TenantInitializer>
    </BaseTenantProvider>
  );
}
