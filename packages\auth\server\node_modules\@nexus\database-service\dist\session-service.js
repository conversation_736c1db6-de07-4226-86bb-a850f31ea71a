import { createTenantClient } from "./client";
export class SessionService {
    tenantId;
    constructor(tenantId) {
        this.tenantId = tenantId;
    }
    // Create a new session
    async create(userId, token, expiresAt) {
        const client = createTenantClient(this.tenantId);
        return client.tenant.session.create({
            data: {
                userId,
                token,
                expiresAt,
            },
        });
    }
    // Find session by token
    async findByToken(token) {
        const client = createTenantClient(this.tenantId);
        return client.tenant.session.findUnique({
            where: { token },
            include: {
                user: true,
            },
        });
    }
    // Find sessions by user ID
    async findByUserId(userId) {
        const client = createTenantClient(this.tenantId);
        return client.tenant.session.findMany({
            where: { userId },
            orderBy: { createdAt: "desc" },
        });
    }
    // Update session expiration
    async updateExpiration(token, expiresAt) {
        const client = createTenantClient(this.tenantId);
        return client.tenant.session.update({
            where: { token },
            data: { expiresAt },
        });
    }
    // Delete session
    async delete(token) {
        const client = createTenantClient(this.tenantId);
        return client.tenant.session.delete({
            where: { token },
        });
    }
    // Delete all sessions for a user
    async deleteAllForUser(userId) {
        const client = createTenantClient(this.tenantId);
        const result = await client.tenant.session.deleteMany({
            where: { userId },
        });
        return result.count;
    }
    // Delete expired sessions
    async deleteExpired() {
        const client = createTenantClient(this.tenantId);
        const result = await client.tenant.session.deleteMany({
            where: {
                expiresAt: {
                    lt: new Date(),
                },
            },
        });
        return result.count;
    }
    // Check if session is valid
    async isValid(token) {
        const session = await this.findByToken(token);
        if (!session)
            return false;
        return session.expiresAt > new Date();
    }
    // Refresh session (extend expiration)
    async refresh(token, extensionMs = 24 * 60 * 60 * 1000) {
        const session = await this.findByToken(token);
        if (!session || session.expiresAt <= new Date()) {
            return null;
        }
        const newExpiresAt = new Date(Date.now() + extensionMs);
        return this.updateExpiration(token, newExpiresAt);
    }
}
export const createSessionService = (tenantId) => {
    return new SessionService(tenantId);
};
