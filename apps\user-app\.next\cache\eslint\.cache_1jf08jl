[{"C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\accordion.tsx": "3", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\alert-dialog.tsx": "4", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\alert.tsx": "5", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\aspect-ratio.tsx": "6", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\avatar.tsx": "7", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\badge.tsx": "8", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\breadcrumb.tsx": "9", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\button.tsx": "10", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\calendar.tsx": "11", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\card.tsx": "12", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\carousel.tsx": "13", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\chart.tsx": "14", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\checkbox.tsx": "15", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\collapsible.tsx": "16", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\command.tsx": "17", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\context-menu.tsx": "18", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\dialog.tsx": "19", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\drawer.tsx": "20", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\dropdown-menu.tsx": "21", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\form.tsx": "22", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\hover-card.tsx": "23", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\input-otp.tsx": "24", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\input.tsx": "25", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\label.tsx": "26", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\menubar.tsx": "27", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\navigation-menu.tsx": "28", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\pagination.tsx": "29", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\popover.tsx": "30", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\progress.tsx": "31", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\radio-group.tsx": "32", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\resizable.tsx": "33", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\scroll-area.tsx": "34", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\select.tsx": "35", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\separator.tsx": "36", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\sheet.tsx": "37", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\sidebar.tsx": "38", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\skeleton.tsx": "39", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\slider.tsx": "40", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\sonner.tsx": "41", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\switch.tsx": "42", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\table.tsx": "43", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\tabs.tsx": "44", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\textarea.tsx": "45", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\toggle-group.tsx": "46", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\toggle.tsx": "47", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\tooltip.tsx": "48", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\hooks\\use-mobile.ts": "49", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\lib\\utils.ts": "50", "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\types\\globals.d.ts": "51"}, {"size": 689, "mtime": 1752784026494, "results": "52", "hashOfConfig": "53"}, {"size": 3990, "mtime": 1752784043683, "results": "54", "hashOfConfig": "53"}, {"size": 2053, "mtime": 1752784453287, "results": "55", "hashOfConfig": "53"}, {"size": 3864, "mtime": 1752784453325, "results": "56", "hashOfConfig": "53"}, {"size": 1614, "mtime": 1752784453319, "results": "57", "hashOfConfig": "53"}, {"size": 280, "mtime": 1752784453340, "results": "58", "hashOfConfig": "53"}, {"size": 1097, "mtime": 1752784453353, "results": "59", "hashOfConfig": "53"}, {"size": 1631, "mtime": 1752784453366, "results": "60", "hashOfConfig": "53"}, {"size": 2357, "mtime": 1752784453381, "results": "61", "hashOfConfig": "53"}, {"size": 2123, "mtime": 1752784453340, "results": "62", "hashOfConfig": "53"}, {"size": 7660, "mtime": 1752784453423, "results": "63", "hashOfConfig": "53"}, {"size": 1989, "mtime": 1752784453430, "results": "64", "hashOfConfig": "53"}, {"size": 5556, "mtime": 1752784453460, "results": "65", "hashOfConfig": "53"}, {"size": 9781, "mtime": 1752784453535, "results": "66", "hashOfConfig": "53"}, {"size": 1226, "mtime": 1752784453539, "results": "67", "hashOfConfig": "53"}, {"size": 800, "mtime": 1752784453539, "results": "68", "hashOfConfig": "53"}, {"size": 4818, "mtime": 1752784453557, "results": "69", "hashOfConfig": "53"}, {"size": 8222, "mtime": 1752784453586, "results": "70", "hashOfConfig": "53"}, {"size": 3982, "mtime": 1752784453574, "results": "71", "hashOfConfig": "53"}, {"size": 4255, "mtime": 1752784453605, "results": "72", "hashOfConfig": "53"}, {"size": 8284, "mtime": 1752784453622, "results": "73", "hashOfConfig": "53"}, {"size": 3759, "mtime": 1752784453643, "results": "74", "hashOfConfig": "53"}, {"size": 1532, "mtime": 1752784453650, "results": "75", "hashOfConfig": "53"}, {"size": 2254, "mtime": 1752784453662, "results": "76", "hashOfConfig": "53"}, {"size": 967, "mtime": 1752784453650, "results": "77", "hashOfConfig": "53"}, {"size": 611, "mtime": 1752784453648, "results": "78", "hashOfConfig": "53"}, {"size": 8394, "mtime": 1752784453684, "results": "79", "hashOfConfig": "53"}, {"size": 6664, "mtime": 1752784453698, "results": "80", "hashOfConfig": "53"}, {"size": 2712, "mtime": 1752784453700, "results": "81", "hashOfConfig": "53"}, {"size": 1635, "mtime": 1752784453713, "results": "82", "hashOfConfig": "53"}, {"size": 740, "mtime": 1752784453713, "results": "83", "hashOfConfig": "53"}, {"size": 1466, "mtime": 1752784453713, "results": "84", "hashOfConfig": "53"}, {"size": 2028, "mtime": 1752784453729, "results": "85", "hashOfConfig": "53"}, {"size": 1645, "mtime": 1752784453729, "results": "86", "hashOfConfig": "53"}, {"size": 6253, "mtime": 1752784453755, "results": "87", "hashOfConfig": "53"}, {"size": 699, "mtime": 1752784453760, "results": "88", "hashOfConfig": "53"}, {"size": 4090, "mtime": 1752784453760, "results": "89", "hashOfConfig": "53"}, {"size": 21633, "mtime": 1752784453895, "results": "90", "hashOfConfig": "53"}, {"size": 276, "mtime": 1752784453922, "results": "91", "hashOfConfig": "53"}, {"size": 2001, "mtime": 1752784453934, "results": "92", "hashOfConfig": "53"}, {"size": 564, "mtime": 1752784453934, "results": "93", "hashOfConfig": "53"}, {"size": 1177, "mtime": 1752784453934, "results": "94", "hashOfConfig": "53"}, {"size": 2448, "mtime": 1752784453950, "results": "95", "hashOfConfig": "53"}, {"size": 1969, "mtime": 1752784453950, "results": "96", "hashOfConfig": "53"}, {"size": 759, "mtime": 1752784453966, "results": "97", "hashOfConfig": "53"}, {"size": 1925, "mtime": 1752784453983, "results": "98", "hashOfConfig": "53"}, {"size": 1570, "mtime": 1752784453972, "results": "99", "hashOfConfig": "53"}, {"size": 1891, "mtime": 1752784453914, "results": "100", "hashOfConfig": "53"}, {"size": 565, "mtime": 1752784453920, "results": "101", "hashOfConfig": "53"}, {"size": 166, "mtime": 1752784418129, "results": "102", "hashOfConfig": "53"}, {"size": 238, "mtime": 1752953531314, "results": "103", "hashOfConfig": "53"}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1yg7il8", {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\accordion.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\alert-dialog.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\aspect-ratio.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\breadcrumb.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\calendar.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\carousel.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\chart.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\checkbox.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\collapsible.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\command.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\context-menu.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\drawer.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\hover-card.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\input-otp.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\menubar.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\pagination.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\popover.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\radio-group.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\resizable.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\scroll-area.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\sonner.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\toggle-group.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\toggle.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\hooks\\use-mobile.ts", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Downloads\\saas-starter-cursor-temp\\apps\\user-app\\src\\types\\globals.d.ts", [], []]