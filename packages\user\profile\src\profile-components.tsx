"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { useProfile } from "./profile-hooks";
import { ProfileUpdateData, PreferencesUpdateData } from "./profile-types";

// Profile edit form
export function ProfileEditForm({ onSuccess }: { onSuccess?: () => void }) {
  const { profile, updateProfile, isLoading } = useProfile();
  const { register, handleSubmit, formState: { errors } } = useForm<ProfileUpdateData>({
    defaultValues: {
      firstName: profile?.firstName || "",
      lastName: profile?.lastName || "",
      bio: profile?.bio || "",
      location: profile?.location || "",
      website: profile?.website || "",
      company: profile?.company || "",
      jobTitle: profile?.jobTitle || "",
      username: profile?.username || "",
    },
  });

  const onSubmit = async (data: ProfileUpdateData) => {
    try {
      await updateProfile(data);
      onSuccess?.();
    } catch (error) {
      console.error("Failed to update profile:", error);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">First Name</label>
          <input
            {...register("firstName", { required: "First name is required" })}
            className="w-full px-3 py-2 border rounded-md"
          />
          {errors.firstName && (
            <p className="text-red-500 text-sm mt-1">{errors.firstName.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Last Name</label>
          <input
            {...register("lastName", { required: "Last name is required" })}
            className="w-full px-3 py-2 border rounded-md"
          />
          {errors.lastName && (
            <p className="text-red-500 text-sm mt-1">{errors.lastName.message}</p>
          )}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Username</label>
        <input
          {...register("username", {
            pattern: {
              value: /^[a-zA-Z0-9_-]+$/,
              message: "Username can only contain letters, numbers, underscores, and hyphens"
            }
          })}
          className="w-full px-3 py-2 border rounded-md"
        />
        {errors.username && (
          <p className="text-red-500 text-sm mt-1">{errors.username.message}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Bio</label>
        <textarea
          {...register("bio", { maxLength: { value: 500, message: "Bio must be less than 500 characters" } })}
          className="w-full px-3 py-2 border rounded-md"
          rows={4}
          placeholder="Tell us about yourself..."
        />
        {errors.bio && (
          <p className="text-red-500 text-sm mt-1">{errors.bio.message}</p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">Location</label>
          <input
            {...register("location")}
            className="w-full px-3 py-2 border rounded-md"
            placeholder="City, Country"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Website</label>
          <input
            {...register("website", {
              pattern: {
                value: /^https?:\/\/.+/,
                message: "Please enter a valid URL"
              }
            })}
            className="w-full px-3 py-2 border rounded-md"
            placeholder="https://example.com"
          />
          {errors.website && (
            <p className="text-red-500 text-sm mt-1">{errors.website.message}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">Company</label>
          <input
            {...register("company")}
            className="w-full px-3 py-2 border rounded-md"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Job Title</label>
          <input
            {...register("jobTitle")}
            className="w-full px-3 py-2 border rounded-md"
          />
        </div>
      </div>

      <button
        type="submit"
        disabled={isLoading}
        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
      >
        {isLoading ? "Saving..." : "Save Changes"}
      </button>
    </form>
  );
}

// Profile completion widget
export function ProfileCompletionWidget() {
  const { profileCompletion, missingFields } = useProfile();

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-medium text-blue-900">Profile Completion</h3>
        <span className="text-blue-700 font-semibold">{profileCompletion}%</span>
      </div>
      
      <div className="w-full bg-blue-200 rounded-full h-2 mb-3">
        <div
          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
          style={{ width: `${profileCompletion}%` }}
        />
      </div>
      
      {missingFields.length > 0 && (
        <div>
          <p className="text-sm text-blue-700 mb-2">Complete your profile by adding:</p>
          <ul className="text-sm text-blue-600 space-y-1">
            {missingFields.map((field) => (
              <li key={field} className="capitalize">• {field.replace(/([A-Z])/g, ' $1').trim()}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}

// Avatar upload component
export function AvatarUpload() {
  const { profile } = useProfile();
  const [uploading, setUploading] = useState(false);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploading(true);
    try {
      // Avatar upload logic would go here
      console.log("Uploading avatar:", file);
    } catch (error) {
      console.error("Failed to upload avatar:", error);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="flex items-center space-x-4">
      <div className="relative">
        {profile?.image ? (
          <img
            src={profile.image}
            alt="Profile"
            className="w-20 h-20 rounded-full object-cover"
          />
        ) : (
          <div className="w-20 h-20 rounded-full bg-gray-300 flex items-center justify-center text-gray-600 text-xl font-semibold">
            {profile?.firstName?.[0] || profile?.email?.[0] || "?"}
          </div>
        )}
        
        {uploading && (
          <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white" />
          </div>
        )}
      </div>
      
      <div>
        <label className="cursor-pointer bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
          {uploading ? "Uploading..." : "Change Avatar"}
          <input
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="hidden"
            disabled={uploading}
          />
        </label>
        <p className="text-sm text-gray-500 mt-1">JPG, PNG or WebP. Max 5MB.</p>
      </div>
    </div>
  );
}
