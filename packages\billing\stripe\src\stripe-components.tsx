"use client";

import React, { useState } from "react";
import { Elements, PaymentElement, useStripe, useElements } from "@stripe/react-stripe-js";
import { getStripe, stripeElementsAppearance, formatAmountForDisplay } from "./stripe-client";
import { useStripe as useStripeHook } from "./stripe-hooks";

// Stripe Elements provider wrapper
export function StripeProvider({ children }: { children: React.ReactNode }) {
  const stripePromise = getStripe();
  
  return (
    <Elements stripe={stripePromise} options={{ appearance: stripeElementsAppearance }}>
      {children}
    </Elements>
  );
}

// Payment form component
export function PaymentForm({ 
  clientSecret, 
  onSuccess, 
  onError 
}: { 
  clientSecret: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}) {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);

    try {
      const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/payment/success`,
        },
        redirect: "if_required",
      });

      if (error) {
        onError?.(error.message || "Payment failed");
      } else {
        onSuccess?.();
      }
    } catch (error) {
      onError?.(error instanceof Error ? error.message : "Payment failed");
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <PaymentElement />
      
      <button
        type="submit"
        disabled={!stripe || isProcessing}
        className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isProcessing ? "Processing..." : "Pay Now"}
      </button>
    </form>
  );
}

// Payment methods list
export function PaymentMethodsList() {
  const { paymentMethods, detachPaymentMethod, isLoading } = useStripeHook();

  const handleRemove = async (paymentMethodId: string) => {
    try {
      await detachPaymentMethod(paymentMethodId);
    } catch (error) {
      console.error("Failed to remove payment method:", error);
    }
  };

  if (isLoading) {
    return <div>Loading payment methods...</div>;
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Payment Methods</h3>
      
      {paymentMethods.length === 0 ? (
        <p className="text-gray-500">No payment methods added yet.</p>
      ) : (
        <div className="space-y-3">
          {paymentMethods.map((paymentMethod) => (
            <div
              key={paymentMethod.id}
              className="flex items-center justify-between p-4 border rounded-lg"
            >
              <div className="flex items-center space-x-3">
                <div className="text-2xl">💳</div>
                <div>
                  <p className="font-medium">
                    **** **** **** {paymentMethod.card?.last4}
                  </p>
                  <p className="text-sm text-gray-500">
                    {paymentMethod.card?.brand?.toUpperCase()} • Expires {paymentMethod.card?.expMonth}/{paymentMethod.card?.expYear}
                  </p>
                </div>
                {paymentMethod.isDefault && (
                  <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                    Default
                  </span>
                )}
              </div>
              
              <button
                onClick={() => handleRemove(paymentMethod.id)}
                className="text-red-600 hover:text-red-800 text-sm"
              >
                Remove
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// Subscription plans component
export function SubscriptionPlans() {
  const { products, createSubscription, isLoading } = useStripeHook();

  const handleSelectPlan = async (priceId: string) => {
    try {
      await createSubscription({
        customerId: "", // This would come from the current user
        priceId,
      });
    } catch (error) {
      console.error("Failed to create subscription:", error);
    }
  };

  if (isLoading) {
    return <div>Loading plans...</div>;
  }

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Choose Your Plan</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {products.map((product) => (
          <div key={product.id} className="border rounded-lg p-6">
            <h4 className="text-xl font-semibold mb-2">{product.name}</h4>
            <p className="text-gray-600 mb-4">{product.description}</p>
            
            <div className="space-y-3">
              {product.prices.map((price) => (
                <div key={price.id} className="border rounded p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-2xl font-bold">
                      {formatAmountForDisplay(price.amount, price.currency)}
                    </span>
                    {price.interval && (
                      <span className="text-gray-500">
                        /{price.interval}
                      </span>
                    )}
                  </div>
                  
                  <button
                    onClick={() => handleSelectPlan(price.stripePriceId)}
                    disabled={isLoading}
                    className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:opacity-50"
                  >
                    {isLoading ? "Processing..." : "Select Plan"}
                  </button>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Current subscription component
export function CurrentSubscription() {
  const { currentSubscription, cancelSubscription, createBillingPortalSession, isLoading } = useStripeHook();

  const handleCancel = async () => {
    if (!currentSubscription) return;
    
    try {
      await cancelSubscription(currentSubscription.stripeSubscriptionId);
    } catch (error) {
      console.error("Failed to cancel subscription:", error);
    }
  };

  const handleManageBilling = async () => {
    try {
      const session = await createBillingPortalSession(window.location.href);
      window.location.href = session.url;
    } catch (error) {
      console.error("Failed to create billing portal session:", error);
    }
  };

  if (!currentSubscription) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No active subscription</p>
      </div>
    );
  }

  return (
    <div className="border rounded-lg p-6">
      <h3 className="text-lg font-semibold mb-4">Current Subscription</h3>
      
      <div className="space-y-3">
        <div className="flex justify-between">
          <span>Status:</span>
          <span className={`px-2 py-1 text-xs rounded ${
            currentSubscription.status === "active" 
              ? "bg-green-100 text-green-800" 
              : "bg-yellow-100 text-yellow-800"
          }`}>
            {currentSubscription.status}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Current Period:</span>
          <span>
            {new Date(currentSubscription.currentPeriodStart).toLocaleDateString()} - {" "}
            {new Date(currentSubscription.currentPeriodEnd).toLocaleDateString()}
          </span>
        </div>
        
        {currentSubscription.cancelAtPeriodEnd && (
          <div className="text-orange-600 text-sm">
            Subscription will cancel at the end of the current period.
          </div>
        )}
      </div>
      
      <div className="flex space-x-3 mt-6">
        <button
          onClick={handleManageBilling}
          disabled={isLoading}
          className="flex-1 bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:opacity-50"
        >
          Manage Billing
        </button>
        
        {!currentSubscription.cancelAtPeriodEnd && (
          <button
            onClick={handleCancel}
            disabled={isLoading}
            className="flex-1 bg-red-600 text-white py-2 px-4 rounded hover:bg-red-700 disabled:opacity-50"
          >
            Cancel Subscription
          </button>
        )}
      </div>
    </div>
  );
}
