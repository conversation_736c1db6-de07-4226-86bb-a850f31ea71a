component_guidelines:
  id: nextjs-component-guidelines
  name: Next.js Component Creation Guidelines
  version: 1.0
  framework: nexus
  purpose: Instructions for creating production-ready React components

component_types:
  server_components:
    description: "Default React 19 Server Components"
    file_location: "components/[component-name].tsx"
    requirements:
      - "No 'use client' directive"
      - "Can use async/await"
      - "Access server-side data directly"
      - "Optimal for data fetching"
  
  client_components:
    description: "Interactive components with state/events"
    file_location: "components/[component-name].tsx"
    requirements:
      - "Start with 'use client' directive"
      - "Use hooks (useState, useEffect, etc.)"
      - "Handle user interactions"
      - "Manage client-side state"
  
  shared_components:
    description: "Components that work in both environments"
    file_location: "components/[component-name].tsx"
    requirements:
      - "No client-specific APIs"
      - "No server-specific APIs"
      - "Pure rendering logic only"

required_imports:
  - "cn utility from @/lib/utils for className merging"
  - "React types for TypeScript (ReactNode, etc.)"
  - "Component-specific dependencies only"

typescript_patterns:
  props_interface:
    naming: "[ComponentName]Props"
    required_fields:
      - "className?: string (for styling flexibility)"
      - "children?: React.ReactNode (if accepting children)"
    optional_fields: "Component-specific props as needed"
  
  component_declaration:
    server: "export default async function ComponentName(props: ComponentNameProps)"
    client: "export default function ComponentName(props: ComponentNameProps)"
    shared: "export default function ComponentName(props: ComponentNameProps)"

styling_patterns:
  className_handling:
    - "Always accept className prop"
    - "Use cn() utility to merge classes"
    - "Apply default styles first, then className override"
  
  responsive_design:
    - "Use Tailwind CSS responsive prefixes"
    - "Consider mobile-first approach"
    - "Test across different screen sizes"

accessibility_requirements:
  - "Include appropriate ARIA attributes"
  - "Ensure keyboard navigation support"
  - "Provide meaningful alt text for images"
  - "Use semantic HTML elements"
  - "Test with screen readers"

performance_patterns:
  server_components:
    - "Leverage server-side rendering benefits"
    - "Minimize client-side JavaScript"
    - "Use async data fetching when needed"
  
  client_components:
    - "Minimize re-renders with useMemo/useCallback"
    - "Lazy load heavy components"
    - "Optimize bundle size"

implementation_checklist:
  - "Component file in correct location"
  - "Appropriate component type selected"
  - "TypeScript interface defined"
  - "Styling patterns implemented"
  - "Accessibility requirements met"
  - "Performance considerations applied"
  - "Error boundaries when needed"
  - "Testing approach planned"

examples:
  basic_server: "Data display components, static content"
  interactive_client: "Forms, buttons, modals, dynamic content"
  reusable_shared: "UI primitives, layout components"
