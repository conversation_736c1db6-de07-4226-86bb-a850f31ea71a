import { FastifyRequest, FastifyReply } from "fastify";
import { AuthenticatedRequest, AuthorizationError } from "../types";
import { accessControl } from "@nexus/rbac";
import { ResourceType, ActionType } from "@nexus/rbac";

// RBAC middleware factory
export const requirePermission = (resource: ResourceType, action: ActionType) => {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    const authRequest = request as AuthenticatedRequest;
    
    if (!authRequest.user) {
      throw new AuthorizationError("Authentication required");
    }

    const { user, context } = authRequest;
    
    try {
      const hasPermission = await accessControl.can(
        user.id,
        action,
        resource,
        {
          tenantId: user.tenantId,
          workspaceId: context.workspaceId,
          teamId: context.teamId,
          resourceId: getResourceIdFromRequest(request),
          requestData: {
            ip: context.ip,
            userAgent: context.userAgent,
            method: request.method,
            url: request.url,
          },
        }
      );

      if (!hasPermission) {
        throw new AuthorizationError(
          `Insufficient permissions for ${action} on ${resource}`
        );
      }
    } catch (error) {
      if (error instanceof AuthorizationError) {
        throw error;
      }
      throw new AuthorizationError("Permission check failed");
    }
  };
};

// Multiple permissions middleware (user must have ALL permissions)
export const requireAllPermissions = (permissions: Array<{ resource: ResourceType; action: ActionType }>) => {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    const authRequest = request as AuthenticatedRequest;
    
    if (!authRequest.user) {
      throw new AuthorizationError("Authentication required");
    }

    const { user, context } = authRequest;
    
    try {
      const results = await accessControl.canMultiple(
        user.id,
        permissions,
        {
          tenantId: user.tenantId,
          workspaceId: context.workspaceId,
          teamId: context.teamId,
          requestData: {
            ip: context.ip,
            userAgent: context.userAgent,
            method: request.method,
            url: request.url,
          },
        }
      );

      const hasAllPermissions = results.every(result => result);
      
      if (!hasAllPermissions) {
        const deniedPermissions = permissions
          .filter((_, index) => !results[index])
          .map(p => `${p.action} on ${p.resource}`)
          .join(", ");
        
        throw new AuthorizationError(
          `Missing required permissions: ${deniedPermissions}`
        );
      }
    } catch (error) {
      if (error instanceof AuthorizationError) {
        throw error;
      }
      throw new AuthorizationError("Permission check failed");
    }
  };
};

// Any permissions middleware (user must have AT LEAST ONE permission)
export const requireAnyPermission = (permissions: Array<{ resource: ResourceType; action: ActionType }>) => {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    const authRequest = request as AuthenticatedRequest;
    
    if (!authRequest.user) {
      throw new AuthorizationError("Authentication required");
    }

    const { user, context } = authRequest;
    
    try {
      const hasAnyPermission = await accessControl.canAny(
        user.id,
        permissions,
        {
          tenantId: user.tenantId,
          workspaceId: context.workspaceId,
          teamId: context.teamId,
          requestData: {
            ip: context.ip,
            userAgent: context.userAgent,
            method: request.method,
            url: request.url,
          },
        }
      );

      if (!hasAnyPermission) {
        const permissionList = permissions
          .map(p => `${p.action} on ${p.resource}`)
          .join(", ");
        
        throw new AuthorizationError(
          `Missing any of required permissions: ${permissionList}`
        );
      }
    } catch (error) {
      if (error instanceof AuthorizationError) {
        throw error;
      }
      throw new AuthorizationError("Permission check failed");
    }
  };
};

// Resource ownership middleware
export const requireOwnership = (resource: ResourceType) => {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    const authRequest = request as AuthenticatedRequest;
    
    if (!authRequest.user) {
      throw new AuthorizationError("Authentication required");
    }

    const resourceId = getResourceIdFromRequest(request);
    
    if (!resourceId) {
      throw new AuthorizationError("Resource ID required for ownership check");
    }

    // Check if user owns the resource or has higher permissions
    const isOwner = await accessControl.isOwner(
      authRequest.user.id,
      resource,
      resourceId,
      {
        tenantId: authRequest.user.tenantId,
        workspaceId: authRequest.context.workspaceId,
        teamId: authRequest.context.teamId,
      }
    );

    if (!isOwner) {
      throw new AuthorizationError("Resource ownership required");
    }
  };
};

// Team membership middleware
export const requireTeamMembership = () => {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    const authRequest = request as AuthenticatedRequest;
    
    if (!authRequest.user) {
      throw new AuthorizationError("Authentication required");
    }

    const teamId = getTeamIdFromRequest(request);
    
    if (!teamId) {
      throw new AuthorizationError("Team ID required");
    }

    const isMember = await accessControl.isTeamMember(
      authRequest.user.id,
      teamId,
      {
        tenantId: authRequest.user.tenantId,
      }
    );

    if (!isMember) {
      throw new AuthorizationError("Team membership required");
    }

    // Add team ID to context
    authRequest.context.teamId = teamId;
  };
};

// Workspace membership middleware
export const requireWorkspaceMembership = () => {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    const authRequest = request as AuthenticatedRequest;
    
    if (!authRequest.user) {
      throw new AuthorizationError("Authentication required");
    }

    const workspaceId = getWorkspaceIdFromRequest(request);
    
    if (!workspaceId) {
      throw new AuthorizationError("Workspace ID required");
    }

    const isMember = await accessControl.isWorkspaceMember(
      authRequest.user.id,
      workspaceId,
      {
        tenantId: authRequest.user.tenantId,
      }
    );

    if (!isMember) {
      throw new AuthorizationError("Workspace membership required");
    }

    // Add workspace ID to context
    authRequest.context.workspaceId = workspaceId;
  };
};

// Convenience permission middlewares
export const canRead = (resource: ResourceType) => requirePermission(resource, "read");
export const canWrite = (resource: ResourceType) => requirePermission(resource, "update");
export const canCreate = (resource: ResourceType) => requirePermission(resource, "create");
export const canDelete = (resource: ResourceType) => requirePermission(resource, "delete");
export const canManage = (resource: ResourceType) => requirePermission(resource, "manage");

// Helper functions to extract IDs from request
function getResourceIdFromRequest(request: FastifyRequest): string | undefined {
  const params = request.params as any;
  return params?.id || params?.resourceId || params?.userId || params?.projectId;
}

function getWorkspaceIdFromRequest(request: FastifyRequest): string | undefined {
  const params = request.params as any;
  const headers = request.headers;
  return params?.workspaceId || headers["x-workspace-id"] as string;
}

function getTeamIdFromRequest(request: FastifyRequest): string | undefined {
  const params = request.params as any;
  const headers = request.headers;
  return params?.teamId || headers["x-team-id"] as string;
}
