{"name": "@nexus/notifications", "version": "0.1.0", "description": "Multi-channel notification service for Nexus SaaS", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "clean": "rm -rf dist", "test": "jest", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "@nexus/utils": "workspace:*", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "nodemailer": "^6.9.8", "handlebars": "^4.7.8", "twilio": "^4.19.3", "web-push": "^3.6.6", "firebase-admin": "^12.0.0", "bull": "^4.12.2", "redis": "^4.6.12", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "zod": "^4.0.5", "uuid": "^9.0.1", "winston": "^3.11.0", "cron": "^3.1.6", "mjml": "^4.14.1", "juice": "^10.0.0"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.0", "@types/node": "^22.10.2", "@types/cron": "^2.4.0", "jest": "^29.5.0", "tsx": "^4.6.2", "typescript": "^5.8.0", "eslint": "^8.57.0"}}