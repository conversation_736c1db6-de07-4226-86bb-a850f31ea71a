import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { restApiClient } from "../rest";
import { endpoints } from "../config";
import { User, CreateUserInput, UpdateUserInput, PaginationParams, SearchParams } from "../types";

interface UserState {
  users: User[];
  isLoading: boolean;
  error: string | null;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  addUser: (user: User) => void;
  updateUserInList: (id: string, updates: Partial<User>) => void;
  removeUser: (id: string) => void;
  fetchUsers: (pagination?: PaginationParams, search?: SearchParams) => Promise<void>;
  createUser: (input: CreateUserInput) => Promise<User>;
  updateUser: (id: string, input: UpdateUserInput) => Promise<User>;
  deleteUser: (id: string) => Promise<void>;
  assignRole: (userId: string, roleId: string) => Promise<void>;
  revokeRole: (userId: string, roleId: string) => Promise<void>;
}

export const useUserStore = create<UserState>()(
  immer((set, get) => ({
    users: [],
    isLoading: false,
    error: null,

    setLoading: (loading) =>
      set((state) => {
        state.isLoading = loading;
      }),

    setError: (error) =>
      set((state) => {
        state.error = error;
      }),

    addUser: (user) =>
      set((state) => {
        state.users.push(user);
      }),

    updateUserInList: (id, updates) =>
      set((state) => {
        const index = state.users.findIndex(u => u.id === id);
        if (index !== -1) {
          state.users[index] = { ...state.users[index], ...updates };
        }
      }),

    removeUser: (id) =>
      set((state) => {
        state.users = state.users.filter(u => u.id !== id);
      }),

    fetchUsers: async (pagination?: PaginationParams, search?: SearchParams) => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (pagination?.page) params.append("page", pagination.page.toString());
        if (pagination?.limit) params.append("limit", pagination.limit.toString());
        if (pagination?.sort) params.append("sort", pagination.sort);
        if (pagination?.order) params.append("order", pagination.order);
        if (search?.query) params.append("query", search.query);
        if (search?.filters) {
          Object.entries(search.filters).forEach(([key, value]) => {
            params.append(`filters[${key}]`, value.toString());
          });
        }

        const response = await restApiClient.get(
          `${endpoints.users.list}?${params.toString()}`
        );

        set((state) => {
          state.users = response.data.items || response.data;
        });
      } catch (error: any) {
        setError(error.message || "Failed to fetch users");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    createUser: async (input: CreateUserInput) => {
      const { setLoading, setError, addUser } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.post(endpoints.users.create, input);
        const user = response.data;

        addUser(user);
        return user;
      } catch (error: any) {
        setError(error.message || "Failed to create user");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    updateUser: async (id: string, input: UpdateUserInput) => {
      const { setLoading, setError, updateUserInList } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.put(endpoints.users.update(id), input);
        const user = response.data;

        updateUserInList(id, user);
        return user;
      } catch (error: any) {
        setError(error.message || "Failed to update user");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    deleteUser: async (id: string) => {
      const { setLoading, setError, removeUser } = get();
      
      try {
        setLoading(true);
        setError(null);

        await restApiClient.delete(endpoints.users.delete(id));
        removeUser(id);
      } catch (error: any) {
        setError(error.message || "Failed to delete user");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    assignRole: async (userId: string, roleId: string) => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        await restApiClient.post(endpoints.users.assignRole(userId), { roleId });
      } catch (error: any) {
        setError(error.message || "Failed to assign role");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    revokeRole: async (userId: string, roleId: string) => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        await restApiClient.delete(endpoints.users.revokeRole(userId), {
          data: { roleId }
        });
      } catch (error: any) {
        setError(error.message || "Failed to revoke role");
        throw error;
      } finally {
        setLoading(false);
      }
    },
  }))
);

// User utilities
export const userUtils = {
  // Get user by ID
  getUserById: (id: string): User | undefined => {
    const { users } = useUserStore.getState();
    return users.find(u => u.id === id);
  },

  // Get users by role
  getUsersByRole: (role: string): User[] => {
    const { users } = useUserStore.getState();
    return users.filter(u => u.roles.includes(role));
  },

  // Check if user has role
  userHasRole: (userId: string, role: string): boolean => {
    const user = userUtils.getUserById(userId);
    return user?.roles.includes(role) || false;
  },

  // Get active users
  getActiveUsers: (): User[] => {
    const { users } = useUserStore.getState();
    return users.filter(u => u.isActive);
  },

  // Search users
  searchUsers: (query: string): User[] => {
    const { users } = useUserStore.getState();
    const lowercaseQuery = query.toLowerCase();
    
    return users.filter(u =>
      u.name.toLowerCase().includes(lowercaseQuery) ||
      u.email.toLowerCase().includes(lowercaseQuery)
    );
  },

  // Get user display name
  getUserDisplayName: (user: User): string => {
    return user.name || user.email;
  },

  // Get user initials
  getUserInitials: (user: User): string => {
    const name = userUtils.getUserDisplayName(user);
    return name
      .split(" ")
      .map(part => part.charAt(0).toUpperCase())
      .slice(0, 2)
      .join("");
  },

  // Format last login
  formatLastLogin: (lastLoginAt?: string): string => {
    if (!lastLoginAt) return "Never";
    
    const date = new Date(lastLoginAt);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return "Today";
    if (diffDays === 1) return "Yesterday";
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
    return `${Math.floor(diffDays / 365)} years ago`;
  },
};
