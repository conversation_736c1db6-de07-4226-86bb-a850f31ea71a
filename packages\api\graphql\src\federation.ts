import { buildSubgraphSchema } from "@apollo/subgraph";
import { GraphQLSchema } from "graphql";
import { typeDefs, resolvers } from "./schema";

// Federation entity resolvers
const federationResolvers = {
  ...resolvers,
  
  // Entity reference resolvers for federation
  User: {
    ...resolvers.User,
    __resolveReference: async (reference: any, context: any) => {
      // Resolve User entity by ID for federation
      return context.dataloaders.userLoader.load(reference.id);
    },
  },

  Workspace: {
    ...resolvers.Workspace,
    __resolveReference: async (reference: any, context: any) => {
      // Resolve Workspace entity by ID for federation
      return context.dataloaders.workspaceLoader.load(reference.id);
    },
  },

  Team: {
    ...resolvers.Team,
    __resolveReference: async (reference: any, context: any) => {
      // Resolve Team entity by ID for federation
      return context.dataloaders.teamLoader.load(reference.id);
    },
  },

  Project: {
    ...resolvers.Project,
    __resolveReference: async (reference: any, context: any) => {
      // Resolve Project entity by ID for federation
      return context.dataloaders.projectLoader.load(reference.id);
    },
  },

  File: {
    ...resolvers.File,
    __resolveReference: async (reference: any, context: any) => {
      // Resolve File entity by ID for federation
      return context.dataloaders.fileLoader.load(reference.id);
    },
  },
};

// Federation type definitions with entity keys
const federationTypeDefs = `
  ${typeDefs}

  # Federation entity extensions
  extend type User @key(fields: "id") {
    id: ID! @external
  }

  extend type Workspace @key(fields: "id") {
    id: ID! @external
  }

  extend type Team @key(fields: "id") {
    id: ID! @external
  }

  extend type Project @key(fields: "id") {
    id: ID! @external
  }

  extend type File @key(fields: "id") {
    id: ID! @external
  }

  extend type Tenant @key(fields: "id") {
    id: ID! @external
  }

  extend type Role @key(fields: "id") {
    id: ID! @external
  }
`;

// Create federated subgraph schema
export const createFederatedSchema = (): GraphQLSchema => {
  return buildSubgraphSchema({
    typeDefs: federationTypeDefs,
    resolvers: federationResolvers,
  });
};

// Gateway configuration for Apollo Federation
export const gatewayConfig = {
  supergraphSdl: `
    # This would be generated by Apollo Studio or rover CLI
    # For now, this is a placeholder
  `,
  
  // Service list for local development
  serviceList: [
    {
      name: "core",
      url: "http://localhost:4000/graphql",
    },
    {
      name: "auth",
      url: "http://localhost:4001/graphql",
    },
    {
      name: "files",
      url: "http://localhost:4002/graphql",
    },
    {
      name: "analytics",
      url: "http://localhost:4003/graphql",
    },
    {
      name: "billing",
      url: "http://localhost:4004/graphql",
    },
  ],
};

// Federation utilities
export const createEntityReference = (typename: string, id: string) => {
  return {
    __typename: typename,
    id,
  };
};

export const isEntityReference = (obj: any): boolean => {
  return obj && typeof obj === "object" && obj.__typename && obj.id;
};

// Federation directives for schema composition
export const federationDirectives = `
  directive @key(fields: String!) on OBJECT | INTERFACE
  directive @requires(fields: String!) on FIELD_DEFINITION
  directive @provides(fields: String!) on FIELD_DEFINITION
  directive @external on FIELD_DEFINITION
  directive @extends on OBJECT | INTERFACE
  directive @shareable on FIELD_DEFINITION | OBJECT
  directive @inaccessible on FIELD_DEFINITION | OBJECT | INTERFACE | UNION | ARGUMENT_DEFINITION | SCALAR | ENUM | ENUM_VALUE | INPUT_OBJECT | INPUT_FIELD_DEFINITION
  directive @override(from: String!) on FIELD_DEFINITION
  directive @composeDirective(name: String!) repeatable on SCHEMA
  directive @tag(name: String!) repeatable on FIELD_DEFINITION | OBJECT | INTERFACE | UNION | ARGUMENT_DEFINITION | SCALAR | ENUM | ENUM_VALUE | INPUT_OBJECT | INPUT_FIELD_DEFINITION
`;

// Service metadata for federation
export const serviceMetadata = {
  name: "nexus-core",
  version: "1.0.0",
  description: "Core GraphQL service for Nexus SaaS platform",
  entities: [
    "User",
    "Workspace", 
    "Team",
    "Project",
    "File",
    "Tenant",
    "Role",
  ],
  capabilities: [
    "authentication",
    "authorization", 
    "user-management",
    "workspace-management",
    "team-management",
    "project-management",
    "file-management",
    "role-management",
  ],
};

// Health check for federation gateway
export const federationHealthCheck = async (): Promise<{
  status: "healthy" | "unhealthy";
  services: Array<{
    name: string;
    status: "healthy" | "unhealthy";
    url: string;
    responseTime?: number;
  }>;
}> => {
  const serviceChecks = await Promise.allSettled(
    gatewayConfig.serviceList.map(async (service) => {
      const start = Date.now();
      try {
        // TODO: Implement actual health check
        // const response = await fetch(`${service.url}/health`);
        const responseTime = Date.now() - start;
        
        return {
          name: service.name,
          status: "healthy" as const,
          url: service.url,
          responseTime,
        };
      } catch (error) {
        return {
          name: service.name,
          status: "unhealthy" as const,
          url: service.url,
          responseTime: Date.now() - start,
        };
      }
    })
  );

  const services = serviceChecks.map((result) => 
    result.status === "fulfilled" ? result.value : result.reason
  );

  const allHealthy = services.every(service => service.status === "healthy");

  return {
    status: allHealthy ? "healthy" : "unhealthy",
    services,
  };
};
