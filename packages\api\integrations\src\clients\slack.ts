import { WebClient } from "@slack/web-api";
import { APIClient, IntegrationCredentials } from "../types";

export class SlackAP<PERSON>lient implements APIClient {
  private client: WebClient;
  private credentials?: IntegrationCredentials;

  constructor() {
    this.client = new WebClient();
  }

  setAuth(credentials: IntegrationCredentials): void {
    this.credentials = credentials;
    this.client = new WebClient(credentials.accessToken);
  }

  async get(url: string, params?: Record<string, any>): Promise<any> {
    // Slack Web API doesn't use direct HTTP calls
    throw new Error("Use specific Slack API methods instead of generic HTTP calls");
  }

  async post(url: string, data?: any): Promise<any> {
    // Slack Web API doesn't use direct HTTP calls
    throw new Error("Use specific Slack API methods instead of generic HTTP calls");
  }

  async put(url: string, data?: any): Promise<any> {
    throw new Error("PUT method not supported by Slack API");
  }

  async patch(url: string, data?: any): Promise<any> {
    throw new Error("PATCH method not supported by Slack API");
  }

  async delete(url: string): Promise<any> {
    throw new Error("Use specific Slack API methods instead of generic HTTP calls");
  }

  // Slack doesn't use refresh tokens in the same way
  async refreshToken(): Promise<any> {
    throw new Error("Slack tokens don't require refresh");
  }

  // Authentication test
  async testAuth(): Promise<any> {
    const response = await this.client.auth.test();
    return response;
  }

  // Channel methods
  async listChannels(params?: {
    excludeArchived?: boolean;
    types?: string;
    limit?: number;
    cursor?: string;
  }): Promise<any> {
    const response = await this.client.conversations.list({
      exclude_archived: params?.excludeArchived,
      types: params?.types || "public_channel,private_channel",
      limit: params?.limit || 100,
      cursor: params?.cursor,
    });

    return response;
  }

  async getChannelInfo(channelId: string): Promise<any> {
    const response = await this.client.conversations.info({
      channel: channelId,
    });

    return response;
  }

  async createChannel(params: {
    name: string;
    isPrivate?: boolean;
  }): Promise<any> {
    const response = await this.client.conversations.create({
      name: params.name,
      is_private: params.isPrivate || false,
    });

    return response;
  }

  async joinChannel(channelId: string): Promise<any> {
    const response = await this.client.conversations.join({
      channel: channelId,
    });

    return response;
  }

  async leaveChannel(channelId: string): Promise<any> {
    const response = await this.client.conversations.leave({
      channel: channelId,
    });

    return response;
  }

  // Message methods
  async sendMessage(params: {
    channel: string;
    text?: string;
    blocks?: any[];
    attachments?: any[];
    threadTs?: string;
    asUser?: boolean;
  }): Promise<any> {
    const response = await this.client.chat.postMessage({
      channel: params.channel,
      text: params.text,
      blocks: params.blocks,
      attachments: params.attachments,
      thread_ts: params.threadTs,
      as_user: params.asUser,
    });

    return response;
  }

  async updateMessage(params: {
    channel: string;
    ts: string;
    text?: string;
    blocks?: any[];
    attachments?: any[];
  }): Promise<any> {
    const response = await this.client.chat.update({
      channel: params.channel,
      ts: params.ts,
      text: params.text,
      blocks: params.blocks,
      attachments: params.attachments,
    });

    return response;
  }

  async deleteMessage(params: {
    channel: string;
    ts: string;
  }): Promise<any> {
    const response = await this.client.chat.delete({
      channel: params.channel,
      ts: params.ts,
    });

    return response;
  }

  async getMessageHistory(params: {
    channel: string;
    latest?: string;
    oldest?: string;
    limit?: number;
    cursor?: string;
  }): Promise<any> {
    const response = await this.client.conversations.history({
      channel: params.channel,
      latest: params.latest,
      oldest: params.oldest,
      limit: params.limit || 100,
      cursor: params.cursor,
    });

    return response;
  }

  async getReplies(params: {
    channel: string;
    ts: string;
    latest?: string;
    oldest?: string;
    limit?: number;
    cursor?: string;
  }): Promise<any> {
    const response = await this.client.conversations.replies({
      channel: params.channel,
      ts: params.ts,
      latest: params.latest,
      oldest: params.oldest,
      limit: params.limit || 100,
      cursor: params.cursor,
    });

    return response;
  }

  // User methods
  async listUsers(params?: {
    limit?: number;
    cursor?: string;
  }): Promise<any> {
    const response = await this.client.users.list({
      limit: params?.limit || 100,
      cursor: params?.cursor,
    });

    return response;
  }

  async getUserInfo(userId: string): Promise<any> {
    const response = await this.client.users.info({
      user: userId,
    });

    return response;
  }

  async getUserProfile(userId: string): Promise<any> {
    const response = await this.client.users.profile.get({
      user: userId,
    });

    return response;
  }

  // File methods
  async uploadFile(params: {
    channels?: string[];
    content?: string;
    file?: Buffer;
    filename?: string;
    filetype?: string;
    title?: string;
    initialComment?: string;
    threadTs?: string;
  }): Promise<any> {
    const response = await this.client.files.upload({
      channels: params.channels?.join(","),
      content: params.content,
      file: params.file,
      filename: params.filename,
      filetype: params.filetype,
      title: params.title,
      initial_comment: params.initialComment,
      thread_ts: params.threadTs,
    });

    return response;
  }

  async getFileInfo(fileId: string): Promise<any> {
    const response = await this.client.files.info({
      file: fileId,
    });

    return response;
  }

  async deleteFile(fileId: string): Promise<any> {
    const response = await this.client.files.delete({
      file: fileId,
    });

    return response;
  }

  // Reaction methods
  async addReaction(params: {
    channel: string;
    timestamp: string;
    name: string;
  }): Promise<any> {
    const response = await this.client.reactions.add({
      channel: params.channel,
      timestamp: params.timestamp,
      name: params.name,
    });

    return response;
  }

  async removeReaction(params: {
    channel: string;
    timestamp: string;
    name: string;
  }): Promise<any> {
    const response = await this.client.reactions.remove({
      channel: params.channel,
      timestamp: params.timestamp,
      name: params.name,
    });

    return response;
  }

  // Workspace methods
  async getTeamInfo(): Promise<any> {
    const response = await this.client.team.info();
    return response;
  }

  // Search methods
  async searchMessages(params: {
    query: string;
    sort?: "score" | "timestamp";
    sortDir?: "asc" | "desc";
    highlight?: boolean;
    count?: number;
    page?: number;
  }): Promise<any> {
    const response = await this.client.search.messages({
      query: params.query,
      sort: params.sort,
      sort_dir: params.sortDir,
      highlight: params.highlight,
      count: params.count || 20,
      page: params.page || 1,
    });

    return response;
  }

  async searchFiles(params: {
    query: string;
    sort?: "score" | "timestamp";
    sortDir?: "asc" | "desc";
    highlight?: boolean;
    count?: number;
    page?: number;
  }): Promise<any> {
    const response = await this.client.search.files({
      query: params.query,
      sort: params.sort,
      sort_dir: params.sortDir,
      highlight: params.highlight,
      count: params.count || 20,
      page: params.page || 1,
    });

    return response;
  }

  // Webhook methods
  async sendWebhookMessage(webhookUrl: string, params: {
    text?: string;
    blocks?: any[];
    attachments?: any[];
    username?: string;
    iconEmoji?: string;
    iconUrl?: string;
    channel?: string;
  }): Promise<any> {
    const response = await fetch(webhookUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        text: params.text,
        blocks: params.blocks,
        attachments: params.attachments,
        username: params.username,
        icon_emoji: params.iconEmoji,
        icon_url: params.iconUrl,
        channel: params.channel,
      }),
    });

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.statusText}`);
    }

    return response.text();
  }

  // Bot methods
  async getBotInfo(botId: string): Promise<any> {
    const response = await this.client.bots.info({
      bot: botId,
    });

    return response;
  }

  // App methods
  async getAppInfo(): Promise<any> {
    const response = await this.client.apps.permissions.info();
    return response;
  }

  // Utility methods
  async openDialog(params: {
    triggerId: string;
    dialog: any;
  }): Promise<any> {
    const response = await this.client.dialog.open({
      trigger_id: params.triggerId,
      dialog: params.dialog,
    });

    return response;
  }

  async openView(params: {
    triggerId: string;
    view: any;
  }): Promise<any> {
    const response = await this.client.views.open({
      trigger_id: params.triggerId,
      view: params.view,
    });

    return response;
  }

  async updateView(params: {
    viewId: string;
    view: any;
    hash?: string;
  }): Promise<any> {
    const response = await this.client.views.update({
      view_id: params.viewId,
      view: params.view,
      hash: params.hash,
    });

    return response;
  }
}
