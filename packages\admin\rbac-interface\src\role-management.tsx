"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useRBAC, useIsAdmin } from "@nexus/rbac";
import { Role, RoleLevel, Permission, ResourceType, ActionType } from "@nexus/rbac";
import { format } from "date-fns";

// Main role management component
export function RoleManagement() {
  const { roles, createRole, updateRole, deleteRole, isLoading, error } = useRBAC();
  const isAdmin = useIsAdmin();
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);

  if (!isAdmin) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Access denied. Admin privileges required.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Role Management</h1>
          <p className="text-gray-600">Manage roles and permissions for your organization</p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          Create Role
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Role List */}
        <div className="lg:col-span-2">
          <RoleList
            roles={roles}
            isLoading={isLoading}
            onSelectRole={setSelectedRole}
            onEditRole={(role) => {
              setSelectedRole(role);
              setShowEditForm(true);
            }}
            onDeleteRole={deleteRole}
          />
        </div>

        {/* Role Details */}
        <div>
          {selectedRole ? (
            <RoleDetails role={selectedRole} />
          ) : (
            <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <p className="text-gray-500">Select a role to view details</p>
            </div>
          )}
        </div>
      </div>

      {/* Create Role Modal */}
      {showCreateForm && (
        <CreateRoleModal
          onClose={() => setShowCreateForm(false)}
          onSuccess={(role) => {
            setShowCreateForm(false);
            setSelectedRole(role);
          }}
        />
      )}

      {/* Edit Role Modal */}
      {showEditForm && selectedRole && (
        <EditRoleModal
          role={selectedRole}
          onClose={() => setShowEditForm(false)}
          onSuccess={(role) => {
            setShowEditForm(false);
            setSelectedRole(role);
          }}
        />
      )}
    </div>
  );
}

// Role list component
function RoleList({
  roles,
  isLoading,
  onSelectRole,
  onEditRole,
  onDeleteRole,
}: {
  roles: Role[];
  isLoading: boolean;
  onSelectRole: (role: Role) => void;
  onEditRole: (role: Role) => void;
  onDeleteRole: (roleId: string) => Promise<void>;
}) {
  const [selectedRoleId, setSelectedRoleId] = useState<string | null>(null);

  if (isLoading) {
    return <div className="text-center py-8">Loading roles...</div>;
  }

  return (
    <div className="bg-white rounded-lg border">
      <div className="px-6 py-4 border-b">
        <h2 className="text-lg font-semibold">Roles ({roles.length})</h2>
      </div>
      
      <div className="divide-y">
        {roles.map((role) => (
          <div
            key={role.id}
            className={`p-6 cursor-pointer hover:bg-gray-50 ${
              selectedRoleId === role.id ? "bg-blue-50 border-l-4 border-blue-500" : ""
            }`}
            onClick={() => {
              setSelectedRoleId(role.id);
              onSelectRole(role);
            }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {role.icon && <span className="text-2xl">{role.icon}</span>}
                <div>
                  <h3 className="font-semibold text-gray-900">{role.name}</h3>
                  <p className="text-sm text-gray-500">{role.slug}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className={`px-2 py-1 text-xs rounded ${
                  role.isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                }`}>
                  {role.isActive ? "Active" : "Inactive"}
                </span>
                
                <span className={`px-2 py-1 text-xs rounded ${
                  role.level === "system" ? "bg-red-100 text-red-800" :
                  role.level === "organization" ? "bg-purple-100 text-purple-800" :
                  role.level === "workspace" ? "bg-blue-100 text-blue-800" :
                  "bg-gray-100 text-gray-800"
                }`}>
                  {role.level}
                </span>
              </div>
            </div>
            
            {role.description && (
              <p className="mt-2 text-sm text-gray-600">{role.description}</p>
            )}
            
            <div className="mt-3 flex items-center justify-between">
              <span className="text-xs text-gray-500">
                {role.permissions.length} permissions • Created {format(new Date(role.createdAt), "MMM dd, yyyy")}
              </span>
              
              <div className="flex space-x-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onEditRole(role);
                  }}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  Edit
                </button>
                {!role.isSystem && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      if (confirm(`Are you sure you want to delete the role "${role.name}"?`)) {
                        onDeleteRole(role.id);
                      }
                    }}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    Delete
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Role details component
function RoleDetails({ role }: { role: Role }) {
  return (
    <div className="bg-white rounded-lg border">
      <div className="px-6 py-4 border-b">
        <h2 className="text-lg font-semibold">Role Details</h2>
      </div>
      
      <div className="p-6 space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">Name</label>
          <p className="mt-1 text-sm text-gray-900">{role.name}</p>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">Slug</label>
          <p className="mt-1 text-sm text-gray-900 font-mono">{role.slug}</p>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">Level</label>
          <p className="mt-1 text-sm text-gray-900 capitalize">{role.level}</p>
        </div>
        
        {role.description && (
          <div>
            <label className="block text-sm font-medium text-gray-700">Description</label>
            <p className="mt-1 text-sm text-gray-900">{role.description}</p>
          </div>
        )}
        
        <div>
          <label className="block text-sm font-medium text-gray-700">Status</label>
          <p className="mt-1">
            <span className={`px-2 py-1 text-xs rounded ${
              role.isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
            }`}>
              {role.isActive ? "Active" : "Inactive"}
            </span>
          </p>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">System Role</label>
          <p className="mt-1">
            <span className={`px-2 py-1 text-xs rounded ${
              role.isSystem ? "bg-red-100 text-red-800" : "bg-gray-100 text-gray-800"
            }`}>
              {role.isSystem ? "Yes" : "No"}
            </span>
          </p>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">Permissions ({role.permissions.length})</label>
          <div className="mt-2 space-y-1 max-h-40 overflow-y-auto">
            {role.permissions.map((permission, index) => (
              <div key={index} className="text-xs bg-gray-50 px-2 py-1 rounded">
                {permission.resource}:{permission.action} ({permission.scope})
              </div>
            ))}
          </div>
        </div>
        
        <div className="pt-4 border-t">
          <div className="text-xs text-gray-500">
            <p>Created: {format(new Date(role.createdAt), "PPpp")}</p>
            <p>Updated: {format(new Date(role.updatedAt), "PPpp")}</p>
          </div>
        </div>
      </div>
    </div>
  );
}

// Create role modal
function CreateRoleModal({
  onClose,
  onSuccess,
}: {
  onClose: () => void;
  onSuccess: (role: Role) => void;
}) {
  const { createRole, isLoading } = useRBAC();
  const { register, handleSubmit, formState: { errors } } = useForm<{
    name: string;
    slug: string;
    description: string;
    level: RoleLevel;
  }>();

  const onSubmit = async (data: any) => {
    try {
      const role = await createRole({
        ...data,
        permissions: [],
        isSystem: false,
        isActive: true,
        metadata: {},
      });
      onSuccess(role);
    } catch (error) {
      console.error("Failed to create role:", error);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Create New Role</h3>
        
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Name</label>
            <input
              {...register("name", { required: "Name is required" })}
              className="w-full px-3 py-2 border rounded-md"
            />
            {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>}
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Slug</label>
            <input
              {...register("slug", { required: "Slug is required" })}
              className="w-full px-3 py-2 border rounded-md"
            />
            {errors.slug && <p className="text-red-500 text-sm mt-1">{errors.slug.message}</p>}
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Description</label>
            <textarea
              {...register("description")}
              className="w-full px-3 py-2 border rounded-md"
              rows={3}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Level</label>
            <select
              {...register("level", { required: "Level is required" })}
              className="w-full px-3 py-2 border rounded-md"
            >
              <option value="">Select level</option>
              <option value="organization">Organization</option>
              <option value="workspace">Workspace</option>
              <option value="team">Team</option>
              <option value="user">User</option>
            </select>
            {errors.level && <p className="text-red-500 text-sm mt-1">{errors.level.message}</p>}
          </div>
          
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? "Creating..." : "Create Role"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Edit role modal (simplified for now)
function EditRoleModal({
  role,
  onClose,
  onSuccess,
}: {
  role: Role;
  onClose: () => void;
  onSuccess: (role: Role) => void;
}) {
  const { updateRole, isLoading } = useRBAC();
  const { register, handleSubmit, formState: { errors } } = useForm({
    defaultValues: {
      name: role.name,
      description: role.description || "",
      isActive: role.isActive,
    },
  });

  const onSubmit = async (data: any) => {
    try {
      const updatedRole = await updateRole(role.id, data);
      onSuccess(updatedRole);
    } catch (error) {
      console.error("Failed to update role:", error);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Edit Role</h3>
        
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Name</label>
            <input
              {...register("name", { required: "Name is required" })}
              className="w-full px-3 py-2 border rounded-md"
            />
            {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>}
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Description</label>
            <textarea
              {...register("description")}
              className="w-full px-3 py-2 border rounded-md"
              rows={3}
            />
          </div>
          
          <div className="flex items-center">
            <input
              type="checkbox"
              {...register("isActive")}
              className="mr-2"
            />
            <label className="text-sm font-medium">Active</label>
          </div>
          
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? "Updating..." : "Update Role"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
