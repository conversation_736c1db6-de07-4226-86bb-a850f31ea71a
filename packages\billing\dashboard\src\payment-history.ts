import { PaymentRecord, PaymentFilters } from "./billing-types";

export class PaymentHistoryService {
  private tenantId: string;

  constructor(tenantId: string) {
    this.tenantId = tenantId;
  }

  // Get payment history with filtering and pagination
  async getPayments(
    filters?: PaymentFilters,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    data: PaymentRecord[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (filters) {
      if (filters.status?.length) {
        params.append("status", filters.status.join(","));
      }
      if (filters.dateRange) {
        params.append("startDate", filters.dateRange.start.toISOString());
        params.append("endDate", filters.dateRange.end.toISOString());
      }
      if (filters.paymentMethod?.length) {
        params.append("paymentMethod", filters.paymentMethod.join(","));
      }
      if (filters.amountRange) {
        params.append("minAmount", filters.amountRange.min.toString());
        params.append("maxAmount", filters.amountRange.max.toString());
      }
      if (filters.search) {
        params.append("search", filters.search);
      }
    }

    const response = await fetch(`/api/billing/payments?${params}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch payments");
    }

    return response.json();
  }

  // Get single payment
  async getPayment(paymentId: string): Promise<PaymentRecord> {
    const response = await fetch(`/api/billing/payments/${paymentId}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch payment");
    }

    return response.json();
  }

  // Download payment receipt
  async downloadReceipt(paymentId: string): Promise<Blob> {
    const response = await fetch(`/api/billing/payments/${paymentId}/receipt`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to download receipt");
    }

    return response.blob();
  }

  // Refund payment
  async refundPayment(
    paymentId: string,
    amount?: number,
    reason?: string
  ): Promise<PaymentRecord> {
    const response = await fetch(`/api/billing/payments/${paymentId}/refund`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify({ amount, reason }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to refund payment");
    }

    return response.json();
  }

  // Get payment statistics
  async getPaymentStats(period?: { start: Date; end: Date }): Promise<{
    totalPayments: number;
    totalAmount: number;
    successfulPayments: number;
    failedPayments: number;
    refundedAmount: number;
    averageAmount: number;
    successRate: number;
    paymentMethodBreakdown: Array<{
      method: string;
      count: number;
      amount: number;
    }>;
  }> {
    const params = new URLSearchParams();
    
    if (period) {
      params.append("startDate", period.start.toISOString());
      params.append("endDate", period.end.toISOString());
    }

    const response = await fetch(`/api/billing/payments/stats?${params}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch payment statistics");
    }

    return response.json();
  }

  // Export payment data
  async exportPayments(
    filters?: PaymentFilters,
    format: "csv" | "xlsx" = "csv"
  ): Promise<Blob> {
    const params = new URLSearchParams({ format });

    if (filters) {
      if (filters.status?.length) {
        params.append("status", filters.status.join(","));
      }
      if (filters.dateRange) {
        params.append("startDate", filters.dateRange.start.toISOString());
        params.append("endDate", filters.dateRange.end.toISOString());
      }
      if (filters.paymentMethod?.length) {
        params.append("paymentMethod", filters.paymentMethod.join(","));
      }
      if (filters.amountRange) {
        params.append("minAmount", filters.amountRange.min.toString());
        params.append("maxAmount", filters.amountRange.max.toString());
      }
      if (filters.search) {
        params.append("search", filters.search);
      }
    }

    const response = await fetch(`/api/billing/payments/export?${params}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to export payments");
    }

    return response.blob();
  }
}

export const createPaymentHistoryService = (tenantId: string): PaymentHistoryService => {
  return new PaymentHistoryService(tenantId);
};
