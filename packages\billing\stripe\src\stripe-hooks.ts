import { useCallback, useEffect } from "react";
import { useTenantId } from "@nexus/tenant-context";
import { useStripeStore } from "./stripe-store";
import { createPaymentService } from "./payment-service";
import { createSubscriptionService } from "./subscription-service";
import { CreatePaymentIntentRequest, CreateSubscriptionRequest } from "./stripe-types";

// Main Stripe hook
export function useStripe() {
  const tenantId = useTenantId();
  const {
    customer,
    paymentMethods,
    currentSubscription,
    products,
    isLoading,
    error,
    setCustomer,
    setPaymentMethods,
    setCurrentSubscription,
    setProducts,
    setLoading,
    setError,
    getDefaultPaymentMethod,
    hasActiveSubscription,
  } = useStripeStore();

  const paymentService = tenantId ? createPaymentService(tenantId) : null;
  const subscriptionService = tenantId ? createSubscriptionService(tenantId) : null;

  // Load initial data
  const loadStripeData = useCallback(async () => {
    if (!paymentService || !subscriptionService) return;
    
    setLoading(true);
    try {
      const [paymentMethodsData, subscriptionData, productsData] = await Promise.all([
        paymentService.getPaymentMethods(),
        subscriptionService.getCurrentSubscription(),
        subscriptionService.getProducts(),
      ]);
      
      setPaymentMethods(paymentMethodsData);
      setCurrentSubscription(subscriptionData);
      setProducts(productsData);
      setError(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to load Stripe data");
    } finally {
      setLoading(false);
    }
  }, [paymentService, subscriptionService, setPaymentMethods, setCurrentSubscription, setProducts, setLoading, setError]);

  // Payment operations
  const createPaymentIntent = useCallback(async (data: CreatePaymentIntentRequest) => {
    if (!paymentService) throw new Error("No payment service available");
    
    setLoading(true);
    try {
      const paymentIntent = await paymentService.createPaymentIntent(data);
      setError(null);
      return paymentIntent;
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to create payment intent";
      setError(message);
      throw new Error(message);
    } finally {
      setLoading(false);
    }
  }, [paymentService, setLoading, setError]);

  const attachPaymentMethod = useCallback(async (paymentMethodId: string) => {
    if (!paymentService) throw new Error("No payment service available");
    
    setLoading(true);
    try {
      const paymentMethod = await paymentService.attachPaymentMethod(paymentMethodId);
      await loadStripeData(); // Reload to get updated data
      setError(null);
      return paymentMethod;
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to attach payment method";
      setError(message);
      throw new Error(message);
    } finally {
      setLoading(false);
    }
  }, [paymentService, loadStripeData, setLoading, setError]);

  const detachPaymentMethod = useCallback(async (paymentMethodId: string) => {
    if (!paymentService) throw new Error("No payment service available");
    
    setLoading(true);
    try {
      await paymentService.detachPaymentMethod(paymentMethodId);
      await loadStripeData(); // Reload to get updated data
      setError(null);
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to detach payment method";
      setError(message);
      throw new Error(message);
    } finally {
      setLoading(false);
    }
  }, [paymentService, loadStripeData, setLoading, setError]);

  // Subscription operations
  const createSubscription = useCallback(async (data: CreateSubscriptionRequest) => {
    if (!subscriptionService) throw new Error("No subscription service available");
    
    setLoading(true);
    try {
      const subscription = await subscriptionService.createSubscription(data);
      setCurrentSubscription(subscription);
      setError(null);
      return subscription;
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to create subscription";
      setError(message);
      throw new Error(message);
    } finally {
      setLoading(false);
    }
  }, [subscriptionService, setCurrentSubscription, setLoading, setError]);

  const cancelSubscription = useCallback(async (subscriptionId: string, cancelAtPeriodEnd: boolean = true) => {
    if (!subscriptionService) throw new Error("No subscription service available");
    
    setLoading(true);
    try {
      const subscription = await subscriptionService.cancelSubscription(subscriptionId, cancelAtPeriodEnd);
      setCurrentSubscription(subscription);
      setError(null);
      return subscription;
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to cancel subscription";
      setError(message);
      throw new Error(message);
    } finally {
      setLoading(false);
    }
  }, [subscriptionService, setCurrentSubscription, setLoading, setError]);

  const createBillingPortalSession = useCallback(async (returnUrl: string) => {
    if (!paymentService) throw new Error("No payment service available");
    
    setLoading(true);
    try {
      const session = await paymentService.createBillingPortalSession(returnUrl);
      setError(null);
      return session;
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to create billing portal session";
      setError(message);
      throw new Error(message);
    } finally {
      setLoading(false);
    }
  }, [paymentService, setLoading, setError]);

  // Auto-load data when tenant changes
  useEffect(() => {
    if (tenantId) {
      loadStripeData();
    }
  }, [tenantId, loadStripeData]);

  return {
    customer,
    paymentMethods,
    currentSubscription,
    products,
    isLoading,
    error,
    defaultPaymentMethod: getDefaultPaymentMethod(),
    hasActiveSubscription: hasActiveSubscription(),
    loadStripeData,
    createPaymentIntent,
    attachPaymentMethod,
    detachPaymentMethod,
    createSubscription,
    cancelSubscription,
    createBillingPortalSession,
  };
}

// Convenience hooks
export function usePaymentMethods() {
  const { paymentMethods, defaultPaymentMethod } = useStripeStore();
  return { paymentMethods, defaultPaymentMethod: defaultPaymentMethod };
}

export function useSubscription() {
  const { currentSubscription, hasActiveSubscription } = useStripeStore();
  return { subscription: currentSubscription, hasActiveSubscription: hasActiveSubscription() };
}

export function useProducts() {
  const { products } = useStripeStore();
  return products;
}

export function useStripeLoading() {
  const { isLoading } = useStripeStore();
  return isLoading;
}

export function useStripeError() {
  const { error } = useStripeStore();
  return error;
}
