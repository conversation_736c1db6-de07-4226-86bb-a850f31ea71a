{"name": "@nexus/auth-client", "version": "0.1.0", "description": "Authentication client for Nexus SaaS", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "@nexus/validation": "workspace:*", "better-auth": "1.3.0", "react": "19.1.0"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@types/react": "19.1.8", "typescript": "5.8.3"}}