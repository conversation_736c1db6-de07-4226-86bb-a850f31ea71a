import { FastifyInstance } from "fastify";
import fp from "fastify-plugin";
import { config } from "../config";

export const corsPlugin = fp(async (fastify: FastifyInstance) => {
  await fastify.register(require("@fastify/cors"), {
    origin: (origin, callback) => {
      const hostname = new URL(origin || "").hostname;
      
      // Allow requests with no origin (mobile apps, curl, etc.)
      if (!origin) return callback(null, true);
      
      // Allow localhost in development
      if (hostname === "localhost" || hostname === "127.0.0.1") {
        return callback(null, true);
      }
      
      // Check against configured origins
      const allowedOrigins = config.CORS_ORIGIN.split(",").map(o => o.trim());
      
      if (allowedOrigins.includes("*") || allowedOrigins.includes(origin)) {
        return callback(null, true);
      }
      
      return callback(new Error("Not allowed by CORS"), false);
    },
    credentials: true,
    methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
    allowedHeaders: [
      "Origin",
      "X-Requested-With",
      "Content-Type",
      "Accept",
      "Authorization",
      "X-Tenant-Id",
      "X-Workspace-Id",
      "X-Team-Id",
      "X-API-Key",
    ],
    exposedHeaders: [
      "X-Total-Count",
      "X-Page-Count",
      "X-Current-Page",
      "X-Per-Page",
      "X-Rate-Limit-Limit",
      "X-Rate-Limit-Remaining",
      "X-Rate-Limit-Reset",
    ],
  });
});
