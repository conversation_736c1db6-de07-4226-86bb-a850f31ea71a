import { ProfileImage, AvatarUploadData } from "./profile-types";
import { validateAvatarUpload } from "./profile-validation";

export class AvatarService {
  private userId: string;
  private tenantId: string;

  constructor(userId: string, tenantId: string) {
    this.userId = userId;
    this.tenantId = tenantId;
  }

  // Upload new avatar
  async uploadAvatar(data: AvatarUploadData): Promise<ProfileImage> {
    const validatedData = validateAvatarUpload(data);
    
    const formData = new FormData();
    formData.append("file", validatedData.file);
    if (validatedData.altText) {
      formData.append("altText", validatedData.altText);
    }

    const response = await fetch(`/api/profile/${this.userId}/avatar`, {
      method: "POST",
      headers: {
        "x-tenant-id": this.tenantId,
      },
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to upload avatar");
    }

    return response.json();
  }

  // Get all profile images
  async getProfileImages(): Promise<ProfileImage[]> {
    const response = await fetch(`/api/profile/${this.userId}/images`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch profile images");
    }

    return response.json();
  }

  // Set primary avatar
  async setPrimaryAvatar(imageId: string): Promise<void> {
    const response = await fetch(`/api/profile/${this.userId}/avatar/${imageId}/primary`, {
      method: "PATCH",
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to set primary avatar");
    }
  }

  // Delete avatar
  async deleteAvatar(imageId: string): Promise<void> {
    const response = await fetch(`/api/profile/${this.userId}/avatar/${imageId}`, {
      method: "DELETE",
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to delete avatar");
    }
  }

  // Generate avatar URL with transformations
  generateAvatarUrl(imageUrl: string, size: number = 150, format: "webp" | "jpeg" | "png" = "webp"): string {
    // This would integrate with your image service (Cloudinary, ImageKit, etc.)
    const params = new URLSearchParams({
      w: size.toString(),
      h: size.toString(),
      f: format,
      q: "auto",
      c: "fill",
    });
    
    return `${imageUrl}?${params.toString()}`;
  }

  // Get avatar initials fallback
  generateInitials(name: string): string {
    return name
      .split(" ")
      .map(part => part.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  }

  // Validate image file
  validateImageFile(file: File): { valid: boolean; error?: string } {
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ["image/jpeg", "image/png", "image/webp"];

    if (file.size > maxSize) {
      return { valid: false, error: "File size must be less than 5MB" };
    }

    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: "File must be a JPEG, PNG, or WebP image" };
    }

    return { valid: true };
  }
}

export const createAvatarService = (userId: string, tenantId: string): AvatarService => {
  return new AvatarService(userId, tenantId);
};
