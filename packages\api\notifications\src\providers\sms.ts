import { <PERSON><PERSON><PERSON> } from "twilio";
import { SMSConfig, Notification, NotificationTemplate } from "../types";

export interface SMSProvider {
  send(notification: Notification, template: NotificationTemplate, variables: Record<string, any>): Promise<void>;
  verify(): Promise<boolean>;
}

export class TwilioSMSProvider implements SMSProvider {
  private client: Twilio;
  private config: SMSConfig;

  constructor(config: SMSConfig) {
    this.config = config;
    
    if (!config.twilio?.accountSid || !config.twilio?.authToken || !config.twilio?.from) {
      throw new Error("Twilio configuration is required");
    }

    this.client = new Twilio(config.twilio.accountSid, config.twilio.authToken);
  }

  async send(
    notification: Notification,
    template: NotificationTemplate,
    variables: Record<string, any>
  ): Promise<void> {
    try {
      const smsContent = template.content.sms;
      if (!smsContent) {
        throw new Error("SMS content not found in template");
      }

      const message = this.replaceVariables(smsContent, variables);
      const phoneNumber = variables.phoneNumber || variables.phone;

      if (!phoneNumber) {
        throw new Error("Phone number is required for SMS notification");
      }

      // Validate phone number format
      if (!this.isValidPhoneNumber(phoneNumber)) {
        throw new Error("Invalid phone number format");
      }

      await this.client.messages.create({
        body: message,
        from: this.config.twilio!.from,
        to: phoneNumber,
        statusCallback: variables.statusCallback,
        validityPeriod: this.getValidityPeriod(notification.priority),
      });
    } catch (error: any) {
      throw new Error(`Failed to send SMS via Twilio: ${error.message}`);
    }
  }

  async verify(): Promise<boolean> {
    try {
      // Test Twilio configuration by fetching account info
      await this.client.api.accounts(this.config.twilio!.accountSid).fetch();
      return true;
    } catch (error) {
      return false;
    }
  }

  private replaceVariables(content: string, variables: Record<string, any>): string {
    let result = content;
    
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      result = result.replace(regex, String(value));
    }
    
    return result;
  }

  private isValidPhoneNumber(phoneNumber: string): boolean {
    // Basic E.164 format validation
    const e164Regex = /^\+[1-9]\d{1,14}$/;
    return e164Regex.test(phoneNumber);
  }

  private getValidityPeriod(priority: string): number {
    // Return validity period in seconds
    switch (priority) {
      case "urgent":
        return 3600; // 1 hour
      case "high":
        return 7200; // 2 hours
      case "normal":
        return 14400; // 4 hours
      case "low":
        return 86400; // 24 hours
      default:
        return 14400;
    }
  }
}

export class AWSSNSSMSProvider implements SMSProvider {
  private config: SMSConfig;

  constructor(config: SMSConfig) {
    this.config = config;
    
    if (!config.aws_sns?.region || !config.aws_sns?.accessKeyId || !config.aws_sns?.secretAccessKey) {
      throw new Error("AWS SNS configuration is required");
    }
  }

  async send(
    notification: Notification,
    template: NotificationTemplate,
    variables: Record<string, any>
  ): Promise<void> {
    try {
      const AWS = require('aws-sdk');
      
      const sns = new AWS.SNS({
        region: this.config.aws_sns!.region,
        accessKeyId: this.config.aws_sns!.accessKeyId,
        secretAccessKey: this.config.aws_sns!.secretAccessKey,
      });

      const smsContent = template.content.sms;
      if (!smsContent) {
        throw new Error("SMS content not found in template");
      }

      const message = this.replaceVariables(smsContent, variables);
      const phoneNumber = variables.phoneNumber || variables.phone;

      if (!phoneNumber) {
        throw new Error("Phone number is required for SMS notification");
      }

      // Validate phone number format
      if (!this.isValidPhoneNumber(phoneNumber)) {
        throw new Error("Invalid phone number format");
      }

      const params = {
        Message: message,
        PhoneNumber: phoneNumber,
        MessageAttributes: {
          'AWS.SNS.SMS.SenderID': {
            DataType: 'String',
            StringValue: 'Nexus',
          },
          'AWS.SNS.SMS.SMSType': {
            DataType: 'String',
            StringValue: this.getSMSType(notification.priority),
          },
        },
      };

      await sns.publish(params).promise();
    } catch (error: any) {
      throw new Error(`Failed to send SMS via AWS SNS: ${error.message}`);
    }
  }

  async verify(): Promise<boolean> {
    try {
      const AWS = require('aws-sdk');
      
      const sns = new AWS.SNS({
        region: this.config.aws_sns!.region,
        accessKeyId: this.config.aws_sns!.accessKeyId,
        secretAccessKey: this.config.aws_sns!.secretAccessKey,
      });

      // Test by listing SMS attributes
      await sns.getSMSAttributes().promise();
      return true;
    } catch (error) {
      return false;
    }
  }

  private replaceVariables(content: string, variables: Record<string, any>): string {
    let result = content;
    
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      result = result.replace(regex, String(value));
    }
    
    return result;
  }

  private isValidPhoneNumber(phoneNumber: string): boolean {
    // Basic E.164 format validation
    const e164Regex = /^\+[1-9]\d{1,14}$/;
    return e164Regex.test(phoneNumber);
  }

  private getSMSType(priority: string): "Promotional" | "Transactional" {
    return priority === "urgent" || priority === "high" ? "Transactional" : "Promotional";
  }
}

// Factory function to create SMS provider
export function createSMSProvider(config: SMSConfig): SMSProvider {
  switch (config.provider) {
    case "twilio":
      return new TwilioSMSProvider(config);
    case "aws_sns":
      return new AWSSNSSMSProvider(config);
    default:
      throw new Error(`Unsupported SMS provider: ${config.provider}`);
  }
}
