import { useTenantContext } from "./tenant-context";
import { TenantData, TenantUser } from "./tenant-types";

// Main tenant hook
export { useTenantContext as useTenant };

// Convenience hooks
export function useTenantData(): TenantData | null {
  const { tenant } = useTenantContext();
  return tenant;
}

export function useTenantUser(): TenantUser | null {
  const { user } = useTenantContext();
  return user;
}

export function useTenantId(): string | null {
  const { tenant } = useTenantContext();
  return tenant?.id || null;
}

export function useTenantSlug(): string | null {
  const { tenant } = useTenantContext();
  return tenant?.slug || null;
}

export function useTenantName(): string | null {
  const { tenant } = useTenantContext();
  return tenant?.name || null;
}

export function useTenantPlan(): string | null {
  const { tenant } = useTenantContext();
  return tenant?.plan || null;
}

export function useTenantStatus(): string | null {
  const { tenant } = useTenantContext();
  return tenant?.status || null;
}

export function useTenantSettings() {
  const { tenant } = useTenantContext();
  return tenant?.settings || null;
}

// Permission hooks
export function useTenantPermissions(): string[] {
  const { permissions } = useTenantContext();
  return permissions;
}

export function useHasTenantPermission(permission: string): boolean {
  const { hasPermission } = useTenantContext();
  return hasPermission(permission);
}

export function useHasTenantRole(role: string): boolean {
  const { hasRole } = useTenantContext();
  return hasRole(role);
}

// Role-specific hooks
export function useIsTenantOwner(): boolean {
  return useHasTenantRole("OWNER");
}

export function useIsTenantAdmin(): boolean {
  const { user } = useTenantContext();
  return user?.role === "OWNER" || user?.role === "ADMIN";
}

export function useIsTenantMember(): boolean {
  return useHasTenantRole("MEMBER");
}

export function useIsTenantViewer(): boolean {
  return useHasTenantRole("VIEWER");
}

// Action hooks
export function useTenantActions() {
  const { switchTenant, updateTenant, refreshTenant } = useTenantContext();
  return { switchTenant, updateTenant, refreshTenant };
}

export function useTenantLoading(): boolean {
  const { isLoading } = useTenantContext();
  return isLoading;
}

export function useTenantError(): string | null {
  const { error } = useTenantContext();
  return error;
}

// Feature flag hooks
export function useTenantFeature(feature: keyof TenantData["settings"]["features"]): boolean {
  const settings = useTenantSettings();
  return settings?.features[feature] || false;
}

export function useHasAnalytics(): boolean {
  return useTenantFeature("analytics");
}

export function useHasAPI(): boolean {
  return useTenantFeature("api");
}

export function useHasCustomDomain(): boolean {
  return useTenantFeature("customDomain");
}

export function useHasSSO(): boolean {
  return useTenantFeature("sso");
}

// Limit hooks
export function useTenantLimits() {
  const settings = useTenantSettings();
  return settings?.limits || null;
}

export function useUserLimit(): number {
  const limits = useTenantLimits();
  return limits?.users || 0;
}

export function useStorageLimit(): number {
  const limits = useTenantLimits();
  return limits?.storage || 0;
}

export function useAPICallLimit(): number {
  const limits = useTenantLimits();
  return limits?.apiCalls || 0;
}
