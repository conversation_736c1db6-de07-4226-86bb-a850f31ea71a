import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { restApiClient } from "../rest";
import { endpoints } from "../config";
import { TeamStore, Team, CreateTeamInput, UpdateTeamInput, PaginationParams, SearchParams } from "../types";

interface TeamState extends TeamStore {
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  addTeam: (team: Team) => void;
  updateTeamInList: (id: string, updates: Partial<Team>) => void;
  removeTeam: (id: string) => void;
}

export const useTeamStore = create<TeamState>()(
  immer((set, get) => ({
    teams: [],
    currentTeam: null,
    isLoading: false,
    error: null,

    setLoading: (loading) =>
      set((state) => {
        state.isLoading = loading;
      }),

    setError: (error) =>
      set((state) => {
        state.error = error;
      }),

    addTeam: (team) =>
      set((state) => {
        state.teams.push(team);
      }),

    updateTeamInList: (id, updates) =>
      set((state) => {
        const index = state.teams.findIndex(t => t.id === id);
        if (index !== -1) {
          state.teams[index] = { ...state.teams[index], ...updates };
        }
        if (state.currentTeam?.id === id) {
          state.currentTeam = { ...state.currentTeam, ...updates };
        }
      }),

    removeTeam: (id) =>
      set((state) => {
        state.teams = state.teams.filter(t => t.id !== id);
        if (state.currentTeam?.id === id) {
          state.currentTeam = null;
        }
      }),

    fetchTeams: async (workspaceId?: string, pagination?: PaginationParams, search?: SearchParams) => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (workspaceId) params.append("workspaceId", workspaceId);
        if (pagination?.page) params.append("page", pagination.page.toString());
        if (pagination?.limit) params.append("limit", pagination.limit.toString());
        if (pagination?.sort) params.append("sort", pagination.sort);
        if (pagination?.order) params.append("order", pagination.order);
        if (search?.query) params.append("query", search.query);
        if (search?.filters) {
          Object.entries(search.filters).forEach(([key, value]) => {
            params.append(`filters[${key}]`, value.toString());
          });
        }

        const response = await restApiClient.get(
          `${endpoints.teams.list}?${params.toString()}`
        );

        set((state) => {
          state.teams = response.data.items || response.data;
        });
      } catch (error: any) {
        setError(error.message || "Failed to fetch teams");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    createTeam: async (input: CreateTeamInput) => {
      const { setLoading, setError, addTeam } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.post(endpoints.teams.create, input);
        const team = response.data;

        addTeam(team);
        return team;
      } catch (error: any) {
        setError(error.message || "Failed to create team");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    updateTeam: async (id: string, input: UpdateTeamInput) => {
      const { setLoading, setError, updateTeamInList } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.put(endpoints.teams.update(id), input);
        const team = response.data;

        updateTeamInList(id, team);
        return team;
      } catch (error: any) {
        setError(error.message || "Failed to update team");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    deleteTeam: async (id: string) => {
      const { setLoading, setError, removeTeam } = get();
      
      try {
        setLoading(true);
        setError(null);

        await restApiClient.delete(endpoints.teams.delete(id));
        removeTeam(id);
      } catch (error: any) {
        setError(error.message || "Failed to delete team");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    setCurrentTeam: (team) =>
      set((state) => {
        state.currentTeam = team;
        
        // Update API client context
        if (team) {
          restApiClient.setContext({
            workspaceId: team.workspaceId,
            teamId: team.id,
          });
        }
      }),
  }))
);

// Team utilities
export const teamUtils = {
  // Get team by ID
  getTeamById: (id: string): Team | undefined => {
    const { teams } = useTeamStore.getState();
    return teams.find(t => t.id === id);
  },

  // Get current team
  getCurrentTeam: (): Team | null => {
    return useTeamStore.getState().currentTeam;
  },

  // Get teams by workspace
  getTeamsByWorkspace: (workspaceId: string): Team[] => {
    const { teams } = useTeamStore.getState();
    return teams.filter(t => t.workspaceId === workspaceId);
  },

  // Check if user is team lead
  isTeamLead: (teamId: string, userId: string): boolean => {
    const team = teamUtils.getTeamById(teamId);
    return team?.leadId === userId;
  },

  // Get team stats
  getTeamStats: (team: Team) => ({
    members: team.memberCount,
    projects: team.projectCount,
    isActive: team.isActive,
  }),

  // Format team status
  getTeamStatus: (team: Team): "active" | "inactive" => {
    return team.isActive ? "active" : "inactive";
  },

  // Get team activity level based on project count and member count
  getActivityLevel: (team: Team): "high" | "medium" | "low" => {
    const score = team.memberCount * 2 + team.projectCount;
    if (score >= 15) return "high";
    if (score >= 8) return "medium";
    return "low";
  },
};
