# Database Optimization Implementation

## Feature Overview

Implement comprehensive database optimization strategies for the NEXUS SaaS Starter, including advanced connection pooling, query optimization, indexing strategies, and performance monitoring to achieve sub-200ms query response times while supporting 1000+ concurrent tenants.

## Context & Research

### Current Implementation Analysis
Based on codebase analysis, existing database patterns include:
- **Prisma ORM**: Already configured with multi-tenant architecture
- **Connection Pooling**: Basic setup with default pool sizes
- **Multi-Tenant Isolation**: Row-level security with tenant_id columns
- **Query Patterns**: Standard CRUD operations with some caching

### Technology Stack Verification (Context7)

**Prisma Connection Pooling Patterns**:
- Default pool size: `num_physical_cpus * 2 + 1`
- Connection limit configuration: `connection_limit=40` for high concurrency
- Pool timeout optimization: `pool_timeout=20` for queue management
- Metrics monitoring: Built-in connection pool metrics

**Query Optimization Strategies**:
- N+1 problem solutions: `include`, `relationLoadStrategy: "join"`, batch queries
- Prepared statement caching for repeated queries
- Index optimization for multi-tenant queries
- Query performance monitoring with P50, P99 metrics

**Performance Monitoring**:
- Connection pool metrics: open, busy, idle connections
- Query duration histograms and percentiles
- Wait time tracking for connection acquisition
- Database-specific optimization patterns

## Implementation Blueprint

### Data Models and Structure

```typescript
// Enhanced database configuration
interface DatabaseConfig {
  connection: {
    poolSize: number;
    poolTimeout: number;
    connectionTimeout: number;
    idleTimeout: number;
  };
  optimization: {
    queryTimeout: number;
    preparedStatements: boolean;
    connectionRetries: number;
    batchSize: number;
  };
  monitoring: {
    slowQueryThreshold: number;
    metricsEnabled: boolean;
    logLevel: 'info' | 'warn' | 'error';
  };
  multiTenant: {
    isolationLevel: 'strict' | 'shared';
    indexStrategy: 'tenant_first' | 'composite';
    partitioning: boolean;
  };
}

interface QueryOptimizationMetrics {
  averageLatency: number;
  p50: number;
  p99: number;
  connectionPoolUtilization: number;
  slowQueries: SlowQuery[];
}
```

### Task Breakdown

**Phase 1: Connection Pool Optimization (2-3 hours)**

1. **Enhanced Prisma Configuration**
   - File: `lib/database/prisma-optimized.ts`
   - Implement dynamic connection pool sizing based on environment
   - Configure connection timeouts and retry logic
   - Add connection pool monitoring and metrics
   - Pattern: Extend existing Prisma setup with optimization

2. **Multi-Tenant Connection Strategy**
   - File: `lib/database/tenant-pool.ts`
   - Implement tenant-aware connection pooling
   - Add connection isolation for enterprise tenants
   - Configure read replica routing for analytics
   - Pattern: Build on existing tenant context system

**Phase 2: Query Optimization (3-4 hours)**

3. **N+1 Query Prevention**
   - File: `lib/database/query-optimizer.ts`
   - Implement automatic query batching
   - Add relation loading strategy optimization
   - Create query analysis and warning system
   - Pattern: Enhance existing database query patterns

4. **Index Strategy Implementation**
   - File: `lib/database/indexing.ts`
   - Create multi-tenant optimized indexes
   - Implement composite indexes for common queries
   - Add index usage monitoring
   - Pattern: Extend existing database schema

**Phase 3: Performance Monitoring (2-3 hours)**

5. **Database Metrics Collection**
   - File: `lib/database/metrics.ts`
   - Implement comprehensive query performance tracking
   - Add connection pool utilization monitoring
   - Create slow query detection and alerting
   - Pattern: Integrate with existing monitoring infrastructure

6. **Query Performance Dashboard**
   - File: `app/api/admin/database/metrics/route.ts`
   - Create real-time database performance API
   - Add query optimization recommendations
   - Implement performance trend analysis
   - Pattern: Follow existing admin API patterns

### Integration Points

**Caching Integration**:
- Integrate with Redis caching layer for query result caching
- Implement cache-aside pattern for frequently accessed data
- Add cache invalidation on data mutations
- Maintain cache consistency across tenant boundaries

**Authentication Integration**:
- Optimize user session queries with proper indexing
- Implement connection pooling for auth-heavy operations
- Add tenant context optimization for auth queries
- Cache authentication results appropriately

**API Integration**:
- Add database performance middleware for API routes
- Implement query timeout handling
- Add connection pool health checks
- Monitor API-to-database query patterns

## Validation Gates

### Level 1: Syntax & Style
```bash
npm run lint                    # ESLint checks
npx tsc --noEmit               # TypeScript validation
npm run test:unit              # Unit tests for database layer
```

### Level 2: Performance Tests
```bash
npm run dev                    # Start development server

# Test connection pool performance
node scripts/test-connection-pool.js

# Test query optimization
npm run test:db:performance

# Monitor connection metrics
curl http://localhost:3000/api/admin/database/metrics
```

### Level 3: Load Testing
```bash
# Database load testing
npx autocannon -c 50 -d 60 \
  -H "x-tenant-id=test-tenant" \
  -H "Authorization=Bearer $TOKEN" \
  http://localhost:3000/api/analytics/dashboard

# Connection pool stress test
npm run test:db:stress

# Multi-tenant isolation test
npm run test:db:isolation
```

### Level 4: Production Validation
```bash
# Query performance analysis
npm run db:analyze-queries

# Index usage verification
npm run db:check-indexes

# Connection pool optimization
npm run db:optimize-pool
```

## Quality Standards Checklist

- [ ] Connection pool utilization <80% under normal load
- [ ] Query response times: P95 <200ms, P99 <500ms
- [ ] Zero N+1 queries in critical paths
- [ ] Proper indexing for all tenant-aware queries
- [ ] Connection pool metrics and monitoring
- [ ] Slow query detection and alerting
- [ ] Multi-tenant data isolation verification
- [ ] Read replica routing for analytics queries
- [ ] Prepared statement caching enabled
- [ ] Database connection health checks

## Security Considerations

- **Connection Security**: Encrypted connections with proper SSL/TLS
- **Tenant Isolation**: Strict row-level security enforcement
- **Query Injection**: Parameterized queries and input validation
- **Access Control**: Database user permissions and role separation
- **Audit Logging**: Database access and modification tracking

## Performance Targets

- **Query Response Time**: P95 <200ms, P99 <500ms
- **Connection Pool**: <80% utilization under normal load
- **Throughput**: Support 1000+ concurrent database connections
- **Tenant Isolation**: Zero data leakage between tenants
- **Index Efficiency**: >95% index usage for filtered queries

---

**Implementation Priority**: HIGH - Critical for production scalability
**Estimated Effort**: 8-12 hours
**Dependencies**: Prisma ORM, PostgreSQL, monitoring infrastructure
**Success Metrics**: Query performance, connection efficiency, tenant isolation

## Detailed Implementation

### 1. Enhanced Prisma Configuration

```typescript
// lib/database/prisma-optimized.ts
import { PrismaClient } from '@prisma/client';
import { logger } from '@/lib/logger';

interface DatabaseMetrics {
  connectionPool: {
    open: number;
    busy: number;
    idle: number;
    total: number;
  };
  queries: {
    total: number;
    active: number;
    waiting: number;
    averageLatency: number;
  };
}

class OptimizedPrismaClient extends PrismaClient {
  private metrics: DatabaseMetrics;
  private slowQueryThreshold = 1000; // 1 second

  constructor() {
    const connectionLimit = OptimizedPrismaClient.calculateOptimalPoolSize();
    const poolTimeout = process.env.NODE_ENV === 'production' ? 20 : 10;

    super({
      datasources: {
        db: {
          url: `${process.env.DATABASE_URL}?connection_limit=${connectionLimit}&pool_timeout=${poolTimeout}&connect_timeout=15`
        }
      },
      log: [
        { level: 'query', emit: 'event' },
        { level: 'info', emit: 'event' },
        { level: 'warn', emit: 'event' },
        { level: 'error', emit: 'event' },
      ],
    });

    this.metrics = {
      connectionPool: { open: 0, busy: 0, idle: 0, total: 0 },
      queries: { total: 0, active: 0, waiting: 0, averageLatency: 0 }
    };

    this.setupEventHandlers();
    this.setupMetricsCollection();
  }

  private static calculateOptimalPoolSize(): number {
    const cpuCount = require('os').cpus().length;
    const baseSize = cpuCount * 2 + 1;

    // Adjust based on environment
    if (process.env.NODE_ENV === 'production') {
      return Math.min(baseSize * 2, 50); // Cap at 50 for production
    }

    return Math.min(baseSize, 20); // Cap at 20 for development
  }

  private setupEventHandlers(): void {
    this.$on('query', (e) => {
      this.metrics.queries.total++;

      if (e.duration > this.slowQueryThreshold) {
        logger.warn('Slow query detected', {
          query: e.query,
          duration: e.duration,
          params: e.params
        });
      }
    });

    this.$on('info', (e) => {
      logger.info('Prisma info', { message: e.message });
    });

    this.$on('warn', (e) => {
      logger.warn('Prisma warning', { message: e.message });
    });

    this.$on('error', (e) => {
      logger.error('Prisma error', { message: e.message });
    });
  }

  private async setupMetricsCollection(): Promise<void> {
    // Collect metrics every 30 seconds
    setInterval(async () => {
      try {
        const metrics = await this.$metrics.json();
        this.updateMetrics(metrics);
      } catch (error) {
        logger.error('Failed to collect Prisma metrics', { error });
      }
    }, 30000);
  }

  private updateMetrics(prismaMetrics: any): void {
    const poolMetrics = prismaMetrics.gauges.find((g: any) =>
      g.key === 'prisma_pool_connections_open'
    );

    if (poolMetrics) {
      this.metrics.connectionPool.open = poolMetrics.value;
    }

    const busyMetrics = prismaMetrics.gauges.find((g: any) =>
      g.key === 'prisma_pool_connections_busy'
    );

    if (busyMetrics) {
      this.metrics.connectionPool.busy = busyMetrics.value;
    }

    const idleMetrics = prismaMetrics.gauges.find((g: any) =>
      g.key === 'prisma_pool_connections_idle'
    );

    if (idleMetrics) {
      this.metrics.connectionPool.idle = idleMetrics.value;
    }
  }

  async getMetrics(): Promise<DatabaseMetrics> {
    return this.metrics;
  }

  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details: any;
  }> {
    try {
      const start = Date.now();
      await this.$queryRaw`SELECT 1`;
      const latency = Date.now() - start;

      const poolUtilization = this.metrics.connectionPool.busy /
        (this.metrics.connectionPool.open || 1);

      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

      if (latency > 500 || poolUtilization > 0.8) {
        status = 'degraded';
      }

      if (latency > 2000 || poolUtilization > 0.95) {
        status = 'unhealthy';
      }

      return {
        status,
        details: {
          latency,
          poolUtilization,
          connectionPool: this.metrics.connectionPool,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: { error: error.message }
      };
    }
  }
}

// Singleton instance
let prisma: OptimizedPrismaClient;

if (process.env.NODE_ENV === 'production') {
  prisma = new OptimizedPrismaClient();
} else {
  // In development, use global to prevent multiple instances
  if (!global.__prisma) {
    global.__prisma = new OptimizedPrismaClient();
  }
  prisma = global.__prisma;
}

export { prisma };
export type { DatabaseMetrics };
```

### 2. Multi-Tenant Connection Strategy

```typescript
// lib/database/tenant-pool.ts
import { prisma } from './prisma-optimized';
import { getTenantContext } from '@/lib/auth/tenant-context';
import { logger } from '@/lib/logger';

interface TenantConnectionConfig {
  tenantId: string;
  tier: 'free' | 'pro' | 'enterprise';
  maxConnections: number;
  priority: number;
  readReplica?: boolean;
}

class TenantConnectionManager {
  private tenantConfigs = new Map<string, TenantConnectionConfig>();
  private connectionUsage = new Map<string, number>();

  constructor() {
    this.setupTenantConfigs();
  }

  private setupTenantConfigs(): void {
    // Default configurations by tier
    const defaultConfigs = {
      free: { maxConnections: 5, priority: 1, readReplica: false },
      pro: { maxConnections: 15, priority: 2, readReplica: true },
      enterprise: { maxConnections: 30, priority: 3, readReplica: true }
    };

    // This would typically be loaded from database or config
    // For now, using defaults
  }

  async getTenantConfig(tenantId: string): Promise<TenantConnectionConfig> {
    let config = this.tenantConfigs.get(tenantId);

    if (!config) {
      // Fetch tenant configuration from database
      const tenant = await prisma.tenant.findUnique({
        where: { id: tenantId },
        select: { tier: true }
      });

      const tier = tenant?.tier || 'free';
      config = {
        tenantId,
        tier: tier as 'free' | 'pro' | 'enterprise',
        maxConnections: this.getMaxConnectionsByTier(tier),
        priority: this.getPriorityByTier(tier),
        readReplica: tier !== 'free'
      };

      this.tenantConfigs.set(tenantId, config);
    }

    return config;
  }

  private getMaxConnectionsByTier(tier: string): number {
    switch (tier) {
      case 'enterprise': return 30;
      case 'pro': return 15;
      default: return 5;
    }
  }

  private getPriorityByTier(tier: string): number {
    switch (tier) {
      case 'enterprise': return 3;
      case 'pro': return 2;
      default: return 1;
    }
  }

  async executeWithTenantContext<T>(
    operation: () => Promise<T>,
    options: { readOnly?: boolean } = {}
  ): Promise<T> {
    const tenantId = await getTenantContext();
    if (!tenantId) {
      throw new Error('Tenant context required for database operations');
    }

    const config = await this.getTenantConfig(tenantId);
    const currentUsage = this.connectionUsage.get(tenantId) || 0;

    // Check connection limits
    if (currentUsage >= config.maxConnections) {
      logger.warn('Tenant connection limit reached', {
        tenantId,
        currentUsage,
        maxConnections: config.maxConnections
      });

      // For enterprise tenants, allow slight overrun
      if (config.tier !== 'enterprise' || currentUsage >= config.maxConnections * 1.2) {
        throw new Error('Tenant connection limit exceeded');
      }
    }

    // Track connection usage
    this.connectionUsage.set(tenantId, currentUsage + 1);

    try {
      // For read-only operations on pro/enterprise, consider read replica
      if (options.readOnly && config.readReplica && Math.random() > 0.3) {
        // 70% of read operations go to read replica
        return await this.executeOnReadReplica(operation);
      }

      return await operation();
    } finally {
      // Decrement connection usage
      const newUsage = Math.max(0, (this.connectionUsage.get(tenantId) || 1) - 1);
      this.connectionUsage.set(tenantId, newUsage);
    }
  }

  private async executeOnReadReplica<T>(operation: () => Promise<T>): Promise<T> {
    // This would use a read replica connection
    // For now, just use the main connection with a comment
    logger.debug('Executing on read replica');
    return await operation();
  }

  getConnectionUsage(): Map<string, number> {
    return new Map(this.connectionUsage);
  }

  getTenantConfigs(): Map<string, TenantConnectionConfig> {
    return new Map(this.tenantConfigs);
  }
}

export const tenantConnectionManager = new TenantConnectionManager();
```

### 3. N+1 Query Prevention & Optimization

```typescript
// lib/database/query-optimizer.ts
import { prisma } from './prisma-optimized';
import { logger } from '@/lib/logger';

interface QueryAnalysis {
  queryType: 'select' | 'insert' | 'update' | 'delete';
  hasN1Problem: boolean;
  suggestedOptimization?: string;
  executionTime: number;
  affectedRows?: number;
}

class QueryOptimizer {
  private queryCache = new Map<string, any>();
  private queryAnalytics = new Map<string, QueryAnalysis[]>();

  // Optimized user queries with relations
  async getUsersWithPosts(tenantId: string, options: {
    limit?: number;
    includeInactive?: boolean;
  } = {}): Promise<any[]> {
    const cacheKey = `users_posts_${tenantId}_${JSON.stringify(options)}`;

    if (this.queryCache.has(cacheKey)) {
      return this.queryCache.get(cacheKey);
    }

    const start = Date.now();

    // Use relationLoadStrategy: "join" to prevent N+1
    const users = await prisma.user.findMany({
      where: {
        tenantId,
        ...(options.includeInactive ? {} : { status: 'active' })
      },
      include: {
        posts: {
          where: { published: true },
          orderBy: { createdAt: 'desc' },
          take: 5 // Limit posts per user
        },
        profile: true,
        workspaces: {
          include: {
            workspace: {
              select: { id: true, name: true, slug: true }
            }
          }
        }
      },
      relationLoadStrategy: "join", // Prevents N+1 queries
      take: options.limit || 50,
      orderBy: { createdAt: 'desc' }
    });

    const executionTime = Date.now() - start;

    // Cache for 5 minutes
    this.queryCache.set(cacheKey, users);
    setTimeout(() => this.queryCache.delete(cacheKey), 5 * 60 * 1000);

    this.recordQueryAnalysis('getUsersWithPosts', {
      queryType: 'select',
      hasN1Problem: false,
      executionTime,
      affectedRows: users.length
    });

    return users;
  }

  // Batch query optimization for analytics
  async getAnalyticsData(tenantId: string, userIds: string[]): Promise<any> {
    const start = Date.now();

    // Use Promise.all for parallel queries instead of sequential
    const [userStats, postStats, engagementStats] = await Promise.all([
      // Batch user statistics
      prisma.user.groupBy({
        by: ['status'],
        where: {
          tenantId,
          id: { in: userIds }
        },
        _count: { id: true }
      }),

      // Batch post statistics
      prisma.post.groupBy({
        by: ['status'],
        where: {
          tenantId,
          authorId: { in: userIds }
        },
        _count: { id: true },
        _sum: { viewCount: true }
      }),

      // Batch engagement statistics
      prisma.engagement.groupBy({
        by: ['type'],
        where: {
          tenantId,
          userId: { in: userIds }
        },
        _count: { id: true }
      })
    ]);

    const executionTime = Date.now() - start;

    this.recordQueryAnalysis('getAnalyticsData', {
      queryType: 'select',
      hasN1Problem: false,
      suggestedOptimization: 'Using Promise.all for parallel execution',
      executionTime
    });

    return {
      users: userStats,
      posts: postStats,
      engagement: engagementStats,
      executionTime
    };
  }

  // Optimized search with proper indexing
  async searchContent(tenantId: string, query: string, options: {
    type?: 'posts' | 'users' | 'all';
    limit?: number;
  } = {}): Promise<any> {
    const start = Date.now();
    const limit = options.limit || 20;

    let results: any = {};

    if (options.type === 'posts' || options.type === 'all') {
      // Use full-text search with proper indexing
      results.posts = await prisma.post.findMany({
        where: {
          tenantId,
          OR: [
            { title: { contains: query, mode: 'insensitive' } },
            { content: { contains: query, mode: 'insensitive' } },
            { tags: { hasSome: [query] } }
          ],
          published: true
        },
        include: {
          author: {
            select: { id: true, name: true, email: true }
          }
        },
        orderBy: [
          { updatedAt: 'desc' },
          { viewCount: 'desc' }
        ],
        take: limit
      });
    }

    if (options.type === 'users' || options.type === 'all') {
      results.users = await prisma.user.findMany({
        where: {
          tenantId,
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { email: { contains: query, mode: 'insensitive' } }
          ],
          status: 'active'
        },
        select: {
          id: true,
          name: true,
          email: true,
          avatarUrl: true,
          createdAt: true
        },
        take: limit
      });
    }

    const executionTime = Date.now() - start;

    this.recordQueryAnalysis('searchContent', {
      queryType: 'select',
      hasN1Problem: false,
      suggestedOptimization: 'Using indexed columns and proper OR conditions',
      executionTime
    });

    return results;
  }

  // Batch operations for bulk updates
  async bulkUpdateUserStatus(
    tenantId: string,
    userIds: string[],
    status: string
  ): Promise<number> {
    const start = Date.now();

    // Use updateMany for bulk operations instead of individual updates
    const result = await prisma.user.updateMany({
      where: {
        tenantId,
        id: { in: userIds }
      },
      data: {
        status,
        updatedAt: new Date()
      }
    });

    const executionTime = Date.now() - start;

    this.recordQueryAnalysis('bulkUpdateUserStatus', {
      queryType: 'update',
      hasN1Problem: false,
      suggestedOptimization: 'Using updateMany instead of individual updates',
      executionTime,
      affectedRows: result.count
    });

    return result.count;
  }

  private recordQueryAnalysis(queryName: string, analysis: QueryAnalysis): void {
    if (!this.queryAnalytics.has(queryName)) {
      this.queryAnalytics.set(queryName, []);
    }

    const analyses = this.queryAnalytics.get(queryName)!;
    analyses.push(analysis);

    // Keep only last 100 analyses per query
    if (analyses.length > 100) {
      analyses.shift();
    }

    // Log slow queries
    if (analysis.executionTime > 1000) {
      logger.warn('Slow query detected', {
        queryName,
        executionTime: analysis.executionTime,
        analysis
      });
    }
  }

  getQueryAnalytics(): Map<string, QueryAnalysis[]> {
    return new Map(this.queryAnalytics);
  }

  clearCache(): void {
    this.queryCache.clear();
  }
}

export const queryOptimizer = new QueryOptimizer();
```

### 4. Database Indexing Strategy

```typescript
// lib/database/indexing.ts
import { prisma } from './prisma-optimized';
import { logger } from '@/lib/logger';

interface IndexAnalysis {
  tableName: string;
  indexName: string;
  columns: string[];
  usage: number;
  size: string;
  effectiveness: 'high' | 'medium' | 'low';
}

class DatabaseIndexManager {
  // Critical indexes for multi-tenant architecture
  private readonly CRITICAL_INDEXES = [
    // Tenant isolation indexes (highest priority)
    { table: 'users', columns: ['tenant_id', 'status'], name: 'idx_users_tenant_status' },
    { table: 'posts', columns: ['tenant_id', 'published'], name: 'idx_posts_tenant_published' },
    { table: 'workspaces', columns: ['tenant_id', 'status'], name: 'idx_workspaces_tenant_status' },

    // Performance indexes
    { table: 'users', columns: ['tenant_id', 'email'], name: 'idx_users_tenant_email' },
    { table: 'users', columns: ['tenant_id', 'created_at'], name: 'idx_users_tenant_created' },
    { table: 'posts', columns: ['tenant_id', 'author_id', 'created_at'], name: 'idx_posts_tenant_author_created' },
    { table: 'posts', columns: ['tenant_id', 'updated_at'], name: 'idx_posts_tenant_updated' },

    // Search indexes
    { table: 'posts', columns: ['tenant_id', 'title'], name: 'idx_posts_tenant_title' },
    { table: 'users', columns: ['tenant_id', 'name'], name: 'idx_users_tenant_name' },

    // Analytics indexes
    { table: 'analytics_events', columns: ['tenant_id', 'event_type', 'created_at'], name: 'idx_analytics_tenant_type_created' },
    { table: 'subscriptions', columns: ['tenant_id', 'status', 'current_period_end'], name: 'idx_subscriptions_tenant_status_period' }
  ];

  async analyzeIndexUsage(): Promise<IndexAnalysis[]> {
    try {
      // PostgreSQL specific query to analyze index usage
      const indexStats = await prisma.$queryRaw<any[]>`
        SELECT
          schemaname,
          tablename,
          indexname,
          idx_tup_read,
          idx_tup_fetch,
          pg_size_pretty(pg_relation_size(indexrelid)) as size
        FROM pg_stat_user_indexes
        JOIN pg_index ON pg_index.indexrelid = pg_stat_user_indexes.indexrelid
        WHERE schemaname = 'public'
        ORDER BY idx_tup_read DESC;
      `;

      return indexStats.map(stat => ({
        tableName: stat.tablename,
        indexName: stat.indexname,
        columns: [], // Would need additional query to get columns
        usage: stat.idx_tup_read || 0,
        size: stat.size,
        effectiveness: this.calculateEffectiveness(stat.idx_tup_read, stat.idx_tup_fetch)
      }));
    } catch (error) {
      logger.error('Failed to analyze index usage', { error });
      return [];
    }
  }

  private calculateEffectiveness(reads: number, fetches: number): 'high' | 'medium' | 'low' {
    if (reads === 0) return 'low';

    const ratio = fetches / reads;
    if (ratio > 0.8) return 'high';
    if (ratio > 0.4) return 'medium';
    return 'low';
  }

  async createMissingIndexes(): Promise<void> {
    for (const index of this.CRITICAL_INDEXES) {
      try {
        const indexExists = await this.checkIndexExists(index.table, index.name);

        if (!indexExists) {
          await this.createIndex(index.table, index.columns, index.name);
          logger.info('Created missing index', {
            table: index.table,
            name: index.name,
            columns: index.columns
          });
        }
      } catch (error) {
        logger.error('Failed to create index', {
          index: index.name,
          error: error.message
        });
      }
    }
  }

  private async checkIndexExists(tableName: string, indexName: string): Promise<boolean> {
    try {
      const result = await prisma.$queryRaw<any[]>`
        SELECT 1 FROM pg_indexes
        WHERE tablename = ${tableName}
        AND indexname = ${indexName}
        LIMIT 1;
      `;

      return result.length > 0;
    } catch (error) {
      return false;
    }
  }

  private async createIndex(tableName: string, columns: string[], indexName: string): Promise<void> {
    const columnList = columns.join(', ');

    // Use raw SQL for index creation
    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY ${indexName}
      ON ${tableName} (${columnList});
    `;
  }

  async getSlowQueries(limit: number = 10): Promise<any[]> {
    try {
      // PostgreSQL specific query for slow queries
      const slowQueries = await prisma.$queryRaw<any[]>`
        SELECT
          query,
          calls,
          total_time,
          mean_time,
          rows,
          100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
        FROM pg_stat_statements
        WHERE query NOT LIKE '%pg_stat_statements%'
        ORDER BY mean_time DESC
        LIMIT ${limit};
      `;

      return slowQueries;
    } catch (error) {
      logger.error('Failed to get slow queries', { error });
      return [];
    }
  }

  async optimizeTable(tableName: string): Promise<void> {
    try {
      // Analyze table statistics
      await prisma.$executeRaw`ANALYZE ${tableName};`;

      // Vacuum if needed (in production, this should be scheduled)
      if (process.env.NODE_ENV !== 'production') {
        await prisma.$executeRaw`VACUUM ANALYZE ${tableName};`;
      }

      logger.info('Table optimized', { tableName });
    } catch (error) {
      logger.error('Failed to optimize table', { tableName, error });
    }
  }
}

export const indexManager = new DatabaseIndexManager();
```

*Built with ❤️ by Nexus-Master Agent*
*Where 125 Senior Developers Meet AI Excellence*
