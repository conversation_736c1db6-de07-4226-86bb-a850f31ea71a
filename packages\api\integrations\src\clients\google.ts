import { google } from "googleapis";
import { APIClient, IntegrationCredentials } from "../types";

export class GoogleAPIClient implements APIClient {
  private auth: any;
  private credentials?: IntegrationCredentials;

  constructor() {
    this.auth = new google.auth.OAuth2();
  }

  setAuth(credentials: IntegrationCredentials): void {
    this.credentials = credentials;
    this.auth.setCredentials({
      access_token: credentials.accessToken,
      refresh_token: credentials.refreshToken,
    });
  }

  async get(url: string, params?: Record<string, any>): Promise<any> {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${this.credentials?.accessToken}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`GET request failed: ${response.statusText}`);
    }

    return response.json();
  }

  async post(url: string, data?: any): Promise<any> {
    const response = await fetch(url, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${this.credentials?.accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`POST request failed: ${response.statusText}`);
    }

    return response.json();
  }

  async put(url: string, data?: any): Promise<any> {
    const response = await fetch(url, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${this.credentials?.accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`PUT request failed: ${response.statusText}`);
    }

    return response.json();
  }

  async patch(url: string, data?: any): Promise<any> {
    const response = await fetch(url, {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${this.credentials?.accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`PATCH request failed: ${response.statusText}`);
    }

    return response.json();
  }

  async delete(url: string): Promise<any> {
    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${this.credentials?.accessToken}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`DELETE request failed: ${response.statusText}`);
    }

    return response.json();
  }

  async refreshToken(): Promise<any> {
    if (!this.credentials?.refreshToken) {
      throw new Error("No refresh token available");
    }

    const { credentials } = await this.auth.refreshAccessToken();
    
    return {
      accessToken: credentials.access_token,
      refreshToken: credentials.refresh_token,
      tokenType: "Bearer",
      expiresIn: credentials.expiry_date ? 
        Math.floor((credentials.expiry_date - Date.now()) / 1000) : undefined,
    };
  }

  // Google Drive methods
  async listDriveFiles(params?: {
    q?: string;
    pageSize?: number;
    pageToken?: string;
    orderBy?: string;
  }): Promise<any> {
    const drive = google.drive({ version: "v3", auth: this.auth });
    
    const response = await drive.files.list({
      q: params?.q,
      pageSize: params?.pageSize || 100,
      pageToken: params?.pageToken,
      orderBy: params?.orderBy,
      fields: "nextPageToken, files(id, name, mimeType, size, createdTime, modifiedTime, parents)",
    });

    return response.data;
  }

  async getDriveFile(fileId: string): Promise<any> {
    const drive = google.drive({ version: "v3", auth: this.auth });
    
    const response = await drive.files.get({
      fileId,
      fields: "id, name, mimeType, size, createdTime, modifiedTime, parents, webViewLink, webContentLink",
    });

    return response.data;
  }

  async downloadDriveFile(fileId: string): Promise<Buffer> {
    const drive = google.drive({ version: "v3", auth: this.auth });
    
    const response = await drive.files.get({
      fileId,
      alt: "media",
    }, { responseType: "stream" });

    const chunks: Buffer[] = [];
    
    return new Promise((resolve, reject) => {
      response.data.on("data", (chunk: Buffer) => chunks.push(chunk));
      response.data.on("end", () => resolve(Buffer.concat(chunks)));
      response.data.on("error", reject);
    });
  }

  async uploadDriveFile(params: {
    name: string;
    parents?: string[];
    mimeType?: string;
    content: Buffer | string;
  }): Promise<any> {
    const drive = google.drive({ version: "v3", auth: this.auth });
    
    const response = await drive.files.create({
      requestBody: {
        name: params.name,
        parents: params.parents,
        mimeType: params.mimeType,
      },
      media: {
        mimeType: params.mimeType || "application/octet-stream",
        body: params.content,
      },
      fields: "id, name, mimeType, size, createdTime, webViewLink",
    });

    return response.data;
  }

  // Google Calendar methods
  async listCalendarEvents(params?: {
    calendarId?: string;
    timeMin?: string;
    timeMax?: string;
    maxResults?: number;
    pageToken?: string;
  }): Promise<any> {
    const calendar = google.calendar({ version: "v3", auth: this.auth });
    
    const response = await calendar.events.list({
      calendarId: params?.calendarId || "primary",
      timeMin: params?.timeMin,
      timeMax: params?.timeMax,
      maxResults: params?.maxResults || 100,
      pageToken: params?.pageToken,
      singleEvents: true,
      orderBy: "startTime",
    });

    return response.data;
  }

  async createCalendarEvent(params: {
    calendarId?: string;
    summary: string;
    description?: string;
    start: { dateTime: string; timeZone?: string };
    end: { dateTime: string; timeZone?: string };
    attendees?: Array<{ email: string; displayName?: string }>;
  }): Promise<any> {
    const calendar = google.calendar({ version: "v3", auth: this.auth });
    
    const response = await calendar.events.insert({
      calendarId: params.calendarId || "primary",
      requestBody: {
        summary: params.summary,
        description: params.description,
        start: params.start,
        end: params.end,
        attendees: params.attendees,
      },
    });

    return response.data;
  }

  // Google Sheets methods
  async getSheetValues(params: {
    spreadsheetId: string;
    range: string;
    valueRenderOption?: "FORMATTED_VALUE" | "UNFORMATTED_VALUE" | "FORMULA";
  }): Promise<any> {
    const sheets = google.sheets({ version: "v4", auth: this.auth });
    
    const response = await sheets.spreadsheets.values.get({
      spreadsheetId: params.spreadsheetId,
      range: params.range,
      valueRenderOption: params.valueRenderOption || "FORMATTED_VALUE",
    });

    return response.data;
  }

  async updateSheetValues(params: {
    spreadsheetId: string;
    range: string;
    values: any[][];
    valueInputOption?: "RAW" | "USER_ENTERED";
  }): Promise<any> {
    const sheets = google.sheets({ version: "v4", auth: this.auth });
    
    const response = await sheets.spreadsheets.values.update({
      spreadsheetId: params.spreadsheetId,
      range: params.range,
      valueInputOption: params.valueInputOption || "USER_ENTERED",
      requestBody: {
        values: params.values,
      },
    });

    return response.data;
  }

  async appendSheetValues(params: {
    spreadsheetId: string;
    range: string;
    values: any[][];
    valueInputOption?: "RAW" | "USER_ENTERED";
  }): Promise<any> {
    const sheets = google.sheets({ version: "v4", auth: this.auth });
    
    const response = await sheets.spreadsheets.values.append({
      spreadsheetId: params.spreadsheetId,
      range: params.range,
      valueInputOption: params.valueInputOption || "USER_ENTERED",
      requestBody: {
        values: params.values,
      },
    });

    return response.data;
  }

  // Gmail methods
  async listGmailMessages(params?: {
    q?: string;
    maxResults?: number;
    pageToken?: string;
    labelIds?: string[];
  }): Promise<any> {
    const gmail = google.gmail({ version: "v1", auth: this.auth });
    
    const response = await gmail.users.messages.list({
      userId: "me",
      q: params?.q,
      maxResults: params?.maxResults || 100,
      pageToken: params?.pageToken,
      labelIds: params?.labelIds,
    });

    return response.data;
  }

  async getGmailMessage(messageId: string): Promise<any> {
    const gmail = google.gmail({ version: "v1", auth: this.auth });
    
    const response = await gmail.users.messages.get({
      userId: "me",
      id: messageId,
      format: "full",
    });

    return response.data;
  }

  async sendGmailMessage(params: {
    to: string[];
    cc?: string[];
    bcc?: string[];
    subject: string;
    body: string;
    isHtml?: boolean;
  }): Promise<any> {
    const gmail = google.gmail({ version: "v1", auth: this.auth });
    
    const message = this.createEmailMessage(params);
    
    const response = await gmail.users.messages.send({
      userId: "me",
      requestBody: {
        raw: Buffer.from(message).toString("base64url"),
      },
    });

    return response.data;
  }

  private createEmailMessage(params: {
    to: string[];
    cc?: string[];
    bcc?: string[];
    subject: string;
    body: string;
    isHtml?: boolean;
  }): string {
    const lines = [
      `To: ${params.to.join(", ")}`,
      params.cc ? `Cc: ${params.cc.join(", ")}` : "",
      params.bcc ? `Bcc: ${params.bcc.join(", ")}` : "",
      `Subject: ${params.subject}`,
      `Content-Type: ${params.isHtml ? "text/html" : "text/plain"}; charset=utf-8`,
      "",
      params.body,
    ].filter(Boolean);

    return lines.join("\r\n");
  }
}
