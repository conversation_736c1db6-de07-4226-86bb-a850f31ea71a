import { FastifyInstance } from "fastify";
import { z } from "zod";
import { validate, validatePagination, validateId } from "../middleware";
import { canRead, canCreate, canWrite, canDelete } from "../middleware/rbac";
import { ApiResponse } from "../types";

const createProjectSchema = z.object({
  name: z.string().min(2),
  description: z.string().optional(),
  workspaceId: z.string().uuid(),
  teamId: z.string().uuid().optional(),
  settings: z.object({}).optional(),
});

const updateProjectSchema = z.object({
  name: z.string().min(2).optional(),
  description: z.string().optional(),
  settings: z.object({}).optional(),
  isActive: z.boolean().optional(),
});

export const projectRoutes = async (fastify: FastifyInstance) => {
  // Get projects
  fastify.get("/", {
    schema: {
      tags: ["Projects"],
      summary: "List projects",
      description: "Get a paginated list of projects",
      security: [{ bearerAuth: [] }],
      querystring: {
        type: "object",
        properties: {
          page: { type: "integer", minimum: 1, default: 1 },
          limit: { type: "integer", minimum: 1, maximum: 100, default: 20 },
          workspaceId: { type: "string", format: "uuid" },
          teamId: { type: "string", format: "uuid" },
          search: { type: "string" },
          isActive: { type: "boolean" },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                projects: {
                  type: "array",
                  items: {
                    type: "object",
                    properties: {
                      id: { type: "string", format: "uuid" },
                      name: { type: "string" },
                      description: { type: "string" },
                      workspaceId: { type: "string", format: "uuid" },
                      teamId: { type: "string", format: "uuid" },
                      ownerId: { type: "string", format: "uuid" },
                      settings: { type: "object" },
                      isActive: { type: "boolean" },
                      createdAt: { type: "string", format: "date-time" },
                      updatedAt: { type: "string", format: "date-time" },
                    },
                  },
                },
                pagination: { $ref: "#/components/schemas/Pagination" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
      },
    },
    preHandler: [fastify.authenticate, canRead("project"), validatePagination],
    handler: async (request, reply): Promise<ApiResponse> => {
      const query = request.query as any;
      
      // TODO: Implement actual project fetching
      const mockProjects = [
        {
          id: "project_1",
          name: "Main Project",
          description: "Primary project for the team",
          workspaceId: "workspace_1",
          teamId: "team_1",
          ownerId: "user_123",
          settings: {},
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      return {
        success: true,
        data: {
          projects: mockProjects,
          pagination: {
            page: query.page || 1,
            limit: query.limit || 20,
            total: mockProjects.length,
            totalPages: 1,
          },
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Get project by ID
  fastify.get("/:id", {
    schema: {
      tags: ["Projects"],
      summary: "Get project by ID",
      description: "Get a specific project by its ID",
      security: [{ bearerAuth: [] }],
      params: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
        },
        required: ["id"],
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                project: {
                  type: "object",
                  properties: {
                    id: { type: "string", format: "uuid" },
                    name: { type: "string" },
                    description: { type: "string" },
                    workspaceId: { type: "string", format: "uuid" },
                    teamId: { type: "string", format: "uuid" },
                    ownerId: { type: "string", format: "uuid" },
                    settings: { type: "object" },
                    isActive: { type: "boolean" },
                    createdAt: { type: "string", format: "date-time" },
                    updatedAt: { type: "string", format: "date-time" },
                  },
                },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [fastify.authenticate, canRead("project"), validateId],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { id } = request.params as { id: string };
      
      // TODO: Implement actual project fetching
      const mockProject = {
        id,
        name: "Main Project",
        description: "Primary project for the team",
        workspaceId: "workspace_1",
        teamId: "team_1",
        ownerId: "user_123",
        settings: {},
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return {
        success: true,
        data: {
          project: mockProject,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Create project
  fastify.post("/", {
    schema: {
      tags: ["Projects"],
      summary: "Create project",
      description: "Create a new project",
      security: [{ bearerAuth: [] }],
      body: {
        type: "object",
        properties: {
          name: { type: "string", minLength: 2 },
          description: { type: "string" },
          workspaceId: { type: "string", format: "uuid" },
          teamId: { type: "string", format: "uuid" },
          settings: { type: "object" },
        },
        required: ["name", "workspaceId"],
      },
      response: {
        201: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                project: {
                  type: "object",
                  properties: {
                    id: { type: "string", format: "uuid" },
                    name: { type: "string" },
                    description: { type: "string" },
                    workspaceId: { type: "string", format: "uuid" },
                    teamId: { type: "string", format: "uuid" },
                    ownerId: { type: "string", format: "uuid" },
                    settings: { type: "object" },
                    isActive: { type: "boolean" },
                    createdAt: { type: "string", format: "date-time" },
                    updatedAt: { type: "string", format: "date-time" },
                  },
                },
              },
            },
          },
        },
        400: { $ref: "#/components/responses/BadRequest" },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
      },
    },
    preHandler: [fastify.authenticate, canCreate("project"), validate({ body: createProjectSchema })],
    handler: async (request, reply): Promise<ApiResponse> => {
      const projectData = request.body as z.infer<typeof createProjectSchema>;
      
      // TODO: Implement actual project creation
      const newProject = {
        id: `project_${Date.now()}`,
        name: projectData.name,
        description: projectData.description,
        workspaceId: projectData.workspaceId,
        teamId: projectData.teamId,
        ownerId: (request as any).user.id,
        settings: projectData.settings || {},
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      reply.status(201);
      return {
        success: true,
        data: {
          project: newProject,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Update project
  fastify.patch("/:id", {
    schema: {
      tags: ["Projects"],
      summary: "Update project",
      description: "Update an existing project",
      security: [{ bearerAuth: [] }],
      params: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
        },
        required: ["id"],
      },
      body: {
        type: "object",
        properties: {
          name: { type: "string", minLength: 2 },
          description: { type: "string" },
          settings: { type: "object" },
          isActive: { type: "boolean" },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                project: {
                  type: "object",
                  properties: {
                    id: { type: "string", format: "uuid" },
                    name: { type: "string" },
                    description: { type: "string" },
                    workspaceId: { type: "string", format: "uuid" },
                    teamId: { type: "string", format: "uuid" },
                    ownerId: { type: "string", format: "uuid" },
                    settings: { type: "object" },
                    isActive: { type: "boolean" },
                    createdAt: { type: "string", format: "date-time" },
                    updatedAt: { type: "string", format: "date-time" },
                  },
                },
              },
            },
          },
        },
        400: { $ref: "#/components/responses/BadRequest" },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [
      fastify.authenticate,
      canWrite("project"),
      validateId,
      validate({ body: updateProjectSchema }),
    ],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { id } = request.params as { id: string };
      const updates = request.body as z.infer<typeof updateProjectSchema>;
      
      // TODO: Implement actual project update
      const updatedProject = {
        id,
        name: updates.name || "Project Name",
        description: updates.description,
        workspaceId: "workspace_1",
        teamId: "team_1",
        ownerId: "user_123",
        settings: updates.settings || {},
        isActive: updates.isActive ?? true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return {
        success: true,
        data: {
          project: updatedProject,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Delete project
  fastify.delete("/:id", {
    schema: {
      tags: ["Projects"],
      summary: "Delete project",
      description: "Delete a project",
      security: [{ bearerAuth: [] }],
      params: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
        },
        required: ["id"],
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                message: { type: "string" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [fastify.authenticate, canDelete("project"), validateId],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { id } = request.params as { id: string };
      
      // TODO: Implement actual project deletion
      
      return {
        success: true,
        data: {
          message: "Project deleted successfully",
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });
};
