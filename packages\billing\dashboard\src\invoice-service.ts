import { Invoice, InvoiceFilters, BillingAddress } from "./billing-types";

export class InvoiceService {
  private tenantId: string;

  constructor(tenantId: string) {
    this.tenantId = tenantId;
  }

  // Get invoices with filtering and pagination
  async getInvoices(
    filters?: InvoiceFilters,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    data: Invoice[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (filters) {
      if (filters.status?.length) {
        params.append("status", filters.status.join(","));
      }
      if (filters.dateRange) {
        params.append("startDate", filters.dateRange.start.toISOString());
        params.append("endDate", filters.dateRange.end.toISOString());
      }
      if (filters.customerId) {
        params.append("customerId", filters.customerId);
      }
      if (filters.subscriptionId) {
        params.append("subscriptionId", filters.subscriptionId);
      }
      if (filters.amountRange) {
        params.append("minAmount", filters.amountRange.min.toString());
        params.append("maxAmount", filters.amountRange.max.toString());
      }
      if (filters.search) {
        params.append("search", filters.search);
      }
    }

    const response = await fetch(`/api/billing/invoices?${params}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch invoices");
    }

    return response.json();
  }

  // Get single invoice
  async getInvoice(invoiceId: string): Promise<Invoice> {
    const response = await fetch(`/api/billing/invoices/${invoiceId}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch invoice");
    }

    return response.json();
  }

  // Download invoice PDF
  async downloadInvoicePDF(invoiceId: string): Promise<Blob> {
    const response = await fetch(`/api/billing/invoices/${invoiceId}/pdf`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to download invoice PDF");
    }

    return response.blob();
  }

  // Send invoice via email
  async sendInvoice(invoiceId: string, email?: string): Promise<void> {
    const response = await fetch(`/api/billing/invoices/${invoiceId}/send`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify({ email }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to send invoice");
    }
  }

  // Mark invoice as paid (manual)
  async markInvoiceAsPaid(invoiceId: string): Promise<Invoice> {
    const response = await fetch(`/api/billing/invoices/${invoiceId}/mark-paid`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to mark invoice as paid");
    }

    return response.json();
  }

  // Void invoice
  async voidInvoice(invoiceId: string): Promise<Invoice> {
    const response = await fetch(`/api/billing/invoices/${invoiceId}/void`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to void invoice");
    }

    return response.json();
  }

  // Create manual invoice
  async createInvoice(data: {
    customerId: string;
    items: Array<{
      description: string;
      quantity: number;
      unitAmount: number;
    }>;
    dueDate?: Date;
    description?: string;
    metadata?: Record<string, string>;
  }): Promise<Invoice> {
    const response = await fetch("/api/billing/invoices", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to create invoice");
    }

    return response.json();
  }

  // Update billing address
  async updateBillingAddress(customerId: string, address: BillingAddress): Promise<void> {
    const response = await fetch(`/api/billing/customers/${customerId}/address`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify(address),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to update billing address");
    }
  }

  // Get invoice statistics
  async getInvoiceStats(period?: { start: Date; end: Date }): Promise<{
    totalInvoices: number;
    totalAmount: number;
    paidAmount: number;
    pendingAmount: number;
    overdueAmount: number;
    averageAmount: number;
    paymentRate: number;
  }> {
    const params = new URLSearchParams();
    
    if (period) {
      params.append("startDate", period.start.toISOString());
      params.append("endDate", period.end.toISOString());
    }

    const response = await fetch(`/api/billing/invoices/stats?${params}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch invoice statistics");
    }

    return response.json();
  }
}

export const createInvoiceService = (tenantId: string): InvoiceService => {
  return new InvoiceService(tenantId);
};
