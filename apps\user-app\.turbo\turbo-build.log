
> @nexus/user-app@0.1.0 build C:\Users\<USER>\Downloads\saas-starter-cursor-temp\apps\user-app
> next build

   ▲ Next.js 15.4.2

   Creating an optimized production build ...
 ✓ Compiled successfully in 2000ms
   Linting and checking validity of types ...
   Collecting page data ...
   Generating static pages (0/5) ...
   Generating static pages (1/5) 
   Generating static pages (2/5) 
   Generating static pages (3/5) 
 ✓ Generating static pages (5/5)
   Finalizing page optimization ...
   Collecting build traces ...

Route (app)                                 Size  First Load JS
┌ ○ /                                    5.46 kB         106 kB
└ ○ /_not-found                            990 B         101 kB
+ First Load JS shared by all             100 kB
  ├ chunks/104b06e6-5df3a4f1c270bc82.js  54.1 kB
  ├ chunks/796-7be2c160581854a8.js         44 kB
  └ other shared chunks (total)          1.97 kB


○  (Static)  prerendered as static content

