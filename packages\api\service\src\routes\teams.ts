import { FastifyInstance } from "fastify";
import { z } from "zod";
import { validate, validatePagination, validateId } from "../middleware";
import { canRead, canCreate, canWrite, canDelete } from "../middleware/rbac";
import { ApiResponse } from "../types";

const createTeamSchema = z.object({
  name: z.string().min(2),
  description: z.string().optional(),
  workspaceId: z.string().uuid(),
  leadId: z.string().uuid().optional(),
});

const updateTeamSchema = z.object({
  name: z.string().min(2).optional(),
  description: z.string().optional(),
  leadId: z.string().uuid().optional(),
  isActive: z.boolean().optional(),
});

export const teamRoutes = async (fastify: FastifyInstance) => {
  // Get teams
  fastify.get("/", {
    schema: {
      tags: ["Teams"],
      summary: "List teams",
      description: "Get a paginated list of teams",
      security: [{ bearerAuth: [] }],
      querystring: {
        type: "object",
        properties: {
          page: { type: "integer", minimum: 1, default: 1 },
          limit: { type: "integer", minimum: 1, maximum: 100, default: 20 },
          workspaceId: { type: "string", format: "uuid" },
          search: { type: "string" },
          isActive: { type: "boolean" },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                teams: {
                  type: "array",
                  items: { $ref: "#/components/schemas/Team" },
                },
                pagination: { $ref: "#/components/schemas/Pagination" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
      },
    },
    preHandler: [fastify.authenticate, canRead("team"), validatePagination],
    handler: async (request, reply): Promise<ApiResponse> => {
      const query = request.query as any;
      
      // TODO: Implement actual team fetching
      const mockTeams = [
        {
          id: "team_1",
          name: "Development Team",
          description: "Core development team",
          workspaceId: "workspace_1",
          leadId: "user_123",
          memberCount: 5,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      return {
        success: true,
        data: {
          teams: mockTeams,
          pagination: {
            page: query.page || 1,
            limit: query.limit || 20,
            total: mockTeams.length,
            totalPages: 1,
          },
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Get team by ID
  fastify.get("/:id", {
    schema: {
      tags: ["Teams"],
      summary: "Get team by ID",
      description: "Get a specific team by its ID",
      security: [{ bearerAuth: [] }],
      params: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
        },
        required: ["id"],
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                team: { $ref: "#/components/schemas/Team" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [fastify.authenticate, canRead("team"), validateId],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { id } = request.params as { id: string };
      
      // TODO: Implement actual team fetching
      const mockTeam = {
        id,
        name: "Development Team",
        description: "Core development team",
        workspaceId: "workspace_1",
        leadId: "user_123",
        memberCount: 5,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return {
        success: true,
        data: {
          team: mockTeam,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Create team
  fastify.post("/", {
    schema: {
      tags: ["Teams"],
      summary: "Create team",
      description: "Create a new team",
      security: [{ bearerAuth: [] }],
      body: {
        type: "object",
        properties: {
          name: { type: "string", minLength: 2 },
          description: { type: "string" },
          workspaceId: { type: "string", format: "uuid" },
          leadId: { type: "string", format: "uuid" },
        },
        required: ["name", "workspaceId"],
      },
      response: {
        201: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                team: { $ref: "#/components/schemas/Team" },
              },
            },
          },
        },
        400: { $ref: "#/components/responses/BadRequest" },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
      },
    },
    preHandler: [fastify.authenticate, canCreate("team"), validate({ body: createTeamSchema })],
    handler: async (request, reply): Promise<ApiResponse> => {
      const teamData = request.body as z.infer<typeof createTeamSchema>;
      
      // TODO: Implement actual team creation
      const newTeam = {
        id: `team_${Date.now()}`,
        name: teamData.name,
        description: teamData.description,
        workspaceId: teamData.workspaceId,
        leadId: teamData.leadId || (request as any).user.id,
        memberCount: 1,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      reply.status(201);
      return {
        success: true,
        data: {
          team: newTeam,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Update team
  fastify.patch("/:id", {
    schema: {
      tags: ["Teams"],
      summary: "Update team",
      description: "Update an existing team",
      security: [{ bearerAuth: [] }],
      params: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
        },
        required: ["id"],
      },
      body: {
        type: "object",
        properties: {
          name: { type: "string", minLength: 2 },
          description: { type: "string" },
          leadId: { type: "string", format: "uuid" },
          isActive: { type: "boolean" },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                team: { $ref: "#/components/schemas/Team" },
              },
            },
          },
        },
        400: { $ref: "#/components/responses/BadRequest" },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [
      fastify.authenticate,
      canWrite("team"),
      validateId,
      validate({ body: updateTeamSchema }),
    ],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { id } = request.params as { id: string };
      const updates = request.body as z.infer<typeof updateTeamSchema>;
      
      // TODO: Implement actual team update
      const updatedTeam = {
        id,
        name: updates.name || "Team Name",
        description: updates.description,
        workspaceId: "workspace_1",
        leadId: updates.leadId || "user_123",
        memberCount: 5,
        isActive: updates.isActive ?? true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return {
        success: true,
        data: {
          team: updatedTeam,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Delete team
  fastify.delete("/:id", {
    schema: {
      tags: ["Teams"],
      summary: "Delete team",
      description: "Delete a team",
      security: [{ bearerAuth: [] }],
      params: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
        },
        required: ["id"],
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                message: { type: "string" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [fastify.authenticate, canDelete("team"), validateId],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { id } = request.params as { id: string };
      
      // TODO: Implement actual team deletion
      
      return {
        success: true,
        data: {
          message: "Team deleted successfully",
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });
};
