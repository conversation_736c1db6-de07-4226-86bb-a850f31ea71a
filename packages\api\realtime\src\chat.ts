import { EventEmitter } from "events";
import { v4 as uuidv4 } from "uuid";
import { User } from "@nexus/types";
import { 
  ChatMessage, 
  TypingIndicator, 
  MessageRead,
  SocketContext 
} from "./types";

export class ChatManager extends EventEmitter {
  private messages: Map<string, ChatMessage[]> = new Map(); // channelId -> messages
  private typingUsers: Map<string, Map<string, TypingIndicator>> = new Map(); // channelId -> userId -> typing
  private messageReads: Map<string, Map<string, Date>> = new Map(); // messageId -> userId -> readAt
  private maxMessagesPerChannel: number;
  private messageRetentionDays: number;

  constructor(config: {
    maxMessagesPerChannel?: number;
    messageRetentionDays?: number;
  } = {}) {
    super();
    this.maxMessagesPerChannel = config.maxMessagesPerChannel || 1000;
    this.messageRetentionDays = config.messageRetentionDays || 30;

    // Start cleanup interval
    setInterval(() => {
      this.cleanupOldMessages();
    }, 24 * 60 * 60 * 1000); // Daily cleanup
  }

  // Send message
  async sendMessage(
    channelId: string,
    channelType: ChatMessage["channelType"],
    content: string,
    context: SocketContext,
    options: {
      type?: ChatMessage["type"];
      metadata?: ChatMessage["metadata"];
      replyTo?: string;
    } = {}
  ): Promise<ChatMessage> {
    const { user } = context;
    const { type = "text", metadata, replyTo } = options;

    // Validate message
    if (!content.trim() && type === "text") {
      throw new Error("Message content cannot be empty");
    }

    if (content.length > 10000) {
      throw new Error("Message content too long");
    }

    // Create message
    const message: ChatMessage = {
      id: uuidv4(),
      channelId,
      channelType,
      userId: user.id,
      user: {
        id: user.id,
        name: user.name,
        avatar: user.avatar,
      },
      content,
      type,
      metadata,
      replyTo,
      createdAt: new Date(),
    };

    // Store message
    if (!this.messages.has(channelId)) {
      this.messages.set(channelId, []);
    }

    const channelMessages = this.messages.get(channelId)!;
    channelMessages.push(message);

    // Limit messages per channel
    if (channelMessages.length > this.maxMessagesPerChannel) {
      channelMessages.splice(0, channelMessages.length - this.maxMessagesPerChannel);
    }

    // Clear typing indicator for this user
    this.clearTyping(channelId, user.id);

    // Emit message event
    this.emit("chat:message", {
      channelId,
      message,
    });

    return message;
  }

  // Edit message
  async editMessage(
    messageId: string,
    newContent: string,
    context: SocketContext
  ): Promise<ChatMessage> {
    const { user } = context;
    
    // Find message
    let targetMessage: ChatMessage | null = null;
    let channelId: string | null = null;

    for (const [cId, messages] of this.messages.entries()) {
      const message = messages.find(m => m.id === messageId);
      if (message) {
        targetMessage = message;
        channelId = cId;
        break;
      }
    }

    if (!targetMessage || !channelId) {
      throw new Error("Message not found");
    }

    // Check permissions
    if (targetMessage.userId !== user.id) {
      throw new Error("Cannot edit message from another user");
    }

    if (targetMessage.deletedAt) {
      throw new Error("Cannot edit deleted message");
    }

    // Update message
    targetMessage.content = newContent;
    targetMessage.editedAt = new Date();

    // Emit edit event
    this.emit("chat:message:edit", {
      channelId,
      message: targetMessage,
    });

    return targetMessage;
  }

  // Delete message
  async deleteMessage(
    messageId: string,
    context: SocketContext
  ): Promise<void> {
    const { user } = context;
    
    // Find message
    let targetMessage: ChatMessage | null = null;
    let channelId: string | null = null;

    for (const [cId, messages] of this.messages.entries()) {
      const message = messages.find(m => m.id === messageId);
      if (message) {
        targetMessage = message;
        channelId = cId;
        break;
      }
    }

    if (!targetMessage || !channelId) {
      throw new Error("Message not found");
    }

    // Check permissions
    if (targetMessage.userId !== user.id) {
      // TODO: Check if user has admin permissions
      throw new Error("Cannot delete message from another user");
    }

    // Mark as deleted
    targetMessage.deletedAt = new Date();
    targetMessage.content = "[Message deleted]";

    // Emit delete event
    this.emit("chat:message:delete", {
      channelId,
      messageId,
    });
  }

  // Add reaction to message
  async addReaction(
    messageId: string,
    emoji: string,
    context: SocketContext
  ): Promise<void> {
    const { user } = context;
    
    // Find message
    let targetMessage: ChatMessage | null = null;
    let channelId: string | null = null;

    for (const [cId, messages] of this.messages.entries()) {
      const message = messages.find(m => m.id === messageId);
      if (message) {
        targetMessage = message;
        channelId = cId;
        break;
      }
    }

    if (!targetMessage || !channelId) {
      throw new Error("Message not found");
    }

    // Initialize metadata if needed
    if (!targetMessage.metadata) {
      targetMessage.metadata = {};
    }
    if (!targetMessage.metadata.reactions) {
      targetMessage.metadata.reactions = [];
    }

    // Find or create reaction
    let reaction = targetMessage.metadata.reactions.find(r => r.emoji === emoji);
    if (!reaction) {
      reaction = { emoji, users: [], count: 0 };
      targetMessage.metadata.reactions.push(reaction);
    }

    // Add user to reaction if not already present
    if (!reaction.users.includes(user.id)) {
      reaction.users.push(user.id);
      reaction.count = reaction.users.length;

      // Emit reaction event
      this.emit("chat:message:reaction", {
        channelId,
        messageId,
        emoji,
        userId: user.id,
        action: "add",
      });
    }
  }

  // Remove reaction from message
  async removeReaction(
    messageId: string,
    emoji: string,
    context: SocketContext
  ): Promise<void> {
    const { user } = context;
    
    // Find message
    let targetMessage: ChatMessage | null = null;
    let channelId: string | null = null;

    for (const [cId, messages] of this.messages.entries()) {
      const message = messages.find(m => m.id === messageId);
      if (message) {
        targetMessage = message;
        channelId = cId;
        break;
      }
    }

    if (!targetMessage || !channelId || !targetMessage.metadata?.reactions) {
      return;
    }

    // Find reaction
    const reaction = targetMessage.metadata.reactions.find(r => r.emoji === emoji);
    if (!reaction) return;

    // Remove user from reaction
    const userIndex = reaction.users.indexOf(user.id);
    if (userIndex !== -1) {
      reaction.users.splice(userIndex, 1);
      reaction.count = reaction.users.length;

      // Remove reaction if no users left
      if (reaction.count === 0) {
        const reactionIndex = targetMessage.metadata.reactions.indexOf(reaction);
        targetMessage.metadata.reactions.splice(reactionIndex, 1);
      }

      // Emit reaction event
      this.emit("chat:message:reaction", {
        channelId,
        messageId,
        emoji,
        userId: user.id,
        action: "remove",
      });
    }
  }

  // Set typing indicator
  setTyping(channelId: string, context: SocketContext): void {
    const { user } = context;
    
    if (!this.typingUsers.has(channelId)) {
      this.typingUsers.set(channelId, new Map());
    }

    const channelTyping = this.typingUsers.get(channelId)!;
    const typing: TypingIndicator = {
      channelId,
      userId: user.id,
      user: {
        id: user.id,
        name: user.name,
      },
      isTyping: true,
      timestamp: new Date(),
    };

    channelTyping.set(user.id, typing);

    // Emit typing event
    this.emit("chat:typing", {
      channelId,
      typing,
    });

    // Auto-clear typing after 5 seconds
    setTimeout(() => {
      this.clearTyping(channelId, user.id);
    }, 5000);
  }

  // Clear typing indicator
  clearTyping(channelId: string, userId: string): void {
    const channelTyping = this.typingUsers.get(channelId);
    if (!channelTyping) return;

    const typing = channelTyping.get(userId);
    if (typing) {
      typing.isTyping = false;
      channelTyping.delete(userId);

      // Emit typing stop event
      this.emit("chat:typing", {
        channelId,
        typing,
      });
    }
  }

  // Mark message as read
  markAsRead(messageId: string, context: SocketContext): void {
    const { user } = context;
    
    if (!this.messageReads.has(messageId)) {
      this.messageReads.set(messageId, new Map());
    }

    const messageReads = this.messageReads.get(messageId)!;
    messageReads.set(user.id, new Date());

    // Emit read event
    this.emit("chat:message:read", {
      messageId,
      userId: user.id,
      readAt: new Date(),
    });
  }

  // Get messages for channel
  getMessages(
    channelId: string,
    options: {
      limit?: number;
      before?: string;
      after?: string;
    } = {}
  ): ChatMessage[] {
    const { limit = 50, before, after } = options;
    const messages = this.messages.get(channelId) || [];
    
    let filteredMessages = messages;

    // Filter by before/after
    if (before) {
      const beforeIndex = messages.findIndex(m => m.id === before);
      if (beforeIndex !== -1) {
        filteredMessages = messages.slice(0, beforeIndex);
      }
    }

    if (after) {
      const afterIndex = messages.findIndex(m => m.id === after);
      if (afterIndex !== -1) {
        filteredMessages = messages.slice(afterIndex + 1);
      }
    }

    // Apply limit
    return filteredMessages.slice(-limit);
  }

  // Get typing users for channel
  getTypingUsers(channelId: string): TypingIndicator[] {
    const channelTyping = this.typingUsers.get(channelId);
    if (!channelTyping) return [];

    return Array.from(channelTyping.values()).filter(t => t.isTyping);
  }

  // Get message read status
  getMessageReads(messageId: string): Array<{ userId: string; readAt: Date }> {
    const reads = this.messageReads.get(messageId);
    if (!reads) return [];

    return Array.from(reads.entries()).map(([userId, readAt]) => ({
      userId,
      readAt,
    }));
  }

  // Cleanup old messages
  private cleanupOldMessages(): void {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.messageRetentionDays);

    for (const [channelId, messages] of this.messages.entries()) {
      const filteredMessages = messages.filter(m => m.createdAt > cutoffDate);
      
      if (filteredMessages.length !== messages.length) {
        this.messages.set(channelId, filteredMessages);
      }
    }
  }

  // Get chat statistics
  getStats(): {
    totalChannels: number;
    totalMessages: number;
    activeTypingUsers: number;
    messagesPerChannel: Record<string, number>;
  } {
    let totalMessages = 0;
    let activeTypingUsers = 0;
    const messagesPerChannel: Record<string, number> = {};

    for (const [channelId, messages] of this.messages.entries()) {
      totalMessages += messages.length;
      messagesPerChannel[channelId] = messages.length;
    }

    for (const channelTyping of this.typingUsers.values()) {
      activeTypingUsers += channelTyping.size;
    }

    return {
      totalChannels: this.messages.size,
      totalMessages,
      activeTypingUsers,
      messagesPerChannel,
    };
  }

  // Destroy chat manager
  destroy(): void {
    this.messages.clear();
    this.typingUsers.clear();
    this.messageReads.clear();
    this.removeAllListeners();
  }
}
