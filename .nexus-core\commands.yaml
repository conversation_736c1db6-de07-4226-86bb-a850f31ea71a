# NEXUS Agent Capabilities Reference
# These are the core capabilities the AI agent can perform
capabilities:
  help: "Show available capabilities and guidance"
  plan: "Generate comprehensive project plan with task breakdown and session tracking"
  create: "Create new component/page/api"
  analyze: "Analyze code quality and performance"
  optimize: "Optimize code and performance"
  test: "Run comprehensive testing"
  deploy: "Deploy application"
  validate: "Validate against quality standards"
  debug: "Debug and troubleshoot issues"
  refactor: "Refactor code for better maintainability"
  secure: "Apply security best practices"
