import { AuthTokens, ApiError } from "./types";

// Token storage keys
const TOKEN_STORAGE_KEY = "nexus_auth_tokens";
const USER_STORAGE_KEY = "nexus_user";

// Token management
export class TokenManager {
  private static instance: TokenManager;
  private tokens: AuthTokens | null = null;
  private refreshPromise: Promise<AuthTokens> | null = null;

  private constructor() {
    this.loadTokensFromStorage();
  }

  static getInstance(): TokenManager {
    if (!TokenManager.instance) {
      TokenManager.instance = new TokenManager();
    }
    return TokenManager.instance;
  }

  // Load tokens from localStorage
  private loadTokensFromStorage(): void {
    if (typeof window === "undefined") return;
    
    try {
      const stored = localStorage.getItem(TOKEN_STORAGE_KEY);
      if (stored) {
        this.tokens = JSON.parse(stored);
        
        // Check if tokens are expired
        if (this.tokens && this.isTokenExpired(this.tokens)) {
          this.clearTokens();
        }
      }
    } catch (error) {
      console.error("Failed to load tokens from storage:", error);
      this.clearTokens();
    }
  }

  // Save tokens to localStorage
  private saveTokensToStorage(tokens: AuthTokens): void {
    if (typeof window === "undefined") return;
    
    try {
      localStorage.setItem(TOKEN_STORAGE_KEY, JSON.stringify(tokens));
    } catch (error) {
      console.error("Failed to save tokens to storage:", error);
    }
  }

  // Check if token is expired
  private isTokenExpired(tokens: AuthTokens): boolean {
    return Date.now() >= tokens.expiresAt;
  }

  // Check if token will expire soon (within 5 minutes)
  private isTokenExpiringSoon(tokens: AuthTokens): boolean {
    return Date.now() >= tokens.expiresAt - 5 * 60 * 1000;
  }

  // Set tokens
  setTokens(tokens: AuthTokens): void {
    this.tokens = tokens;
    this.saveTokensToStorage(tokens);
  }

  // Get access token
  getAccessToken(): string | null {
    return this.tokens?.accessToken || null;
  }

  // Get refresh token
  getRefreshToken(): string | null {
    return this.tokens?.refreshToken || null;
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return this.tokens !== null && !this.isTokenExpired(this.tokens);
  }

  // Check if token needs refresh
  needsRefresh(): boolean {
    return this.tokens !== null && this.isTokenExpiringSoon(this.tokens);
  }

  // Clear tokens
  clearTokens(): void {
    this.tokens = null;
    if (typeof window !== "undefined") {
      localStorage.removeItem(TOKEN_STORAGE_KEY);
      localStorage.removeItem(USER_STORAGE_KEY);
    }
  }

  // Refresh tokens
  async refreshTokens(refreshFn: (refreshToken: string) => Promise<AuthTokens>): Promise<AuthTokens> {
    // Prevent multiple simultaneous refresh requests
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    if (!this.tokens?.refreshToken) {
      throw new Error("No refresh token available");
    }

    this.refreshPromise = refreshFn(this.tokens.refreshToken)
      .then((newTokens) => {
        this.setTokens(newTokens);
        this.refreshPromise = null;
        return newTokens;
      })
      .catch((error) => {
        this.clearTokens();
        this.refreshPromise = null;
        throw error;
      });

    return this.refreshPromise;
  }
}

// JWT utilities
export const jwtUtils = {
  // Decode JWT payload (without verification)
  decode: (token: string): any => {
    try {
      const payload = token.split(".")[1];
      const decoded = atob(payload);
      return JSON.parse(decoded);
    } catch (error) {
      console.error("Failed to decode JWT:", error);
      return null;
    }
  },

  // Check if JWT is expired
  isExpired: (token: string): boolean => {
    try {
      const payload = jwtUtils.decode(token);
      if (!payload || !payload.exp) return true;
      
      return Date.now() >= payload.exp * 1000;
    } catch (error) {
      return true;
    }
  },

  // Get JWT expiration time
  getExpirationTime: (token: string): number | null => {
    try {
      const payload = jwtUtils.decode(token);
      return payload?.exp ? payload.exp * 1000 : null;
    } catch (error) {
      return null;
    }
  },

  // Extract user info from JWT
  extractUserInfo: (token: string): any => {
    try {
      const payload = jwtUtils.decode(token);
      return {
        id: payload?.sub,
        email: payload?.email,
        name: payload?.name,
        tenantId: payload?.tenantId,
        roles: payload?.roles || [],
      };
    } catch (error) {
      return null;
    }
  },
};

// Auth error handling
export class AuthError extends Error {
  constructor(
    message: string,
    public code?: string,
    public status?: number
  ) {
    super(message);
    this.name = "AuthError";
  }
}

// Auth event emitter for cross-component communication
export class AuthEventEmitter {
  private static instance: AuthEventEmitter;
  private listeners: Map<string, Set<Function>> = new Map();

  private constructor() {}

  static getInstance(): AuthEventEmitter {
    if (!AuthEventEmitter.instance) {
      AuthEventEmitter.instance = new AuthEventEmitter();
    }
    return AuthEventEmitter.instance;
  }

  // Add event listener
  on(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(callback);
  }

  // Remove event listener
  off(event: string, callback: Function): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.delete(callback);
    }
  }

  // Emit event
  emit(event: string, data?: any): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach((callback) => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in auth event listener for ${event}:`, error);
        }
      });
    }
  }
}

// Auth events
export const AUTH_EVENTS = {
  LOGIN: "auth:login",
  LOGOUT: "auth:logout",
  TOKEN_REFRESH: "auth:token_refresh",
  TOKEN_EXPIRED: "auth:token_expired",
  USER_UPDATED: "auth:user_updated",
} as const;

// User storage utilities
export const userStorage = {
  save: (user: any): void => {
    if (typeof window === "undefined") return;
    
    try {
      localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user));
    } catch (error) {
      console.error("Failed to save user to storage:", error);
    }
  },

  load: (): any => {
    if (typeof window === "undefined") return null;
    
    try {
      const stored = localStorage.getItem(USER_STORAGE_KEY);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error("Failed to load user from storage:", error);
      return null;
    }
  },

  clear: (): void => {
    if (typeof window === "undefined") return;
    localStorage.removeItem(USER_STORAGE_KEY);
  },
};
