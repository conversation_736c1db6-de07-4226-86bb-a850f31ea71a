import { useCallback, useEffect } from "react";
import { useAuthStore, authUtils, initializeAuth } from "../stores/auth";
import { AuthEventEmitter, AUTH_EVENTS } from "../auth";
import { User } from "../types";

export function useAuth() {
  const {
    user,
    tokens,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    refreshToken,
    updateUser,
    setError,
  } = useAuthStore();

  // Initialize auth on mount
  useEffect(() => {
    initializeAuth();
  }, []);

  // Auth event handlers
  useEffect(() => {
    const authEventEmitter = AuthEventEmitter.getInstance();

    const handleTokenExpired = () => {
      logout();
    };

    authEventEmitter.on(AUTH_EVENTS.TOKEN_EXPIRED, handleTokenExpired);

    return () => {
      authEventEmitter.off(AUTH_EVENTS.TOKEN_EXPIRED, handleTokenExpired);
    };
  }, [logout]);

  const loginWithCredentials = useCallback(
    async (email: string, password: string) => {
      try {
        await login(email, password);
      } catch (error: any) {
        throw error;
      }
    },
    [login]
  );

  const logoutUser = useCallback(async () => {
    try {
      await logout();
    } catch (error) {
      // Logout should always succeed locally even if API fails
      console.error("Logout error:", error);
    }
  }, [logout]);

  const refreshUserToken = useCallback(async () => {
    try {
      await refreshToken();
    } catch (error: any) {
      throw error;
    }
  }, [refreshToken]);

  const updateUserProfile = useCallback(
    (updates: Partial<User>) => {
      updateUser(updates);
    },
    [updateUser]
  );

  const clearError = useCallback(() => {
    setError(null);
  }, [setError]);

  // Permission helpers
  const hasRole = useCallback(
    (role: string): boolean => {
      return authUtils.hasRole(role);
    },
    [user]
  );

  const hasAnyRole = useCallback(
    (roles: string[]): boolean => {
      return authUtils.hasAnyRole(roles);
    },
    [user]
  );

  const hasAllRoles = useCallback(
    (roles: string[]): boolean => {
      return authUtils.hasAllRoles(roles);
    },
    [user]
  );

  const isAdmin = useCallback((): boolean => {
    return authUtils.isAdmin();
  }, [user]);

  const isOwner = useCallback((): boolean => {
    return authUtils.isOwner();
  }, [user]);

  return {
    // State
    user,
    tokens,
    isAuthenticated,
    isLoading,
    error,

    // Actions
    login: loginWithCredentials,
    logout: logoutUser,
    refreshToken: refreshUserToken,
    updateUser: updateUserProfile,
    clearError,

    // Permission helpers
    hasRole,
    hasAnyRole,
    hasAllRoles,
    isAdmin,
    isOwner,

    // Computed values
    currentTenantId: user?.tenantId || null,
    userDisplayName: user?.name || user?.email || "Unknown User",
    userInitials: user ? authUtils.getUserInitials(user) : "??",
  };
}

// Hook for protecting routes/components
export function useRequireAuth(redirectTo?: string) {
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading && !isAuthenticated && redirectTo) {
      // This would typically use Next.js router or React Router
      window.location.href = redirectTo;
    }
  }, [isAuthenticated, isLoading, redirectTo]);

  return {
    isAuthenticated,
    isLoading,
    shouldRedirect: !isLoading && !isAuthenticated,
  };
}

// Hook for role-based access control
export function useRequireRole(requiredRoles: string | string[], redirectTo?: string) {
  const { user, hasRole, hasAnyRole, isLoading } = useAuth();
  
  const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
  const hasRequiredRole = roles.length === 1 ? hasRole(roles[0]) : hasAnyRole(roles);

  useEffect(() => {
    if (!isLoading && user && !hasRequiredRole && redirectTo) {
      window.location.href = redirectTo;
    }
  }, [user, hasRequiredRole, isLoading, redirectTo]);

  return {
    hasRequiredRole,
    isLoading,
    shouldRedirect: !isLoading && user && !hasRequiredRole,
  };
}

// Hook for admin access
export function useRequireAdmin(redirectTo?: string) {
  return useRequireRole(["admin", "system_admin"], redirectTo);
}

// Hook for listening to auth events
export function useAuthEvents() {
  const authEventEmitter = AuthEventEmitter.getInstance();

  const onLogin = useCallback((callback: (data: any) => void) => {
    authEventEmitter.on(AUTH_EVENTS.LOGIN, callback);
    return () => authEventEmitter.off(AUTH_EVENTS.LOGIN, callback);
  }, [authEventEmitter]);

  const onLogout = useCallback((callback: () => void) => {
    authEventEmitter.on(AUTH_EVENTS.LOGOUT, callback);
    return () => authEventEmitter.off(AUTH_EVENTS.LOGOUT, callback);
  }, [authEventEmitter]);

  const onTokenRefresh = useCallback((callback: (tokens: any) => void) => {
    authEventEmitter.on(AUTH_EVENTS.TOKEN_REFRESH, callback);
    return () => authEventEmitter.off(AUTH_EVENTS.TOKEN_REFRESH, callback);
  }, [authEventEmitter]);

  const onTokenExpired = useCallback((callback: () => void) => {
    authEventEmitter.on(AUTH_EVENTS.TOKEN_EXPIRED, callback);
    return () => authEventEmitter.off(AUTH_EVENTS.TOKEN_EXPIRED, callback);
  }, [authEventEmitter]);

  const onUserUpdated = useCallback((callback: (user: User) => void) => {
    authEventEmitter.on(AUTH_EVENTS.USER_UPDATED, callback);
    return () => authEventEmitter.off(AUTH_EVENTS.USER_UPDATED, callback);
  }, [authEventEmitter]);

  return {
    onLogin,
    onLogout,
    onTokenRefresh,
    onTokenExpired,
    onUserUpdated,
  };
}
