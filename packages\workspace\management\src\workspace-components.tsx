"use client";

import React, { useState } from "react";
import { useWorkspaces } from "./workspace-hooks";
import { CreateWorkspaceData } from "./workspace-types";

// Workspace creation form
export function CreateWorkspaceForm({ onSuccess }: { onSuccess?: () => void }) {
  const [formData, setFormData] = useState<CreateWorkspaceData>({
    name: "",
    slug: "",
    description: "",
    visibility: "private",
  });
  const { createWorkspace, isLoading } = useWorkspaces();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createWorkspace(formData);
      onSuccess?.();
    } catch (error) {
      console.error("Failed to create workspace:", error);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-1">Name</label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          className="w-full px-3 py-2 border rounded-md"
          required
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium mb-1">Slug</label>
        <input
          type="text"
          value={formData.slug}
          onChange={(e) => setFormData({ ...formData, slug: e.target.value })}
          className="w-full px-3 py-2 border rounded-md"
          required
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium mb-1">Description</label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          className="w-full px-3 py-2 border rounded-md"
          rows={3}
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium mb-1">Visibility</label>
        <select
          value={formData.visibility}
          onChange={(e) => setFormData({ ...formData, visibility: e.target.value as any })}
          className="w-full px-3 py-2 border rounded-md"
        >
          <option value="private">Private</option>
          <option value="public">Public</option>
          <option value="restricted">Restricted</option>
        </select>
      </div>
      
      <button
        type="submit"
        disabled={isLoading}
        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
      >
        {isLoading ? "Creating..." : "Create Workspace"}
      </button>
    </form>
  );
}

// Workspace list component
export function WorkspaceList() {
  const { workspaces, isLoading, switchWorkspace } = useWorkspaces();

  if (isLoading) {
    return <div>Loading workspaces...</div>;
  }

  return (
    <div className="space-y-2">
      {workspaces.map((workspace) => (
        <div
          key={workspace.id}
          className="p-4 border rounded-md cursor-pointer hover:bg-gray-50"
          onClick={() => switchWorkspace(workspace)}
        >
          <h3 className="font-medium">{workspace.name}</h3>
          <p className="text-sm text-gray-600">{workspace.description}</p>
          <div className="flex items-center gap-2 mt-2">
            <span className={`px-2 py-1 text-xs rounded ${
              workspace.status === "active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
            }`}>
              {workspace.status}
            </span>
            <span className="text-xs text-gray-500">{workspace.visibility}</span>
          </div>
        </div>
      ))}
    </div>
  );
}
