import { ApiConfig } from "./types";

// Default API configuration
export const defaultConfig: ApiConfig = {
  baseURL: process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001/api/v1",
  graphqlURL: process.env.NEXT_PUBLIC_GRAPHQL_URL || "http://localhost:4000/graphql",
  timeout: 30000, // 30 seconds
  retries: 3,
  retryDelay: 1000, // 1 second
};

// Environment-specific configurations
export const configs = {
  development: {
    ...defaultConfig,
    baseURL: "http://localhost:3001/api/v1",
    graphqlURL: "http://localhost:4000/graphql",
  },
  
  staging: {
    ...defaultConfig,
    baseURL: "https://api-staging.nexus.com/api/v1",
    graphqlURL: "https://graphql-staging.nexus.com/graphql",
  },
  
  production: {
    ...defaultConfig,
    baseURL: "https://api.nexus.com/api/v1",
    graphqlURL: "https://graphql.nexus.com/graphql",
    timeout: 15000, // Shorter timeout in production
    retries: 2,
  },
};

// Get configuration based on environment
export const getConfig = (): ApiConfig => {
  const env = process.env.NODE_ENV || "development";
  return configs[env as keyof typeof configs] || defaultConfig;
};

// API endpoints
export const endpoints = {
  // Authentication
  auth: {
    login: "/auth/login",
    logout: "/auth/logout",
    refresh: "/auth/refresh",
    register: "/auth/register",
    forgotPassword: "/auth/forgot-password",
    resetPassword: "/auth/reset-password",
    verifyEmail: "/auth/verify-email",
    me: "/auth/me",
  },

  // Users
  users: {
    list: "/users",
    create: "/users",
    get: (id: string) => `/users/${id}`,
    update: (id: string) => `/users/${id}`,
    delete: (id: string) => `/users/${id}`,
    assignRole: (id: string) => `/users/${id}/roles`,
    revokeRole: (id: string) => `/users/${id}/roles`,
  },

  // Workspaces
  workspaces: {
    list: "/workspaces",
    create: "/workspaces",
    get: (id: string) => `/workspaces/${id}`,
    update: (id: string) => `/workspaces/${id}`,
    delete: (id: string) => `/workspaces/${id}`,
    members: (id: string) => `/workspaces/${id}/members`,
  },

  // Teams
  teams: {
    list: "/teams",
    create: "/teams",
    get: (id: string) => `/teams/${id}`,
    update: (id: string) => `/teams/${id}`,
    delete: (id: string) => `/teams/${id}`,
    members: (id: string) => `/teams/${id}/members`,
    addMember: (id: string) => `/teams/${id}/members`,
    removeMember: (id: string, userId: string) => `/teams/${id}/members/${userId}`,
  },

  // Projects
  projects: {
    list: "/projects",
    create: "/projects",
    get: (id: string) => `/projects/${id}`,
    update: (id: string) => `/projects/${id}`,
    delete: (id: string) => `/projects/${id}`,
    collaborators: (id: string) => `/projects/${id}/collaborators`,
  },

  // Files
  files: {
    list: "/files",
    upload: "/files/upload",
    get: (id: string) => `/files/${id}`,
    update: (id: string) => `/files/${id}`,
    delete: (id: string) => `/files/${id}`,
    download: (id: string) => `/files/${id}/download`,
    thumbnail: (id: string) => `/files/${id}/thumbnail`,
    share: (id: string) => `/files/${id}/share`,
  },

  // Roles
  roles: {
    list: "/roles",
    create: "/roles",
    get: (id: string) => `/roles/${id}`,
    update: (id: string) => `/roles/${id}`,
    delete: (id: string) => `/roles/${id}`,
    permissions: "/permissions",
  },

  // Analytics
  analytics: {
    dashboard: "/analytics/dashboard",
    userActivity: "/analytics/user-activity",
    projectAnalytics: "/analytics/projects",
    apiUsage: "/analytics/api-usage",
  },

  // Billing
  billing: {
    subscription: "/billing/subscription",
    invoices: "/billing/invoices",
    invoice: (id: string) => `/billing/invoices/${id}`,
    plans: "/billing/plans",
    paymentMethods: "/billing/payment-methods",
    addPaymentMethod: "/billing/payment-methods",
    removePaymentMethod: (id: string) => `/billing/payment-methods/${id}`,
  },

  // Admin
  admin: {
    stats: "/admin/stats",
    tenants: "/admin/tenants",
    health: "/admin/health",
    auditLogs: "/admin/audit-logs",
    suspendTenant: (id: string) => `/admin/tenants/${id}/suspend`,
    reactivateTenant: (id: string) => `/admin/tenants/${id}/reactivate`,
    settings: "/admin/settings",
  },
};

// Request headers
export const headers = {
  common: {
    "Content-Type": "application/json",
    "Accept": "application/json",
  },
  
  multipart: {
    "Content-Type": "multipart/form-data",
  },
  
  auth: (token: string) => ({
    "Authorization": `Bearer ${token}`,
  }),
  
  tenant: (tenantId: string) => ({
    "X-Tenant-ID": tenantId,
  }),
  
  workspace: (workspaceId: string) => ({
    "X-Workspace-ID": workspaceId,
  }),
  
  team: (teamId: string) => ({
    "X-Team-ID": teamId,
  }),
};
