TITLE: Serve Registry Locally with Development Server
DESCRIPTION: This command starts the development server, typically for a Next.js application, which will serve the generated registry JSON files at a local URL like `http://localhost:3000/r/[NAME].json`.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/getting-started.mdx#_snippet_6

LANGUAGE: bash
CODE:
```
npm run dev
```

----------------------------------------

TITLE: Create Initial registry.json File
DESCRIPTION: This snippet shows the basic structure of the `registry.json` file, which is essential for `shadcn` CLI-based registries. It includes the schema reference, registry name, homepage, and an empty items array for components.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/getting-started.mdx#_snippet_0

LANGUAGE: json
CODE:
```
{
  "$schema": "https://ui.shadcn.com/schema/registry.json",
  "name": "acme",
  "homepage": "https://acme.com",
  "items": [
    // ...
  ]
}
```

----------------------------------------

TITLE: Add Component Definition to registry.json
DESCRIPTION: This JSON snippet demonstrates how to add a component's metadata to the `items` array within `registry.json`. It specifies the component's name, type, title, description, and the path and type of its associated file.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/getting-started.mdx#_snippet_2

LANGUAGE: json
CODE:
```
{
  "$schema": "https://ui.shadcn.com/schema/registry.json",
  "name": "acme",
  "homepage": "https://acme.com",
  "items": [
    {
      "name": "hello-world",
      "type": "registry:block",
      "title": "Hello World",
      "description": "A simple hello world component.",
      "files": [
        {
          "path": "registry/new-york/hello-world/hello-world.tsx",
          "type": "registry:component"
        }
      ]
    }
  ]
}
```

----------------------------------------

TITLE: Configure Build Script in package.json
DESCRIPTION: This JSON snippet shows how to add a `registry:build` script to the `scripts` section of `package.json`. This script uses the `shadcn build` command to generate the registry's JSON files.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/getting-started.mdx#_snippet_4

LANGUAGE: json
CODE:
```
{
  "scripts": {
    "registry:build": "shadcn build"
  }
}
```

----------------------------------------

TITLE: Define a Basic React Component for Registry
DESCRIPTION: This TypeScript React snippet defines a simple `HelloWorld` component using a `Button` from `@/components/ui/button`. It serves as an example of a component to be added to the registry.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/getting-started.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
import { Button } from "@/components/ui/button"

export function HelloWorld() {
  return <Button>Hello World</Button>
}
```

----------------------------------------

TITLE: Create a Sample React Component for Registry
DESCRIPTION: Demonstrates how to create a simple `HelloWorld` React component using TypeScript (TSX) and a shadcn-ui Button. This component serves as an example of an item to be included in the custom component registry.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/registry/getting-started.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
import { Button } from "@/components/ui/button"

export function HelloWorld() {
  return <Button>Hello World</Button>
}
```

----------------------------------------

TITLE: Add Component Definition to registry.json
DESCRIPTION: Illustrates how to extend the `registry.json` file by adding a new item definition for the `hello-world` component. This entry specifies the component's metadata (name, type, title, description) and the relative path to its source file within the project.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/registry/getting-started.mdx#_snippet_3

LANGUAGE: json
CODE:
```
{
  "$schema": "https://ui.shadcn.com/schema/registry.json",
  "name": "acme",
  "homepage": "https://acme.com",
  "items": [
    {
      "name": "hello-world",
      "type": "registry:block",
      "title": "Hello World",
      "description": "A simple hello world component.",
      "files": [
        {
          "path": "registry/new-york/hello-world/hello-world.tsx",
          "type": "registry:component"
        }
      ]
    }
  ]
}
```

----------------------------------------

TITLE: OpenAI API Capabilities Overview
DESCRIPTION: Describes the core functionalities of the OpenAI API, including text generation, natural language processing, and computer vision. It highlights its ability to generate text output from prompts, similar to ChatGPT.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/(root)/styleguide.mdx#_snippet_0

LANGUAGE: APIDOC
CODE:
```
The OpenAI API provides a simple interface to state-of-the-art AI models for text generation, natural language processing, computer vision, and more. This example generates text output from a prompt, as you might using ChatGPT.
```

----------------------------------------

TITLE: Serve Registry in Development Mode
DESCRIPTION: Starts the local development server for the project, typically using `npm run dev`. This makes the generated registry JSON files accessible via HTTP, allowing for local testing and consumption of the registry.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/registry/getting-started.mdx#_snippet_7

LANGUAGE: bash
CODE:
```
npm run dev
```

----------------------------------------

TITLE: Example Block File Structure
DESCRIPTION: This example demonstrates a typical file structure for a complex block, including a main page, components, hooks, and utility functions. Blocks can start with a single file and expand as needed.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/(root)/blocks.mdx#_snippet_5

LANGUAGE: txt
CODE:
```
dashboard-01
└── page.tsx
└── components
    └── hello-world.tsx
    └── example-card.tsx
└── hooks
    └── use-hello-world.ts
└── lib
    └── format-date.ts
```

----------------------------------------

TITLE: Example: Add alert-dialog Component
DESCRIPTION: A concrete example demonstrating how to add the 'alert-dialog' component to a shadcn UI project using the `add` command.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/packages/shadcn/README.md#_snippet_2

LANGUAGE: bash
CODE:
```
npx shadcn add alert-dialog
```

----------------------------------------

TITLE: SidebarProvider Component API Reference
DESCRIPTION: Detailed API documentation for the `SidebarProvider` component, outlining its configurable properties (`defaultOpen`, `open`, `onOpenChange`) for controlling the sidebar's open state and behavior.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/components/sidebar.mdx#_snippet_7

LANGUAGE: APIDOC
CODE:
```
SidebarProvider:
  Props:
    defaultOpen: boolean - Default open state of the sidebar.
    open: boolean - Open state of the sidebar (controlled).
    onOpenChange: (open: boolean) => void - Sets open state of the sidebar (controlled).
```

----------------------------------------

TITLE: Install Shadcn UI Registry Item using CLI
DESCRIPTION: Demonstrates how to add a registry item to your project using the `shadcn` CLI's `add` command, specifying the URL of the registry item. This command fetches and integrates the component into your local project.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/getting-started.mdx#_snippet_7

LANGUAGE: bash
CODE:
```
npx shadcn@latest add http://localhost:3000/r/hello-world.json
```

----------------------------------------

TITLE: useSidebar Hook API Reference
DESCRIPTION: Detailed API documentation for the `useSidebar` hook, outlining all available properties and their types, descriptions, and purposes for controlling sidebar behavior.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/components/sidebar.mdx#_snippet_20

LANGUAGE: APIDOC
CODE:
```
useSidebar Hook Properties:
  state: 'expanded' | 'collapsed' - The current state of the sidebar.
  open: boolean - Whether the sidebar is open.
  setOpen: (open: boolean) => void - Sets the open state of the sidebar.
  openMobile: boolean - Whether the sidebar is open on mobile.
  setOpenMobile: (open: boolean) => void - Sets the open state of the sidebar on mobile.
  isMobile: boolean - Whether the sidebar is on mobile.
  toggleSidebar: () => void - Toggles the sidebar. Desktop and mobile.
```

----------------------------------------

TITLE: Define Functional Custom Utilities in shadcn/ui
DESCRIPTION: Learn to create functional custom utility classes in shadcn/ui that accept dynamic values. This example shows defining a `tab-*` utility, allowing you to set `tab-size` based on a variable, providing flexible and scalable styling options.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/registry/examples.mdx#_snippet_12

LANGUAGE: json
CODE:
```
{
  "$schema": "https://ui.shadcn.com/schema/registry-item.json",
  "name": "custom-component",
  "type": "registry:component",
  "css": {
    "@utility tab-*": {
      "tab-size": "var(--tab-size-*)"
    }
  }
}
```

----------------------------------------

TITLE: Example shadcn/ui CLI configuration prompt
DESCRIPTION: Illustrates an example interaction during the shadcn/ui initialization process. This shows a typical question asked by the CLI to configure `components.json`.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/installation/remix.mdx#_snippet_2

LANGUAGE: bash
CODE:
```
Which color would you like to use as base color? › Neutral
```

----------------------------------------

TITLE: Initialize registry.json for Component Registry
DESCRIPTION: Sets up the foundational `registry.json` file at the project root, defining the schema, registry name, homepage, and an empty array for component items. This file is crucial for the `shadcn` CLI to build the registry.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/registry/getting-started.mdx#_snippet_0

LANGUAGE: json
CODE:
```
{
  "$schema": "https://ui.shadcn.com/schema/registry.json",
  "name": "acme",
  "homepage": "https://acme.com",
  "items": [
    // ...
  ]
}
```

----------------------------------------

TITLE: Execute Registry Build Command
DESCRIPTION: This command runs the `registry:build` script defined in `package.json`, initiating the process of generating the registry's JSON files, typically outputting them to `public/r`.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/getting-started.mdx#_snippet_5

LANGUAGE: bash
CODE:
```
npm run registry:build
```

----------------------------------------

TITLE: Example Commit Message Format
DESCRIPTION: An example demonstrating the `category(scope): message` commit convention used in the repository, specifically for adding a new property to a component.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/CONTRIBUTING.md#_snippet_13

LANGUAGE: text
CODE:
```
feat(components): add new prop to the avatar component
```

----------------------------------------

TITLE: Accessing Carousel API Instance in React
DESCRIPTION: This example demonstrates how to obtain and manage the CarouselApi instance using React's useState and useEffect hooks. It shows how to track the currently selected slide and the total number of slides by listening to the 'select' event.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/components/carousel.mdx#_snippet_10

LANGUAGE: tsx
CODE:
```
import { type CarouselApi } from "@/components/ui/carousel"

export function Example() {
  const [api, setApi] = React.useState<CarouselApi>()
  const [current, setCurrent] = React.useState(0)
  const [count, setCount] = React.useState(0)

  React.useEffect(() => {
    if (!api) {
      return
    }

    setCount(api.scrollSnapList().length)
    setCurrent(api.selectedScrollSnap() + 1)

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1)
    })
  }, [api])

  return (
    <Carousel setApi={setApi}>
      <CarouselContent>
        <CarouselItem>...</CarouselItem>
        <CarouselItem>...</CarouselItem>
        <CarouselItem>...</CarouselItem>
      </CarouselContent>
    </Carousel>
  )
}
```

----------------------------------------

TITLE: Example registry.json schema
DESCRIPTION: Demonstrates a complete `registry.json` file structure, including the schema reference, registry name, homepage, and a sample component item with its files.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/registry-json.mdx#_snippet_0

LANGUAGE: json
CODE:
```
{
  "$schema": "https://ui.shadcn.com/schema/registry.json",
  "name": "shadcn",
  "homepage": "https://ui.shadcn.com",
  "items": [
    {
      "name": "hello-world",
      "type": "registry:block",
      "title": "Hello World",
      "description": "A simple hello world component.",
      "files": [
        {
          "path": "registry/new-york/hello-world/hello-world.tsx",
          "type": "registry:component"
        }
      ]
    }
  ]
}
```

----------------------------------------

TITLE: Project Directory Structure Example
DESCRIPTION: Illustrates a typical monorepo project structure with separate `apps` for applications and `packages` for shared UI components and utilities.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/(root)/styleguide.mdx#_snippet_10

LANGUAGE: txt
CODE:
```
apps
└── web         # Your app goes here.
    ├── app
    │   └── page.tsx
    ├── components
    │   └── login-form.tsx
    ├── components.json
    └── package.json
packages
└── ui          # Your components and dependencies are installed here.
    ├── src
    │   ├── components
    │   │   └── button.tsx
    │   ├── hooks
    │   ├── lib
    │   │   └── utils.ts
    │   └── styles
    │       └── globals.css
    ├── components.json
    └── package.json
package.json
turbo.json
```

----------------------------------------

TITLE: Install shadcn/ui Block and Override Primitives
DESCRIPTION: Demonstrates how to install a block from the shadcn/ui registry and override its default primitives (button, input, label) with custom ones by referencing external JSON files.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/examples.mdx#_snippet_5

LANGUAGE: json
CODE:
```
{
  "$schema": "https://ui.shadcn.com/schema/registry-item.json",
  "name": "custom-login",
  "type": "registry:block",
  "registryDependencies": [
    "login-01",
    "https://example.com/r/button.json",
    "https://example.com/r/input.json",
    "https://example.com/r/label.json"
  ]
}
```

----------------------------------------

TITLE: SidebarProvider Component API Reference
DESCRIPTION: API documentation for the `SidebarProvider` component, detailing its purpose and available props for controlling the sidebar's open state. It should always wrap the application to provide sidebar context.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/components/sidebar.mdx#_snippet_6

LANGUAGE: APIDOC
CODE:
```
SidebarProvider:
  Description: Used to provide the sidebar context to the Sidebar component. You should always wrap your application in a SidebarProvider component.
  Props:
    defaultOpen:
      Type: boolean
      Description: Default open state of the sidebar.
    open:
      Type: boolean
      Description: Open state of the sidebar (controlled).
    onOpenChange:
      Type: (open: boolean) => void
      Description: Sets open state of the sidebar (controlled).
```

----------------------------------------

TITLE: Install shadcn CLI Canary Version
DESCRIPTION: This command installs the `shadcn` CLI at its canary version, which is required for accessing the `build` command used to generate registry JSON files.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/getting-started.mdx#_snippet_3

LANGUAGE: bash
CODE:
```
npm install shadcn@canary
```

----------------------------------------

TITLE: Define Simple Custom Utility in shadcn/ui
DESCRIPTION: Discover how to create a simple custom utility class within your shadcn/ui configuration. This example shows defining a utility like `content-auto` that directly maps to a CSS property, enhancing reusability and simplifying styling.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/registry/examples.mdx#_snippet_10

LANGUAGE: json
CODE:
```
{
  "$schema": "https://ui.shadcn.com/schema/registry-item.json",
  "name": "custom-component",
  "type": "registry:component",
  "css": {
    "@utility content-auto": {
      "content-visibility": "auto"
    }
  }
}
```

----------------------------------------

TITLE: Start Remix Development Server
DESCRIPTION: Command to start the Remix application in development mode, which typically includes live reloading and asset rebuilding on file changes.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/packages/shadcn/test/fixtures/frameworks/remix-indie-stack/README.md#_snippet_3

LANGUAGE: sh
CODE:
```
npm run dev
```

----------------------------------------

TITLE: Install shadcn/ui Block and Override Primitives
DESCRIPTION: Learn how to install a shadcn/ui block from the registry and simultaneously override its default primitives. This example demonstrates adding the 'login-01' block while replacing its 'button', 'input', and 'label' components with custom ones from a remote registry.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/registry/examples.mdx#_snippet_5

LANGUAGE: json
CODE:
```
{
  "$schema": "https://ui.shadcn.com/schema/registry-item.json",
  "name": "custom-login",
  "type": "registry:block",
  "registryDependencies": [
    "login-01",
    "https://example.com/r/button.json",
    "https://example.com/r/input.json",
    "https://example.com/r/label.json"
  ]
}
```

----------------------------------------

TITLE: Define Registry Build Script in package.json
DESCRIPTION: Adds a custom script named `registry:build` to the `scripts` section of `package.json`. This script executes the `shadcn build` command, streamlining the process of generating the registry's output files.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/registry/getting-started.mdx#_snippet_5

LANGUAGE: json
CODE:
```
{
  "scripts": {
    "registry:build": "shadcn build"
  }
}
```

----------------------------------------

TITLE: Open in v0 API Endpoint for Registry Integration
DESCRIPTION: Describes the API endpoint used to open a registry item in v0.dev by providing its public URL. This endpoint allows direct integration for hosted and publicly accessible registries.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/registry/open-in-v0.mdx#_snippet_0

LANGUAGE: APIDOC
CODE:
```
Endpoint:
  GET https://v0.dev/chat/api/open
Parameters:
  url: string (required)
    The URL of the registry item to open in v0.dev.
Example:
  https://v0.dev/chat/api/open?url=https://ui.shadcn.com/r/styles/new-york/login-01.json
```

----------------------------------------

TITLE: Add Simple Custom CSS Utility to shadcn/ui
DESCRIPTION: Shows how to define a simple custom CSS utility, like `content-auto`, using the `@utility` directive in your shadcn/ui configuration.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/examples.mdx#_snippet_10

LANGUAGE: json
CODE:
```
{
  "$schema": "https://ui.shadcn.com/schema/registry-item.json",
  "name": "custom-component",
  "type": "registry:component",
  "css": {
    "@utility content-auto": {
      "content-visibility": "auto"
    }
  }
}
```

----------------------------------------

TITLE: Install shadcn CLI Canary Version
DESCRIPTION: Installs the `shadcn` command-line interface using `npm`, specifically targeting the `canary` release. This version is required to access the `build` command, which is essential for generating the registry's JSON files.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/registry/getting-started.mdx#_snippet_4

LANGUAGE: bash
CODE:
```
npm install shadcn@canary
```

----------------------------------------

TITLE: Open in v0 Registry Integration API Endpoint
DESCRIPTION: This API endpoint allows opening a publicly accessible registry item directly within v0.dev. The `url` parameter must point to the JSON definition of the registry item.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/open-in-v0.mdx#_snippet_0

LANGUAGE: APIDOC
CODE:
```
Endpoint: GET https://v0.dev/chat/api/open
Parameters:
  url: string (required) - The URL of the publicly accessible registry item JSON.
```

----------------------------------------

TITLE: Add Functional Custom CSS Utility to shadcn/ui
DESCRIPTION: Illustrates how to define a functional custom CSS utility, like `tab-*`, which allows for dynamic values based on CSS variables.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/examples.mdx#_snippet_12

LANGUAGE: json
CODE:
```
{
  "$schema": "https://ui.shadcn.com/schema/registry-item.json",
  "name": "custom-component",
  "type": "registry:component",
  "css": {
    "@utility tab-*": {
      "tab-size": "var(--tab-size-*)"
    }
  }
}
```

----------------------------------------

TITLE: Shadcn/ui Registry Style: Create from Scratch
DESCRIPTION: This JSON configuration demonstrates creating a new `shadcn/ui` style without extending the default framework, indicated by `"extends": "none"`. It defines new dependencies (`tailwind-merge`, `clsx`), integrates remote components (`button`, `input`, `label`, `select`), and establishes a comprehensive set of custom CSS variables for various UI elements in both light and dark themes.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/examples.mdx#_snippet_1

LANGUAGE: json
CODE:
```
{
  "$schema": "https://ui.shadcn.com/schema/registry-item.json",
  "extends": "none",
  "name": "new-style",
  "type": "registry:style",
  "dependencies": ["tailwind-merge", "clsx"],
  "registryDependencies": [
    "utils",
    "https://example.com/r/button.json",
    "https://example.com/r/input.json",
    "https://example.com/r/label.json",
    "https://example.com/r/select.json"
  ],
  "cssVars": {
    "theme": {
      "font-sans": "Inter, sans-serif"
    },
    "light": {
      "main": "#88aaee",
      "bg": "#dfe5f2",
      "border": "#000",
      "text": "#000",
      "ring": "#000"
    },
    "dark": {
      "main": "#88aaee",
      "bg": "#272933",
      "border": "#000",
      "text": "#e6e6e6",
      "ring": "#fff"
    }
  }
}
```

----------------------------------------

TITLE: Run Registry Build Command
DESCRIPTION: Executes the `registry:build` script defined in `package.json` using `npm run`. This command triggers the `shadcn build` process, which compiles the registry components into their respective JSON files.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/registry/getting-started.mdx#_snippet_6

LANGUAGE: bash
CODE:
```
npm run registry:build
```

----------------------------------------

TITLE: Example: Complete Shadcn UI Registry Item Definition
DESCRIPTION: Illustrates the comprehensive structure of a `registry-item.json` file, showcasing metadata, associated files, and custom CSS variables for a 'Hello World' component.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/registry/registry-item-json.mdx#_snippet_0

LANGUAGE: json
CODE:
```
{
  "$schema": "https://ui.shadcn.com/schema/registry-item.json",
  "name": "hello-world",
  "type": "registry:block",
  "title": "Hello World",
  "description": "A simple hello world component.",
  "files": [
    {
      "path": "registry/new-york/hello-world/hello-world.tsx",
      "type": "registry:component"
    },
    {
      "path": "registry/new-york/hello-world/use-hello-world.ts",
      "type": "registry:hook"
    }
  ],
  "cssVars": {
    "theme": {
      "font-heading": "Poppins, sans-serif"
    },
    "light": {
      "brand": "20 14.3% 4.1%"
    },
    "dark": {
      "brand": "20 14.3% 4.1%"
    }
  }
}
```

----------------------------------------

TITLE: Basic Tooltip Usage Example in TSX
DESCRIPTION: Demonstrates a basic implementation of the Tooltip component, showing how to wrap content with TooltipTrigger and define the tooltip's content with TooltipContent. This example provides a foundational structure for displaying interactive tooltips.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/components/tooltip.mdx#_snippet_3

LANGUAGE: tsx
CODE:
```
<Tooltip>
  <TooltipTrigger>Hover</TooltipTrigger>
  <TooltipContent>
    <p>Add to library</p>
  </TooltipContent>
</Tooltip>
```

----------------------------------------

TITLE: Install shadcn/ui Registry Item via CLI
DESCRIPTION: This command demonstrates how to install a component from a custom shadcn/ui registry using the `npx shadcn@latest add` command. It requires the full URL of the registry item to be provided as an argument.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/registry/getting-started.mdx#_snippet_8

LANGUAGE: bash
CODE:
```
npx shadcn@latest add http://localhost:3000/r/hello-world.json
```

----------------------------------------

TITLE: Add Custom CSS Plugins to shadcn/ui
DESCRIPTION: Shows how to include external CSS plugins, such as `@tailwindcss/typography` or custom ones, within your shadcn/ui configuration using the `@plugin` directive.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/examples.mdx#_snippet_13

LANGUAGE: json
CODE:
```
{
  "$schema": "https://ui.shadcn.com/schema/registry-item.json",
  "name": "custom-plugin",
  "type": "registry:component",
  "css": {
    "@plugin @tailwindcss/typography": {},
    "@plugin foo": {}
  }
}
```

----------------------------------------

TITLE: Example: Providing Item Description
DESCRIPTION: Explains the `description` property, allowing for a more detailed explanation of the registry item's purpose and functionality.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/registry/registry-item-json.mdx#_snippet_4

LANGUAGE: json
CODE:
```
{
  "description": "A simple hello world component."
}
```

----------------------------------------

TITLE: React JSX Example for Open in v0 Button Usage
DESCRIPTION: A simple example demonstrating how to integrate the `OpenInV0Button` component into a React application, passing a sample URL to it.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/open-in-v0.mdx#_snippet_2

LANGUAGE: jsx
CODE:
```
<OpenInV0Button url="https://example.com/r/hello-world.json" />
```

----------------------------------------

TITLE: Accessing Carousel API and Tracking Slide State (React/TSX)
DESCRIPTION: This snippet demonstrates how to obtain the Carousel API instance using the `setApi` prop and `React.useState`. It then shows how to use the API to track the current active slide and total slide count, updating these states dynamically on 'select' events.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/components/carousel.mdx#_snippet_10

LANGUAGE: tsx
CODE:
```
import { type CarouselApi } from "@/components/ui/carousel"

export function Example() {
  const [api, setApi] = React.useState<CarouselApi>()
  const [current, setCurrent] = React.useState(0)
  const [count, setCount] = React.useState(0)

  React.useEffect(() => {
    if (!api) {
      return
    }

    setCount(api.scrollSnapList().length)
    setCurrent(api.selectedScrollSnap() + 1)

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1)
    })
  }, [api])

  return (
    <Carousel setApi={setApi}>
      <CarouselContent>
        <CarouselItem>...</CarouselItem>
        <CarouselItem>...</CarouselItem>
        <CarouselItem>...</CarouselItem>
      </CarouselContent>
    </Carousel>
  )
}
```

----------------------------------------

TITLE: Listening to Carousel Events (React/TSX)
DESCRIPTION: This example illustrates how to subscribe to specific events emitted by the carousel, such as the 'select' event, using the API instance. The `api.on("select", ...)` method allows executing custom logic when a slide selection changes.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/components/carousel.mdx#_snippet_11

LANGUAGE: tsx
CODE:
```
import { type CarouselApi } from "@/components/ui/carousel"

export function Example() {
  const [api, setApi] = React.useState<CarouselApi>()

  React.useEffect(() => {
    if (!api) {
      return
    }

    api.on("select", () => {
      // Do something on select.
    })
  }, [api])

  return (
    <Carousel setApi={setApi}>
      <CarouselContent>
        <CarouselItem>...</CarouselItem>
        <CarouselItem>...</CarouselItem>
        <CarouselItem>...</CarouselItem>
      </CarouselContent>
    </Carousel>
  )
}
```

----------------------------------------

TITLE: Start shadcn/ui Registry for CLI Development
DESCRIPTION: Before testing the CLI locally, it is crucial to run the registry to ensure components are up to date. This command starts the development server for the registry, which the CLI interacts with.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/CONTRIBUTING.md#_snippet_7

LANGUAGE: bash
CODE:
```
pnpm v4:dev
```

----------------------------------------

TITLE: Configure Tailwind CSS to Scan Registry Directory
DESCRIPTION: Modifies the `tailwind.config.ts` file to include the `registry` directory in its content paths. This ensures that Tailwind CSS correctly processes and includes utility classes used within components located in the registry.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/registry/getting-started.mdx#_snippet_2

LANGUAGE: ts
CODE:
```
export default {
  content: ["./registry/**/*.{js,ts,jsx,tsx}"],
}
```

----------------------------------------

TITLE: Create New Remix Indie Stack Project
DESCRIPTION: Command to initialize a new Remix project using the Indie Stack template, setting up the basic application structure.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/packages/shadcn/test/fixtures/frameworks/remix-indie-stack/README.md#_snippet_0

LANGUAGE: sh
CODE:
```
npx create-remix@latest --template remix-run/indie-stack
```

----------------------------------------

TITLE: Define registry items in registry.json
DESCRIPTION: Provides an example of the `items` array within `registry.json`, showcasing how to list individual components, each conforming to the `registry-item` schema specification.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/registry-json.mdx#_snippet_4

LANGUAGE: json
CODE:
```
{
  "items": [
    {
      "name": "hello-world",
      "type": "registry:block",
      "title": "Hello World",
      "description": "A simple hello world component.",
      "files": [
        {
          "path": "registry/new-york/hello-world/hello-world.tsx",
          "type": "registry:component"
        }
      ]
    }
  ]
}
```

----------------------------------------

TITLE: Example: Specifying JSON Schema URL
DESCRIPTION: Demonstrates how to include the `$schema` property to link the `registry-item.json` file to its official schema definition for validation.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/registry/registry-item-json.mdx#_snippet_1

LANGUAGE: json
CODE:
```
{
  "$schema": "https://ui.shadcn.com/schema/registry-item.json"
}
```

----------------------------------------

TITLE: Basic Collapsible Component Implementation Example
DESCRIPTION: Demonstrates a fundamental usage pattern for the Collapsible component, showing how to structure the trigger and content for an expandable UI element.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/components/collapsible.mdx#_snippet_3

LANGUAGE: tsx
CODE:
```
<Collapsible>
  <CollapsibleTrigger>Can I use this in my project?</CollapsibleTrigger>
  <CollapsibleContent>
    Yes. Free to use for personal and commercial projects. No attribution
    required.
  </CollapsibleContent>
</Collapsible>
```

----------------------------------------

TITLE: Complete registry.json Schema Example
DESCRIPTION: Illustrates the full structure of a `registry.json` file, defining a custom component registry with a sample 'hello-world' item. This schema is essential for organizing and serving UI components.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/registry/registry-json.mdx#_snippet_0

LANGUAGE: json
CODE:
```
{
  "$schema": "https://ui.shadcn.com/schema/registry.json",
  "name": "shadcn",
  "homepage": "https://ui.shadcn.com",
  "items": [
    {
      "name": "hello-world",
      "type": "registry:block",
      "title": "Hello World",
      "description": "A simple hello world component.",
      "files": [
        {
          "path": "registry/new-york/hello-world/hello-world.tsx",
          "type": "registry:component"
        }
      ]
    }
  ]
}
```

----------------------------------------

TITLE: Add Complex Custom CSS Utility to shadcn/ui
DESCRIPTION: Demonstrates how to define a more complex custom CSS utility, such as `scrollbar-hidden`, which includes nested CSS rules for browser-specific scrollbar hiding.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/examples.mdx#_snippet_11

LANGUAGE: json
CODE:
```
{
  "$schema": "https://ui.shadcn.com/schema/registry-item.json",
  "name": "custom-component",
  "type": "registry:component",
  "css": {
    "@utility scrollbar-hidden": {
      "scrollbar-hidden": {
        "&::-webkit-scrollbar": {
          "display": "none"
        }
      }
    }
  }
}
```

----------------------------------------

TITLE: Define Custom Component CSS Styles for shadcn/ui
DESCRIPTION: Illustrates how to add custom CSS styles for specific components, such as a 'card', using the `@layer components` directive in your shadcn/ui configuration.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/examples.mdx#_snippet_9

LANGUAGE: json
CODE:
```
{
  "$schema": "https://ui.shadcn.com/schema/registry-item.json",
  "name": "custom-card",
  "type": "registry:component",
  "css": {
    "@layer components": {
      "card": {
        "background-color": "var(--color-white)",
        "border-radius": "var(--rounded-lg)",
        "padding": "var(--spacing-6)",
        "box-shadow": "var(--shadow-xl)"
      }
    }
  }
}
```

----------------------------------------

TITLE: `useSidebar` Hook Properties API Documentation
DESCRIPTION: Detailed properties available from the `useSidebar` hook, including their types and descriptions for controlling sidebar behavior.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/components/sidebar.mdx#_snippet_20

LANGUAGE: APIDOC
CODE:
```
useSidebar Hook Properties:
  state: string ('expanded' or 'collapsed')
    Description: The current state of the sidebar.
  open: boolean
    Description: Whether the sidebar is open.
  setOpen: (open: boolean) => void
    Description: Sets the open state of the sidebar.
  openMobile: boolean
    Description: Whether the sidebar is open on mobile.
  setOpenMobile: (open: boolean) => void
    Description: Sets the open state of the sidebar on mobile.
  isMobile: boolean
    Description: Whether the sidebar is on mobile.
  toggleSidebar: () => void
    Description: Toggles the sidebar. Desktop and mobile.
```

----------------------------------------

TITLE: Example: Declaring NPM Package Dependencies
DESCRIPTION: Illustrates the `dependencies` property, an array used to list required `npm` packages, optionally specifying versions for precise control.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/registry/registry-item-json.mdx#_snippet_7

LANGUAGE: json
CODE:
```
{
  "dependencies": [
    "@radix-ui/react-accordion",
    "zod",
    "lucide-react",
    "name@1.0.2"
  ]
}
```

----------------------------------------

TITLE: Pass Options to Shadcn UI Carousel
DESCRIPTION: This example demonstrates how to pass configuration options to the underlying Embla Carousel instance via the `opts` prop. This allows for customization of behavior such as alignment and looping, as defined by the Embla Carousel API.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/components/carousel.mdx#_snippet_9

LANGUAGE: tsx
CODE:
```
<Carousel
  opts={{
    align: "start",
    loop: true,
  }}
>
  <CarouselContent>
    <CarouselItem>...</CarouselItem>
    <CarouselItem>...</CarouselItem>
    <CarouselItem>...</CarouselItem>
  </CarouselContent>
</Carousel>
```

----------------------------------------

TITLE: New Block Directory Structure Example
DESCRIPTION: This example illustrates the required directory structure for a new block within the `apps/www/registry/new-york/blocks` path. It's crucial that the block folder is named in kebab-case for proper recognition.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/(root)/blocks.mdx#_snippet_4

LANGUAGE: txt
CODE:
```
apps
└── www
    └── registry
        └── new-york
            └── blocks
                └── dashboard-01
```

----------------------------------------

TITLE: Render Basic Menubar Component Example in TSX
DESCRIPTION: Provides a complete example of how to structure and render a functional Menubar component with a 'File' menu. It demonstrates the use of MenubarTrigger, MenubarContent, MenubarItem, MenubarSeparator, and MenubarShortcut to create an interactive menu.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/components/menubar.mdx#_snippet_3

LANGUAGE: tsx
CODE:
```
<Menubar>
  <MenubarMenu>
    <MenubarTrigger>File</MenubarTrigger>
    <MenubarContent>
      <MenubarItem>
        New Tab <MenubarShortcut>⌘T</MenubarShortcut>
      </MenubarItem>
      <MenubarItem>New Window</MenubarItem>
      <MenubarSeparator />
      <MenubarItem>Share</MenubarItem>
      <MenubarSeparator />
      <MenubarItem>Print</MenubarItem>
    </MenubarContent>
  </MenubarMenu>
</Menubar>
```

----------------------------------------

TITLE: Add Custom CSS Animations to shadcn/ui
DESCRIPTION: Demonstrates how to define custom CSS animations using `@keyframes` and link them to theme variables for use in shadcn/ui components. Requires both `cssVars` and `css` definitions.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/v4/content/docs/registry/examples.mdx#_snippet_14

LANGUAGE: json
CODE:
```
{
  "$schema": "https://ui.shadcn.com/schema/registry-item.json",
  "name": "custom-component",
  "type": "registry:component",
  "cssVars": {
    "theme": {
      "--animate-wiggle": "wiggle 1s ease-in-out infinite"
    }
  },
  "css": {
    "@keyframes wiggle": {
      "0%, 100%": {
        "transform": "rotate(-3deg)"
      },
      "50%": {
        "transform": "rotate(3deg)"
      }
    }
  }
}
```

----------------------------------------

TITLE: shadcn/ui Component Registry Structure
DESCRIPTION: This snippet illustrates the directory structure within the `apps/www/registry` path, showing how components are organized by styles (e.g., `default`, `new-york`) and further by `example` and `ui` subdirectories.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/CONTRIBUTING.md#_snippet_12

LANGUAGE: bash
CODE:
```
apps
└── www
    └── registry
        ├── default
        │   ├── example
        │   └── ui
        └── new-york
            ├── example
            └── ui
```

----------------------------------------

TITLE: Import app.css into TanStack Start Root Component
DESCRIPTION: Modify your `app/routes/__root.tsx` file to import the `app.css` stylesheet. This ensures that your global styles, including Tailwind CSS, are loaded and applied across your TanStack Start application.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/installation/tanstack.mdx#_snippet_3

LANGUAGE: tsx
CODE:
```
import type { ReactNode } from "react"
import { Outlet, createRootRoute } from "@tanstack/react-router"
import { Meta, Scripts } from "@tanstack/start"

import appCss from "@/styles/app.css?url"

export const Route = createRootRoute({
  head: () => ({
    meta: [
      {
        charSet: "utf-8",
      },
      {
        name: "viewport",
        content: "width=device-width, initial-scale=1",
      },
      {
        title: "TanStack Start Starter",
      },
    ],
    links: [
      {
        rel: "stylesheet",
        href: appCss,
      },
    ],
  }),
  component: RootComponent,
})
```

----------------------------------------

TITLE: Example of shadcn/ui Init Command Output
DESCRIPTION: Illustrates a typical interactive prompt encountered during the `shadcn/ui` initialization process, specifically asking for the base color preference.
SOURCE: https://github.com/shadcn-ui/ui/blob/main/apps/www/content/docs/installation/vite.mdx#_snippet_8

LANGUAGE: txt
CODE:
```
Which color would you like to use as base color? › Neutral
```