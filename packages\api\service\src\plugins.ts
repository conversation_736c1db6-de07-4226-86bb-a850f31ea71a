import { FastifyInstance } from "fastify";
import { authPlugin } from "./middleware/auth";
import { rateLimitPlugin } from "./middleware/rate-limit";
import { swaggerPlugin } from "./plugins/swagger";
import { corsPlugin } from "./plugins/cors";
import { helmetPlugin } from "./plugins/helmet";
import { multipartPlugin } from "./plugins/multipart";
import { staticPlugin } from "./plugins/static";

export const registerPlugins = async (fastify: FastifyInstance) => {
  // Security plugins
  await fastify.register(helmetPlugin);
  await fastify.register(corsPlugin);
  
  // Rate limiting
  await fastify.register(rateLimitPlugin);
  
  // Authentication
  await fastify.register(authPlugin);
  
  // File handling
  await fastify.register(multipartPlugin);
  await fastify.register(staticPlugin);
  
  // Documentation
  await fastify.register(swaggerPlugin);
};
