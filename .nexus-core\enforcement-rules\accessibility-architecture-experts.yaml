# Accessibility, Architecture & Expert Panel Protocols
# Additional critical rules from CRM_DEVELOPMENT_RULES_2.md and CRM_DEVELOPMENT_RULES.md

accessibility_compliance:
  interactive_component_accessibility:
    rule: "ALL interactive components must be fully accessible with ARIA support"
    enforcement:
      - aria_labels: true
      - aria_descriptions: true
      - keyboard_navigation: true
      - screen_reader_support: true
      - touch_mouse_support: true
    implementation:
      - aria_implementation: "Add ARIA labels and descriptions to all interactive elements"
      - keyboard_support: "Implement keyboard navigation for all interactive components"
      - screen_reader_instructions: "Provide clear screen reader instructions"
      - multi_input_support: "Support both touch and mouse interactions"
    validation:
      - accessibility_audit: "axe-core accessibility testing"
      - keyboard_testing: "Test all keyboard interactions"
      - screen_reader_testing: "Test with screen readers"
      - wcag_compliance: "WCAG 2.1 AA compliance validation"
    
  wcag_compliance:
    rule: "Maintain WCAG 2.1 AA compliance across all components"
    enforcement:
      - color_contrast: "4.5:1 minimum ratio"
      - focus_indicators: "Visible focus indicators"
      - semantic_html: "Proper semantic HTML structure"
      - alt_text: "Meaningful alt text for images"
    implementation:
      - contrast_checking: "Automated color contrast validation"
      - focus_management: "Proper focus management"
      - semantic_structure: "Use semantic HTML elements"
      - image_accessibility: "Comprehensive alt text strategy"
    validation:
      - contrast_audit: "Color contrast ratio validation"
      - focus_audit: "Focus indicator testing"
      - semantic_audit: "HTML semantic validation"
      - image_audit: "Alt text coverage check"

css_architecture:
  tailwind_compliance:
    rule: "Use utility-first approach, avoid complex global CSS"
    enforcement:
      - utility_first: true
      - minimal_globals: true
      - no_important_declarations: true
      - component_specific_utilities: true
    implementation:
      - globals_minimization: "Minimal globals.css with theme tokens only"
      - utility_usage: "Prefer Tailwind utilities over custom CSS"
      - important_avoidance: "No !important declarations"
      - component_utilities: "Component-specific utilities only when necessary"
    validation:
      - global_css_audit: "Audit globals.css for bloat"
      - important_check: "grep -r '!important' src/ --exclude-dir=node_modules"
      - utility_coverage: "Maximize Tailwind utility usage"
    
  css_organization:
    rule: "Maintain clean, scalable CSS architecture"
    enforcement:
      - component_scoped_styles: true
      - css_modules_when_needed: true
      - no_css_in_js_bloat: true
      - performance_conscious_styles: true
    implementation:
      - scoped_styling: "Component-scoped styles when custom CSS needed"
      - module_usage: "CSS modules for component-specific styles"
      - performance_optimization: "Optimize CSS for performance"
    validation:
      - css_bundle_size: "Monitor CSS bundle size"
      - unused_css_check: "PurgeCSS unused style detection"

expert_panel_protocol:
  six_expert_synthesis:
    rule: "Implement 6-expert synthesis protocol for critical decisions"
    enforcement:
      - silent_generation: true
      - silent_analysis: true
      - synthesis_voting: true
      - master_solution: true
    implementation:
      - phase_1_silent_generation: "Council of Masters approach - independent solution generation"
      - phase_2_silent_analysis: "Automated audit and cross-critique of solutions"
      - phase_3_synthesis: "Voting mechanism and master solution creation"
      - expert_roles: "Define specific expert roles (architect, security, performance, accessibility, etc.)"
    validation:
      - decision_quality: "Track decision quality metrics"
      - expert_consensus: "Measure expert agreement rates"
      - solution_effectiveness: "Validate solution effectiveness"
    
  decision_framework:
    rule: "Systematic decision-making for architectural choices"
    enforcement:
      - evidence_based_decisions: true
      - performance_impact_analysis: true
      - security_impact_assessment: true
      - scalability_evaluation: true
    implementation:
      - decision_templates: "Standardized decision templates"
      - impact_analysis: "Comprehensive impact analysis framework"
      - decision_documentation: "Document all architectural decisions"
    validation:
      - decision_audit: "Audit decision quality and outcomes"
      - impact_tracking: "Track decision impact over time"

naming_conventions:
  file_naming:
    rule: "Use kebab-case for cross-platform compatibility"
    enforcement:
      - kebab_case_files: true
      - cross_platform_compatibility: true
      - consistent_naming: true
    implementation:
      - file_naming_standard: "All files use kebab-case naming"
      - directory_structure: "Consistent directory naming"
      - asset_naming: "Consistent asset naming convention"
    validation:
      - naming_audit: "Audit file naming consistency"
      - case_sensitivity_check: "Check for case sensitivity issues"
    
  component_naming:
    rule: "PascalCase components, camelCase functions"
    enforcement:
      - pascal_case_components: true
      - camel_case_functions: true
      - descriptive_names: true
    implementation:
      - component_naming: "PascalCase for all React components"
      - function_naming: "camelCase for functions and variables"
      - descriptive_naming: "Use descriptive, meaningful names"
    validation:
      - naming_convention_check: "eslint naming convention rules"
      - name_clarity_audit: "Audit name clarity and meaning"
    
  import_export_patterns:
    rule: "Consistent import organization and export patterns"
    enforcement:
      - organized_imports: true
      - named_exports_preferred: true
      - barrel_exports: true
    implementation:
      - import_sorting: "Sort imports by type and source"
      - export_consistency: "Consistent export patterns"
      - barrel_index_files: "Use barrel exports for clean imports"
    validation:
      - import_organization_check: "eslint import organization rules"
      - export_pattern_audit: "Audit export pattern consistency"

metadata_strategy:
  page_metadata_management:
    rule: "Centralized metadata management with usePageMetadata hook"
    enforcement:
      - centralized_metadata: true
      - seo_optimization: true
      - professional_polish: true
    implementation:
      - metadata_hook: "src/hooks/usePageMetadata.ts"
      - seo_strategy: "SaaS-first approach with selective SEO"
      - tab_titles: "Clean tab titles and context"
    validation:
      - metadata_coverage: "All pages have proper metadata"
      - seo_audit: "SEO optimization validation"
    
  seo_strategy:
    rule: "SaaS-first approach with selective SEO optimization"
    enforcement:
      - saas_first_approach: true
      - selective_seo: true
      - performance_conscious: true
    implementation:
      - meta_tags: "Comprehensive meta tag strategy"
      - structured_data: "Structured data for relevant pages"
      - performance_optimization: "SEO without performance compromise"
    validation:
      - seo_performance: "SEO impact on performance"
      - search_visibility: "Track search visibility metrics"

testing_quality:
  comprehensive_testing:
    rule: "80% minimum test coverage with comprehensive testing standards"
    enforcement:
      - coverage_minimum: "80%"
      - unit_testing: true
      - integration_testing: true
      - accessibility_testing: true
    implementation:
      - testing_framework: "Jest + React Testing Library"
      - coverage_reporting: "Comprehensive coverage reporting"
      - accessibility_tests: "Automated accessibility testing"
    validation:
      - coverage_check: "jest --coverage --coverageThreshold=80"
      - test_quality_audit: "Audit test quality and effectiveness"
    
  performance_testing:
    rule: "Core Web Vitals monitoring and performance testing"
    enforcement:
      - core_web_vitals: true
      - performance_budgets: true
      - lighthouse_audits: true
    implementation:
      - vitals_monitoring: "Track LCP, CLS, FID metrics"
      - performance_budgets: "Set and enforce performance budgets"
      - lighthouse_ci: "Automated Lighthouse audits"
    validation:
      - vitals_tracking: "Core Web Vitals performance tracking"
      - budget_enforcement: "Performance budget enforcement"

crm_specific_patterns:
  contact_management:
    rule: "Implement MLM-specific contact management patterns"
    enforcement:
      - prospect_patterns: true
      - customer_patterns: true
      - team_member_patterns: true
    implementation:
      - contact_types: "Prospect/Customer/Team-member type definitions"
      - relationship_modeling: "MLM relationship modeling"
      - status_tracking: "Contact status and progression tracking"
    validation:
      - pattern_consistency: "Validate CRM pattern consistency"
      - relationship_integrity: "Validate relationship data integrity"
    
  pipeline_management:
    rule: "MLM-specific workflow stages and pipeline management"
    enforcement:
      - workflow_stages: true
      - progression_tracking: true
      - automation_rules: true
    implementation:
      - stage_definitions: "Define MLM workflow stages"
      - progression_logic: "Implement progression tracking"
      - automation_framework: "Automated workflow rules"
    validation:
      - workflow_validation: "Validate workflow logic"
      - automation_testing: "Test automation rules"
    
  team_performance:
    rule: "MLM business metrics and team performance visualization"
    enforcement:
      - performance_metrics: true
      - visualization_components: true
      - real_time_updates: true
    implementation:
      - metrics_definition: "Define MLM performance metrics"
      - dashboard_components: "Team performance dashboards"
      - real_time_data: "Real-time performance updates"
    validation:
      - metrics_accuracy: "Validate metric calculations"
      - visualization_testing: "Test visualization components"

migration_priorities:
  immediate_fixes:
    - console_logging_production: "Remove console logging in production"
    - type_safety_issues: "Fix Record<string, unknown> and unsafe types"
    - hardcoded_mock_data: "Eliminate hardcoded mock data"
    - css_architecture: "Implement utility-first Tailwind approach"
    - algorithmic_complexity: "Optimize O(n²) algorithms to O(n log n)"
    - accessibility_compliance: "Add ARIA support to all interactive components"
    - security_headers: "Implement comprehensive security headers"
    
ui_ux_patterns:
  shadcn_radix_integration:
    rule: "Implement Shadcn/ui + Radix UI patterns for accessible components"
    enforcement:
      - shadcn_base_system: true
      - radix_accessibility: true
      - customization_consistency: true
      - design_token_system: true
    implementation:
      - shadcn_usage: "Use shadcn/ui as base component system"
      - css_customization: "Customize through CSS variables"
      - variant_extension: "Extend with additional variants"
      - design_tokens: "Maintain consistent design tokens"
      - radix_primitives: "Leverage Radix primitives for accessibility"
      - keyboard_navigation: "Built-in keyboard navigation"
      - screen_reader_optimization: "Screen reader optimization"
      - focus_management: "Proper focus management"
    validation:
      - component_accessibility: "Validate component accessibility"
      - design_consistency: "Check design token consistency"
      - integration_quality: "Shadcn/Radix integration quality"
    
  tailwind_architecture:
    rule: "Implement Tailwind CSS 4.1.11+ architecture patterns"
    enforcement:
      - utility_first_approach: true
      - container_queries: true
      - design_system_consistency: true
      - responsive_mobile_first: true
    implementation:
      - utility_classes: "Utility classes over custom CSS"
      - spacing_consistency: "Consistent spacing and sizing"
      - mobile_first_responsive: "Responsive design mobile-first"
      - component_composition: "Component composition patterns"
      - container_queries: "Component-responsive design with container queries"
      - intrinsic_design: "Intrinsic web design patterns"
      - flexible_layouts: "Flexible layout systems"
    validation:
      - utility_coverage: "Maximize Tailwind utility usage"
      - responsive_testing: "Test responsive design patterns"
      - container_query_effectiveness: "Container query implementation"
    
  design_system_patterns:
    rule: "Implement comprehensive design system patterns"
    enforcement:
      - color_system_semantic: true
      - typography_consistency: true
      - spacing_harmony: true
      - animation_standards: true
    implementation:
      - semantic_colors: "Color system with semantic naming"
      - typography_scale: "Typography scale consistency"
      - spacing_system: "Spacing system harmony"
      - animation_transitions: "Animation and transition standards"
      - theme_configuration: "Theme configuration via CSS variables"
      - variant_system: "Component variant system"
      - dark_mode_support: "Built-in dark mode support"
    validation:
      - design_system_audit: "Design system consistency audit"
      - color_contrast_check: "Color contrast validation"
      - typography_validation: "Typography system validation"

validation_commands:
  accessibility_validation:
    - accessibility_audit: "axe-core accessibility testing"
    - keyboard_testing: "Test all keyboard interactions"
    - screen_reader_testing: "Test with screen readers"
    - wcag_compliance: "WCAG 2.1 AA compliance validation"
    
  architecture_validation:
    - component_structure_audit: "Component architecture validation"
    - naming_convention_check: "eslint naming convention rules"
    - expert_consensus: "6-expert synthesis validation"
    - pattern_consistency: "Architecture pattern consistency"
    
  ui_ux_validation:
    - design_system_audit: "Design system consistency validation"
    - component_accessibility: "Component accessibility testing"
    - responsive_testing: "Responsive design validation"
    - interaction_testing: "User interaction testing"
    
  crm_validation:
    - contact_pattern_validation: "CRM contact pattern validation"
    - workflow_validation: "MLM workflow validation"
    - performance_metrics: "CRM performance metrics validation"

expert_validation:
  rule: "Every change must be validated by virtual expert panel"
  enforcement:
    - cot_tot_methodology: true
    - expert_critique: true
    - iterative_refinement: true
    - consensus_requirement: true
  implementation:
    - expert_simulation: "Simulate 10 senior developers"
    - critique_assessment: "Performance, best practices, edge cases"
    - iterative_process: "Keep iterating until 100% agreement"
    - final_validation: "Comprehensive validation before implementation"
  validation:
    - expert_consensus: "All experts must agree"
    - comprehensive_review: "Consider all aspects: performance, scalability, security"
    - edge_case_coverage: "All edge cases considered"
    - lint_tsc_final: "Final lint/tsc check before deployment"