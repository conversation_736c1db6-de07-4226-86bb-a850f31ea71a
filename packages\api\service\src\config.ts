import { z } from "zod";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Configuration schema
const configSchema = z.object({
  // Server
  NODE_ENV: z.enum(["development", "production", "test"]).default("development"),
  PORT: z.string().transform(Number).default("3001"),
  HOST: z.string().default("0.0.0.0"),
  
  // Database
  DATABASE_URL: z.string(),
  
  // JWT
  JWT_SECRET: z.string(),
  JWT_EXPIRES_IN: z.string().default("24h"),
  JWT_REFRESH_EXPIRES_IN: z.string().default("7d"),
  
  // Rate Limiting
  RATE_LIMIT_MAX: z.string().transform(Number).default("100"),
  RATE_LIMIT_WINDOW: z.string().default("15m"),
  
  // CORS
  CORS_ORIGIN: z.string().default("*"),
  
  // Logging
  LOG_LEVEL: z.enum(["fatal", "error", "warn", "info", "debug", "trace"]).default("info"),
  
  // File Upload
  MAX_FILE_SIZE: z.string().transform(Number).default("10485760"), // 10MB
  UPLOAD_DIR: z.string().default("./uploads"),
  
  // Redis (optional)
  REDIS_URL: z.string().optional(),
  
  // Monitoring
  ENABLE_METRICS: z.string().transform(Boolean).default("true"),
  METRICS_PORT: z.string().transform(Number).default("9090"),
  
  // Security
  ENABLE_HELMET: z.string().transform(Boolean).default("true"),
  ENABLE_RATE_LIMIT: z.string().transform(Boolean).default("true"),
  
  // API Documentation
  ENABLE_SWAGGER: z.string().transform(Boolean).default("true"),
  SWAGGER_PATH: z.string().default("/docs"),
});

// Parse and validate configuration
const parseConfig = () => {
  try {
    return configSchema.parse(process.env);
  } catch (error) {
    console.error("Configuration validation failed:", error);
    process.exit(1);
  }
};

export const config = parseConfig();

// Type for configuration
export type Config = z.infer<typeof configSchema>;

// Environment helpers
export const isDevelopment = config.NODE_ENV === "development";
export const isProduction = config.NODE_ENV === "production";
export const isTest = config.NODE_ENV === "test";
