import { EventEmitter } from "events";
import { v4 as uuidv4 } from "uuid";
import { 
  AnalyticsEvent, 
  AnalyticsQuery, 
  AnalyticsResult, 
  SearchAnalytics,
  AnalyticsEventType 
} from "./types";
import { ElasticsearchService } from "./elasticsearch";

export class AnalyticsService extends EventEmitter {
  private elasticsearch: ElasticsearchService;
  private eventBuffer: AnalyticsEvent[] = [];
  private flushInterval: NodeJS.Timeout;
  private batchSize: number = 100;
  private flushIntervalMs: number = 5000;

  constructor(elasticsearch: ElasticsearchService) {
    super();
    this.elasticsearch = elasticsearch;
    
    // Start flush interval
    this.flushInterval = setInterval(() => {
      this.flushEvents();
    }, this.flushIntervalMs);
  }

  // Track event
  async trackEvent(params: {
    type: AnalyticsEventType;
    tenantId: string;
    workspaceId?: string;
    projectId?: string;
    userId?: string;
    sessionId?: string;
    properties?: Record<string, any>;
    context?: {
      userAgent?: string;
      ip?: string;
      referrer?: string;
      page?: string;
    };
  }): Promise<void> {
    const event: AnalyticsEvent = {
      id: uuidv4(),
      type: params.type,
      tenantId: params.tenantId,
      workspaceId: params.workspaceId,
      projectId: params.projectId,
      userId: params.userId,
      sessionId: params.sessionId,
      properties: params.properties || {},
      context: {
        ...params.context,
        timestamp: new Date(),
      },
      createdAt: new Date(),
    };

    // Add to buffer
    this.eventBuffer.push(event);

    // Flush if buffer is full
    if (this.eventBuffer.length >= this.batchSize) {
      await this.flushEvents();
    }

    // Emit event for real-time processing
    this.emit("event:tracked", event);
  }

  // Track search query
  async trackSearchQuery(params: {
    query: string;
    tenantId: string;
    workspaceId?: string;
    projectId?: string;
    userId?: string;
    sessionId?: string;
    resultsCount: number;
    executionTime: number;
    filters?: Record<string, any>;
    context?: {
      userAgent?: string;
      ip?: string;
      referrer?: string;
      page?: string;
    };
  }): Promise<void> {
    await this.trackEvent({
      type: "search_query",
      tenantId: params.tenantId,
      workspaceId: params.workspaceId,
      projectId: params.projectId,
      userId: params.userId,
      sessionId: params.sessionId,
      properties: {
        query: params.query,
        resultsCount: params.resultsCount,
        executionTime: params.executionTime,
        filters: params.filters,
        hasResults: params.resultsCount > 0,
        queryLength: params.query.length,
        wordCount: params.query.split(/\s+/).length,
      },
      context: params.context,
    });
  }

  // Track search click
  async trackSearchClick(params: {
    query: string;
    documentId: string;
    documentType: string;
    position: number;
    tenantId: string;
    workspaceId?: string;
    projectId?: string;
    userId?: string;
    sessionId?: string;
    context?: {
      userAgent?: string;
      ip?: string;
      referrer?: string;
      page?: string;
    };
  }): Promise<void> {
    await this.trackEvent({
      type: "search_click",
      tenantId: params.tenantId,
      workspaceId: params.workspaceId,
      projectId: params.projectId,
      userId: params.userId,
      sessionId: params.sessionId,
      properties: {
        query: params.query,
        documentId: params.documentId,
        documentType: params.documentType,
        position: params.position,
        clickedInTopThree: params.position <= 3,
        clickedInTopTen: params.position <= 10,
      },
      context: params.context,
    });
  }

  // Query analytics
  async queryAnalytics(query: AnalyticsQuery): Promise<AnalyticsResult> {
    const startTime = Date.now();
    
    try {
      const searchBody = this.buildAnalyticsQuery(query);
      
      const response = await this.elasticsearch.searchDocuments({
        query: "",
        filters: {
          tenantId: query.tenantId,
          workspaceId: query.workspaceId,
          projectId: query.projectId,
          dateRange: {
            field: "createdAt",
            start: query.dateRange.start,
            end: query.dateRange.end,
          },
        },
        aggregations: this.buildAnalyticsAggregations(query),
      });

      return this.parseAnalyticsResponse(response, query, Date.now() - startTime);
    } catch (error) {
      console.error("Analytics query failed:", error);
      throw error;
    }
  }

  // Get search analytics
  async getSearchAnalytics(params: {
    tenantId: string;
    workspaceId?: string;
    projectId?: string;
    dateRange: { start: Date; end: Date };
  }): Promise<SearchAnalytics> {
    const { tenantId, workspaceId, projectId, dateRange } = params;

    // Query for search events
    const searchQuery: AnalyticsQuery = {
      eventTypes: ["search_query", "search_click"],
      tenantId,
      workspaceId,
      projectId,
      dateRange,
      groupBy: ["properties.query"],
      metrics: [
        { name: "totalQueries", type: "count" },
        { name: "uniqueUsers", type: "unique", field: "userId" },
        { name: "avgResults", type: "avg", field: "properties.resultsCount" },
      ],
    };

    const result = await this.queryAnalytics(searchQuery);

    // Calculate additional metrics
    const totalQueries = result.total.totalQueries || 0;
    const uniqueUsers = result.total.uniqueUsers || 0;
    const avgResults = result.total.avgResults || 0;

    // Get top queries
    const topQueries = result.data
      .filter(item => item.dimensions.query)
      .sort((a, b) => b.metrics.totalQueries - a.metrics.totalQueries)
      .slice(0, 10)
      .map(item => ({
        query: item.dimensions.query,
        count: item.metrics.totalQueries,
        avgResults: item.metrics.avgResults || 0,
        clickThroughRate: this.calculateClickThroughRate(item),
      }));

    // Get zero result queries
    const zeroResultQueries = result.data.filter(
      item => item.metrics.avgResults === 0
    ).length;

    // Calculate overall CTR
    const totalClicks = result.data.reduce(
      (sum, item) => sum + (item.metrics.clicks || 0), 0
    );
    const clickThroughRate = totalQueries > 0 ? (totalClicks / totalQueries) * 100 : 0;

    return {
      totalQueries,
      uniqueUsers,
      averageResultsPerQuery: avgResults,
      clickThroughRate,
      zeroResultQueries,
      topQueries,
      topResults: [], // TODO: Implement top results
      queryTrends: [], // TODO: Implement query trends
    };
  }

  // Flush events to Elasticsearch
  private async flushEvents(): Promise<void> {
    if (this.eventBuffer.length === 0) return;

    const events = [...this.eventBuffer];
    this.eventBuffer = [];

    try {
      // Convert events to search documents for indexing
      const documents = events.map(event => ({
        id: event.id,
        type: "analytics_event" as any,
        tenantId: event.tenantId,
        workspaceId: event.workspaceId || "",
        title: `${event.type} event`,
        content: JSON.stringify(event.properties),
        author: {
          id: event.userId || "system",
          name: "System",
          email: "",
        },
        tags: [event.type],
        categories: ["analytics"],
        metadata: {
          eventType: event.type,
          properties: event.properties,
          context: event.context,
        },
        permissions: {
          public: false,
          users: event.userId ? [event.userId] : [],
          teams: [],
          workspaces: event.workspaceId ? [event.workspaceId] : [],
        },
        status: "active" as any,
        language: "en",
        createdAt: event.createdAt,
        updatedAt: event.createdAt,
        indexedAt: new Date(),
      }));

      await this.elasticsearch.bulkIndexDocuments(documents);
      
      this.emit("events:flushed", { count: events.length });
    } catch (error) {
      console.error("Failed to flush analytics events:", error);
      
      // Re-add events to buffer for retry
      this.eventBuffer.unshift(...events);
      
      this.emit("events:flush_failed", { count: events.length, error });
    }
  }

  // Build analytics query
  private buildAnalyticsQuery(query: AnalyticsQuery): any {
    const mustClauses: any[] = [];
    const filterClauses: any[] = [];

    // Filter by event types
    if (query.eventTypes && query.eventTypes.length > 0) {
      filterClauses.push({
        terms: { "metadata.eventType": query.eventTypes },
      });
    }

    // Filter by tenant/workspace/project
    if (query.tenantId) {
      filterClauses.push({ term: { tenantId: query.tenantId } });
    }
    
    if (query.workspaceId) {
      filterClauses.push({ term: { workspaceId: query.workspaceId } });
    }
    
    if (query.projectId) {
      filterClauses.push({ term: { projectId: query.projectId } });
    }

    // Filter by user
    if (query.userId) {
      filterClauses.push({ term: { "author.id": query.userId } });
    }

    // Date range filter
    filterClauses.push({
      range: {
        createdAt: {
          gte: query.dateRange.start,
          lte: query.dateRange.end,
        },
      },
    });

    // Additional filters
    if (query.filters) {
      Object.entries(query.filters).forEach(([key, value]) => {
        filterClauses.push({ term: { [`metadata.properties.${key}`]: value } });
      });
    }

    return {
      bool: {
        must: mustClauses.length > 0 ? mustClauses : [{ match_all: {} }],
        filter: filterClauses,
      },
    };
  }

  // Build analytics aggregations
  private buildAnalyticsAggregations(query: AnalyticsQuery): any[] {
    const aggregations: any[] = [];

    // Group by aggregations
    if (query.groupBy) {
      query.groupBy.forEach(field => {
        aggregations.push({
          name: `group_by_${field.replace(/\./g, "_")}`,
          type: "terms",
          field: `metadata.${field}`,
          size: 100,
        });
      });
    }

    // Metric aggregations
    if (query.metrics) {
      query.metrics.forEach(metric => {
        switch (metric.type) {
          case "count":
            aggregations.push({
              name: metric.name,
              type: "terms",
              field: "_id",
              size: 0,
            });
            break;
          
          case "unique":
            aggregations.push({
              name: metric.name,
              type: "terms",
              field: metric.field ? `metadata.${metric.field}` : "_id",
              size: 0,
            });
            break;
          
          case "avg":
          case "sum":
          case "min":
          case "max":
            aggregations.push({
              name: metric.name,
              type: "stats",
              field: metric.field ? `metadata.${metric.field}` : "_score",
            });
            break;
        }
      });
    }

    // Date histogram for trends
    aggregations.push({
      name: "date_histogram",
      type: "date_histogram",
      field: "createdAt",
      interval: "day",
    });

    return aggregations;
  }

  // Parse analytics response
  private parseAnalyticsResponse(
    response: any,
    query: AnalyticsQuery,
    executionTime: number
  ): AnalyticsResult {
    const data: any[] = [];
    const total: Record<string, number> = {};

    // Process aggregations
    if (response.aggregations) {
      Object.entries(response.aggregations).forEach(([name, agg]: [string, any]) => {
        if (agg.buckets) {
          agg.buckets.forEach((bucket: any) => {
            data.push({
              timestamp: bucket.key_as_string ? new Date(bucket.key_as_string) : undefined,
              dimensions: { [name]: bucket.key },
              metrics: { [name]: bucket.doc_count },
            });
          });
        } else if (agg.value !== undefined) {
          total[name] = agg.value;
        }
      });
    }

    return {
      data,
      total,
      metadata: {
        query,
        executionTime,
        totalEvents: response.total?.value || 0,
      },
    };
  }

  // Calculate click-through rate
  private calculateClickThroughRate(item: any): number {
    const queries = item.metrics.totalQueries || 0;
    const clicks = item.metrics.clicks || 0;
    return queries > 0 ? (clicks / queries) * 100 : 0;
  }

  // Get analytics summary
  async getAnalyticsSummary(params: {
    tenantId: string;
    workspaceId?: string;
    dateRange: { start: Date; end: Date };
  }): Promise<{
    totalEvents: number;
    uniqueUsers: number;
    topEventTypes: Array<{ type: string; count: number }>;
    dailyTrend: Array<{ date: string; events: number; users: number }>;
  }> {
    const query: AnalyticsQuery = {
      tenantId: params.tenantId,
      workspaceId: params.workspaceId,
      dateRange: params.dateRange,
      groupBy: ["eventType"],
      metrics: [
        { name: "totalEvents", type: "count" },
        { name: "uniqueUsers", type: "unique", field: "userId" },
      ],
    };

    const result = await this.queryAnalytics(query);

    return {
      totalEvents: result.total.totalEvents || 0,
      uniqueUsers: result.total.uniqueUsers || 0,
      topEventTypes: result.data
        .map(item => ({
          type: item.dimensions.eventType || "unknown",
          count: item.metrics.totalEvents || 0,
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10),
      dailyTrend: [], // TODO: Implement daily trend
    };
  }

  // Cleanup
  destroy(): void {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
    }
    
    // Flush remaining events
    this.flushEvents();
    
    this.removeAllListeners();
  }
}
