import { User, UserRole, UserStatus } from "@nexus/database-schema";
import { CreateUser } from "@nexus/validation";
export declare class UserService {
    private tenantId;
    constructor(tenantId: string);
    create(data: CreateUser): Promise<User>;
    findById(id: string): Promise<User | null>;
    findByEmail(email: string): Promise<User | null>;
    update(id: string, data: Partial<CreateUser>): Promise<User>;
    updateRole(id: string, role: UserRole): Promise<User>;
    updateStatus(id: string, status: UserStatus): Promise<User>;
    delete(id: string): Promise<User>;
    list(page?: number, limit?: number): Promise<{
        data: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>;
    isEmailAvailable(email: string): Promise<boolean>;
    findByRole(role: UserRole): Promise<User[]>;
    findByStatus(status: UserStatus): Promise<User[]>;
}
export declare const createUserService: (tenantId: string) => UserService;
//# sourceMappingURL=user-service.d.ts.map