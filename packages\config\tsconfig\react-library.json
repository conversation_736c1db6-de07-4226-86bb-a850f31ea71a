{"$schema": "https://json.schemastore.org/tsconfig", "display": "React Library", "extends": "./base.json", "compilerOptions": {"lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "target": "ES2022", "jsx": "react-jsx", "declaration": true, "declarationMap": true, "outDir": "dist", "rootDir": "src", "composite": true, "incremental": true}, "include": ["src"], "exclude": ["dist", "build", "node_modules"]}