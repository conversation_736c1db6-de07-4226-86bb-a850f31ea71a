#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Downloads/saas-starter-cursor-temp/node_modules/.pnpm/@pothos+plugin-prisma@4.10.0_@pothos+core@4.7.2_@prisma+client@6.12.0_graphql@16.11.0_typescript@5.8.3/node_modules/@pothos/plugin-prisma/bin/node_modules:/mnt/c/Users/<USER>/Downloads/saas-starter-cursor-temp/node_modules/.pnpm/@pothos+plugin-prisma@4.10.0_@pothos+core@4.7.2_@prisma+client@6.12.0_graphql@16.11.0_typescript@5.8.3/node_modules/@pothos/plugin-prisma/node_modules:/mnt/c/Users/<USER>/Downloads/saas-starter-cursor-temp/node_modules/.pnpm/@pothos+plugin-prisma@4.10.0_@pothos+core@4.7.2_@prisma+client@6.12.0_graphql@16.11.0_typescript@5.8.3/node_modules/@pothos/node_modules:/mnt/c/Users/<USER>/Downloads/saas-starter-cursor-temp/node_modules/.pnpm/@pothos+plugin-prisma@4.10.0_@pothos+core@4.7.2_@prisma+client@6.12.0_graphql@16.11.0_typescript@5.8.3/node_modules:/mnt/c/Users/<USER>/Downloads/saas-starter-cursor-temp/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Downloads/saas-starter-cursor-temp/node_modules/.pnpm/@pothos+plugin-prisma@4.10.0_@pothos+core@4.7.2_@prisma+client@6.12.0_graphql@16.11.0_typescript@5.8.3/node_modules/@pothos/plugin-prisma/bin/node_modules:/mnt/c/Users/<USER>/Downloads/saas-starter-cursor-temp/node_modules/.pnpm/@pothos+plugin-prisma@4.10.0_@pothos+core@4.7.2_@prisma+client@6.12.0_graphql@16.11.0_typescript@5.8.3/node_modules/@pothos/plugin-prisma/node_modules:/mnt/c/Users/<USER>/Downloads/saas-starter-cursor-temp/node_modules/.pnpm/@pothos+plugin-prisma@4.10.0_@pothos+core@4.7.2_@prisma+client@6.12.0_graphql@16.11.0_typescript@5.8.3/node_modules/@pothos/node_modules:/mnt/c/Users/<USER>/Downloads/saas-starter-cursor-temp/node_modules/.pnpm/@pothos+plugin-prisma@4.10.0_@pothos+core@4.7.2_@prisma+client@6.12.0_graphql@16.11.0_typescript@5.8.3/node_modules:/mnt/c/Users/<USER>/Downloads/saas-starter-cursor-temp/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@pothos/plugin-prisma/bin/generator.js" "$@"
else
  exec node  "$basedir/../@pothos/plugin-prisma/bin/generator.js" "$@"
fi
