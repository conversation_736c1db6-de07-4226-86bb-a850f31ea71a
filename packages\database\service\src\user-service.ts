import { User, UserRole, UserStatus } from "@nexus/database-schema";
import { createTenantClient } from "./client";
import { CreateUser } from "@nexus/validation";

export class UserService {
  private tenantId: string;

  constructor(tenantId: string) {
    this.tenantId = tenantId;
  }

  // Create a new user
  async create(data: CreateUser): Promise<User> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.user.create({
      data: {
        ...data,
        role: UserRole.MEMBER,
        status: UserStatus.ACTIVE,
      },
    });
  }

  // Find user by ID
  async findById(id: string): Promise<User | null> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.user.findUnique({
      where: { id },
    });
  }

  // Find user by email
  async findByEmail(email: string): Promise<User | null> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.user.findUnique({
      where: { 
        tenantId_email: {
          tenantId: this.tenantId,
          email,
        },
      },
    });
  }

  // Update user
  async update(id: string, data: Partial<CreateUser>): Promise<User> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.user.update({
      where: { id },
      data,
    });
  }

  // Update user role
  async updateRole(id: string, role: UserRole): Promise<User> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.user.update({
      where: { id },
      data: { role },
    });
  }

  // Update user status
  async updateStatus(id: string, status: UserStatus): Promise<User> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.user.update({
      where: { id },
      data: { status },
    });
  }

  // Delete user
  async delete(id: string): Promise<User> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.user.delete({
      where: { id },
    });
  }

  // List users with pagination
  async list(page: number = 1, limit: number = 10) {
    const client = createTenantClient(this.tenantId);
    const skip = (page - 1) * limit;
    
    const [users, total] = await Promise.all([
      client.tenant.user.findMany({
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      client.tenant.user.count(),
    ]);

    return {
      data: users,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  // Check if email is available
  async isEmailAvailable(email: string): Promise<boolean> {
    const user = await this.findByEmail(email);
    return !user;
  }

  // Get users by role
  async findByRole(role: UserRole): Promise<User[]> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.user.findMany({
      where: { role },
      orderBy: { createdAt: "desc" },
    });
  }

  // Get users by status
  async findByStatus(status: UserStatus): Promise<User[]> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.user.findMany({
      where: { status },
      orderBy: { createdAt: "desc" },
    });
  }
}

export const createUserService = (tenantId: string): UserService => {
  return new UserService(tenantId);
};
