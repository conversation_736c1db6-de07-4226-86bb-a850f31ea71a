"use client";

import React, { useState, useEffect } from "react";
import { useRBAC, useIsAdmin, createAuditLogger } from "@nexus/rbac";
import { useTenantId } from "@nexus/tenant-context";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from "recharts";
import { format, subDays } from "date-fns";

// Main RBAC dashboard component
export function RBACDashboard() {
  const { roles, userRoles, isLoading } = useRBAC();
  const isAdmin = useIsAdmin();
  const tenantId = useTenantId();
  const [auditStats, setAuditStats] = useState<any>(null);
  const [recentActivity, setRecentActivity] = useState<any[]>([]);

  const auditLogger = tenantId ? createAuditLogger(tenantId) : null;

  // Load dashboard data
  useEffect(() => {
    if (tenantId && isAdmin && auditLogger) {
      loadDashboardData();
    }
  }, [tenantId, isAdmin, auditLogger]);

  const loadDashboardData = async () => {
    if (!auditLogger) return;

    try {
      const [stats, logs] = await Promise.all([
        auditLogger.getAuditStats({
          start: subDays(new Date(), 30),
          end: new Date(),
        }),
        auditLogger.getAuditLogs({}, 1, 10),
      ]);

      setAuditStats(stats);
      setRecentActivity(logs.data);
    } catch (error) {
      console.error("Failed to load dashboard data:", error);
    }
  };

  if (!isAdmin) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Access denied. Admin privileges required.</p>
      </div>
    );
  }

  if (isLoading) {
    return <div className="text-center py-8">Loading RBAC dashboard...</div>;
  }

  // Calculate statistics
  const activeRoles = roles.filter(r => r.isActive);
  const systemRoles = roles.filter(r => r.isSystem);
  const customRoles = roles.filter(r => !r.isSystem);
  const activeUserRoles = userRoles.filter(ur => ur.isActive);

  // Role distribution data
  const roleDistribution = roles.reduce((acc, role) => {
    const count = userRoles.filter(ur => ur.roleId === role.id && ur.isActive).length;
    if (count > 0) {
      acc.push({ name: role.name, value: count });
    }
    return acc;
  }, [] as Array<{ name: string; value: number }>);

  // Colors for pie chart
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">RBAC Dashboard</h1>
        <p className="text-gray-600">Overview of roles, permissions, and access control activity</p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <MetricCard
          title="Total Roles"
          value={roles.length}
          subtitle={`${activeRoles.length} active`}
          color="blue"
        />
        <MetricCard
          title="System Roles"
          value={systemRoles.length}
          subtitle={`${customRoles.length} custom`}
          color="purple"
        />
        <MetricCard
          title="User Assignments"
          value={activeUserRoles.length}
          subtitle="Active assignments"
          color="green"
        />
        <MetricCard
          title="Permission Checks"
          value={auditStats?.permissionChecks || 0}
          subtitle="Last 30 days"
          color="orange"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Role Distribution */}
        <div className="bg-white p-6 rounded-lg border">
          <h3 className="text-lg font-semibold mb-4">Role Distribution</h3>
          {roleDistribution.length > 0 ? (
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={roleDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {roleDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          ) : (
            <div className="text-center py-8 text-gray-500">No role assignments found</div>
          )}
        </div>

        {/* Activity Overview */}
        <div className="bg-white p-6 rounded-lg border">
          <h3 className="text-lg font-semibold mb-4">Activity Overview (Last 30 Days)</h3>
          {auditStats ? (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Permission Checks</span>
                <span className="font-semibold">{auditStats.permissionChecks.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Role Assignments</span>
                <span className="font-semibold">{auditStats.roleAssignments.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Role Revocations</span>
                <span className="font-semibold">{auditStats.roleRevocations.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Denied Attempts</span>
                <span className="font-semibold text-red-600">{auditStats.deniedAttempts.toLocaleString()}</span>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">Loading activity data...</div>
          )}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg border">
        <div className="px-6 py-4 border-b">
          <h3 className="text-lg font-semibold">Recent Activity</h3>
        </div>
        <div className="p-6">
          {recentActivity.length > 0 ? (
            <div className="space-y-3">
              {recentActivity.map((log) => (
                <div key={log.id} className="flex items-center justify-between py-2 border-b last:border-b-0">
                  <div className="flex items-center space-x-3">
                    <div className={`w-2 h-2 rounded-full ${
                      log.result === "success" ? "bg-green-500" :
                      log.result === "denied" ? "bg-red-500" :
                      "bg-yellow-500"
                    }`} />
                    <div>
                      <p className="text-sm font-medium">
                        {log.action.replace(/_/g, " ")} on {log.resource}
                      </p>
                      <p className="text-xs text-gray-500">User: {log.userId}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-gray-500">
                      {format(new Date(log.createdAt), "MMM dd, HH:mm")}
                    </p>
                    <span className={`px-2 py-1 text-xs rounded ${
                      log.result === "success" ? "bg-green-100 text-green-800" :
                      log.result === "denied" ? "bg-red-100 text-red-800" :
                      "bg-yellow-100 text-yellow-800"
                    }`}>
                      {log.result}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">No recent activity</div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white p-6 rounded-lg border">
        <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <QuickActionCard
            title="Create Role"
            description="Create a new role with custom permissions"
            action="Create"
            href="/admin/rbac/roles/new"
          />
          <QuickActionCard
            title="Assign Roles"
            description="Assign roles to users in your organization"
            action="Assign"
            href="/admin/rbac/assignments"
          />
          <QuickActionCard
            title="View Audit Logs"
            description="Monitor access control activity and security events"
            action="View"
            href="/admin/rbac/audit"
          />
        </div>
      </div>
    </div>
  );
}

// Metric card component
function MetricCard({
  title,
  value,
  subtitle,
  color,
}: {
  title: string;
  value: number;
  subtitle: string;
  color: "blue" | "purple" | "green" | "orange";
}) {
  const colorClasses = {
    blue: "bg-blue-50 text-blue-700",
    purple: "bg-purple-50 text-purple-700",
    green: "bg-green-50 text-green-700",
    orange: "bg-orange-50 text-orange-700",
  };

  return (
    <div className={`p-6 rounded-lg border ${colorClasses[color]}`}>
      <h3 className="text-sm font-medium opacity-75">{title}</h3>
      <p className="text-2xl font-bold">{value.toLocaleString()}</p>
      <p className="text-sm opacity-75">{subtitle}</p>
    </div>
  );
}

// Quick action card component
function QuickActionCard({
  title,
  description,
  action,
  href,
}: {
  title: string;
  description: string;
  action: string;
  href: string;
}) {
  return (
    <div className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
      <h4 className="font-medium mb-2">{title}</h4>
      <p className="text-sm text-gray-600 mb-4">{description}</p>
      <a
        href={href}
        className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
      >
        {action} →
      </a>
    </div>
  );
}
