import { CreatePaymentIntentRequest, PaymentIntent, PaymentMethod } from "./stripe-types";

export class PaymentService {
  private tenantId: string;

  constructor(tenantId: string) {
    this.tenantId = tenantId;
  }

  // Create payment intent
  async createPaymentIntent(data: CreatePaymentIntentRequest): Promise<PaymentIntent> {
    const response = await fetch("/api/stripe/payment-intents", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to create payment intent");
    }

    return response.json();
  }

  // Get payment intent
  async getPaymentIntent(paymentIntentId: string): Promise<PaymentIntent> {
    const response = await fetch(`/api/stripe/payment-intents/${paymentIntentId}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to get payment intent");
    }

    return response.json();
  }

  // Confirm payment intent
  async confirmPaymentIntent(paymentIntentId: string): Promise<PaymentIntent> {
    const response = await fetch(`/api/stripe/payment-intents/${paymentIntentId}/confirm`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to confirm payment intent");
    }

    return response.json();
  }

  // Get payment methods
  async getPaymentMethods(): Promise<PaymentMethod[]> {
    const response = await fetch("/api/stripe/payment-methods", {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to get payment methods");
    }

    return response.json();
  }

  // Attach payment method
  async attachPaymentMethod(paymentMethodId: string): Promise<PaymentMethod> {
    const response = await fetch(`/api/stripe/payment-methods/${paymentMethodId}/attach`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to attach payment method");
    }

    return response.json();
  }

  // Detach payment method
  async detachPaymentMethod(paymentMethodId: string): Promise<void> {
    const response = await fetch(`/api/stripe/payment-methods/${paymentMethodId}/detach`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to detach payment method");
    }
  }

  // Set default payment method
  async setDefaultPaymentMethod(paymentMethodId: string): Promise<void> {
    const response = await fetch(`/api/stripe/payment-methods/${paymentMethodId}/default`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to set default payment method");
    }
  }

  // Create billing portal session
  async createBillingPortalSession(returnUrl: string): Promise<{ url: string }> {
    const response = await fetch("/api/stripe/billing-portal", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify({ returnUrl }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to create billing portal session");
    }

    return response.json();
  }

  // Create checkout session
  async createCheckoutSession(params: {
    priceId: string;
    mode: "payment" | "subscription" | "setup";
    successUrl: string;
    cancelUrl: string;
    metadata?: Record<string, string>;
  }): Promise<{ url: string }> {
    const response = await fetch("/api/stripe/checkout", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to create checkout session");
    }

    return response.json();
  }
}

export const createPaymentService = (tenantId: string): PaymentService => {
  return new PaymentService(tenantId);
};
