# AI Development & Security Enforcement Rules
# Critical rules from CRM_DEVELOPMENT_RULES_2.md for production-ready AI development

ai_development_philosophy:
  production_first_mindset:
    rule: "AI must always assume code will be deployed to production immediately"
    enforcement:
      - no_console_logging_production: true
      - no_hardcoded_mock_data: true
      - no_todo_fixme_placeholders: true
      - error_handling_mandatory: true
    validation:
      - check_console_statements: "grep -r 'console\\.' src/ --exclude-dir=node_modules"
      - check_mock_data: "grep -r 'mock\\|dummy\\|fake' src/ --exclude-dir=node_modules"
      - check_todos: "grep -r 'TODO\\|FIXME\\|HACK' src/ --exclude-dir=node_modules"
    
  anti_over_engineering:
    rule: "Prefer simple, focused implementations over complex abstractions"
    enforcement:
      - justify_complexity: true
      - documentation_proportional: true
      - avoid_premature_optimization: true
    validation:
      - complexity_review: "Check for unnecessary abstraction layers"
      - documentation_ratio: "Ensure docs match complexity level"
    
  evidence_based_development:
    rule: "All architectural decisions must be based on measurable performance and security impact"
    enforcement:
      - profile_before_optimize: true
      - measure_before_implement: true
      - validate_security_impact: true
    validation:
      - performance_metrics: "Measure before/after performance"
      - security_assessment: "Validate security implications"

security_first_patterns:
  input_sanitization:
    rule: "ALL user input and external data MUST be sanitized and validated"
    enforcement:
      - centralized_sanitization: true
      - runtime_type_validation: true
      - xss_protection: true
    implementation:
      - create_sanitization_utils: "src/utils/sanitization.ts"
      - api_validation: "Runtime validation for all API data"
      - dynamic_content_protection: "XSS protection for user content"
    validation:
      - input_coverage: "100% input sanitization coverage"
      - validation_tests: "Test all sanitization functions"
    
  production_logging_security:
    rule: "NEVER log sensitive data, error objects, or user information in production"
    enforcement:
      - error_codes_only: true
      - redact_user_info: true
      - structured_logging: true
    implementation:
      - error_code_system: "Use error codes instead of full error objects"
      - user_redaction: "Redact PII from all logs"
      - log_levels: "Implement severity-based logging"
    validation:
      - log_audit: "Audit all logging statements for sensitive data"
      - production_log_check: "Zero sensitive data in production logs"
    
  runtime_type_safety:
    rule: "No 'any', 'Record<string, unknown>', or unsafe generics in production"
    enforcement:
      - strict_interfaces: true
      - discriminated_unions: true
      - proper_error_types: true
    implementation:
      - interface_definitions: "Define strict interfaces with runtime validation"
      - union_types: "Use discriminated unions for type safety"
      - error_handling: "Implement proper error types"
    validation:
      - typescript_strict: "tsc --strict --noEmit"
      - any_detection: "grep -r ': any\\|as any' src/ --exclude-dir=node_modules"
      - record_detection: "grep -r 'Record<string, unknown>' src/ --exclude-dir=node_modules"

performance_intelligence:
  algorithmic_efficiency:
    rule: "Maximum O(n log n) complexity for data operations, avoid nested loops"
    enforcement:
      - complexity_limit: "O(n log n)"
      - nested_loop_prevention: true
      - profile_expensive_operations: true
    implementation:
      - efficient_sorting: "Use efficient sorting algorithms"
      - avoid_nested_loops: "Prevent O(n²) patterns"
      - performance_profiling: "Profile before optimization"
    validation:
      - complexity_analysis: "Analyze algorithmic complexity"
      - performance_testing: "Test expensive operations"
    
  input_debouncing:
    rule: "ALL search inputs and API calls must be debounced and deduplicated"
    enforcement:
      - debounce_minimum: "300ms"
      - request_deduplication: true
      - excessive_api_prevention: true
    implementation:
      - debounce_hook: "src/hooks/use-debounce.ts"
      - api_deduplication: "Prevent duplicate API calls"
      - search_optimization: "Optimize search inputs"
    validation:
      - debounce_coverage: "All search inputs debounced"
      - api_call_analysis: "Monitor API call patterns"

component_architecture:
  production_mock_elimination:
    rule: "NO hardcoded data in production components - use service layer abstraction"
    enforcement:
      - service_layer_abstraction: true
      - environment_based_switching: true
      - api_abstraction_interfaces: true
    implementation:
      - service_interfaces: "Define service interfaces"
      - mock_data_separation: "Separate mock data into dedicated services"
      - environment_switching: "Environment-based service switching"
    validation:
      - hardcoded_data_check: "Zero hardcoded data in components"
      - service_layer_coverage: "100% service layer abstraction"
    
  component_size_enforcement:
    rule: "Enforce 250-line limit strictly with automated detection"
    enforcement:
      - line_limit: 250
      - automated_detection: true
      - modular_responsibility: true
      - skip_blank_lines: true
      - skip_comments: true
    implementation:
      - component_splitting: "Split large components into focused sub-components"
      - automated_counting: "Line counting in CI/CD with ESLint max-lines rule"
      - responsibility_modularization: "Modularize by responsibility"
    validation:
      - line_count_check: "wc -l src/components/**/*.tsx | awk '$1 > 250'"
      - eslint_enforcement: "ESLint max-lines rule with warn level"
      - component_analysis: "Analyze component responsibilities"
    
  strict_security_enforcement:
    rule: "Comprehensive security rules - no eval, no new Function, no script URLs"
    enforcement:
      - no_eval: "error"
      - no_new_func: "error"
      - no_script_url: "error"
      - security_headers: "mandatory"
      - csp_enforcement: "strict"
    implementation:
      - eslint_security_rules: "ESLint security plugin with error level"
      - security_headers: "Implement CSP, X-Frame-Options, X-Content-Type-Options"
      - runtime_validation: "Runtime security validation"
    validation:
      - security_audit: "npm audit and security linting"
      - header_validation: "Validate security headers in tests"
      - penetration_testing: "Basic security testing"
    
  typescript_ultra_strict:
    rule: "Ultra-strict TypeScript with no any types, exact optional properties"
    enforcement:
      - no_any_types: "error"
      - exact_optional_properties: true
      - no_implicit_returns: true
      - no_fallthrough_cases: true
      - no_unchecked_indexed_access: true
      - verbatim_module_syntax: true
      - no_unchecked_side_effects: true
    implementation:
      - tsconfig_strict: "Configure tsconfig.json with ultra-strict settings"
      - eslint_typescript: "ESLint TypeScript plugin with strict rules"
      - type_checking: "Comprehensive type checking"
    validation:
      - type_coverage: "100% type coverage required"
      - strict_compilation: "Zero TypeScript errors tolerance"
      - lint_validation: "ESLint TypeScript rules validation"
    
  error_boundary_security:
    rule: "Comprehensive error handling with secure logging"
    enforcement:
      - error_boundary_wrapping: true
      - error_id_generation: true
      - sanitized_error_display: true
    implementation:
      - async_error_boundaries: "Wrap async operations in error boundaries"
      - error_id_system: "Generate error IDs instead of exposing stack traces"
      - error_sanitization: "Sanitize error information before display"
    validation:
      - error_boundary_coverage: "All async operations wrapped"
      - error_exposure_check: "No stack traces in production"

configuration_standards:
  security_headers:
    rule: "ALL Next.js applications must implement comprehensive security headers"
    enforcement:
      - x_frame_options: "DENY"
      - x_content_type_options: "nosniff"
      - content_security_policy: true
      - referrer_policy: "strict-origin-when-cross-origin"
    implementation:
      - next_config_security: "Configure security headers in Next.js config"
      - csp_policy: "Implement Content Security Policy"
      - security_scanning: "Automated security header validation"
    validation:
      - header_check: "Validate all security headers present"
      - csp_validation: "Test CSP implementation"
    
  typescript_strictness:
    rule: "Use strictest possible TypeScript configuration"
    enforcement:
      - strict_mode: true
      - no_unchecked_indexed_access: true
      - exact_optional_property_types: true
      - verbatim_module_syntax: true
    implementation:
      - tsconfig_strict: "Enable all strict TypeScript options"
      - type_checking: "Comprehensive type checking"
      - strict_compilation: "Zero TypeScript errors"
    validation:
      - strict_check: "tsc --strict --noEmit"
      - type_coverage: "typescript-coverage-report"
    
  eslint_security_performance:
    rule: "Comprehensive linting for security and performance"
    enforcement:
      - security_rules: true
      - performance_rules: true
      - type_safety_rules: true
    implementation:
      - security_linting: "Enable security rules (no-eval, no-script-url)"
      - performance_linting: "Performance rules (react/jsx-key)"
      - type_safety_linting: "Type safety rules (no-explicit-any)"
    validation:
      - eslint_check: "eslint src/ --max-warnings=0"
      - security_scan: "eslint-plugin-security validation"

data_security_privacy:
  pii_protection:
    rule: "Personally Identifiable Information must be handled with explicit protection"
    enforcement:
      - pii_masking: true
      - existence_flags: true
      - data_sanitization: true
    implementation:
      - display_masking: "Mask PII in displays"
      - flag_based_access: "Use existence flags instead of raw data"
      - logging_sanitization: "Sanitize PII for logging"
    validation:
      - pii_audit: "Audit all PII handling"
      - masking_coverage: "100% PII masking coverage"
    
  service_layer_architecture:
    rule: "Centralized API abstraction with environment switching"
    enforcement:
      - service_interfaces: true
      - environment_switching: true
      - data_access_abstraction: true
    implementation:
      - interface_definition: "Define service interfaces"
      - environment_based_switching: "Environment-based service switching"
      - abstraction_layer: "Abstract all data access through service layer"
    validation:
      - service_coverage: "100% service layer abstraction"
      - environment_testing: "Test all environment configurations"

development_hygiene:
  code_cleanliness:
    rule: "No dead code, commented-out code, or development artifacts in commits"
    enforcement:
      - no_dead_code: true
      - no_commented_code: true
      - no_development_artifacts: true
    implementation:
      - dead_code_removal: "Remove void statements"
      - timeout_cleanup: "Eliminate setTimeout mock delays"
      - comment_cleanup: "Clean up commented code before commits"
    validation:
      - dead_code_check: "ts-unused-exports"
      - commented_code_check: "grep -r '//' src/ | grep -v 'TODO\\|FIXME'"
      - artifact_check: "Check for development artifacts"
    
  input_validation_utilities:
    rule: "Create and use centralized validation utilities"
    enforcement:
      - centralized_utilities: true
      - comprehensive_validation: true
      - reusable_functions: true
    implementation:
      - validation_library: "src/utils/validation.ts"
      - email_validation: "Email validation utility"
      - sanitization_functions: "String sanitization utilities"
      - phone_formatting: "Phone number formatting"
      - url_validation: "URL validation utility"
      - html_sanitization: "HTML sanitization utility"
    validation:
      - utility_coverage: "All validation types covered"
      - validation_testing: "Test all validation functions"

validation_enforcement:
  pre_commit_validation:
    rule: "Every commit must pass comprehensive quality gates"
    enforcement:
      - zero_typescript_errors: true
      - no_console_production: true
      - aria_attributes: true
      - automated_security_scanning: true
    implementation:
      - pre_commit_hooks: "Comprehensive pre-commit validation"
      - typescript_check: "Zero TypeScript errors"
      - console_check: "No console.log in production code"
      - accessibility_check: "ARIA attributes on interactive components"
      - security_scan: "Automated security scanning"
    validation:
      - commit_gate_check: "All quality gates pass"
      - comprehensive_validation: "Full validation pipeline"
    
  performance_monitoring:
    rule: "Monitor Core Web Vitals and component performance"
    enforcement:
      - core_web_vitals: true
      - component_performance: true
      - performance_budgets: true
    implementation:
      - vitals_tracking: "Track LCP, CLS metrics"
      - bundle_monitoring: "Monitor bundle size"
      - render_tracking: "Track component render times"
      - performance_budgets: "Implement performance budgets"
    validation:
      - vitals_check: "Core Web Vitals monitoring"
      - performance_analysis: "Component performance analysis"

framework_specific_patterns:
  nextjs_app_router:
    rule: "Follow Next.js 15+ App Router patterns for optimal performance"
    enforcement:
      - file_structure_compliance: true
      - server_components_default: true
      - routing_patterns: true
      - metadata_api_usage: true
    implementation:
      - file_structure: "app/ for routing, components/ for reusable, lib/ for utilities, types/ for TypeScript"
      - server_components: "Default to Server Components, use 'use client' only when needed"
      - data_fetching: "Fetch data at component level, parallel fetching with Promise.all"
      - routing: "Route groups for organization, loading/error UI patterns, dynamic routes with validation"
    validation:
      - structure_audit: "Validate file structure compliance"
      - component_type_check: "Ensure proper server/client component usage"
      - metadata_validation: "Validate metadata API implementation"
    
  react_19_patterns:
    rule: "Implement React 19 concurrent features and server components correctly"
    enforcement:
      - concurrent_features: true
      - server_component_optimization: true
      - suspense_boundaries: true
      - error_boundaries: true
    implementation:
      - server_components: "Data fetching and business logic in server components"
      - client_components: "Interactive functionality only, minimize boundaries"
      - composition: "Server as container, client as leaf components"
      - suspense_usage: "Suspense boundaries for loading states"
    validation:
      - concurrent_feature_check: "Validate React 19 feature usage"
      - component_boundary_audit: "Audit server/client component boundaries"
      - composition_validation: "Validate component composition patterns"
    
  component_architecture:
    rule: "Follow strict component architecture patterns for maintainability"
    enforcement:
      - server_client_separation: true
      - composition_patterns: true
      - props_management: true
      - state_boundaries: true
    implementation:
      - server_components: "No event handlers or hooks, database queries and API calls"
      - client_components: "Event handlers and state management, browser APIs only"
      - composition: "Props drilling vs context consideration, proper state boundaries"
      - performance: "Optimize re-rendering patterns, avoid memory leaks"
    validation:
      - architecture_compliance: "Validate component architecture patterns"
      - separation_audit: "Audit server/client component separation"
      - performance_check: "Check component performance patterns"

success_metrics:
  security_metrics:
    input_sanitization_coverage: "100%"
    sensitive_data_logs: "0"
    csp_implementation: "complete"
    
  performance_metrics:
    data_operations_complexity: "≤ O(n log n)"
    component_render_time: "< 16ms"
    bundle_size_growth: "< 5% per feature"
    
  architecture_metrics:
    production_mock_data: "0"
    component_line_limit: "< 250 lines"
    service_layer_abstraction: "100%"
    
  framework_compliance:
    nextjs_pattern_compliance: "100%"
    react_19_feature_usage: "Optimal"
    component_architecture_score: "> 95%"
