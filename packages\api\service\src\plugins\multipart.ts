import { FastifyInstance } from "fastify";
import fp from "fastify-plugin";
import { config } from "../config";

export const multipartPlugin = fp(async (fastify: FastifyInstance) => {
  await fastify.register(require("@fastify/multipart"), {
    limits: {
      fieldNameSize: 100,
      fieldSize: 100,
      fields: 10,
      fileSize: config.MAX_FILE_SIZE,
      files: 5,
      headerPairs: 2000,
    },
    attachFieldsToBody: true,
  });
});
