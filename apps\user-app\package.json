{"name": "@nexus/user-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@apollo/server": "^5.0.0", "@hookform/resolvers": "5.1.1", "@monaco-editor/react": "^4.7.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.62.0", "@paralleldrive/cuid2": "^2.2.2", "@pothos/core": "^4.7.2", "@pothos/plugin-prisma": "^4.10.0", "@radix-ui/react-accordion": "1.2.11", "@radix-ui/react-alert-dialog": "1.1.14", "@radix-ui/react-aspect-ratio": "1.1.7", "@radix-ui/react-avatar": "1.1.10", "@radix-ui/react-checkbox": "1.3.2", "@radix-ui/react-collapsible": "1.1.11", "@radix-ui/react-context-menu": "2.2.15", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-hover-card": "1.1.14", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-menubar": "1.1.15", "@radix-ui/react-navigation-menu": "1.2.13", "@radix-ui/react-popover": "1.1.14", "@radix-ui/react-progress": "1.1.7", "@radix-ui/react-radio-group": "1.3.7", "@radix-ui/react-scroll-area": "1.2.9", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-slider": "1.3.5", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-switch": "1.2.5", "@radix-ui/react-tabs": "1.1.12", "@radix-ui/react-toggle": "1.1.9", "@radix-ui/react-toggle-group": "1.1.10", "@radix-ui/react-tooltip": "1.2.7", "@react-pdf/renderer": "^4.3.0", "@stripe/stripe-js": "^7.5.0", "@tanstack/react-query": "^5.83.0", "@tanstack/react-table": "^8.21.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jsonwebtoken": "^9.0.10", "@types/qrcode": "^1.5.5", "better-auth": "^1.2.12", "bull": "^4.16.5", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.1.1", "date-fns": "4.1.0", "embla-carousel-react": "8.6.0", "framer-motion": "^12.23.6", "graphql": "^16.11.0", "graphql-tag": "^2.12.6", "input-otp": "1.4.2", "ioredis": "^5.6.1", "jest-axe": "^10.0.0", "jsonwebtoken": "^9.0.2", "lucide-react": "0.525.0", "monaco-editor": "^0.52.2", "next": "15.4.2", "next-intl": "^4.3.4", "next-themes": "0.4.6", "qrcode": "^1.5.4", "react": "19.1.0", "react-day-picker": "9.8.0", "react-dom": "19.1.0", "react-hook-form": "7.60.0", "react-resizable-panels": "3.0.3", "recharts": "3.1.0", "resend": "^4.7.0", "sonner": "2.0.6", "stripe": "^18.3.0", "tailwind-merge": "3.3.1", "vaul": "1.1.2", "winston": "^3.17.0", "zod": "4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@nexus/eslint-config": "workspace:*", "@nexus/prettier-config": "workspace:*", "@nexus/tsconfig": "workspace:*", "@eslint/eslintrc": "3.3.1", "@tailwindcss/postcss": "4.1.11", "@types/node": "24.0.15", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "eslint": "9.31.0", "eslint-config-next": "15.4.2", "tailwindcss": "4.1.11", "tw-animate-css": "1.3.5", "typescript": "5.8.3"}}