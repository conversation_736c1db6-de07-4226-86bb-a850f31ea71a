import { Workspace } from "@nexus/database-schema";
import { CreateWorkspace } from "@nexus/validation";
export declare class WorkspaceService {
    private tenantId;
    constructor(tenantId: string);
    create(data: CreateWorkspace): Promise<Workspace>;
    findById(id: string): Promise<Workspace | null>;
    findBySlug(slug: string): Promise<Workspace | null>;
    update(id: string, data: Partial<CreateWorkspace>): Promise<Workspace>;
    delete(id: string): Promise<Workspace>;
    list(page?: number, limit?: number): Promise<{
        data: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>;
    isSlugAvailable(slug: string): Promise<boolean>;
    findAll(): Promise<Workspace[]>;
}
export declare const createWorkspaceService: (tenantId: string) => WorkspaceService;
//# sourceMappingURL=workspace-service.d.ts.map