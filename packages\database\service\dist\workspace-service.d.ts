import { CreateWorkspace } from "@nexus/validation";
export declare class WorkspaceService {
    private tenantId;
    constructor(tenantId: string);
    create(data: CreateWorkspace): Promise<any>;
    findById(id: string): Promise<any | null>;
    findBySlug(slug: string): Promise<any | null>;
    update(id: string, data: Partial<CreateWorkspace>): Promise<any>;
    delete(id: string): Promise<any>;
    list(page?: number, limit?: number): Promise<{
        data: unknown;
        pagination: {
            page: number;
            limit: number;
            total: unknown;
            totalPages: number;
        };
    }>;
    isSlugAvailable(slug: string): Promise<boolean>;
    findAll(): Promise<any[]>;
}
export declare const createWorkspaceService: (tenantId: string) => WorkspaceService;
//# sourceMappingURL=workspace-service.d.ts.map