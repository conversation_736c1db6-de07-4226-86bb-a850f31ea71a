{"name": "@nexus/stripe-integration", "version": "0.1.0", "description": "Stripe payment integration for Nexus SaaS", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "jest", "type-check": "tsc --noEmit"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "@nexus/utils": "workspace:*", "@nexus/validation": "workspace:*", "@nexus/database-schema": "workspace:*", "@nexus/database-service": "workspace:*", "@nexus/auth-client": "workspace:*", "@nexus/tenant-context": "workspace:*", "@stripe/stripe-js": "7.5.0", "@stripe/react-stripe-js": "3.7.0", "stripe": "18.3.0", "react": "19.1.0", "zustand": "5.0.6", "zod": "4.0.5"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@types/jest": "30.0.0", "@types/react": "19.1.8", "jest": "30.0.4", "typescript": "5.8.3"}}