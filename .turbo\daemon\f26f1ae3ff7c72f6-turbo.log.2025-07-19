2025-07-19T19:23:55.543488Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:23:55.545494Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:55.942798Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".husky\\_"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:23:55.942822Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:56.038504Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\types\\.turbo"), AnchoredSystemPathBuf("packages\\core\\types"), AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log")}
2025-07-19T19:23:56.038531Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:56.837808Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:56.837878Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:56.936977Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:56.936994Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:57.046794Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:57.046860Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:57.140612Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts.map"), AnchoredSystemPathBuf("packages\\core\\types\\dist"), AnchoredSystemPathBuf("packages\\core\\types"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.js")}
2025-07-19T19:23:57.140632Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }}))
2025-07-19T19:23:57.391087Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\dist"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:23:57.391144Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }}))
2025-07-19T19:23:57.391216Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:23:57.547470Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:57.547486Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:58.143288Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:58.143307Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:58.643751Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:58.643773Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:58.942390Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:58.942412Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:59.243495Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:59.243517Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:59.441915Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:59.441932Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:59.546372Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:59.546388Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:59.642749Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:59.642765Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:59.837584Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:59.837606Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:59.941851Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:59.941864Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:00.050980Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:00.050998Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:00.144397Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:00.144412Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:00.339281Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:00.339297Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:00.436801Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:00.436822Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:00.639387Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:00.639402Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:01.051879Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:01.051891Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:01.243382Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:01.243398Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:01.445737Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:01.445756Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:01.939792Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:01.939806Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:02.538510Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:02.538525Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:02.644042Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:02.644070Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:02.850265Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:02.850280Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:02.945039Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:02.945070Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.042722Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.042738Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.143818Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.143842Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.339030Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.339045Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.444590Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.444606Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.649625Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.649641Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.745873Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.746051Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.951950Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.951988Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.041225Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.041245Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.243042Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.243082Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.344880Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.344915Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.439756Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.439780Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.641470Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.641492Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.736510Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.736533Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.944251Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.944289Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.043718Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.043741Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.245554Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.245596Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.341402Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.341444Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.544343Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.544363Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.645079Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.645101Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.843737Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.843756Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.944403Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.944419Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:07.494914Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:24:07.494988Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:07.597464Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-07-19T19:24:07.597487Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:07.634164Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-19T19:24:08.317025Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:24:08.317109Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:08.398060Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie")}
2025-07-19T19:24:08.398087Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:08.510107Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log")}
2025-07-19T19:24:08.510132Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }}))
2025-07-19T19:24:09.595251Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\4.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("packages\\core\\utils"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo")}
2025-07-19T19:24:09.595392Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }}))
2025-07-19T19:24:10.114032Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\utils\\.turbo")}
2025-07-19T19:24:10.114046Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }}))
2025-07-19T19:24:10.114193Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:24:11.010792Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\utils\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\core\\utils\\dist"), AnchoredSystemPathBuf("packages\\core\\utils\\dist\\index.js"), AnchoredSystemPathBuf("packages\\core\\utils"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("packages\\core\\utils\\dist\\index.d.ts.map")}
2025-07-19T19:24:11.010817Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }}))
2025-07-19T19:24:13.707926Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:13.707959Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:14.895320Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:14.895340Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:14.996018Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:14.996037Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.095771Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.095789Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.196308Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.196342Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.408626Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.408643Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.504558Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.504578Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.706566Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.706607Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.798761Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.798794Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.895760Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.895782Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.009049Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.009089Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.106056Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.106072Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.203974Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.203993Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.401072Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.401094Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.495139Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.495162Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.695105Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.695125Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.810247Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.810281Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:17.009656Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:17.009678Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:17.094757Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:17.094777Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
