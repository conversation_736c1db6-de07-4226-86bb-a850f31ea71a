2025-07-19T19:23:55.543488Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:23:55.545494Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:55.942798Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".husky\\_"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:23:55.942822Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:56.038504Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\types\\.turbo"), AnchoredSystemPathBuf("packages\\core\\types"), AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log")}
2025-07-19T19:23:56.038531Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:56.837808Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:56.837878Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:56.936977Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:56.936994Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:57.046794Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:57.046860Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:57.140612Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts.map"), AnchoredSystemPathBuf("packages\\core\\types\\dist"), AnchoredSystemPathBuf("packages\\core\\types"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.js")}
2025-07-19T19:23:57.140632Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }}))
2025-07-19T19:23:57.391087Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\dist"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:23:57.391144Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }}))
2025-07-19T19:23:57.391216Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:23:57.547470Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:57.547486Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:58.143288Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:58.143307Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:58.643751Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:58.643773Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:58.942390Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:58.942412Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:59.243495Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:59.243517Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:59.441915Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:59.441932Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:59.546372Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:59.546388Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:59.642749Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:59.642765Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:59.837584Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:59.837606Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:59.941851Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:59.941864Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:00.050980Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:00.050998Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:00.144397Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:00.144412Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:00.339281Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:00.339297Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:00.436801Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:00.436822Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:00.639387Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:00.639402Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:01.051879Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:01.051891Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:01.243382Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:01.243398Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:01.445737Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:01.445756Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:01.939792Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:01.939806Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:02.538510Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:02.538525Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:02.644042Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:02.644070Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:02.850265Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:02.850280Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:02.945039Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:02.945070Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.042722Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.042738Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.143818Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.143842Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.339030Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.339045Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.444590Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.444606Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.649625Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.649641Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.745873Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.746051Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.951950Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.951988Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.041225Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.041245Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.243042Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.243082Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.344880Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.344915Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.439756Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.439780Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.641470Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.641492Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.736510Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.736533Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.944251Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.944289Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.043718Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.043741Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.245554Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.245596Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.341402Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.341444Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.544343Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.544363Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.645079Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.645101Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.843737Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.843756Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.944403Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.944419Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:07.494914Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:24:07.494988Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:07.597464Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-07-19T19:24:07.597487Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:07.634164Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-19T19:24:08.317025Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:24:08.317109Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:08.398060Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie")}
2025-07-19T19:24:08.398087Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:08.510107Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log")}
2025-07-19T19:24:08.510132Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }}))
2025-07-19T19:24:09.595251Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\4.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("packages\\core\\utils"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo")}
2025-07-19T19:24:09.595392Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }}))
2025-07-19T19:24:10.114032Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\utils\\.turbo")}
2025-07-19T19:24:10.114046Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }}))
2025-07-19T19:24:10.114193Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:24:11.010792Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\utils\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\core\\utils\\dist"), AnchoredSystemPathBuf("packages\\core\\utils\\dist\\index.js"), AnchoredSystemPathBuf("packages\\core\\utils"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("packages\\core\\utils\\dist\\index.d.ts.map")}
2025-07-19T19:24:11.010817Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }}))
2025-07-19T19:24:13.707926Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:13.707959Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:14.895320Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:14.895340Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:14.996018Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:14.996037Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.095771Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.095789Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.196308Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.196342Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.408626Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.408643Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.504558Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.504578Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.706566Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.706607Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.798761Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.798794Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.895760Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.895782Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.009049Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.009089Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.106056Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.106072Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.203974Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.203993Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.401072Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.401094Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.495139Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.495162Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.695105Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.695125Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.810247Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.810281Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:17.009656Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:17.009678Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:17.094757Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:17.094777Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:28:37.137382Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-07-19T19:28:37.137668Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:28:37.725863Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.js"), AnchoredSystemPathBuf(".turbo\\cookies\\4.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts.map"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie")}
2025-07-19T19:28:37.725886Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }, WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }}))
2025-07-19T19:28:39.029969Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\utils\\dist\\index.js"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\5.cookie"), AnchoredSystemPathBuf("packages\\core\\utils\\dist\\index.d.ts.map")}
2025-07-19T19:28:39.029984Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }}))
2025-07-19T19:28:59.857729Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-07-19T19:28:59.857920Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:29:00.003111Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-19T19:29:04.752772Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf(".turbo")}
2025-07-19T19:29:04.752788Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:29:04.854708Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf("apps\\user-app\\.turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.js"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\4.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\5.cookie"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("apps\\user-app"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts.map")}
2025-07-19T19:29:04.854738Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }, WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:29:48.157441Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types"), AnchoredSystemPathBuf("apps\\user-app\\.next\\package.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\edge-server-production\\index.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\framework-41fd4a937747039f.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\edge-server-production\\0.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\package.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\layout-09c129a3a6da053c.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\_not-found"), AnchoredSystemPathBuf("apps\\user-app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\main-app-60e45917836c6123.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics"), AnchoredSystemPathBuf("apps\\user-app\\.next\\build-manifest.json"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\edge-server-production"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\app-build-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\edge-server-production\\0.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\cache-life.d.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\357.a0101afc97fd612d.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages\\_app-00060991db141ec3.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\GxzM5n4eRjYJZginghig4\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\server-reference-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\GxzM5n4eRjYJZginghig4"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\_not-found\\page-ce0095320e560e10.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\page-a4c47c9a192a0433.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\css"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\css\\3f499d5aeb2ea513.css"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\508.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\server-production\\0.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\93f479601ee12b01-s.p.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\9610d9e46709d722-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\trace"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\ba015fad6dcf6784-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\164.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\server-production"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\swc\\plugins\\v7_windows_x86_64_15.0.1"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\867-dacd5683055c89de.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-build-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\0.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\next-font-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\8d697b304b401681-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app\\layout.ts"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\GxzM5n4eRjYJZginghig4\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\104b06e6-5df3a4f1c270bc82.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\webpack-125b2d51288eed09.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app\\page.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\server-production\\index.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\569ce4b8f30dc480-s.p.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\next-font-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js"), AnchoredSystemPathBuf(".turbo\\cookies\\6.cookie"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app-paths-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\server-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\swc\\plugins"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\.previewinfo"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\server-production\\index.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\webpack-runtime.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\server-production\\0.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages\\_error-72338adfc9ba8e12.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\polyfills-42372ed130431b0a.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\0.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\64.0c5782f7e857a97d.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\.rscinfo"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\swc"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\113.js"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\edge-server-production\\index.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics\\build-diagnostics.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\main-fb1412fb0369d6b8.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics\\framework.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\796-7be2c160581854a8.js"), AnchoredSystemPathBuf("apps\\user-app\\.next"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\747892c23ea88013-s.woff2")}
2025-07-19T19:29:48.157476Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:29:48.167751Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:31:08.198080Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-07-19T19:31:08.198244Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:31:08.304350Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie")}
2025-07-19T19:31:08.304375Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:31:08.363460Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-19T19:31:13.594380Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:31:13.594396Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:31:13.701459Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:31:13.701475Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:31:42.671003Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\113.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\64.0c5782f7e857a97d.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\webpack-125b2d51288eed09.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\9610d9e46709d722-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\cache-life.d.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\package.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\package.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\css\\3f499d5aeb2ea513.css"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics\\build-diagnostics.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\polyfills-42372ed130431b0a.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\pBqxUUj8pnWyblJVbIuvi"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\2.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\pBqxUUj8pnWyblJVbIuvi\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\trace"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\next-font-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\GxzM5n4eRjYJZginghig4\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app\\layout.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\server-reference-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\8d697b304b401681-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\104b06e6-5df3a4f1c270bc82.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\0.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\867-dacd5683055c89de.js"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\357.a0101afc97fd612d.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\css"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\569ce4b8f30dc480-s.p.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\796-7be2c160581854a8.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\_not-found\\page-ce0095320e560e10.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\0.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\164.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\GxzM5n4eRjYJZginghig4\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages\\_app-00060991db141ec3.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\webpack-runtime.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static"), AnchoredSystemPathBuf("apps\\user-app\\.next\\app-build-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\93f479601ee12b01-s.p.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\1.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\main-app-60e45917836c6123.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\747892c23ea88013-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\next-font-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\server-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\508.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack.old"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\_not-found"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\layout-09c129a3a6da053c.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\pBqxUUj8pnWyblJVbIuvi\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app\\page.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\build-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\page-a4c47c9a192a0433.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages\\_error-72338adfc9ba8e12.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\ba015fad6dcf6784-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\main-fb1412fb0369d6b8.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics\\framework.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-build-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app-paths-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\1.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\framework-41fd4a937747039f.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\GxzM5n4eRjYJZginghig4"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\2.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page_client-reference-manifest.js")}
2025-07-19T19:31:42.671032Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:31:42.681248Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:31:47.592439Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\eslint.config.mjs")}
2025-07-19T19:31:47.592463Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:32:19.133975Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:32:19.134011Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:32:19.239043Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:32:19.239063Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:32:19.301615Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-19T19:32:24.422789Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".husky\\_")}
2025-07-19T19:32:24.422812Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:32:24.533753Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:32:24.533773Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:33:47.911886Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.next\\export\\_next"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\framework-41fd4a937747039f.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\104b06e6-5df3a4f1c270bc82.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\webpack-125b2d51288eed09.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\867-dacd5683055c89de.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\prerender-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\eslint\\.cache_1jf08jl"), AnchoredSystemPathBuf("apps\\user-app\\.next\\app-build-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\ba015fad6dcf6784-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\oHOh2fnLlfC06Ysa37uUK"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\eslint"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\server-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\1.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\508.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\pBqxUUj8pnWyblJVbIuvi"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\113.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\164.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\package.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\BUILD_ID"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\_not-found"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\next-font-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found.meta"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export\\_next\\oHOh2fnLlfC06Ysa37uUK"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found.rsc"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\polyfills-42372ed130431b0a.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\trace"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\css\\3f499d5aeb2ea513.css"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\index.meta"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack.old"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\357.a0101afc97fd612d.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\page-a4c47c9a192a0433.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\569ce4b8f30dc480-s.p.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\pBqxUUj8pnWyblJVbIuvi\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\pBqxUUj8pnWyblJVbIuvi\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\1.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\404.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\next-font-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\required-server-files.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-build-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\cache-life.d.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages\\_app-00060991db141ec3.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\500.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\main-fb1412fb0369d6b8.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\93f479601ee12b01-s.p.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\webpack-runtime.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export-detail.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export\\500.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\_not-found\\page-ce0095320e560e10.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages\\_error-72338adfc9ba8e12.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\package.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\oHOh2fnLlfC06Ysa37uUK\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico.body"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics\\framework.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\8d697b304b401681-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\3.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\3.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app-paths-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export-marker.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\server-reference-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\796-7be2c160581854a8.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\main-app-60e45917836c6123.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production"), AnchoredSystemPathBuf("apps\\user-app\\.next\\routes-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\747892c23ea88013-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\layout-09c129a3a6da053c.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\images-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\64.0c5782f7e857a97d.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\functions-config-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\build-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\index.rsc"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-manifest.json"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf("apps\\user-app\\.next\\app-path-routes-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\index.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\css"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\9610d9e46709d722-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\oHOh2fnLlfC06Ysa37uUK\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app\\layout.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics\\build-diagnostics.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app\\page.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export\\404.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico.meta"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js.nft.json")}
2025-07-19T19:33:47.911947Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:33:47.952725Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:33:53.931820Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js.nft.json")}
2025-07-19T19:33:53.931844Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:34:00.234944Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.next\\trace"), AnchoredSystemPathBuf("apps\\user-app\\.next\\next-server.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("apps\\user-app\\.next"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf("apps\\user-app\\.next\\next-minimal-server.js.nft.json"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie")}
2025-07-19T19:34:00.234973Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:34:00.247379Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:34:03.256218Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:34:03.256271Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:34:03.365244Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie")}
2025-07-19T19:34:03.365266Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:34:03.448762Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-19T19:34:04.663974Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:34:04.664000Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:34:10.164820Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:34:10.164835Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:34:10.266647Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\5.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\6.cookie"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.js"), AnchoredSystemPathBuf("packages\\core\\utils\\dist\\index.js"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts.map"), AnchoredSystemPathBuf("packages\\core\\utils\\dist\\index.d.ts.map"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo\\cookies\\4.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("packages\\core\\utils\\dist\\index.d.ts")}
2025-07-19T19:34:10.266675Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }, WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:35:24.306971Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\3.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics\\build-diagnostics.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\796-7be2c160581854a8.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\app-build-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\next-server.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\server-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\113.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\64.0c5782f7e857a97d.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages\\_app-00060991db141ec3.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\webpack-125b2d51288eed09.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\357.a0101afc97fd612d.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\index.meta"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\oHOh2fnLlfC06Ysa37uUK"), AnchoredSystemPathBuf("apps\\user-app\\.next\\prerender-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\508.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\cache-life.d.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\webpack-runtime.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\3.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\500.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app\\layout.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-build-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\KYiL1TJpTVq7D1bjwUpKf\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico.meta"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\ba015fad6dcf6784-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export\\500.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\css\\3f499d5aeb2ea513.css"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics\\framework.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\layout-09c129a3a6da053c.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\0.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages\\_error-72338adfc9ba8e12.js"), AnchoredSystemPathBuf("apps\\user-app\\.next"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\css"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\93f479601ee12b01-s.p.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\functions-config-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\104b06e6-5df3a4f1c270bc82.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\routes-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\trace"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export-marker.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\164.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\KYiL1TJpTVq7D1bjwUpKf\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export\\_next\\KYiL1TJpTVq7D1bjwUpKf"), AnchoredSystemPathBuf("apps\\user-app\\.next\\images-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\index.rsc"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app-paths-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\build-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\_not-found\\page-ce0095320e560e10.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found.meta"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found.rsc"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\867-dacd5683055c89de.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\BUILD_ID"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export\\404.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server"), AnchoredSystemPathBuf("apps\\user-app\\.next\\package.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\oHOh2fnLlfC06Ysa37uUK\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\4.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\next-font-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\framework-41fd4a937747039f.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\747892c23ea88013-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\8d697b304b401681-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\eslint\\.cache_1jf08jl"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\4.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\required-server-files.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\page-a4c47c9a192a0433.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\9610d9e46709d722-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\5.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\oHOh2fnLlfC06Ysa37uUK\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\5.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\main-fb1412fb0369d6b8.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\569ce4b8f30dc480-s.p.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\polyfills-42372ed130431b0a.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app\\page.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\app-path-routes-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\KYiL1TJpTVq7D1bjwUpKf"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico.body"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\server-reference-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export\\_next"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack.old"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\next-font-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\next-minimal-server.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export-detail.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\package.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\index.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\_not-found"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\0.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\404.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\main-app-60e45917836c6123.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js.nft.json")}
2025-07-19T19:35:24.307043Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:35:24.327152Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:35:28.263084Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js.nft.json")}
2025-07-19T19:35:28.263100Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:35:34.749879Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.next\\next-minimal-server.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\trace"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("apps\\user-app\\.next\\next-server.js.nft.json"), AnchoredSystemPathBuf(".turbo\\cookies\\7.cookie"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf("apps\\user-app\\.next")}
2025-07-19T19:35:34.749898Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:35:34.761307Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:35:37.972074Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:35:37.972110Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:38.075939Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-07-19T19:35:38.075969Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:38.100817Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-19T19:35:38.179824Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:38.179881Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:38.407174Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:38.407369Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:38.468283Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:38.468305Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:38.676109Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:38.676177Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:38.986458Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:38.986733Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:39.073265Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:39.073290Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:39.168887Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:39.168913Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:39.368747Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:39.368771Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:39.573182Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:39.573208Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:39.679917Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:39.679941Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:39.880424Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:39.880449Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:39.969636Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:39.969662Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:40.072698Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:40.072723Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:40.279859Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:40.279881Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:40.380700Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:40.380765Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:40.580702Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:40.580768Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:40.679724Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:40.679746Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:40.880505Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:40.880528Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:40.968515Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:40.968536Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:41.176542Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:41.176561Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:41.282265Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:41.282292Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:41.481203Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:41.481225Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:41.577825Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:41.577850Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:41.674047Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:41.674069Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:41.877268Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:41.877293Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:41.979766Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:41.979791Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:42.180347Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:42.180370Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:42.277687Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:42.277713Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:42.481290Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:42.481308Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:42.575183Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:42.575213Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:42.774752Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:42.774774Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:42.880467Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:42.880487Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:43.075849Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:43.075873Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:43.180235Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:43.180257Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:43.277491Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:43.277509Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:43.479698Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:43.479720Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:43.577931Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:43.577948Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:43.770463Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:43.770485Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:43.878175Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:43.878199Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:44.081099Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:44.081124Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:44.182019Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:44.182042Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:44.379732Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:44.379768Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:44.477699Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:44.477722Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:44.576897Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:44.576920Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:44.777026Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:44.777077Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:44.872348Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:44.872367Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:45.081276Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:45.081299Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:45.183106Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:45.183203Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:45.279146Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:45.279169Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:45.381745Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:35:45.381791Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:45.483839Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.js"), AnchoredSystemPathBuf(".turbo\\cookies\\5.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\4.cookie"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-type-check.log"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo\\cookies\\6.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-type-check.log"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts"), AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts.map"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-type-check.log"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie")}
2025-07-19T19:35:45.483866Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }, WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }}))
2025-07-19T19:35:56.402033Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-type-check.log"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\7.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf("apps\\user-app\\.turbo"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-type-check.log"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-type-check.log"), AnchoredSystemPathBuf(".turbo\\cookies\\9.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\8.cookie"), AnchoredSystemPathBuf("apps\\user-app")}
2025-07-19T19:35:56.402054Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:35:56.402118Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:35:56.414440Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:35:58.828422Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-07-19T19:35:58.828472Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:58.942731Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:35:58.942768Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:58.979282Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-19T19:35:59.257197Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:59.257271Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:59.422487Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:59.422512Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:59.557133Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:59.557160Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:59.719052Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:59.719072Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:59.830363Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:59.830390Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:00.018369Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:00.018422Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:00.126608Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:00.126633Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:00.315470Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:00.315496Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:00.427643Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:00.427665Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:00.629618Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:00.629636Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:00.724209Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:00.724229Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:00.817368Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:00.817386Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:01.030747Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:01.030770Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:01.124454Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:01.124479Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:01.326438Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:01.326461Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:01.426874Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:01.426898Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:01.626800Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:01.626825Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:01.722755Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:01.722808Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:01.817762Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:01.817779Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:02.025601Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:02.025663Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:02.130116Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:02.130141Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:02.215320Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:02.215342Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:02.326106Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:02.326132Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:02.428366Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:02.428391Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:02.630666Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:02.630685Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:02.727023Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:02.727042Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:02.927977Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:02.927999Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:03.029853Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:03.029876Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:03.115147Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:03.115170Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:03.323022Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:03.323047Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:03.427223Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:03.427244Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:03.628000Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:03.628054Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:03.728873Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:03.728897Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:03.926989Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:03.927024Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:04.028923Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:04.028946Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:04.228065Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:04.228088Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:04.315456Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:04.315482Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:04.523246Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:04.523268Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:04.620848Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:04.620868Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:04.830936Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:04.830958Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:04.926979Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:04.927024Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:05.125674Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:05.125698Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:05.224615Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:05.224664Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:05.325826Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:05.325852Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:05.530531Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:36:05.530550Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:05.715391Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-lint.log")}
2025-07-19T19:36:05.715417Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:15.834781Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\eslint\\.cache_1jf08jl"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie"), AnchoredSystemPathBuf("apps\\user-app\\.turbo"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-lint.log"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:36:15.834801Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:15.848340Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:36:30.611419Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:36:30.611561Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:05:34.421583Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-19T20:06:30.443956Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T20:06:30.444018Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:06:30.558487Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T20:06:30.558515Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:06:30.869981Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-19T20:06:35.151379Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T20:06:35.151452Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:06:36.890686Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log")}
2025-07-19T20:06:36.890715Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }}))
2025-07-19T20:06:38.864733Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\constants\\.turbo"), AnchoredSystemPathBuf("packages\\core\\constants\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("packages\\core\\constants")}
2025-07-19T20:06:38.864760Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/constants"), path: AnchoredSystemPathBuf("packages\\core\\constants") }}))
2025-07-19T20:06:43.145129Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\4.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\5.cookie"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf("packages\\core\\constants\\dist\\index.js"), AnchoredSystemPathBuf("packages\\core\\constants\\dist"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log")}
2025-07-19T20:06:43.145169Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }, WorkspacePackage { name: Other("@nexus/constants"), path: AnchoredSystemPathBuf("packages\\core\\constants") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:06:44.560898Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\constants"), AnchoredSystemPathBuf("packages\\core\\constants\\dist\\index.d.ts.map"), AnchoredSystemPathBuf("packages\\core\\constants\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\core\\constants\\dist"), AnchoredSystemPathBuf("packages\\core\\constants\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-build.log")}
2025-07-19T20:06:44.560925Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/constants"), path: AnchoredSystemPathBuf("packages\\core\\constants") }, WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }}))
2025-07-19T20:06:44.561117Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T20:06:57.247064Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\constants\\package.json")}
2025-07-19T20:06:57.247093Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/constants"), path: AnchoredSystemPathBuf("packages\\core\\constants") }}))
2025-07-19T20:07:06.346659Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:07:06.346678Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:06.443981Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:07:06.444007Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:06.645663Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:07:06.645684Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:06.748547Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:07:06.748571Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:06.848950Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:07:06.849039Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:07.042188Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:07:07.042211Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:07.149468Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:07:07.149531Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:07.339342Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:07:07.339362Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:07.449021Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:07:07.449044Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:07.640666Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:07:07.640682Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:07.747069Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:07:07.747099Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:07.943436Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:07:07.943455Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:08.039489Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:07:08.039506Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:08.245366Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:07:08.245390Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:08.345111Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:07:08.345135Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:08.546975Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:07:08.546999Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:08.641010Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:07:08.641100Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:08.847966Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:07:08.847995Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:35.086497Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-07-19T20:07:35.086690Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:35.200768Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo")}
2025-07-19T20:07:35.200804Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:35.404150Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-19T20:07:37.902205Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T20:07:37.902276Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:40.819635Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf("packages\\core\\constants\\.turbo\\turbo-build.log")}
2025-07-19T20:07:40.819710Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/constants"), path: AnchoredSystemPathBuf("packages\\core\\constants") }}))
2025-07-19T20:07:42.333478Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T20:07:42.333503Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:46.191520Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\constants\\dist\\index.js"), AnchoredSystemPathBuf("packages\\core\\constants\\dist\\index.d.ts.map")}
2025-07-19T20:07:46.191547Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/constants"), path: AnchoredSystemPathBuf("packages\\core\\constants") }}))
2025-07-19T20:07:47.641626Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\constants\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("packages\\core\\constants\\dist\\index.d.ts.map"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie"), AnchoredSystemPathBuf("packages\\core\\constants\\dist\\index.d.ts")}
2025-07-19T20:07:47.641657Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/constants"), path: AnchoredSystemPathBuf("packages\\core\\constants") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:07:47.641825Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T20:08:08.198636Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:08.198654Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:08.296582Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:08.296604Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:08.392318Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:08.392338Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:08.592168Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:08.592191Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:08.688749Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:08.688768Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:08.892441Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:08.892463Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:08.988800Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:08.988819Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:09.197075Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:09.197091Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:09.296840Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:09.296861Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:09.487323Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:09.487346Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:09.591728Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:09.591746Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:09.795375Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:09.795392Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:09.897218Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:09.897236Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:10.093155Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:10.093176Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:10.189202Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:10.189219Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:10.391402Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:10.391564Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:10.490971Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:10.490996Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:10.699056Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:10.699078Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:10.795187Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:10.795209Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:10.893442Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:10.893459Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:11.091537Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:11.091554Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:11.196160Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:11.196186Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:11.386482Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:11.386504Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:11.496459Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:11.496482Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:11.592393Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:11.592411Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:11.794988Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:11.795009Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:11.892343Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:11.892359Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:12.094161Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:12.094180Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:12.295512Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:12.295533Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:12.391580Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:12.391600Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:12.487704Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:12.487721Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:12.695452Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:12.695468Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:12.796111Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:12.796139Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:12.990550Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:12.990568Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:13.095242Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:13.095264Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:13.195542Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:13.195558Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:13.388307Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:13.388329Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:13.501369Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:13.501390Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:13.693373Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:13.693428Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:13.797455Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:13.797491Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:13.996221Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:13.996244Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:14.093758Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:14.093781Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:14.293690Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:14.293707Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:14.391084Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:14.391119Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:14.590886Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:14.590902Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:14.687144Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:14.687162Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:14.794027Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:14.794050Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:14.999694Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:14.999711Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:15.090435Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:15.090454Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:15.297364Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:15.297381Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:15.394564Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:15.394581Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:15.595595Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:15.595662Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:15.689143Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:15.689161Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:15.891254Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:15.891270Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:15.990131Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:15.990163Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:16.196616Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:16.196635Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:16.296976Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:16.296996Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:16.490869Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:16.490894Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:16.591324Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:16.591348Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:16.797363Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:16.797382Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:16.892978Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:16.893007Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:17.091580Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:17.091600Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:17.187615Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:17.187639Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:17.296494Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:17.296512Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:17.489333Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:17.489361Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:17.592219Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:17.592238Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:17.796961Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:17.796979Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:17.894537Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:17.894555Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:18.094072Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:18.094092Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:18.198616Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:18.198661Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:18.296329Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:18.296351Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:18.497717Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:18.497734Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:18.586970Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:18.586987Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:18.796125Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:18.796141Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:18.895056Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:18.895077Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:19.089461Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:19.089484Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:19.201446Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:19.201474Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:19.293048Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:19.293066Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:19.488547Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:19.488566Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:19.595981Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:19.596005Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:19.794896Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:19.794952Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:19.894561Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:19.894581Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:20.093623Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:20.093649Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:20.198590Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:20.198610Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:20.389963Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:20.389981Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:20.499221Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:20.499243Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:20.595143Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:20.595162Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:20.794104Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:20.794120Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:20.891676Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:20.891694Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:21.091573Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:21.091591Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:21.195884Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:21.195903Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:21.394621Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:21.394641Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:21.491454Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:21.491471Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:21.686869Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:21.686890Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:21.795956Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:21.795984Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:21.989359Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:21.989378Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:22.096292Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:22.096314Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:22.296071Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:22.296089Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:22.391612Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:22.391629Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:22.594205Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:22.594250Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:22.695581Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:22.695603Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:22.791710Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:22.791726Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:22.999945Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:22.999963Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:23.090812Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:23.090854Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:23.296164Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:23.296213Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:23.394536Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:23.394557Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:23.598039Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:23.598059Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:23.688143Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:23.688167Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:23.796208Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:23.796234Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:23.993388Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:23.993407Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:24.092327Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:24.092347Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:24.293680Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:24.293724Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:24.392556Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:24.392575Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:24.592242Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:24.592262Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:24.690736Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:24.690752Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:24.891484Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:24.891505Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:24.987969Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:24.987991Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:25.094804Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:25.094827Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:25.197137Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:25.197160Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:25.295773Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:25.295795Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:25.391918Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:25.391935Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:25.592275Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:25.592295Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:25.687130Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:25.687152Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:25.893589Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:25.893619Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:25.988741Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:25.988758Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:26.197477Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:26.197567Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:26.288568Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:26.288589Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:26.394533Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:26.394556Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:26.591478Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:26.591497Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:26.696729Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:26.696752Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:26.894540Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:26.894572Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:26.991034Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:26.991053Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:27.186650Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:27.186675Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:27.296913Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:27.296933Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:27.492960Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:27.492982Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:27.594372Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:27.594389Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:27.794440Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:27.794463Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:27.896561Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:27.896582Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:28.091627Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:28.091651Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:28.194939Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:28.194963Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:28.295713Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:28.295737Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:28.394340Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:28.394356Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:28.598363Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:28.598382Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:28.686732Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:28.686752Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:28.890710Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:28.890728Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:28.994945Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:28.994963Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:29.194917Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:29.194937Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:29.294051Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:29.294067Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:29.487122Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:29.487140Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:29.591875Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:29.591892Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:29.796377Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:29.796397Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:29.895451Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:29.895473Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:29.990331Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:29.990352Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:30.195483Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:30.195505Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:30.292748Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:30.292777Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:30.497724Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:30.497744Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:30.596048Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:30.596069Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:30.792930Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:30.792951Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:30.888253Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:30.888275Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:30.987199Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:30.987223Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:31.194374Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:31.194477Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:31.292453Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:31.292469Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:31.486438Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:31.486460Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:31.593673Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:31.593688Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:31.791895Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:31.791919Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:31.887792Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:31.887831Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:32.095642Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:32.095661Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:32.193024Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:32.193068Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:32.392945Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:32.392967Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:32.496322Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:32.496352Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:32.692809Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:32.692828Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:32.791546Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:32.791616Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:32.886258Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:32.886275Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:33.094673Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:33.094696Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:33.195013Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:33.195038Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:33.400239Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:33.400261Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:33.495254Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:33.495273Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:33.686395Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:33.686413Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:33.792377Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:33.792409Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:33.992186Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:33.992205Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:34.093177Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:34.093193Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:34.188606Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:34.188623Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:34.386446Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:34.386464Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:34.493319Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:34.493336Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:34.695055Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:34.695077Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:34.797563Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:34.797598Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:34.892950Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:34.892968Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:35.095128Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:35.095143Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:35.194329Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:35.194351Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:35.396357Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:35.396381Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:35.517699Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:35.517722Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:35.693209Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:35.693234Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:35.887804Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:35.887822Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:35.988040Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:35.988059Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:36.195758Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:36.195783Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:36.293082Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:36.293122Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:36.492645Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:36.492671Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:36.589756Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:36.589781Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:36.794765Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:36.794784Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:36.893838Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:36.893877Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:37.095370Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:37.095392Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:37.196261Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:37.196282Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:37.396712Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:37.396733Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:37.496656Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:37.496680Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:37.593257Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:37.593282Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:37.787680Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:37.787718Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:37.896683Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:37.896730Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:38.091706Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:38.091723Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:38.199697Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:38.199718Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:38.392649Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:38.392670Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:38.493389Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:38.493412Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:38.700692Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:38.700732Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:38.795448Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:38.795500Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:38.892473Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:38.892491Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:39.097278Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:39.097304Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:39.195648Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:39.195700Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:39.395327Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:39.395349Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:39.495752Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:39.495776Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:39.698036Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:39.698055Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:39.789309Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:39.789331Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:39.886455Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:39.886476Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:39.987047Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:39.987066Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:40.090918Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:40.090943Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:40.187643Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:40.187667Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:42.396375Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("nexus-implementation\\TASK_TRACKER_Phase4.md")}
2025-07-19T20:08:42.396435Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:44.795396Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:44.795415Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:46.092990Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:46.093018Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:46.193295Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:46.193347Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:46.391547Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:46.391573Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:46.494004Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:46.494022Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:46.690374Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:46.690397Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:46.793808Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:46.793838Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:46.888670Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:46.888691Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:47.093962Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:47.093992Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:47.193143Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:47.193168Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:47.394383Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:47.394405Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:47.493321Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:47.493345Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:47.694447Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:47.694471Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:47.793524Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:47.793547Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:47.992872Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:47.992895Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:48.094899Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:48.094923Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:48.294553Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:48.294570Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:48.394096Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:48.394125Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:48.594266Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:48.594291Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:48.694460Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:48.694478Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:48.894797Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:48.894817Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:48.995042Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:48.995082Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:49.195750Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:49.195774Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:49.293499Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:49.293516Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:49.494079Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:49.494098Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:49.594165Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:49.594188Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:49.794948Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:49.794977Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:49.893612Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:49.893636Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:50.094960Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:50.094987Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:50.195272Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:50.195295Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:50.394639Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:50.394660Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:50.493413Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:50.493435Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:50.697316Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:50.697333Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:50.786348Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:50.786372Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:50.995177Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:50.995213Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:51.096101Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:51.096151Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:51.295641Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:51.295670Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:51.396892Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:51.396946Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:51.593916Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:51.593937Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:51.698841Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:51.698865Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:51.786219Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:51.786246Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:51.993899Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:51.993918Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:52.092862Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:52.092895Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:52.298550Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:52.298565Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:52.393098Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:52.393126Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:52.593688Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:52.593738Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:52.690913Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:52.690933Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:52.896488Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:52.896510Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:52.995015Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:52.995038Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:53.095204Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:53.095280Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:53.287519Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:53.287555Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:53.397899Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:53.397921Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:53.592292Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:53.592316Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:53.691600Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:53.691622Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:53.900972Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:53.901013Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:53.998284Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:53.998303Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:54.194156Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:54.194171Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:54.291122Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:54.291152Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:54.500433Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:54.500449Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:54.589374Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:54.589391Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:54.687339Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:54.687359Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:54.887808Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:54.887824Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:54.998466Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:54.998487Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:55.200836Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:55.200856Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:55.289164Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:55.289199Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:55.496517Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:55.496547Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:55.592436Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:55.592506Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:55.687383Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:55.687405Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:55.900912Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:55.900945Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:55.996043Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:55.996061Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:56.198687Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:56.198709Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:56.297651Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:56.297669Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:56.498493Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:56.498520Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:56.587493Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:56.587516Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:56.791381Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:56.791414Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:56.894745Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:56.894766Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:57.094993Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:57.095012Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:57.193749Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:57.193771Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:57.390932Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:57.390955Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:57.486757Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:57.486789Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:57.595027Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:57.595055Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:57.800795Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:57.800825Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:57.896261Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:57.896291Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:58.098327Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:58.098347Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:58.193913Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:58.193939Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:58.394272Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:58.394292Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:58.494099Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:58.494121Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:58.596062Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:58.596081Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:58.789544Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:58.789582Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:58.889720Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:58.889750Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:59.091638Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:59.091669Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:59.187967Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:59.187987Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:59.396951Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:59.396967Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:59.493839Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:59.493869Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:59.688208Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:59.688238Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:59.797127Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:59.797149Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:08:59.893887Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:08:59.893915Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:00.093532Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:00.093567Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:00.188801Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:00.188820Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:00.398961Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:00.398991Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:00.494713Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:00.494730Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:00.689534Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:00.689558Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:00.799709Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:00.799723Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:00.992854Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:00.992883Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:01.088820Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:01.088836Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:01.186986Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:01.187006Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:01.298889Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:01.298916Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:01.398062Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:01.398087Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:01.492979Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:01.492998Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:01.699028Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:01.699051Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:01.794932Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:01.794950Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:01.989550Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:01.989582Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:02.087129Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:02.087164Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:02.290932Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:02.290956Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:02.388599Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:02.388620Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:02.591542Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:02.591578Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:02.686735Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:02.686770Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:02.798865Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:02.798881Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:02.998360Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:02.998376Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:03.198381Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:03.198422Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:03.299148Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:03.299167Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:03.386656Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:03.386672Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:03.495143Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:03.495180Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:03.592362Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:03.592403Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:03.687472Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:03.687496Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:03.895856Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:03.895871Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:03.992506Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:03.992525Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:04.195965Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:04.195994Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:04.294210Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:04.294235Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:04.388338Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:04.388354Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:04.591355Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:04.591379Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:04.691499Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:04.691515Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:04.900300Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:04.900328Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:04.988008Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:04.988027Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:05.190971Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:05.191015Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:05.294277Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:05.294299Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:05.401170Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:05.401203Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:05.593597Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:05.593621Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:05.686050Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:05.686067Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:05.892525Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:05.892545Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:05.988763Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:05.988780Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:06.198405Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:06.198423Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:06.298798Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:06.298814Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:06.394425Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:06.394442Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:06.598950Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:06.598989Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:06.690914Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:06.690928Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:06.898995Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:06.899032Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:06.993311Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:06.993327Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:07.098602Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:07.098622Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:07.295987Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:07.296008Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:07.397401Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:07.397423Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:07.595352Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:07.595392Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:07.695147Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:07.695169Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:07.887385Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:07.887406Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:07.998331Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:07.998366Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:08.189218Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:08.189260Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:08.287649Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:08.287688Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:08.491856Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:08.491873Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:08.595384Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:08.595413Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:08.690355Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:08.690398Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:08.899219Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:08.899235Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:08.993510Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:08.993529Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:09.191394Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:09.191418Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:09.293740Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:09.293782Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:09.491532Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:09.491547Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:09.596474Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:09.596495Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:09.791652Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:09.791689Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:09.887100Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:09.887129Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:10.092654Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:10.092680Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:10.193457Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:10.193476Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:10.401293Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:10.401311Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:10.496458Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:10.496483Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:10.589538Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:10.589566Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:10.796638Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:10.796659Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:10.893547Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:10.893562Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:11.094141Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:11.094172Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:11.194711Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:11.194733Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:11.395511Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:11.395532Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:11.493925Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:11.493964Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:11.694572Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:11.694658Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:11.792971Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:11.792998Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:11.999093Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:11.999125Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:12.089508Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:12.089545Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:12.292341Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:12.292397Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:12.392969Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:12.392995Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:12.495094Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:12.495134Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:12.687604Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:12.687630Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:12.794663Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:12.794680Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:12.992529Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:12.992551Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:13.094040Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:13.094060Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:13.295430Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:13.295491Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:13.392513Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:13.392535Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:13.590595Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:13.590644Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:13.692442Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:13.692467Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:13.897194Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:13.897212Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:13.992849Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:13.992871Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:14.188034Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:14.188077Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:14.294998Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:14.295028Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:14.486022Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:14.486035Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:14.593231Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:14.593274Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:14.698778Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:14.698797Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:14.898744Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:14.898763Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:14.995737Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:14.995757Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:15.096585Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:15.096605Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:15.301244Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:15.301274Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:15.398054Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:15.398085Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:15.591876Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:15.591912Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:15.686220Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:15.686249Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:15.891767Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:15.891803Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:15.986211Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:15.986237Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:16.190972Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:16.190990Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:16.301472Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:16.301509Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:16.396345Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:16.396397Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:16.593320Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:16.593340Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:16.692179Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:16.692204Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:16.890231Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:16.890253Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:17.096398Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:17.096419Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:17.196999Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:17.197022Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:17.293237Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:17.293258Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:17.499397Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:17.499439Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:17.589548Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:17.589587Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:17.792213Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:17.792247Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:17.887689Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:17.887725Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:18.090049Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:18.090070Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:18.295256Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:18.295289Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:18.390870Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:18.390908Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:18.590857Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:18.590898Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:18.695908Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:18.695928Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:18.888973Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:18.889010Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:18.997975Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:18.998009Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:19.091954Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:19.091983Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:19.292341Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:19.292381Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:19.388074Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:19.388117Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:19.591336Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:19.591366Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:19.695268Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:19.695282Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:19.893827Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:19.893870Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:19.995922Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:19.995943Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:20.100915Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:20.100935Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:20.289016Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:20.289038Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:20.393500Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:20.393539Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:20.519494Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:20.519518Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:20.685969Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:20.685987Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:20.888263Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:20.888278Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:20.993808Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:20.993842Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:21.194224Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:21.194276Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:21.291013Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:21.291053Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:21.493518Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:21.493534Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:21.592027Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:21.592057Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:21.689060Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:21.689077Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:21.793833Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:21.793867Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:21.891464Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:21.891510Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:21.993230Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:21.993264Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:22.191174Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:22.191241Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:22.292871Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:22.292888Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:26.896250Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:26.896267Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:28.186976Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:28.187017Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:28.288202Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:28.288239Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:28.492204Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:28.492244Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:28.591063Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:28.591102Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:28.793819Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:28.793840Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:28.890665Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:28.890682Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:28.989484Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:28.989509Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:29.089293Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:29.089315Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:29.192151Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:29.192186Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:29.289715Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:29.289742Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:29.494825Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:29.494845Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:29.590313Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:29.590340Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:29.796662Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:29.796696Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:29.892139Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:29.892182Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:30.090853Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:30.090887Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:30.200086Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:30.200109Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:30.392454Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:30.392493Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:30.500760Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:30.500786Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:30.694294Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:30.694336Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:30.795474Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:30.795492Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:30.988647Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:30.988677Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:31.095455Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:31.095503Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:31.190617Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:31.190639Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:31.293849Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:31.293879Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:31.388656Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:31.388672Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:31.501746Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:31.501787Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:31.687598Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:31.687635Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:31.796791Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:31.796827Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:31.988738Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:31.988781Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:32.091067Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:32.091109Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:32.295343Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:32.295381Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:32.388897Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:32.388914Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:32.497857Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:32.497880Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:32.693999Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:32.694024Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:32.795363Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:32.795392Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:32.994025Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:32.994038Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:33.094409Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:33.094424Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:33.295790Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:33.295834Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:33.393874Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:33.393909Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:33.594487Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:33.594505Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:33.696517Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:33.696545Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:33.798512Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:33.798527Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:33.995105Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:33.995138Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:34.088371Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:34.088388Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:34.293255Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:34.293275Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:34.389431Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:34.389448Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:34.593828Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:34.593858Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:34.695880Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:34.695900Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:34.894002Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:34.894035Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:34.995666Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:34.995702Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:35.194669Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:35.194697Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:35.293082Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:35.293100Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:35.397405Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:35.397421Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:35.594178Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:35.594196Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:35.692547Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:35.692582Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:35.886859Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:35.886880Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:35.995413Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:35.995442Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:36.197536Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:36.197551Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:36.295457Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:36.295479Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:36.394946Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:36.394978Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:36.596547Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:36.596566Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:36.695930Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:36.695961Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:36.895404Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:36.895453Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:36.995157Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:36.995182Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:37.192120Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:37.192141Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:37.293496Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:37.293512Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:37.493750Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:37.493774Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:37.593778Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:37.593793Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:37.792879Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:37.792899Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:37.890426Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:37.890440Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:38.093972Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:38.094001Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:38.188148Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:38.188196Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:38.396567Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:38.396612Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:38.493989Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:38.494059Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:38.694063Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:38.694114Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:38.798263Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:38.798280Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:38.895416Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:38.895445Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:39.095312Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:39.095334Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:39.194996Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:39.195029Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:39.393959Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:39.394001Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:39.488930Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:39.488943Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:39.595478Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:39.595505Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:39.791477Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:39.791492Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:39.886095Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:39.886123Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:40.092314Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:40.092330Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:40.193345Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:40.193390Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:40.287888Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:40.287904Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:40.398609Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:40.398647Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:40.494814Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:40.494857Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:40.694356Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:40.694377Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:40.791855Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:40.791874Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:40.994034Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:40.994057Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:41.093482Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:41.093500Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:41.193524Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:41.193543Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:41.396319Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:41.396349Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:41.493952Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:41.493970Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:41.692358Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:41.692429Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:41.793431Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:41.793449Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:41.992512Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:41.992533Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:42.093853Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:42.093874Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:42.292797Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:42.292822Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:42.392532Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:42.392576Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:42.592558Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:42.592583Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:42.686133Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:42.686159Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:42.896001Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:42.896023Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:42.990950Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:42.990986Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:43.189241Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:43.189257Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:43.295045Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:43.295065Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:43.389759Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:43.389787Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:43.594878Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:43.594899Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:43.699812Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:43.699826Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:43.899412Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:43.899449Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:43.993230Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:43.993254Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:44.197688Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:44.197708Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:44.293313Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:44.293331Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:44.494015Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:44.494061Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:44.591149Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:44.591167Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:44.792092Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:44.792108Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:44.985747Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:44.985768Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:45.096167Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:45.096182Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:45.287910Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:45.287927Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:45.398617Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:45.398655Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:45.587025Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:45.587058Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:45.698240Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:45.698264Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:45.795839Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:45.795857Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:45.986940Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:45.986963Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:46.193430Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:46.193471Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:46.298013Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:46.298033Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:46.391016Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:46.391131Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:46.593227Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:46.593268Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:46.694742Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:46.694765Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:46.894834Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:46.894848Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:46.992731Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:46.992771Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:47.192921Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:47.192961Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:47.288681Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:47.288698Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:47.400663Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:47.400684Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:47.591651Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:47.591679Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:47.693857Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:47.693890Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:47.896236Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:47.896250Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:47.987573Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:47.987592Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:48.194084Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:48.194124Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:48.295414Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:48.295443Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:48.394353Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:48.394388Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:48.594578Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:48.594619Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:48.694784Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:48.694805Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:48.889049Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:48.889085Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:49.000451Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:49.000475Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:49.192806Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:49.192853Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:49.291684Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:49.291700Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:49.388009Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:49.388034Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:49.498719Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:49.498766Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:49.585576Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:49.585606Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:49.695685Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:49.695702Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:49.794953Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:49.794974Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:49.898488Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:49.898502Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:50.098372Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:50.098391Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:50.197831Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:50.197850Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:50.393631Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:50.393646Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:50.497677Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:50.497692Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:50.694082Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:50.694122Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:50.791088Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:50.791105Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:50.996340Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:50.996360Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:51.092945Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:51.092973Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:51.288931Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:51.288961Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:51.393450Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:51.393465Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:51.595358Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:51.595407Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:51.692629Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:51.692645Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:51.899173Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:51.899216Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:51.995616Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:51.995637Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:52.091871Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:52.091888Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:52.297454Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:52.297469Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:52.390259Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:52.390276Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:52.595047Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:52.595088Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:52.690760Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:52.690798Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:52.893401Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:52.893419Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:52.993376Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:52.993393Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:53.190851Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:53.190875Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:53.300439Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:53.300488Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:53.397346Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:53.397366Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:53.592938Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:53.592959Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:53.691009Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:53.691026Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:53.895455Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:53.895490Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:53.989738Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:53.989776Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:54.192553Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:54.192583Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:54.291669Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:54.291689Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:54.496807Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:54.496847Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:54.595246Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:54.595286Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:54.788452Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:54.788498Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:54.900257Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:54.900296Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:54.994798Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:54.994819Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:55.190270Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:55.190311Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:55.300281Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:55.300297Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:55.398061Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:55.398095Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:55.591210Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:55.591244Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:55.691361Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:55.691399Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:55.891659Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:55.891688Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:55.995421Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:55.995451Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:56.188196Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:56.188229Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:56.291407Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:56.291444Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:56.493530Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:56.493545Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:56.587924Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:56.587960Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:56.687701Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:56.687718Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:56.794312Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:56.794425Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:56.896022Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:56.896044Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:56.993941Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:56.993962Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:57.193576Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:57.193610Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:57.293820Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:57.293995Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:57.492899Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:57.492920Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:57.591306Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:57.591340Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:57.689749Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("nexus-implementation\\TASK_TRACKER.md")}
2025-07-19T20:09:57.689773Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:57.886994Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:57.887024Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:57.995876Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:57.995916Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:58.093703Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:58.093719Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:58.290529Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:58.290544Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:58.391994Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:58.392013Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:58.602424Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:58.602454Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:58.699314Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:58.699329Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:58.892389Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:58.892407Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:58.988919Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:58.988935Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:59.195646Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:59.195662Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:59.290835Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:59.290852Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:59.497192Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:59.497206Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:59.593680Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:59.593698Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:59.690317Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:59.690350Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:59.896606Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:59.896626Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:09:59.992532Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:09:59.992556Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:00.200667Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:00.200684Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:00.295738Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:00.295777Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:00.499226Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:00.499249Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:00.593445Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:00.593481Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:00.689162Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:00.689177Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:00.900309Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:00.900326Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:00.994941Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:00.994961Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:01.197663Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:01.197695Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:01.293938Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:01.293963Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:01.486562Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:01.486598Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:01.599270Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:01.599307Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:01.695841Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:01.695858Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:01.901792Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:01.901833Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:01.997399Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:01.997438Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:02.188454Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:02.188494Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:02.298856Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:02.298894Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:02.494439Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:02.494457Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:02.594797Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:02.594814Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:02.788194Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:02.788223Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:02.894734Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:02.894765Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:02.994657Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:02.994675Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:03.188920Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:03.188968Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:03.286512Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:03.286541Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:03.493240Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:03.493262Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:03.593029Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:03.593049Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:03.787466Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:03.787527Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:03.895855Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:03.895887Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:04.095307Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:04.095324Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:04.195505Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:04.195535Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:04.294782Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:04.294797Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:04.493908Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:04.493952Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:04.590007Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:04.590032Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:04.793296Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:04.793338Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:04.888805Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:04.888857Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:05.089215Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:05.089237Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:05.295627Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:05.295668Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:05.389560Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:05.389602Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:05.500193Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:05.500221Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:05.699178Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:05.699209Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:05.794194Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:05.794241Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:05.987672Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:05.987691Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:06.094980Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:06.095020Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:06.194716Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:06.194733Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:06.395058Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:06.395086Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:06.493206Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:06.493245Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:06.695982Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:06.696001Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:06.794703Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:06.794733Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:06.991709Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:06.991732Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:07.094780Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:07.094813Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:07.192845Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:07.192863Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:07.389923Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:07.389944Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:07.499522Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:07.499560Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:07.592414Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:07.592431Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:07.787375Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:07.787403Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:07.893361Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:07.893412Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:08.091970Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:08.091990Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:08.186815Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:08.186831Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:08.398071Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:08.398091Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:08.494684Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:08.494710Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:08.694390Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:08.694412Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:08.793827Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:08.793850Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:08.994361Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:08.994380Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:09.090911Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:09.090949Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:09.194951Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:09.194972Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:09.398482Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:09.398498Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:09.493860Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:09.493903Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:09.688884Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:09.688899Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:09.799040Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:09.799091Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:09.891568Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:09.891600Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:10.093334Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:10.093373Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:10.191618Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:10.191634Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:10.402069Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:10.402109Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:10.498579Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:10.498609Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:10.594325Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:10.594365Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:10.800981Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:10.801019Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:10.898424Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:10.898461Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:10.993219Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:10.993235Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:11.192159Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:11.192175Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:11.287399Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:11.287417Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:11.497242Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:11.497284Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:11.592346Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:11.592378Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:11.788722Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:11.788764Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:11.901436Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:11.901473Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:12.092124Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:12.092141Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:12.194589Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:12.194604Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:12.398973Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:12.399005Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:12.496470Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:12.496516Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:12.591568Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:12.591605Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:12.792221Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:12.792265Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:12.888920Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:12.888948Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:13.092557Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:13.092585Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:13.297608Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:13.297630Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:13.394289Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:13.394326Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:13.491788Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:13.491806Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:13.697904Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:13.697942Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:13.799615Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:13.799634Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:13.989798Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:13.989814Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:14.086176Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:14.086209Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:14.188453Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:14.188480Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:14.293651Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:14.293670Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:14.393418Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:14.393454Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:14.486779Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:14.486797Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:14.686564Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:14.686604Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:14.798151Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:14.798194Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:14.990685Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:14.990715Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:15.090461Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:15.090499Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:15.301327Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:15.301420Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:15.395706Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:15.395741Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:15.489685Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:15.489701Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:15.691603Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:15.691637Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:15.788329Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:15.788344Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:15.998912Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:15.998943Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:16.092283Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:16.092319Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:16.292324Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:16.292372Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:16.392262Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:16.392276Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:16.488288Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:16.488320Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:16.691832Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:16.691919Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:16.800785Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:16.800804Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:16.993113Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:16.993127Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:17.088072Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:17.088093Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:17.300146Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:17.300161Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:17.394294Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:17.394346Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:17.491863Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:17.491878Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:17.693886Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:17.693914Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:17.800068Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:17.800093Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:17.989622Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:17.989641Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:18.101743Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:18.101762Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:18.292813Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:18.292842Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:18.390491Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:18.390511Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:18.594142Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:18.594169Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:18.690246Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:18.690263Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:18.892923Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:18.892962Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:18.994291Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:18.994330Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:19.186574Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:19.186617Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:19.296850Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:19.296867Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:19.392474Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:19.392504Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:19.597111Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:19.597129Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:19.686785Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:19.686804Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:19.888034Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:19.888055Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:20.000541Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:20.000588Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:20.191860Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:20.191903Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:20.288547Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:20.288566Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:20.493653Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:20.493683Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:20.591108Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:20.591137Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:20.686404Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:20.686441Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:20.889109Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:20.889137Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:20.989739Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:20.989755Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:21.192702Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:21.192718Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:21.293929Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:21.293949Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:21.488915Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:21.488942Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:21.590829Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:21.590878Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:21.799515Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:21.799559Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:21.896454Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:21.896559Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:21.993418Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:21.993441Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:22.188945Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:22.188991Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:22.289852Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:22.289869Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:22.399613Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:22.399635Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:22.495080Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:22.495117Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:22.586941Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:22.586958Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:22.691945Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:22.691966Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:22.792481Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:22.792513Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:22.891930Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:22.891950Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:23.093920Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:23.093941Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:23.191148Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:23.191176Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:23.395761Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:23.395816Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:23.492729Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:23.492751Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:23.687319Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:23.687341Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:23.792892Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:23.792913Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:23.893679Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:23.893700Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:24.099406Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:24.099433Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:24.188513Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:24.188540Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:24.392506Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:24.392547Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:24.494100Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:24.494130Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:24.693373Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:24.693396Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:24.791147Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:24.791165Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:24.992712Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:24.992742Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:25.093936Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:25.093961Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:25.292781Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:25.292819Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:25.390690Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:25.390713Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:28.286018Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:28.286036Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:28.392871Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:28.392888Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:28.595040Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:28.595080Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:28.688941Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:28.688959Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:28.892674Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:28.892688Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:28.994345Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:28.994358Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:29.194144Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:29.194161Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:29.289752Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:29.289768Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:29.488155Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:29.488202Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:29.600027Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:29.600064Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:29.801198Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:29.801213Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:29.896243Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:29.896260Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:29.993892Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:29.993906Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:31.389645Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:31.389674Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:31.490551Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:31.490575Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:31.593809Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:31.593839Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:31.693537Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:31.693558Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:31.892937Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:31.892985Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:31.987006Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:31.987019Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:32.188260Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:32.188280Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:32.301793Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:32.301829Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:32.494793Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:32.494815Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:32.591059Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:32.591108Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:32.787823Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:32.787856Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:32.898500Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:32.898541Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:33.090669Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:33.090688Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:33.192552Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:33.192569Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:33.394673Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:33.394753Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:33.492392Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:33.492415Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:33.592148Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:33.592176Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:33.790730Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:33.790770Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:33.892304Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:33.892321Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:34.092727Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:34.092767Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:34.190133Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:34.190247Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:34.398541Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:34.398555Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:34.492258Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:34.492273Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:34.689960Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:34.689978Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:34.791611Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:34.791648Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:34.997039Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:34.997060Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:35.096182Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:35.096304Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:35.286796Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:35.286825Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:35.391805Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:35.391837Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:35.592992Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:35.593007Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:35.692891Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:35.692923Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:35.894139Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:35.894155Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:35.991470Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:35.991487Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:36.188559Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:36.188572Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:36.293113Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:36.293130Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:36.485999Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:36.486016Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:36.592143Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:36.592182Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:36.790674Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:36.790714Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:36.887412Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:36.887426Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:37.088683Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:37.088722Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:37.195140Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:37.195174Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:37.391619Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:37.391642Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:37.496458Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:37.496479Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:37.693926Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:37.693964Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:37.793964Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:37.793979Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:38.001536Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:38.001563Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:38.100953Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:38.100988Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:38.190088Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:38.190109Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:38.388834Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:38.388875Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:38.491681Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:38.491699Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:38.693108Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:38.693145Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:38.787125Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:38.787142Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:38.993557Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:38.993582Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:39.089890Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:39.089920Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:39.298373Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:39.298394Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:39.392693Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:39.392711Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:39.588077Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:39.588099Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:39.694156Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:39.694175Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:39.888060Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:39.888077Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:39.986512Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:39.986543Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:40.097036Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:40.097054Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:40.291009Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:40.291050Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:40.386004Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:40.386022Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:40.592378Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:40.592418Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:40.689404Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:40.689422Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:40.894291Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:40.894339Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:40.993724Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:40.993768Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:41.090809Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:41.090830Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:41.291171Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:41.291188Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:41.387128Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:41.387148Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:41.587298Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:41.587320Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:41.689024Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:41.689040Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:41.889192Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:41.889208Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:41.990157Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:41.990171Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:42.192903Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:42.192936Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:42.293945Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:42.293960Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:42.488780Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:42.488798Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:42.591730Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:42.591764Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:42.686512Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:42.686587Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:42.892164Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:42.892190Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:42.990587Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:42.990621Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:43.197354Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:43.197396Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:43.291709Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:43.291748Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:43.391222Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:43.391241Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:43.491955Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:43.491975Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:43.587511Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:43.587551Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:43.789286Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:43.789320Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:43.894168Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:43.894211Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:44.092760Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:44.092809Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:44.191594Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:44.191614Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:44.291304Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:44.291329Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:44.490710Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:44.490741Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:44.588270Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:44.588285Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:44.794746Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:44.794765Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:44.890192Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:44.890211Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:45.090247Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:45.090283Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:45.187180Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:45.187204Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:45.394569Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:45.394608Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:45.492779Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:45.492804Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:45.594145Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:45.594165Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:45.791125Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:45.791145Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:45.900136Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:45.900173Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:46.091199Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:46.091225Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:46.189926Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:46.189942Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:46.387461Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:46.387478Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:46.499238Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:46.499264Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:46.591935Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:46.591957Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:46.702441Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:46.702477Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:46.892972Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:46.892992Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:46.989264Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:46.989302Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:47.191251Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:47.191287Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:47.290288Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:47.290325Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:47.389659Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:47.389675Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:47.497816Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:47.497865Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:47.595390Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:47.595428Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:47.692672Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:47.692692Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:47.886070Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:47.886111Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:47.996542Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:47.996561Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:48.218590Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:48.218609Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:48.294901Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:48.294949Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:48.487627Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:48.487665Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:48.600338Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:48.600378Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:48.695720Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:48.695742Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:48.889667Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:48.889715Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:48.985519Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:48.985540Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:49.190949Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:49.190981Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T20:10:49.290148Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T20:10:49.290175Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
