2025-07-19T19:23:55.543488Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:23:55.545494Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:55.942798Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".husky\\_"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:23:55.942822Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:56.038504Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\types\\.turbo"), AnchoredSystemPathBuf("packages\\core\\types"), AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log")}
2025-07-19T19:23:56.038531Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:56.837808Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:56.837878Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:56.936977Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:56.936994Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:57.046794Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:57.046860Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:57.140612Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts.map"), AnchoredSystemPathBuf("packages\\core\\types\\dist"), AnchoredSystemPathBuf("packages\\core\\types"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.js")}
2025-07-19T19:23:57.140632Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }}))
2025-07-19T19:23:57.391087Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\dist"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:23:57.391144Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }}))
2025-07-19T19:23:57.391216Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:23:57.547470Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:57.547486Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:58.143288Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:58.143307Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:58.643751Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:58.643773Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:58.942390Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:58.942412Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:59.243495Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:59.243517Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:59.441915Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:59.441932Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:59.546372Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:59.546388Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:59.642749Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:59.642765Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:59.837584Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:59.837606Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:23:59.941851Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:23:59.941864Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:00.050980Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:00.050998Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:00.144397Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:00.144412Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:00.339281Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:00.339297Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:00.436801Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:00.436822Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:00.639387Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:00.639402Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:01.051879Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:01.051891Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:01.243382Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:01.243398Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:01.445737Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:01.445756Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:01.939792Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:01.939806Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:02.538510Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:02.538525Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:02.644042Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:02.644070Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:02.850265Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:02.850280Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:02.945039Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:02.945070Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.042722Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.042738Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.143818Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.143842Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.339030Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.339045Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.444590Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.444606Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.649625Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.649641Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.745873Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.746051Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:03.951950Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:03.951988Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.041225Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.041245Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.243042Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.243082Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.344880Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.344915Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.439756Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.439780Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.641470Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.641492Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.736510Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.736533Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:04.944251Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:04.944289Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.043718Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.043741Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.245554Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.245596Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.341402Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.341444Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.544343Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.544363Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.645079Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.645101Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.843737Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.843756Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:05.944403Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:05.944419Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:07.494914Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:24:07.494988Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:07.597464Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-07-19T19:24:07.597487Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:07.634164Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-19T19:24:08.317025Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:24:08.317109Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:08.398060Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie")}
2025-07-19T19:24:08.398087Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:08.510107Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log")}
2025-07-19T19:24:08.510132Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }}))
2025-07-19T19:24:09.595251Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\4.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("packages\\core\\utils"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo")}
2025-07-19T19:24:09.595392Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }}))
2025-07-19T19:24:10.114032Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\utils\\.turbo")}
2025-07-19T19:24:10.114046Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }}))
2025-07-19T19:24:10.114193Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:24:11.010792Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\utils\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\core\\utils\\dist"), AnchoredSystemPathBuf("packages\\core\\utils\\dist\\index.js"), AnchoredSystemPathBuf("packages\\core\\utils"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("packages\\core\\utils\\dist\\index.d.ts.map")}
2025-07-19T19:24:11.010817Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }}))
2025-07-19T19:24:13.707926Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:13.707959Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:14.895320Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:14.895340Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:14.996018Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:14.996037Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.095771Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.095789Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.196308Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.196342Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.408626Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.408643Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.504558Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.504578Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.706566Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.706607Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.798761Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.798794Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:15.895760Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:15.895782Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.009049Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.009089Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.106056Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.106072Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.203974Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.203993Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.401072Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.401094Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.495139Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.495162Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.695105Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.695125Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:16.810247Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:16.810281Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:17.009656Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:17.009678Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:24:17.094757Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:24:17.094777Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:28:37.137382Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-07-19T19:28:37.137668Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:28:37.725863Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.js"), AnchoredSystemPathBuf(".turbo\\cookies\\4.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts.map"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie")}
2025-07-19T19:28:37.725886Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }, WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }}))
2025-07-19T19:28:39.029969Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\utils\\dist\\index.js"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\5.cookie"), AnchoredSystemPathBuf("packages\\core\\utils\\dist\\index.d.ts.map")}
2025-07-19T19:28:39.029984Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }}))
2025-07-19T19:28:59.857729Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-07-19T19:28:59.857920Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:29:00.003111Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-19T19:29:04.752772Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf(".turbo")}
2025-07-19T19:29:04.752788Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:29:04.854708Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf("apps\\user-app\\.turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.js"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\4.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\5.cookie"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("apps\\user-app"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts.map")}
2025-07-19T19:29:04.854738Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }, WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:29:48.157441Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types"), AnchoredSystemPathBuf("apps\\user-app\\.next\\package.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\edge-server-production\\index.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\framework-41fd4a937747039f.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\edge-server-production\\0.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\package.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\layout-09c129a3a6da053c.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\_not-found"), AnchoredSystemPathBuf("apps\\user-app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\main-app-60e45917836c6123.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics"), AnchoredSystemPathBuf("apps\\user-app\\.next\\build-manifest.json"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\edge-server-production"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\app-build-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\edge-server-production\\0.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\cache-life.d.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\357.a0101afc97fd612d.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages\\_app-00060991db141ec3.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\GxzM5n4eRjYJZginghig4\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\server-reference-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\GxzM5n4eRjYJZginghig4"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\_not-found\\page-ce0095320e560e10.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\page-a4c47c9a192a0433.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\css"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\css\\3f499d5aeb2ea513.css"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\508.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\server-production\\0.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\93f479601ee12b01-s.p.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\9610d9e46709d722-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\trace"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\ba015fad6dcf6784-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\164.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\server-production"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\swc\\plugins\\v7_windows_x86_64_15.0.1"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\867-dacd5683055c89de.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-build-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\0.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\next-font-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\8d697b304b401681-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app\\layout.ts"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\GxzM5n4eRjYJZginghig4\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\104b06e6-5df3a4f1c270bc82.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\webpack-125b2d51288eed09.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app\\page.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\server-production\\index.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\569ce4b8f30dc480-s.p.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\next-font-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js"), AnchoredSystemPathBuf(".turbo\\cookies\\6.cookie"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app-paths-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\server-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\swc\\plugins"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\.previewinfo"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\server-production\\index.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\webpack-runtime.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\server-production\\0.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages\\_error-72338adfc9ba8e12.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\polyfills-42372ed130431b0a.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\0.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\64.0c5782f7e857a97d.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\.rscinfo"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\swc"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\113.js"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\edge-server-production\\index.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics\\build-diagnostics.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\main-fb1412fb0369d6b8.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics\\framework.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\796-7be2c160581854a8.js"), AnchoredSystemPathBuf("apps\\user-app\\.next"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\747892c23ea88013-s.woff2")}
2025-07-19T19:29:48.157476Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:29:48.167751Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:31:08.198080Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-07-19T19:31:08.198244Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:31:08.304350Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie")}
2025-07-19T19:31:08.304375Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:31:08.363460Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-19T19:31:13.594380Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:31:13.594396Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:31:13.701459Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:31:13.701475Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:31:42.671003Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\113.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\64.0c5782f7e857a97d.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\webpack-125b2d51288eed09.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\9610d9e46709d722-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\cache-life.d.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\package.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\package.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\css\\3f499d5aeb2ea513.css"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics\\build-diagnostics.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\polyfills-42372ed130431b0a.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\pBqxUUj8pnWyblJVbIuvi"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\2.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\pBqxUUj8pnWyblJVbIuvi\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\trace"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\next-font-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\GxzM5n4eRjYJZginghig4\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app\\layout.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\server-reference-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\8d697b304b401681-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\104b06e6-5df3a4f1c270bc82.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\0.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\867-dacd5683055c89de.js"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\357.a0101afc97fd612d.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\css"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\569ce4b8f30dc480-s.p.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\796-7be2c160581854a8.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\_not-found\\page-ce0095320e560e10.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\0.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\164.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\GxzM5n4eRjYJZginghig4\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages\\_app-00060991db141ec3.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\webpack-runtime.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static"), AnchoredSystemPathBuf("apps\\user-app\\.next\\app-build-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\93f479601ee12b01-s.p.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\1.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\main-app-60e45917836c6123.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\747892c23ea88013-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\next-font-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\server-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\508.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack.old"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\_not-found"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\layout-09c129a3a6da053c.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\pBqxUUj8pnWyblJVbIuvi\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app\\page.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\build-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\page-a4c47c9a192a0433.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages\\_error-72338adfc9ba8e12.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\ba015fad6dcf6784-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\main-fb1412fb0369d6b8.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics\\framework.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-build-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app-paths-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\1.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\framework-41fd4a937747039f.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\GxzM5n4eRjYJZginghig4"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\2.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page_client-reference-manifest.js")}
2025-07-19T19:31:42.671032Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:31:42.681248Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:31:47.592439Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\eslint.config.mjs")}
2025-07-19T19:31:47.592463Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:32:19.133975Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:32:19.134011Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:32:19.239043Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:32:19.239063Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:32:19.301615Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-19T19:32:24.422789Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".husky\\_")}
2025-07-19T19:32:24.422812Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:32:24.533753Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:32:24.533773Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:33:47.911886Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.next\\export\\_next"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\framework-41fd4a937747039f.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\104b06e6-5df3a4f1c270bc82.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\webpack-125b2d51288eed09.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\867-dacd5683055c89de.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\prerender-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\eslint\\.cache_1jf08jl"), AnchoredSystemPathBuf("apps\\user-app\\.next\\app-build-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\ba015fad6dcf6784-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\oHOh2fnLlfC06Ysa37uUK"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\eslint"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\server-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\1.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\508.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\pBqxUUj8pnWyblJVbIuvi"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\113.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\164.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\package.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\BUILD_ID"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\_not-found"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\next-font-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found.meta"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export\\_next\\oHOh2fnLlfC06Ysa37uUK"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found.rsc"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\polyfills-42372ed130431b0a.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\trace"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\css\\3f499d5aeb2ea513.css"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\index.meta"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack.old"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\357.a0101afc97fd612d.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\page-a4c47c9a192a0433.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\569ce4b8f30dc480-s.p.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\pBqxUUj8pnWyblJVbIuvi\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\pBqxUUj8pnWyblJVbIuvi\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\1.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\404.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\next-font-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\required-server-files.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-build-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\cache-life.d.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages\\_app-00060991db141ec3.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\500.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\main-fb1412fb0369d6b8.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\93f479601ee12b01-s.p.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\webpack-runtime.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export-detail.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export\\500.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\_not-found\\page-ce0095320e560e10.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages\\_error-72338adfc9ba8e12.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\package.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\oHOh2fnLlfC06Ysa37uUK\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico.body"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics\\framework.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\8d697b304b401681-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\3.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\3.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app-paths-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export-marker.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\server-reference-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\796-7be2c160581854a8.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\main-app-60e45917836c6123.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production"), AnchoredSystemPathBuf("apps\\user-app\\.next\\routes-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\747892c23ea88013-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\layout-09c129a3a6da053c.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\images-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\64.0c5782f7e857a97d.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\functions-config-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\build-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\index.rsc"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-manifest.json"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf("apps\\user-app\\.next\\app-path-routes-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\index.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\css"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\9610d9e46709d722-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\oHOh2fnLlfC06Ysa37uUK\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app\\layout.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics\\build-diagnostics.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app\\page.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export\\404.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico.meta"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js.nft.json")}
2025-07-19T19:33:47.911947Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:33:47.952725Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:33:53.931820Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js.nft.json")}
2025-07-19T19:33:53.931844Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:34:00.234944Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.next\\trace"), AnchoredSystemPathBuf("apps\\user-app\\.next\\next-server.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("apps\\user-app\\.next"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf("apps\\user-app\\.next\\next-minimal-server.js.nft.json"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie")}
2025-07-19T19:34:00.234973Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:34:00.247379Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:34:03.256218Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:34:03.256271Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:34:03.365244Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie")}
2025-07-19T19:34:03.365266Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:34:03.448762Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-19T19:34:04.663974Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:34:04.664000Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:34:10.164820Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:34:10.164835Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:34:10.266647Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\5.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\6.cookie"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.js"), AnchoredSystemPathBuf("packages\\core\\utils\\dist\\index.js"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts.map"), AnchoredSystemPathBuf("packages\\core\\utils\\dist\\index.d.ts.map"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo\\cookies\\4.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("packages\\core\\utils\\dist\\index.d.ts")}
2025-07-19T19:34:10.266675Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }, WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:35:24.306971Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\3.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics\\build-diagnostics.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\796-7be2c160581854a8.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\app-build-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\next-server.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\server-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\113.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\64.0c5782f7e857a97d.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages\\_app-00060991db141ec3.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\webpack-125b2d51288eed09.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\357.a0101afc97fd612d.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\index.meta"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\oHOh2fnLlfC06Ysa37uUK"), AnchoredSystemPathBuf("apps\\user-app\\.next\\prerender-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\508.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\cache-life.d.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\webpack-runtime.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\3.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\500.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app\\layout.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-build-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\KYiL1TJpTVq7D1bjwUpKf\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico.meta"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\ba015fad6dcf6784-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export\\500.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\css\\3f499d5aeb2ea513.css"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics\\framework.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\layout-09c129a3a6da053c.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\0.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\pages\\_error-72338adfc9ba8e12.js"), AnchoredSystemPathBuf("apps\\user-app\\.next"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\css"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\93f479601ee12b01-s.p.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\functions-config-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\104b06e6-5df3a4f1c270bc82.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\routes-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\trace"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export-marker.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks\\164.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\KYiL1TJpTVq7D1bjwUpKf\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export\\_next\\KYiL1TJpTVq7D1bjwUpKf"), AnchoredSystemPathBuf("apps\\user-app\\.next\\images-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\index.rsc"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app-paths-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\build-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\_not-found\\page-ce0095320e560e10.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found.meta"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found.rsc"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\867-dacd5683055c89de.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\BUILD_ID"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export\\404.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\chunks"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server"), AnchoredSystemPathBuf("apps\\user-app\\.next\\package.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\oHOh2fnLlfC06Ysa37uUK\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\4.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\next-font-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\framework-41fd4a937747039f.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\747892c23ea88013-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\8d697b304b401681-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\eslint\\.cache_1jf08jl"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\4.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\required-server-files.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\page-a4c47c9a192a0433.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\9610d9e46709d722-s.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\middleware-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\5.pack_"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\oHOh2fnLlfC06Ysa37uUK\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\5.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\main-fb1412fb0369d6b8.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media\\569ce4b8f30dc480-s.p.woff2"), AnchoredSystemPathBuf("apps\\user-app\\.next\\diagnostics"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\polyfills-42372ed130431b0a.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app\\page.ts"), AnchoredSystemPathBuf("apps\\user-app\\.next\\app-path-routes-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\KYiL1TJpTVq7D1bjwUpKf"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\media"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico.body"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\server-reference-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export\\_next"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\app"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\index.pack.old"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\next-font-manifest.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\next-minimal-server.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\export-detail.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\types\\package.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\index.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\app\\_not-found"), AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\webpack\\client-production\\0.pack"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages-manifest.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\404.html"), AnchoredSystemPathBuf("apps\\user-app\\.next\\static\\chunks\\main-app-60e45917836c6123.js"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js.nft.json")}
2025-07-19T19:35:24.307043Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:35:24.327152Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:35:28.263084Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_document.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\_not-found\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\page.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_error.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\pages\\_app.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\server\\app\\favicon.ico\\route.js.nft.json")}
2025-07-19T19:35:28.263100Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:35:34.749879Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.next\\next-minimal-server.js.nft.json"), AnchoredSystemPathBuf("apps\\user-app\\.next\\trace"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf("apps\\user-app\\.next\\next-server.js.nft.json"), AnchoredSystemPathBuf(".turbo\\cookies\\7.cookie"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf("apps\\user-app\\.next")}
2025-07-19T19:35:34.749898Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:35:34.761307Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:35:37.972074Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:35:37.972110Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:38.075939Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-07-19T19:35:38.075969Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:38.100817Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-19T19:35:38.179824Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:38.179881Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:38.407174Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:38.407369Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:38.468283Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:38.468305Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:38.676109Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:38.676177Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:38.986458Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:38.986733Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:39.073265Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:39.073290Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:39.168887Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:39.168913Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:39.368747Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:39.368771Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:39.573182Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:39.573208Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:39.679917Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:39.679941Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:39.880424Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:39.880449Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:39.969636Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:39.969662Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:40.072698Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:40.072723Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:40.279859Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:40.279881Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:40.380700Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:40.380765Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:40.580702Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:40.580768Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:40.679724Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:40.679746Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:40.880505Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:40.880528Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:40.968515Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:40.968536Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:41.176542Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:41.176561Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:41.282265Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:41.282292Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:41.481203Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:41.481225Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:41.577825Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:41.577850Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:41.674047Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:41.674069Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:41.877268Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:41.877293Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:41.979766Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:41.979791Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:42.180347Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:42.180370Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:42.277687Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:42.277713Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:42.481290Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:42.481308Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:42.575183Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:42.575213Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:42.774752Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:42.774774Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:42.880467Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:42.880487Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:43.075849Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:43.075873Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:43.180235Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:43.180257Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:43.277491Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:43.277509Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:43.479698Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:43.479720Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:43.577931Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:43.577948Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:43.770463Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:43.770485Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:43.878175Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:43.878199Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:44.081099Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:44.081124Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:44.182019Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:44.182042Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:44.379732Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:44.379768Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:44.477699Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:44.477722Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:44.576897Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:44.576920Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:44.777026Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:44.777077Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:44.872348Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:44.872367Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:45.081276Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:45.081299Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:45.183106Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:45.183203Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:45.279146Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:45.279169Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:45.381745Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:35:45.381791Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:45.483839Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.js"), AnchoredSystemPathBuf(".turbo\\cookies\\5.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\4.cookie"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-type-check.log"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-build.log"), AnchoredSystemPathBuf(".turbo\\cookies\\6.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-type-check.log"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts"), AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\dist\\index.d.ts.map"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-type-check.log"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie")}
2025-07-19T19:35:45.483866Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }, WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }}))
2025-07-19T19:35:56.402033Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-type-check.log"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\7.cookie"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf("apps\\user-app\\.turbo"), AnchoredSystemPathBuf("packages\\core\\types\\.turbo\\turbo-type-check.log"), AnchoredSystemPathBuf("packages\\core\\utils\\.turbo\\turbo-type-check.log"), AnchoredSystemPathBuf(".turbo\\cookies\\9.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\8.cookie"), AnchoredSystemPathBuf("apps\\user-app")}
2025-07-19T19:35:56.402054Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/types"), path: AnchoredSystemPathBuf("packages\\core\\types") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@nexus/utils"), path: AnchoredSystemPathBuf("packages\\core\\utils") }, WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }}))
2025-07-19T19:35:56.402118Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:35:56.414440Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:35:58.828422Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-07-19T19:35:58.828472Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:58.942731Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:35:58.942768Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:58.979282Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-19T19:35:59.257197Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:59.257271Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:59.422487Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:59.422512Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:59.557133Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:59.557160Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:59.719052Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:59.719072Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:35:59.830363Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:35:59.830390Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:00.018369Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:00.018422Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:00.126608Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:00.126633Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:00.315470Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:00.315496Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:00.427643Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:00.427665Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:00.629618Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:00.629636Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:00.724209Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:00.724229Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:00.817368Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:00.817386Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:01.030747Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:01.030770Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:01.124454Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:01.124479Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:01.326438Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:01.326461Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:01.426874Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:01.426898Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:01.626800Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:01.626825Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:01.722755Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:01.722808Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:01.817762Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:01.817779Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:02.025601Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:02.025663Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:02.130116Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:02.130141Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:02.215320Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:02.215342Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:02.326106Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:02.326132Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:02.428366Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:02.428391Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:02.630666Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:02.630685Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:02.727023Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:02.727042Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:02.927977Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:02.927999Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:03.029853Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:03.029876Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:03.115147Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:03.115170Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:03.323022Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:03.323047Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:03.427223Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:03.427244Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:03.628000Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:03.628054Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:03.728873Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:03.728897Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:03.926989Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:03.927024Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:04.028923Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:04.028946Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:04.228065Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:04.228088Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:04.315456Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:04.315482Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:04.523246Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:04.523268Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:04.620848Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:04.620868Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:04.830936Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:04.830958Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:04.926979Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:04.927024Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:05.125674Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:05.125698Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:05.224615Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:05.224664Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:05.325826Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19")}
2025-07-19T19:36:05.325852Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:05.530531Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:36:05.530550Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:05.715391Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf(".turbo\\cookies\\2.cookie"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-lint.log")}
2025-07-19T19:36:05.715417Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:15.834781Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\user-app\\.next\\cache\\eslint\\.cache_1jf08jl"), AnchoredSystemPathBuf(".turbo\\cookies\\3.cookie"), AnchoredSystemPathBuf("apps\\user-app\\.turbo"), AnchoredSystemPathBuf("apps\\user-app\\.turbo\\turbo-lint.log"), AnchoredSystemPathBuf(".turbo\\daemon\\f26f1ae3ff7c72f6-turbo.log.2025-07-19"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:36:15.834801Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@nexus/user-app"), path: AnchoredSystemPathBuf("apps\\user-app") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-19T19:36:15.848340Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-19T19:36:30.611419Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-07-19T19:36:30.611561Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
