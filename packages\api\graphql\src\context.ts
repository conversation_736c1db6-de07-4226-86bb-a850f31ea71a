import { PubSub } from "graphql-subscriptions";
import DataLoader from "dataloader";
import { GraphQLContext, DataLoaders } from "./types";

// Create PubSub instance for subscriptions
export const pubsub = new PubSub();

// DataLoader batch functions
const batchUsers = async (ids: readonly string[]) => {
  // TODO: Implement actual user batch loading from database
  return ids.map(id => ({
    id,
    email: `user${id}@example.com`,
    name: `User ${id}`,
    tenantId: "tenant_123",
    roles: ["member"],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  }));
};

const batchRoles = async (ids: readonly string[]) => {
  // TODO: Implement actual role batch loading from database
  return ids.map(id => ({
    id,
    name: `Role ${id}`,
    slug: `role_${id}`,
    description: `Description for role ${id}`,
    level: "WORKSPACE",
    permissions: [],
    isSystem: false,
    isActive: true,
    tenantId: "tenant_123",
    createdAt: new Date(),
    updatedAt: new Date(),
  }));
};

const batchWorkspaces = async (ids: readonly string[]) => {
  // TODO: Implement actual workspace batch loading from database
  return ids.map(id => ({
    id,
    name: `Workspace ${id}`,
    slug: `workspace_${id}`,
    description: `Description for workspace ${id}`,
    tenantId: "tenant_123",
    ownerId: "user_123",
    settings: {},
    isActive: true,
    memberCount: 5,
    teamCount: 2,
    projectCount: 10,
    storageUsed: 1073741824,
    createdAt: new Date(),
    updatedAt: new Date(),
  }));
};

const batchTeams = async (ids: readonly string[]) => {
  // TODO: Implement actual team batch loading from database
  return ids.map(id => ({
    id,
    name: `Team ${id}`,
    description: `Description for team ${id}`,
    workspaceId: "workspace_1",
    leadId: "user_123",
    isActive: true,
    memberCount: 5,
    projectCount: 3,
    createdAt: new Date(),
    updatedAt: new Date(),
  }));
};

const batchProjects = async (ids: readonly string[]) => {
  // TODO: Implement actual project batch loading from database
  return ids.map(id => ({
    id,
    name: `Project ${id}`,
    description: `Description for project ${id}`,
    workspaceId: "workspace_1",
    teamId: "team_1",
    ownerId: "user_123",
    settings: {},
    status: "ACTIVE",
    isActive: true,
    fileCount: 25,
    storageUsed: 536870912,
    lastActivity: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  }));
};

const batchFiles = async (ids: readonly string[]) => {
  // TODO: Implement actual file batch loading from database
  return ids.map(id => ({
    id,
    filename: `file_${id}.pdf`,
    originalName: `Document ${id}.pdf`,
    mimetype: "application/pdf",
    size: 1048576,
    url: `/uploads/file_${id}.pdf`,
    downloadUrl: `/api/files/${id}/download`,
    thumbnailUrl: null,
    workspaceId: "workspace_1",
    projectId: "project_1",
    uploadedById: "user_123",
    description: `Description for file ${id}`,
    tags: ["document"],
    isPublic: false,
    metadata: {},
    checksum: "abc123",
    version: 1,
    parentFileId: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  }));
};

// Create DataLoaders
export const createDataLoaders = (): DataLoaders => {
  return {
    userLoader: new DataLoader(batchUsers, {
      cache: true,
      maxBatchSize: 100,
    }),
    roleLoader: new DataLoader(batchRoles, {
      cache: true,
      maxBatchSize: 100,
    }),
    workspaceLoader: new DataLoader(batchWorkspaces, {
      cache: true,
      maxBatchSize: 100,
    }),
    teamLoader: new DataLoader(batchTeams, {
      cache: true,
      maxBatchSize: 100,
    }),
    projectLoader: new DataLoader(batchProjects, {
      cache: true,
      maxBatchSize: 100,
    }),
    fileLoader: new DataLoader(batchFiles, {
      cache: true,
      maxBatchSize: 100,
    }),
  };
};

// Create GraphQL context
export const createContext = async (req: any): Promise<GraphQLContext> => {
  // Extract user from request (set by authentication middleware)
  const user = req.user || null;
  
  // Extract tenant/workspace/team context from headers
  const tenantId = req.headers["x-tenant-id"] || user?.tenantId;
  const workspaceId = req.headers["x-workspace-id"];
  const teamId = req.headers["x-team-id"];
  
  // Generate request ID
  const requestId = req.id || `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  // Extract client info
  const ip = req.ip || req.connection?.remoteAddress;
  const userAgent = req.headers["user-agent"];

  return {
    user,
    tenantId,
    workspaceId,
    teamId,
    requestId,
    ip,
    userAgent,
    pubsub,
    dataloaders: createDataLoaders(),
  };
};

// Context for WebSocket subscriptions
export const createSubscriptionContext = async (
  connectionParams: any,
  websocket: any,
  connectionContext: any
): Promise<GraphQLContext> => {
  // Extract authentication token from connection params
  const token = connectionParams?.authorization || connectionParams?.Authorization;
  
  // TODO: Verify JWT token and extract user
  let user = null;
  if (token) {
    try {
      // Verify token and extract user
      // user = await verifyJWT(token);
    } catch (error) {
      throw new Error("Invalid authentication token");
    }
  }

  // Extract context from connection params
  const tenantId = connectionParams?.tenantId || user?.tenantId;
  const workspaceId = connectionParams?.workspaceId;
  const teamId = connectionParams?.teamId;
  
  const requestId = `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  return {
    user,
    tenantId,
    workspaceId,
    teamId,
    requestId,
    ip: websocket?.remoteAddress,
    userAgent: connectionParams?.userAgent,
    pubsub,
    dataloaders: createDataLoaders(),
  };
};
