import { BillingSettings, BillingStats, UsageMetrics } from "./billing-types";

export class BillingService {
  private tenantId: string;

  constructor(tenantId: string) {
    this.tenantId = tenantId;
  }

  // Get billing settings
  async getBillingSettings(): Promise<BillingSettings> {
    const response = await fetch("/api/billing/settings", {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch billing settings");
    }

    return response.json();
  }

  // Update billing settings
  async updateBillingSettings(settings: Partial<BillingSettings>): Promise<BillingSettings> {
    const response = await fetch("/api/billing/settings", {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify(settings),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to update billing settings");
    }

    return response.json();
  }

  // Get billing statistics
  async getBillingStats(period?: { start: Date; end: Date }): Promise<BillingStats> {
    const params = new URLSearchParams();
    
    if (period) {
      params.append("startDate", period.start.toISOString());
      params.append("endDate", period.end.toISOString());
    }

    const response = await fetch(`/api/billing/stats?${params}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch billing statistics");
    }

    return response.json();
  }

  // Get usage metrics
  async getUsageMetrics(period?: { start: Date; end: Date }): Promise<UsageMetrics> {
    const params = new URLSearchParams();
    
    if (period) {
      params.append("startDate", period.start.toISOString());
      params.append("endDate", period.end.toISOString());
    }

    const response = await fetch(`/api/billing/usage?${params}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch usage metrics");
    }

    return response.json();
  }

  // Update subscription plan
  async updateSubscriptionPlan(subscriptionId: string, newPriceId: string): Promise<void> {
    const response = await fetch(`/api/billing/subscriptions/${subscriptionId}/plan`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify({ priceId: newPriceId }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to update subscription plan");
    }
  }

  // Cancel subscription
  async cancelSubscription(subscriptionId: string, cancelAtPeriodEnd: boolean = true): Promise<void> {
    const response = await fetch(`/api/billing/subscriptions/${subscriptionId}/cancel`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify({ cancelAtPeriodEnd }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to cancel subscription");
    }
  }

  // Resume subscription
  async resumeSubscription(subscriptionId: string): Promise<void> {
    const response = await fetch(`/api/billing/subscriptions/${subscriptionId}/resume`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to resume subscription");
    }
  }

  // Preview subscription change
  async previewSubscriptionChange(
    subscriptionId: string,
    newPriceId: string
  ): Promise<{
    amountDue: number;
    currency: string;
    prorationDate: Date;
    nextInvoiceAmount: number;
  }> {
    const response = await fetch(`/api/billing/subscriptions/${subscriptionId}/preview`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify({ priceId: newPriceId }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to preview subscription change");
    }

    return response.json();
  }

  // Get upcoming invoice
  async getUpcomingInvoice(): Promise<any> {
    const response = await fetch("/api/billing/invoices/upcoming", {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch upcoming invoice");
    }

    return response.json();
  }

  // Apply coupon
  async applyCoupon(couponCode: string): Promise<void> {
    const response = await fetch("/api/billing/coupons/apply", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify({ couponCode }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to apply coupon");
    }
  }

  // Remove coupon
  async removeCoupon(): Promise<void> {
    const response = await fetch("/api/billing/coupons/remove", {
      method: "DELETE",
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to remove coupon");
    }
  }
}

export const createBillingService = (tenantId: string): BillingService => {
  return new BillingService(tenantId);
};
