"use client";

import React, { createContext, useContext, useReducer, useCallback } from "react";
import { TenantContext, TenantAction, TenantContextState, TenantData, TenantUser } from "./tenant-types";
import { useAuth } from "@nexus/auth-client";

// Initial state
const initialState: TenantContextState = {
  tenant: null,
  user: null,
  isLoading: false,
  error: null,
  permissions: [],
};

// Reducer
function tenantReducer(state: TenantContextState, action: TenantAction): TenantContextState {
  switch (action.type) {
    case "SET_LOADING":
      return { ...state, isLoading: action.payload };
    case "SET_TENANT":
      return { ...state, tenant: action.payload, error: null };
    case "SET_USER":
      return { ...state, user: action.payload };
    case "SET_ERROR":
      return { ...state, error: action.payload, isLoading: false };
    case "SET_PERMISSIONS":
      return { ...state, permissions: action.payload };
    case "RESET":
      return initialState;
    default:
      return state;
  }
}

// Context
const TenantContextInstance = createContext<TenantContext | undefined>(undefined);

// Provider component
export function TenantProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(tenantReducer, initialState);
  const { user: authUser } = useAuth();

  // Actions
  const setLoading = useCallback((loading: boolean) => {
    dispatch({ type: "SET_LOADING", payload: loading });
  }, []);

  const setError = useCallback((error: string | null) => {
    dispatch({ type: "SET_ERROR", payload: error });
  }, []);

  const hasPermission = useCallback((permission: string): boolean => {
    return state.permissions.includes(permission);
  }, [state.permissions]);

  const hasRole = useCallback((role: string): boolean => {
    return state.user?.role === role;
  }, [state.user?.role]);

  const switchTenant = useCallback(async (tenantId: string) => {
    setLoading(true);
    try {
      // Implementation would call API to switch tenant
      // For now, just simulate
      await new Promise(resolve => setTimeout(resolve, 1000));
      setError(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to switch tenant");
    } finally {
      setLoading(false);
    }
  }, [setLoading, setError]);

  const updateTenant = useCallback(async (updates: Partial<TenantData>) => {
    if (!state.tenant) return;
    
    setLoading(true);
    try {
      // Implementation would call API to update tenant
      const updatedTenant = { ...state.tenant, ...updates };
      dispatch({ type: "SET_TENANT", payload: updatedTenant });
      setError(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to update tenant");
    } finally {
      setLoading(false);
    }
  }, [state.tenant, setLoading, setError]);

  const refreshTenant = useCallback(async () => {
    if (!authUser?.tenantId) return;
    
    setLoading(true);
    try {
      // Implementation would call API to refresh tenant data
      await new Promise(resolve => setTimeout(resolve, 500));
      setError(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to refresh tenant");
    } finally {
      setLoading(false);
    }
  }, [authUser?.tenantId, setLoading, setError]);

  // Context value
  const value: TenantContext = {
    ...state,
    hasPermission,
    hasRole,
    switchTenant,
    updateTenant,
    refreshTenant,
    setLoading,
    setError,
  };

  return (
    <TenantContextInstance.Provider value={value}>
      {children}
    </TenantContextInstance.Provider>
  );
}

// Hook to use tenant context
export function useTenantContext() {
  const context = useContext(TenantContextInstance);
  if (context === undefined) {
    throw new Error("useTenantContext must be used within a TenantProvider");
  }
  return context;
}
