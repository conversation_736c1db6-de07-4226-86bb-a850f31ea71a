# Comprehensive Monitoring Implementation

## Feature Overview

Implement a comprehensive monitoring and observability stack for the NEXUS SaaS Starter using Prometheus, Grafana, and OpenTelemetry to provide real-time insights into system health, performance metrics, and business KPIs with multi-tenant isolation and alerting.

## Context & Research

### Current Implementation Analysis
Based on codebase analysis, existing monitoring patterns include:
- **Basic Logging**: Winston logger with structured logging
- **Error Tracking**: Basic error handling and reporting
- **Performance Metrics**: Some database and cache performance tracking
- **Health Checks**: Basic API health endpoints

### Technology Stack Verification (Context7)

**Prometheus Monitoring Patterns**:
- Counter, Gauge, Histogram, and Summary metric types
- Custom metrics with labels for multi-dimensional data
- Alert rules with PromQL expressions and thresholds
- Service discovery and scrape configurations

**Grafana Visualization**:
- Dashboard creation with panels and queries
- Alerting integration with multiple notification channels
- Template variables for dynamic dashboards
- Multi-tenant dashboard isolation

**OpenTelemetry Integration**:
- Distributed tracing with spans and traces
- Metrics collection and export to Prometheus
- Resource attributes and service identification
- Auto-instrumentation for popular frameworks

## Implementation Blueprint

### Data Models and Structure

```typescript
// Enhanced monitoring configuration
interface MonitoringConfig {
  metrics: {
    prometheus: PrometheusConfig;
    custom: CustomMetricsConfig;
    business: BusinessMetricsConfig;
  };
  tracing: {
    openTelemetry: OpenTelemetryConfig;
    sampling: SamplingConfig;
    exporters: ExporterConfig[];
  };
  alerting: {
    rules: AlertRule[];
    channels: NotificationChannel[];
    escalation: EscalationPolicy[];
  };
  dashboards: {
    system: SystemDashboard[];
    business: BusinessDashboard[];
    tenant: TenantDashboard[];
  };
}

interface PrometheusConfig {
  scrapeInterval: string;
  evaluationInterval: string;
  retention: string;
  targets: ScrapeTarget[];
}

interface CustomMetricsConfig {
  application: ApplicationMetric[];
  infrastructure: InfrastructureMetric[];
  business: BusinessMetric[];
}
```

### Task Breakdown

**Phase 1: Metrics Collection Infrastructure (3-4 hours)**

1. **Prometheus Setup and Configuration**
   - File: `monitoring/prometheus/prometheus.yml`
   - Configure scrape targets and service discovery
   - Set up recording rules and alert rules
   - Implement multi-tenant metric isolation
   - Pattern: Follow Prometheus best practices

2. **Custom Metrics Implementation**
   - File: `lib/monitoring/metrics.ts`
   - Implement application-specific metrics
   - Add business KPI tracking
   - Configure metric labels and dimensions
   - Pattern: OpenMetrics specification compliance

**Phase 2: Observability and Tracing (3-4 hours)**

3. **OpenTelemetry Integration**
   - File: `lib/monitoring/tracing.ts`
   - Implement distributed tracing
   - Add automatic instrumentation
   - Configure trace sampling and export
   - Pattern: OpenTelemetry best practices

4. **Grafana Dashboard Setup**
   - File: `monitoring/grafana/dashboards/`
   - Create system performance dashboards
   - Implement business metrics visualization
   - Add tenant-specific dashboard isolation
   - Pattern: Grafana dashboard as code

**Phase 3: Alerting and Incident Management (2-3 hours)**

5. **Alert Rules and Notification**
   - File: `monitoring/prometheus/alerts.yml`
   - Define SLI/SLO-based alert rules
   - Configure multi-channel notifications
   - Implement alert escalation policies
   - Pattern: Site reliability engineering practices

6. **Health Checks and Synthetic Monitoring**
   - File: `lib/monitoring/health-checks.ts`
   - Implement comprehensive health checks
   - Add synthetic transaction monitoring
   - Configure uptime and availability tracking
   - Pattern: Proactive monitoring strategies

### Integration Points

**Multi-Tenant Integration**:
- Tenant-specific metric namespaces and labels
- Isolated dashboards and alert rules per tenant
- Tenant-aware resource utilization tracking
- Fair usage monitoring and billing metrics

**Application Integration**:
- Automatic instrumentation of API routes
- Database query performance monitoring
- Cache hit/miss ratio tracking
- User session and authentication metrics

**Infrastructure Integration**:
- Kubernetes cluster monitoring
- Database performance metrics
- Redis cluster health monitoring
- CDN and load balancer metrics

## Validation Gates

### Level 1: Syntax & Style
```bash
npm run lint                    # ESLint checks
npx tsc --noEmit               # TypeScript validation
promtool check config monitoring/prometheus/prometheus.yml
```

### Level 2: Metrics Collection Tests
```bash
# Start monitoring stack
docker-compose -f monitoring/docker-compose.yml up -d

# Test metrics endpoints
curl http://localhost:3000/metrics
curl http://localhost:9090/api/v1/query?query=up

# Test custom metrics
curl http://localhost:3000/api/health
curl http://localhost:9090/api/v1/query?query=nexus_http_requests_total
```

### Level 3: Dashboard and Alerting Tests
```bash
# Test Grafana dashboards
curl -u admin:admin http://localhost:3000/api/dashboards/home

# Test alert rules
curl http://localhost:9090/api/v1/rules

# Trigger test alerts
npm run test:alerts
```

### Level 4: End-to-End Monitoring Tests
```bash
# Load testing with monitoring
npx autocannon -c 50 -d 120 \
  -H "x-tenant-id=test-tenant" \
  https://nexus-app.example.com/api/analytics/dashboard

# Verify metrics collection
curl "http://localhost:9090/api/v1/query?query=rate(nexus_http_requests_total[5m])"

# Check alert firing
curl http://localhost:9093/api/v1/alerts
```

## Quality Standards Checklist

- [ ] Prometheus collecting metrics from all services
- [ ] Custom business metrics tracking key KPIs
- [ ] Grafana dashboards for system and business metrics
- [ ] Alert rules covering critical system conditions
- [ ] Multi-tenant metric isolation and security
- [ ] Distributed tracing for request flow visibility
- [ ] Health checks for all critical components
- [ ] SLI/SLO monitoring and reporting
- [ ] Incident response automation
- [ ] Performance regression detection

## Security Considerations

- **Metric Security**: Sensitive data exclusion from metrics
- **Dashboard Access**: Role-based access control for dashboards
- **Alert Security**: Secure notification channels and PII protection
- **Trace Security**: Sensitive data scrubbing in traces
- **Multi-Tenant Isolation**: Strict metric and dashboard separation

## Performance Targets

- **Metric Collection**: <1% performance overhead
- **Query Response**: <5s for complex dashboard queries
- **Alert Latency**: <60s from condition to notification
- **Trace Sampling**: 1-10% sampling rate for production
- **Dashboard Load**: <3s for standard dashboard rendering

---

**Implementation Priority**: HIGH - Critical for production observability
**Estimated Effort**: 10-15 hours
**Dependencies**: Prometheus, Grafana, OpenTelemetry
**Success Metrics**: MTTR reduction, system visibility, proactive issue detection

## Detailed Implementation

### 1. Prometheus Configuration

```yaml
# monitoring/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'nexus-saas-prod'
    replica: 'prometheus-1'

rule_files:
  - "alerts/*.yml"
  - "recording-rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Next.js application metrics
  - job_name: 'nexus-app'
    scrape_interval: 10s
    metrics_path: '/api/metrics'
    static_configs:
      - targets: ['nexus-app:3000']
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_tenant]
        target_label: tenant_id

  # Database metrics
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']
    relabel_configs:
      - source_labels: [__address__]
        target_label: database_instance

  # Redis metrics
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']

  # Node exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # Kubernetes API server metrics
  - job_name: 'kubernetes-apiservers'
    kubernetes_sd_configs:
      - role: endpoints
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: default;kubernetes;https

  # Kubernetes pods with prometheus.io annotations
  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
      - role: pod
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_pod_label_(.+)
      - source_labels: [__meta_kubernetes_namespace]
        action: replace
        target_label: kubernetes_namespace
      - source_labels: [__meta_kubernetes_pod_name]
        action: replace
        target_label: kubernetes_pod_name

# Remote write configuration for long-term storage
remote_write:
  - url: "https://prometheus-remote-write.example.com/api/v1/write"
    basic_auth:
      username: "nexus-saas"
      password: "secure-password"
    write_relabel_configs:
      - source_labels: [__name__]
        regex: 'nexus_.*'
        action: keep
```

### 2. Custom Metrics Implementation

```typescript
// lib/monitoring/metrics.ts
import { register, Counter, Histogram, Gauge, Summary } from 'prom-client';
import { getTenantContext } from '@/lib/auth/tenant-context';

// HTTP Request Metrics
export const httpRequestsTotal = new Counter({
  name: 'nexus_http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code', 'tenant_id'],
  registers: [register]
});

export const httpRequestDuration = new Histogram({
  name: 'nexus_http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'tenant_id'],
  buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5, 10],
  registers: [register]
});

// Database Metrics
export const databaseQueryDuration = new Histogram({
  name: 'nexus_database_query_duration_seconds',
  help: 'Duration of database queries in seconds',
  labelNames: ['operation', 'table', 'tenant_id'],
  buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5],
  registers: [register]
});

export const databaseConnectionsActive = new Gauge({
  name: 'nexus_database_connections_active',
  help: 'Number of active database connections',
  labelNames: ['pool', 'tenant_id'],
  registers: [register]
});

export const databaseConnectionsMax = new Gauge({
  name: 'nexus_database_connections_max',
  help: 'Maximum number of database connections',
  labelNames: ['pool'],
  registers: [register]
});

// Cache Metrics
export const cacheOperationsTotal = new Counter({
  name: 'nexus_cache_operations_total',
  help: 'Total number of cache operations',
  labelNames: ['operation', 'cache_type', 'result', 'tenant_id'],
  registers: [register]
});

export const cacheHitRatio = new Gauge({
  name: 'nexus_cache_hit_ratio',
  help: 'Cache hit ratio (0-1)',
  labelNames: ['cache_type', 'tenant_id'],
  registers: [register]
});

// Business Metrics
export const activeUsers = new Gauge({
  name: 'nexus_active_users_total',
  help: 'Number of active users',
  labelNames: ['tenant_id', 'time_window'],
  registers: [register]
});

export const subscriptionEvents = new Counter({
  name: 'nexus_subscription_events_total',
  help: 'Total number of subscription events',
  labelNames: ['event_type', 'plan', 'tenant_id'],
  registers: [register]
});

export const revenueGenerated = new Counter({
  name: 'nexus_revenue_generated_total',
  help: 'Total revenue generated in cents',
  labelNames: ['currency', 'plan', 'tenant_id'],
  registers: [register]
});

// System Metrics
export const memoryUsage = new Gauge({
  name: 'nexus_memory_usage_bytes',
  help: 'Memory usage in bytes',
  labelNames: ['type'],
  registers: [register]
});

export const cpuUsage = new Gauge({
  name: 'nexus_cpu_usage_percent',
  help: 'CPU usage percentage',
  registers: [register]
});

// Error Metrics
export const errorsTotal = new Counter({
  name: 'nexus_errors_total',
  help: 'Total number of errors',
  labelNames: ['error_type', 'severity', 'component', 'tenant_id'],
  registers: [register]
});

// Middleware for automatic HTTP metrics collection
export function metricsMiddleware() {
  return async (req: any, res: any, next: any) => {
    const start = Date.now();
    const tenantId = await getTenantContext() || 'unknown';

    // Track request start
    const route = req.route?.path || req.path || 'unknown';
    const method = req.method;

    res.on('finish', () => {
      const duration = (Date.now() - start) / 1000;
      const statusCode = res.statusCode.toString();

      // Record metrics
      httpRequestsTotal.inc({
        method,
        route,
        status_code: statusCode,
        tenant_id: tenantId
      });

      httpRequestDuration.observe({
        method,
        route,
        tenant_id: tenantId
      }, duration);

      // Track errors
      if (res.statusCode >= 400) {
        errorsTotal.inc({
          error_type: res.statusCode >= 500 ? 'server_error' : 'client_error',
          severity: res.statusCode >= 500 ? 'high' : 'medium',
          component: 'http',
          tenant_id: tenantId
        });
      }
    });

    next();
  };
}

// Database metrics collection
export function trackDatabaseQuery(
  operation: string,
  table: string,
  duration: number,
  tenantId?: string
) {
  databaseQueryDuration.observe({
    operation,
    table,
    tenant_id: tenantId || 'unknown'
  }, duration);
}

// Cache metrics collection
export function trackCacheOperation(
  operation: 'get' | 'set' | 'delete',
  cacheType: 'redis' | 'memory',
  result: 'hit' | 'miss' | 'success' | 'error',
  tenantId?: string
) {
  cacheOperationsTotal.inc({
    operation,
    cache_type: cacheType,
    result,
    tenant_id: tenantId || 'unknown'
  });
}

// Business metrics collection
export function trackUserActivity(tenantId: string, activeCount: number) {
  activeUsers.set({
    tenant_id: tenantId,
    time_window: '5m'
  }, activeCount);
}

export function trackSubscriptionEvent(
  eventType: 'created' | 'updated' | 'cancelled',
  plan: string,
  tenantId: string
) {
  subscriptionEvents.inc({
    event_type: eventType,
    plan,
    tenant_id: tenantId
  });
}

export function trackRevenue(
  amount: number,
  currency: string,
  plan: string,
  tenantId: string
) {
  revenueGenerated.inc({
    currency,
    plan,
    tenant_id: tenantId
  }, amount);
}

// System metrics collection
export function collectSystemMetrics() {
  const memUsage = process.memoryUsage();

  memoryUsage.set({ type: 'rss' }, memUsage.rss);
  memoryUsage.set({ type: 'heap_used' }, memUsage.heapUsed);
  memoryUsage.set({ type: 'heap_total' }, memUsage.heapTotal);
  memoryUsage.set({ type: 'external' }, memUsage.external);

  // CPU usage (simplified)
  const cpuUsagePercent = process.cpuUsage();
  cpuUsage.set((cpuUsagePercent.user + cpuUsagePercent.system) / 1000000);
}

// Start system metrics collection
setInterval(collectSystemMetrics, 10000); // Every 10 seconds

// Metrics endpoint
export function getMetrics(): Promise<string> {
  return register.metrics();
}

// Clear metrics (for testing)
export function clearMetrics() {
  register.clear();
}
```

### 3. OpenTelemetry Tracing Implementation

```typescript
// lib/monitoring/tracing.ts
import { NodeSDK } from '@opentelemetry/sdk-node';
import { Resource } from '@opentelemetry/resources';
import { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { PeriodicExportingMetricReader } from '@opentelemetry/sdk-metrics';
import { PrometheusExporter } from '@opentelemetry/exporter-prometheus';
import { JaegerExporter } from '@opentelemetry/exporter-jaeger';
import { BatchSpanProcessor } from '@opentelemetry/sdk-trace-node';
import { trace, context, SpanStatusCode, SpanKind } from '@opentelemetry/api';
import { getTenantContext } from '@/lib/auth/tenant-context';

// Initialize OpenTelemetry SDK
const sdk = new NodeSDK({
  resource: new Resource({
    [SemanticResourceAttributes.SERVICE_NAME]: 'nexus-saas',
    [SemanticResourceAttributes.SERVICE_VERSION]: process.env.APP_VERSION || '1.0.0',
    [SemanticResourceAttributes.SERVICE_NAMESPACE]: 'nexus',
    [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: process.env.NODE_ENV || 'development',
  }),

  // Auto-instrumentation for popular libraries
  instrumentations: [
    getNodeAutoInstrumentations({
      '@opentelemetry/instrumentation-fs': {
        enabled: false, // Disable file system instrumentation for performance
      },
      '@opentelemetry/instrumentation-http': {
        enabled: true,
        requestHook: (span, request) => {
          // Add custom attributes to HTTP spans
          span.setAttributes({
            'http.user_agent': request.headers['user-agent'] || '',
            'http.tenant_id': request.headers['x-tenant-id'] || 'unknown',
          });
        },
      },
      '@opentelemetry/instrumentation-express': {
        enabled: true,
      },
      '@opentelemetry/instrumentation-prisma': {
        enabled: true,
      },
      '@opentelemetry/instrumentation-redis': {
        enabled: true,
      },
    }),
  ],

  // Span processors
  spanProcessor: new BatchSpanProcessor(
    new JaegerExporter({
      endpoint: process.env.JAEGER_ENDPOINT || 'http://localhost:14268/api/traces',
    }),
    {
      maxQueueSize: 1000,
      scheduledDelayMillis: 5000,
      exportTimeoutMillis: 30000,
      maxExportBatchSize: 512,
    }
  ),

  // Metrics
  metricReader: new PeriodicExportingMetricReader({
    exporter: new PrometheusExporter({
      port: 9464,
      endpoint: '/metrics',
    }),
    exportIntervalMillis: 10000,
  }),
});

// Start the SDK
sdk.start();

// Tracer instance
const tracer = trace.getTracer('nexus-saas', process.env.APP_VERSION || '1.0.0');

// Custom tracing utilities
export class TracingService {
  // Create a new span with tenant context
  static async createSpan<T>(
    name: string,
    operation: (span: any) => Promise<T>,
    options: {
      kind?: SpanKind;
      attributes?: Record<string, string | number | boolean>;
    } = {}
  ): Promise<T> {
    return tracer.startActiveSpan(name, {
      kind: options.kind || SpanKind.INTERNAL,
      attributes: {
        'tenant.id': await getTenantContext() || 'unknown',
        ...options.attributes,
      },
    }, async (span) => {
      try {
        const result = await operation(span);
        span.setStatus({ code: SpanStatusCode.OK });
        return result;
      } catch (error) {
        span.recordException(error as Error);
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: (error as Error).message,
        });
        throw error;
      } finally {
        span.end();
      }
    });
  }

  // Trace database operations
  static async traceDatabaseOperation<T>(
    operation: string,
    table: string,
    query: () => Promise<T>
  ): Promise<T> {
    return this.createSpan(
      `db.${operation}`,
      async (span) => {
        span.setAttributes({
          'db.operation': operation,
          'db.table': table,
          'db.system': 'postgresql',
        });

        const start = Date.now();
        try {
          const result = await query();
          const duration = Date.now() - start;

          span.setAttributes({
            'db.duration_ms': duration,
            'db.success': true,
          });

          return result;
        } catch (error) {
          span.setAttributes({
            'db.success': false,
            'db.error': (error as Error).message,
          });
          throw error;
        }
      },
      { kind: SpanKind.CLIENT }
    );
  }

  // Trace cache operations
  static async traceCacheOperation<T>(
    operation: string,
    key: string,
    cacheOperation: () => Promise<T>
  ): Promise<T> {
    return this.createSpan(
      `cache.${operation}`,
      async (span) => {
        span.setAttributes({
          'cache.operation': operation,
          'cache.key': key,
          'cache.system': 'redis',
        });

        const start = Date.now();
        try {
          const result = await cacheOperation();
          const duration = Date.now() - start;

          span.setAttributes({
            'cache.duration_ms': duration,
            'cache.hit': result !== null && result !== undefined,
          });

          return result;
        } catch (error) {
          span.setAttributes({
            'cache.error': (error as Error).message,
          });
          throw error;
        }
      },
      { kind: SpanKind.CLIENT }
    );
  }

  // Trace external API calls
  static async traceExternalCall<T>(
    service: string,
    endpoint: string,
    apiCall: () => Promise<T>
  ): Promise<T> {
    return this.createSpan(
      `external.${service}`,
      async (span) => {
        span.setAttributes({
          'http.url': endpoint,
          'http.method': 'GET', // Default, should be passed as parameter
          'external.service': service,
        });

        const start = Date.now();
        try {
          const result = await apiCall();
          const duration = Date.now() - start;

          span.setAttributes({
            'http.duration_ms': duration,
            'http.status_code': 200, // Should be extracted from response
          });

          return result;
        } catch (error) {
          span.setAttributes({
            'http.error': (error as Error).message,
          });
          throw error;
        }
      },
      { kind: SpanKind.CLIENT }
    );
  }

  // Add custom attributes to current span
  static addAttributes(attributes: Record<string, string | number | boolean>) {
    const span = trace.getActiveSpan();
    if (span) {
      span.setAttributes(attributes);
    }
  }

  // Add custom events to current span
  static addEvent(name: string, attributes?: Record<string, string | number | boolean>) {
    const span = trace.getActiveSpan();
    if (span) {
      span.addEvent(name, attributes);
    }
  }

  // Get current trace ID for correlation
  static getCurrentTraceId(): string {
    const span = trace.getActiveSpan();
    if (span) {
      return span.spanContext().traceId;
    }
    return '';
  }

  // Create a child span manually
  static createChildSpan(name: string, attributes?: Record<string, string | number | boolean>) {
    return tracer.startSpan(name, {
      attributes: {
        'tenant.id': 'unknown', // Should be set by caller
        ...attributes,
      },
    });
  }
}

// Middleware for automatic request tracing
export function tracingMiddleware() {
  return (req: any, res: any, next: any) => {
    const span = tracer.startSpan(`HTTP ${req.method} ${req.route?.path || req.path}`, {
      kind: SpanKind.SERVER,
      attributes: {
        'http.method': req.method,
        'http.url': req.url,
        'http.route': req.route?.path || req.path,
        'http.user_agent': req.headers['user-agent'] || '',
        'tenant.id': req.headers['x-tenant-id'] || 'unknown',
      },
    });

    // Set span in context
    context.with(trace.setSpan(context.active(), span), () => {
      res.on('finish', () => {
        span.setAttributes({
          'http.status_code': res.statusCode,
          'http.response_size': res.get('content-length') || 0,
        });

        if (res.statusCode >= 400) {
          span.setStatus({
            code: SpanStatusCode.ERROR,
            message: `HTTP ${res.statusCode}`,
          });
        } else {
          span.setStatus({ code: SpanStatusCode.OK });
        }

        span.end();
      });

      next();
    });
  };
}

// Graceful shutdown
process.on('SIGTERM', () => {
  sdk.shutdown()
    .then(() => console.log('OpenTelemetry terminated'))
    .catch((error) => console.log('Error terminating OpenTelemetry', error))
    .finally(() => process.exit(0));
});

export { tracer };
```

### 4. Alert Rules Configuration

```yaml
# monitoring/prometheus/alerts/nexus-alerts.yml
groups:
  - name: nexus.system
    rules:
      # High CPU usage
      - alert: HighCPUUsage
        expr: nexus_cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
          component: system
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% for more than 5 minutes"
          runbook_url: "https://docs.nexus-saas.com/runbooks/high-cpu"

      # High memory usage
      - alert: HighMemoryUsage
        expr: (nexus_memory_usage_bytes{type="heap_used"} / nexus_memory_usage_bytes{type="heap_total"}) * 100 > 85
        for: 5m
        labels:
          severity: warning
          component: system
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}% for more than 5 minutes"

      # Application down
      - alert: ApplicationDown
        expr: up{job="nexus-app"} == 0
        for: 1m
        labels:
          severity: critical
          component: application
        annotations:
          summary: "Nexus application is down"
          description: "Nexus application has been down for more than 1 minute"
          runbook_url: "https://docs.nexus-saas.com/runbooks/app-down"

  - name: nexus.http
    rules:
      # High error rate
      - alert: HighErrorRate
        expr: |
          (
            rate(nexus_http_requests_total{status_code=~"5.."}[5m]) /
            rate(nexus_http_requests_total[5m])
          ) * 100 > 5
        for: 2m
        labels:
          severity: critical
          component: http
        annotations:
          summary: "High HTTP error rate"
          description: "HTTP error rate is {{ $value }}% for tenant {{ $labels.tenant_id }}"

      # High response time
      - alert: HighResponseTime
        expr: |
          histogram_quantile(0.95,
            rate(nexus_http_request_duration_seconds_bucket[5m])
          ) > 2
        for: 5m
        labels:
          severity: warning
          component: http
        annotations:
          summary: "High HTTP response time"
          description: "95th percentile response time is {{ $value }}s"

      # Low request rate (potential issue)
      - alert: LowRequestRate
        expr: rate(nexus_http_requests_total[5m]) < 0.1
        for: 10m
        labels:
          severity: warning
          component: http
        annotations:
          summary: "Low HTTP request rate"
          description: "Request rate is {{ $value }} requests/second, which may indicate an issue"

  - name: nexus.database
    rules:
      # High database connection usage
      - alert: HighDatabaseConnectionUsage
        expr: |
          (
            nexus_database_connections_active /
            nexus_database_connections_max
          ) * 100 > 80
        for: 5m
        labels:
          severity: warning
          component: database
        annotations:
          summary: "High database connection usage"
          description: "Database connection usage is {{ $value }}% for pool {{ $labels.pool }}"

      # Slow database queries
      - alert: SlowDatabaseQueries
        expr: |
          histogram_quantile(0.95,
            rate(nexus_database_query_duration_seconds_bucket[5m])
          ) > 1
        for: 5m
        labels:
          severity: warning
          component: database
        annotations:
          summary: "Slow database queries detected"
          description: "95th percentile query time is {{ $value }}s for {{ $labels.operation }} on {{ $labels.table }}"

  - name: nexus.cache
    rules:
      # Low cache hit rate
      - alert: LowCacheHitRate
        expr: nexus_cache_hit_ratio < 0.7
        for: 10m
        labels:
          severity: warning
          component: cache
        annotations:
          summary: "Low cache hit rate"
          description: "Cache hit rate is {{ $value }} for {{ $labels.cache_type }} cache"

  - name: nexus.business
    rules:
      # Revenue drop
      - alert: RevenueDropAlert
        expr: |
          (
            rate(nexus_revenue_generated_total[1h]) -
            rate(nexus_revenue_generated_total[1h] offset 24h)
          ) / rate(nexus_revenue_generated_total[1h] offset 24h) * 100 < -20
        for: 30m
        labels:
          severity: warning
          component: business
        annotations:
          summary: "Significant revenue drop detected"
          description: "Revenue has dropped by {{ $value }}% compared to 24 hours ago"

      # High subscription cancellation rate
      - alert: HighCancellationRate
        expr: |
          rate(nexus_subscription_events_total{event_type="cancelled"}[1h]) /
          rate(nexus_subscription_events_total{event_type="created"}[1h]) > 0.1
        for: 1h
        labels:
          severity: warning
          component: business
        annotations:
          summary: "High subscription cancellation rate"
          description: "Cancellation rate is {{ $value }} (10% threshold exceeded)"

  - name: nexus.tenant
    rules:
      # Tenant resource usage spike
      - alert: TenantResourceSpike
        expr: |
          rate(nexus_http_requests_total[5m]) by (tenant_id) >
          rate(nexus_http_requests_total[5m] offset 1h) by (tenant_id) * 5
        for: 10m
        labels:
          severity: warning
          component: tenant
        annotations:
          summary: "Tenant resource usage spike"
          description: "Tenant {{ $labels.tenant_id }} has 5x higher request rate than 1 hour ago"

      # Tenant error spike
      - alert: TenantErrorSpike
        expr: |
          rate(nexus_errors_total[5m]) by (tenant_id) >
          rate(nexus_errors_total[5m] offset 1h) by (tenant_id) * 3
        for: 5m
        labels:
          severity: critical
          component: tenant
        annotations:
          summary: "Tenant error spike detected"
          description: "Tenant {{ $labels.tenant_id }} has 3x higher error rate than 1 hour ago"
```

*Built with ❤️ by Nexus-Master Agent*
*Where 125 Senior Developers Meet AI Excellence*
