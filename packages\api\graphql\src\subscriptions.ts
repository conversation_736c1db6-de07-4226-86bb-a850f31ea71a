import { WebSocketServer } from "ws";
import { useServer } from "graphql-ws/lib/use/ws";
import { GraphQLSchema } from "graphql";
import { createSubscriptionContext } from "./context";
import { GraphQLContext } from "./types";

export interface SubscriptionServerOptions {
  schema: GraphQLSchema;
  port?: number;
  path?: string;
}

export const createSubscriptionServer = (options: SubscriptionServerOptions) => {
  const { schema, port = 4001, path = "/graphql" } = options;

  // Create WebSocket server
  const wsServer = new WebSocketServer({
    port,
    path,
  });

  // Create GraphQL subscription server
  const serverCleanup = useServer(
    {
      schema,
      context: async (ctx) => {
        return createSubscriptionContext(
          ctx.connectionParams,
          ctx.extra.socket,
          ctx
        );
      },
      onConnect: async (ctx) => {
        console.log("WebSocket client connected");
        
        // Validate connection params
        const { connectionParams } = ctx;
        
        // Optional: Validate authentication token
        if (connectionParams?.authorization) {
          try {
            // TODO: Verify JWT token
            // await verifyJWT(connectionParams.authorization);
          } catch (error) {
            console.error("WebSocket authentication failed:", error);
            return false; // Reject connection
          }
        }

        return true; // Accept connection
      },
      onDisconnect: (ctx, code, reason) => {
        console.log("WebSocket client disconnected:", code, reason);
      },
      onError: (ctx, message, errors) => {
        console.error("WebSocket error:", message, errors);
      },
      onSubscribe: async (ctx, message) => {
        console.log("New subscription:", message.payload.operationName);
        
        // Optional: Add subscription-level authorization
        const context = await createSubscriptionContext(
          ctx.connectionParams,
          ctx.extra.socket,
          ctx
        );

        // Check if user can subscribe to this operation
        if (!context.user && message.payload.operationName !== "publicSubscription") {
          throw new Error("Authentication required for subscriptions");
        }

        return {
          schema,
          operationName: message.payload.operationName,
          document: message.payload.query,
          variableValues: message.payload.variables,
          contextValue: context,
        };
      },
    },
    wsServer
  );

  console.log(`GraphQL subscription server running on ws://localhost:${port}${path}`);

  return {
    wsServer,
    serverCleanup,
    close: () => {
      serverCleanup.dispose();
      wsServer.close();
    },
  };
};

// Subscription event types and helpers
export const SUBSCRIPTION_EVENTS = {
  USER_CREATED: "USER_CREATED",
  USER_UPDATED: "USER_UPDATED",
  USER_DELETED: "USER_DELETED",
  ROLE_ASSIGNED: "ROLE_ASSIGNED",
  ROLE_REVOKED: "ROLE_REVOKED",
  WORKSPACE_CREATED: "WORKSPACE_CREATED",
  WORKSPACE_UPDATED: "WORKSPACE_UPDATED",
  TEAM_CREATED: "TEAM_CREATED",
  TEAM_UPDATED: "TEAM_UPDATED",
  TEAM_MEMBER_ADDED: "TEAM_MEMBER_ADDED",
  TEAM_MEMBER_REMOVED: "TEAM_MEMBER_REMOVED",
  PROJECT_CREATED: "PROJECT_CREATED",
  PROJECT_UPDATED: "PROJECT_UPDATED",
  FILE_UPLOADED: "FILE_UPLOADED",
  FILE_UPDATED: "FILE_UPDATED",
  FILE_DELETED: "FILE_DELETED",
  NOTIFICATION_SENT: "NOTIFICATION_SENT",
} as const;

// Helper functions for publishing events
export const publishUserEvent = (
  pubsub: any,
  event: string,
  payload: any,
  context: { tenantId?: string; userId?: string }
) => {
  pubsub.publish(event, {
    ...payload,
    tenantId: context.tenantId,
    userId: context.userId,
  });
};

export const publishWorkspaceEvent = (
  pubsub: any,
  event: string,
  payload: any,
  context: { tenantId?: string; workspaceId?: string }
) => {
  pubsub.publish(event, {
    ...payload,
    tenantId: context.tenantId,
    workspaceId: context.workspaceId,
  });
};

export const publishTeamEvent = (
  pubsub: any,
  event: string,
  payload: any,
  context: { tenantId?: string; workspaceId?: string; teamId?: string }
) => {
  pubsub.publish(event, {
    ...payload,
    tenantId: context.tenantId,
    workspaceId: context.workspaceId,
    teamId: context.teamId,
  });
};

export const publishProjectEvent = (
  pubsub: any,
  event: string,
  payload: any,
  context: { 
    tenantId?: string; 
    workspaceId?: string; 
    teamId?: string; 
    projectId?: string 
  }
) => {
  pubsub.publish(event, {
    ...payload,
    tenantId: context.tenantId,
    workspaceId: context.workspaceId,
    teamId: context.teamId,
    projectId: context.projectId,
  });
};

export const publishFileEvent = (
  pubsub: any,
  event: string,
  payload: any,
  context: { 
    tenantId?: string; 
    workspaceId?: string; 
    projectId?: string; 
    fileId?: string 
  }
) => {
  pubsub.publish(event, {
    ...payload,
    tenantId: context.tenantId,
    workspaceId: context.workspaceId,
    projectId: context.projectId,
    fileId: context.fileId,
  });
};
