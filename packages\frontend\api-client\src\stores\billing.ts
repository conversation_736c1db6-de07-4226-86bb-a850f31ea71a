import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { restApiClient } from "../rest";
import { endpoints } from "../config";

interface BillingState {
  subscription: any | null;
  invoices: any[];
  plans: any[];
  paymentMethods: any[];
  isLoading: boolean;
  error: string | null;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  fetchSubscription: () => Promise<void>;
  fetchInvoices: (pagination?: any) => Promise<void>;
  fetchPlans: () => Promise<void>;
  fetchPaymentMethods: () => Promise<void>;
  createSubscription: (input: any) => Promise<any>;
  updateSubscription: (input: any) => Promise<any>;
  cancelSubscription: (input?: any) => Promise<any>;
  addPaymentMethod: (input: any) => Promise<any>;
  removePaymentMethod: (id: string) => Promise<void>;
}

export const useBillingStore = create<BillingState>()(
  immer((set, get) => ({
    subscription: null,
    invoices: [],
    plans: [],
    paymentMethods: [],
    isLoading: false,
    error: null,

    setLoading: (loading) =>
      set((state) => {
        state.isLoading = loading;
      }),

    setError: (error) =>
      set((state) => {
        state.error = error;
      }),

    fetchSubscription: async () => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.get(endpoints.billing.subscription);

        set((state) => {
          state.subscription = response.data;
        });
      } catch (error: any) {
        setError(error.message || "Failed to fetch subscription");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    fetchInvoices: async (pagination?: any) => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (pagination?.page) params.append("page", pagination.page.toString());
        if (pagination?.limit) params.append("limit", pagination.limit.toString());

        const response = await restApiClient.get(
          `${endpoints.billing.invoices}?${params.toString()}`
        );

        set((state) => {
          state.invoices = response.data.items || response.data;
        });
      } catch (error: any) {
        setError(error.message || "Failed to fetch invoices");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    fetchPlans: async () => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.get(endpoints.billing.plans);

        set((state) => {
          state.plans = response.data;
        });
      } catch (error: any) {
        setError(error.message || "Failed to fetch plans");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    fetchPaymentMethods: async () => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.get(endpoints.billing.paymentMethods);

        set((state) => {
          state.paymentMethods = response.data;
        });
      } catch (error: any) {
        setError(error.message || "Failed to fetch payment methods");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    createSubscription: async (input: any) => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.post(endpoints.billing.subscription, input);
        const subscription = response.data;

        set((state) => {
          state.subscription = subscription;
        });

        return subscription;
      } catch (error: any) {
        setError(error.message || "Failed to create subscription");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    updateSubscription: async (input: any) => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.put(endpoints.billing.subscription, input);
        const subscription = response.data;

        set((state) => {
          state.subscription = subscription;
        });

        return subscription;
      } catch (error: any) {
        setError(error.message || "Failed to update subscription");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    cancelSubscription: async (input?: any) => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.delete(endpoints.billing.subscription, {
          data: input
        });
        const subscription = response.data;

        set((state) => {
          state.subscription = subscription;
        });

        return subscription;
      } catch (error: any) {
        setError(error.message || "Failed to cancel subscription");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    addPaymentMethod: async (input: any) => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.post(endpoints.billing.addPaymentMethod, input);
        const paymentMethod = response.data;

        set((state) => {
          state.paymentMethods.push(paymentMethod);
        });

        return paymentMethod;
      } catch (error: any) {
        setError(error.message || "Failed to add payment method");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    removePaymentMethod: async (id: string) => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        await restApiClient.delete(endpoints.billing.removePaymentMethod(id));

        set((state) => {
          state.paymentMethods = state.paymentMethods.filter(pm => pm.id !== id);
        });
      } catch (error: any) {
        setError(error.message || "Failed to remove payment method");
        throw error;
      } finally {
        setLoading(false);
      }
    },
  }))
);

// Billing utilities
export const billingUtils = {
  // Format currency
  formatCurrency: (amount: number, currency: string = "USD"): string => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency,
    }).format(amount);
  },

  // Get subscription status color
  getSubscriptionStatusColor: (status: string): string => {
    switch (status.toLowerCase()) {
      case "active":
        return "green";
      case "trialing":
        return "blue";
      case "past_due":
        return "yellow";
      case "canceled":
      case "unpaid":
        return "red";
      default:
        return "gray";
    }
  },

  // Get invoice status color
  getInvoiceStatusColor: (status: string): string => {
    switch (status.toLowerCase()) {
      case "paid":
        return "green";
      case "open":
        return "blue";
      case "draft":
        return "gray";
      case "void":
      case "uncollectible":
        return "red";
      default:
        return "gray";
    }
  },

  // Format billing cycle
  formatBillingCycle: (cycle: string): string => {
    switch (cycle.toLowerCase()) {
      case "monthly":
        return "Monthly";
      case "yearly":
        return "Yearly";
      default:
        return cycle;
    }
  },

  // Calculate yearly savings
  calculateYearlySavings: (monthlyPrice: number, yearlyPrice: number): number => {
    const yearlyMonthly = monthlyPrice * 12;
    return ((yearlyMonthly - yearlyPrice) / yearlyMonthly) * 100;
  },

  // Get plan recommendation
  getPlanRecommendation: (plans: any[], currentUsage: any): any => {
    // Simple logic - find the plan that best fits current usage
    return plans.find(plan => 
      (!plan.limits.users || currentUsage.users <= plan.limits.users) &&
      (!plan.limits.projects || currentUsage.projects <= plan.limits.projects) &&
      (!plan.limits.storage || currentUsage.storage <= plan.limits.storage)
    ) || plans[0];
  },

  // Check if feature is available in plan
  isFeatureAvailable: (plan: any, feature: string): boolean => {
    return plan?.features?.includes(feature) || false;
  },

  // Get usage percentage
  getUsagePercentage: (used: number, limit: number): number => {
    if (limit === -1) return 0; // Unlimited
    if (limit === 0) return 100;
    return Math.min((used / limit) * 100, 100);
  },

  // Get usage color
  getUsageColor: (percentage: number): string => {
    if (percentage >= 90) return "red";
    if (percentage >= 75) return "yellow";
    return "green";
  },

  // Format payment method
  formatPaymentMethod: (paymentMethod: any): string => {
    if (paymentMethod.type === "CARD") {
      return `${paymentMethod.brand} •••• ${paymentMethod.last4}`;
    }
    return paymentMethod.type;
  },

  // Check if payment method is expired
  isPaymentMethodExpired: (paymentMethod: any): boolean => {
    if (paymentMethod.type !== "CARD") return false;
    
    const now = new Date();
    const expiry = new Date(paymentMethod.expiryYear, paymentMethod.expiryMonth - 1);
    
    return expiry < now;
  },

  // Get current subscription
  getCurrentSubscription: (): any => {
    return useBillingStore.getState().subscription;
  },

  // Get available plans
  getAvailablePlans: (): any[] => {
    return useBillingStore.getState().plans;
  },

  // Get payment methods
  getPaymentMethods: (): any[] => {
    return useBillingStore.getState().paymentMethods;
  },

  // Get recent invoices
  getRecentInvoices: (limit: number = 5): any[] => {
    const { invoices } = useBillingStore.getState();
    return invoices
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, limit);
  },
};
