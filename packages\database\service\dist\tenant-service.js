import { TenantStatus, SubscriptionPlan } from "@nexus/database-schema";
import { prisma } from "./client";
export class TenantService {
    // Create a new tenant
    async create(data) {
        return prisma.tenant.create({
            data: {
                ...data,
                status: TenantStatus.ACTIVE,
                plan: SubscriptionPlan.STARTER,
            },
        });
    }
    // Find tenant by ID
    async findById(id) {
        return prisma.tenant.findUnique({
            where: { id },
            include: {
                users: true,
                workspaces: true,
            },
        });
    }
    // Find tenant by slug
    async findBySlug(slug) {
        return prisma.tenant.findUnique({
            where: { slug },
            include: {
                users: true,
                workspaces: true,
            },
        });
    }
    // Find tenant by domain
    async findByDomain(domain) {
        return prisma.tenant.findUnique({
            where: { domain },
            include: {
                users: true,
                workspaces: true,
            },
        });
    }
    // Update tenant
    async update(id, data) {
        return prisma.tenant.update({
            where: { id },
            data,
        });
    }
    // Update tenant status
    async updateStatus(id, status) {
        return prisma.tenant.update({
            where: { id },
            data: { status },
        });
    }
    // Update tenant plan
    async updatePlan(id, plan) {
        return prisma.tenant.update({
            where: { id },
            data: { plan },
        });
    }
    // Delete tenant (soft delete by setting status to INACTIVE)
    async delete(id) {
        return prisma.tenant.update({
            where: { id },
            data: { status: TenantStatus.INACTIVE },
        });
    }
    // Hard delete tenant (use with caution)
    async hardDelete(id) {
        return prisma.tenant.delete({
            where: { id },
        });
    }
    // List all tenants with pagination
    async list(page = 1, limit = 10) {
        const skip = (page - 1) * limit;
        const [tenants, total] = await Promise.all([
            prisma.tenant.findMany({
                skip,
                take: limit,
                orderBy: { createdAt: "desc" },
                include: {
                    _count: {
                        select: {
                            users: true,
                            workspaces: true,
                        },
                    },
                },
            }),
            prisma.tenant.count(),
        ]);
        return {
            data: tenants,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    // Check if slug is available
    async isSlugAvailable(slug) {
        const tenant = await prisma.tenant.findUnique({
            where: { slug },
        });
        return !tenant;
    }
    // Check if domain is available
    async isDomainAvailable(domain) {
        const tenant = await prisma.tenant.findUnique({
            where: { domain },
        });
        return !tenant;
    }
}
export const tenantService = new TenantService();
