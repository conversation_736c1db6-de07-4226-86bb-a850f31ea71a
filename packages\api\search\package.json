{"name": "@nexus/search", "version": "0.1.0", "description": "Full-text search and analytics service for Nexus SaaS", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "clean": "rm -rf dist", "test": "jest", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "@nexus/utils": "workspace:*", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "@elastic/elasticsearch": "^8.11.0", "redis": "^4.6.12", "bull": "^4.12.2", "natural": "^6.12.0", "stopword": "^2.0.8", "compromise": "^14.10.0", "sentiment": "^5.0.2", "keyword-extractor": "^0.0.25", "uuid": "^9.0.1", "zod": "^4.0.5", "winston": "^3.11.0", "cron": "^3.1.6", "prisma": "^5.7.1", "@prisma/client": "^5.7.1"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.0", "@types/node": "^22.10.2", "@types/cron": "^2.4.0", "jest": "^29.5.0", "tsx": "^4.6.2", "typescript": "^5.8.0", "eslint": "^8.57.0"}}