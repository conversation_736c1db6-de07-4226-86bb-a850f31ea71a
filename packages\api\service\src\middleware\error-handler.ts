import { FastifyError, FastifyRequest, FastifyReply } from "fastify";
import { ApiError, ApiResponse } from "../types";
import { config, isDevelopment } from "../config";

// Error handler middleware
export const errorHandler = async (
  error: FastifyError,
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> => {
  const requestId = request.id;
  
  // Log error
  request.log.error({
    error: {
      message: error.message,
      stack: error.stack,
      code: error.code,
    },
    requestId,
    url: request.url,
    method: request.method,
  }, "Request error");

  let statusCode = 500;
  let errorCode = "INTERNAL_SERVER_ERROR";
  let message = "Internal server error";
  let details: any = undefined;

  // Handle different error types
  if (error instanceof ApiError) {
    statusCode = error.statusCode;
    errorCode = error.code;
    message = error.message;
    details = error.details;
  } else if (error.validation) {
    // Fastify validation error
    statusCode = 400;
    errorCode = "VALIDATION_ERROR";
    message = "Validation failed";
    details = error.validation;
  } else if (error.statusCode) {
    // HTTP error
    statusCode = error.statusCode;
    errorCode = error.code || "HTTP_ERROR";
    message = error.message;
  } else if (error.code === "FST_JWT_NO_AUTHORIZATION_IN_HEADER") {
    statusCode = 401;
    errorCode = "AUTHENTICATION_ERROR";
    message = "Authentication required";
  } else if (error.code === "FST_JWT_AUTHORIZATION_TOKEN_INVALID") {
    statusCode = 401;
    errorCode = "AUTHENTICATION_ERROR";
    message = "Invalid authentication token";
  } else if (error.code === "FST_JWT_AUTHORIZATION_TOKEN_EXPIRED") {
    statusCode = 401;
    errorCode = "AUTHENTICATION_ERROR";
    message = "Authentication token expired";
  } else if (error.code === "FST_TOO_MANY_REQUESTS") {
    statusCode = 429;
    errorCode = "RATE_LIMIT_ERROR";
    message = "Rate limit exceeded";
  }

  // Create error response
  const errorResponse: ApiResponse = {
    success: false,
    error: {
      code: errorCode,
      message,
      ...(details && { details }),
      ...(isDevelopment && error.stack && { stack: error.stack }),
    },
    meta: {
      timestamp: new Date().toISOString(),
      requestId,
      version: process.env.npm_package_version || "unknown",
    },
  };

  // Send error response
  reply.status(statusCode).send(errorResponse);
};
