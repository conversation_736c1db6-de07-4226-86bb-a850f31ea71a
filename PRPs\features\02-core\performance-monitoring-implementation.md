# Performance Monitoring Implementation

## Overview
Implement comprehensive performance monitoring system for the NEXUS SaaS Starter that provides real-time system health metrics, application performance monitoring, user experience tracking, and intelligent alerting. This implementation leverages React Native Performance patterns adapted for Next.js environments to deliver enterprise-grade monitoring capabilities.

## Context Research Summary

### Performance Monitoring Architecture (Shopify React Native Performance)
- **Real-Time Metrics Collection**: Performance profiling with minimal overhead
- **Render Performance Tracking**: Time-to-interactive and render pass measurements
- **System Health Monitoring**: CPU, memory, network, and disk usage tracking
- **Background Processing**: Efficient data collection without blocking UI
- **Error Tracking**: Comprehensive error monitoring and crash reporting
- **Performance Profiling**: Detailed performance analysis and bottleneck identification

### Analytics Integration (davidwells/analytics)
- **Event Tracking**: Custom performance events and user interactions
- **Multi-Provider Support**: Integration with monitoring platforms (DataDog, New Relic, etc.)
- **Real-Time Streaming**: Live performance data streaming
- **Storage Utilities**: Performance data persistence and retrieval
- **Plugin Architecture**: Extensible monitoring capabilities

### GraphQL Performance (Pothos)
- **Query Performance**: GraphQL query execution monitoring
- **Subscription Monitoring**: Real-time subscription performance tracking
- **Schema Analysis**: Performance impact of schema changes
- **Caching Metrics**: Cache hit/miss ratios and performance impact
- **Error Tracking**: GraphQL error monitoring and resolution

## Implementation Plan

### Phase 1: Core Performance Monitoring
1. **Performance Metrics Collection**
   - Page load times and Core Web Vitals
   - API response times and database query performance
   - Memory usage and CPU utilization
   - Network latency and throughput

2. **Real-Time Monitoring Dashboard**
   - Live performance metrics visualization
   - System health status indicators
   - Performance trend analysis
   - Alerting and notification system

### Phase 2: Advanced Analytics
1. **User Experience Monitoring**
   - Real user monitoring (RUM) implementation
   - User journey performance tracking
   - Session replay for performance issues
   - A/B testing performance impact

2. **Application Performance Monitoring**
   - Database query optimization tracking
   - Third-party service performance monitoring
   - Background job performance analysis
   - Error rate and resolution tracking

### Phase 3: Predictive Analytics
1. **Performance Forecasting**
   - Predictive performance modeling
   - Capacity planning recommendations
   - Performance regression detection
   - Automated performance optimization

2. **Intelligent Alerting**
   - Machine learning-based anomaly detection
   - Contextual alert prioritization
   - Automated incident response
   - Performance degradation prevention

## Technical Implementation

### File Structure
```
src/
├── app/
│   ├── api/
│   │   └── v1/
│   │       ├── performance/
│   │       │   ├── metrics/
│   │       │   │   ├── route.ts                     # Performance metrics API
│   │       │   │   ├── collect/
│   │       │   │   │   └── route.ts                 # Metrics collection endpoint
│   │       │   │   ├── real-time/
│   │       │   │   │   └── route.ts                 # Real-time metrics WebSocket
│   │       │   │   └── export/
│   │       │   │       └── route.ts                 # Metrics export API
│   │       │   ├── monitoring/
│   │       │   │   ├── route.ts                     # Monitoring configuration API
│   │       │   │   ├── alerts/
│   │       │   │   │   ├── route.ts                 # Alert management API
│   │       │   │   │   ├── [id]/
│   │       │   │   │   │   └── route.ts             # Alert CRUD operations
│   │       │   │   │   └── rules/
│   │       │   │   │       └── route.ts             # Alert rules API
│   │       │   │   ├── dashboards/
│   │       │   │   │   ├── route.ts                 # Dashboard management API
│   │       │   │   │   └── [id]/
│   │       │   │   │       └── route.ts             # Dashboard configuration API
│   │       │   │   └── reports/
│   │       │   │       ├── route.ts                 # Performance reports API
│   │       │   │       └── scheduled/
│   │       │   │           └── route.ts             # Scheduled reports API
│   │       │   ├── health/
│   │       │   │   ├── route.ts                     # Health check API
│   │       │   │   ├── system/
│   │       │   │   │   └── route.ts                 # System health API
│   │       │   │   ├── database/
│   │       │   │   │   └── route.ts                 # Database health API
│   │       │   │   └── services/
│   │       │   │       └── route.ts                 # Service health API
│   │       │   └── analytics/
│   │       │       ├── route.ts                     # Performance analytics API
│   │       │       ├── user-experience/
│   │       │       │   └── route.ts                 # UX metrics API
│   │       │       ├── application/
│   │       │       │   └── route.ts                 # Application metrics API
│   │       │       └── predictions/
│   │       │           └── route.ts                 # Predictive analytics API
│   └── monitoring/
│       ├── page.tsx                                 # Main monitoring dashboard
│       ├── metrics/
│       │   └── page.tsx                             # Metrics overview page
│       ├── alerts/
│       │   └── page.tsx                             # Alert management page
│       ├── health/
│       │   └── page.tsx                             # System health page
│       └── reports/
│           └── page.tsx                             # Performance reports page
├── components/
│   ├── monitoring/
│   │   ├── dashboard/
│   │   │   ├── MonitoringDashboard.tsx              # Main monitoring dashboard
│   │   │   ├── MetricsOverview.tsx                  # Metrics overview component
│   │   │   ├── SystemHealthStatus.tsx               # System health indicators
│   │   │   ├── PerformanceCharts.tsx                # Performance visualization
│   │   │   └── AlertsPanel.tsx                      # Active alerts display
│   │   ├── metrics/
│   │   │   ├── MetricsCollector.tsx                 # Client-side metrics collection
│   │   │   ├── RealTimeMetrics.tsx                  # Real-time metrics display
│   │   │   ├── PerformanceMetrics.tsx               # Performance metrics view
│   │   │   ├── CoreWebVitals.tsx                    # Core Web Vitals tracking
│   │   │   └── CustomMetrics.tsx                    # Custom metrics display
│   │   ├── alerts/
│   │   │   ├── AlertManager.tsx                     # Alert management interface
│   │   │   ├── AlertRules.tsx                       # Alert rules configuration
│   │   │   ├── AlertHistory.tsx                     # Alert history view
│   │   │   ├── AlertNotifications.tsx               # Alert notifications display
│   │   │   └── AlertConfiguration.tsx               # Alert configuration form
│   │   ├── health/
│   │   │   ├── HealthCheck.tsx                      # Health check component
│   │   │   ├── SystemStatus.tsx                     # System status display
│   │   │   ├── ServiceHealth.tsx                    # Service health monitoring
│   │   │   ├── DatabaseHealth.tsx                   # Database health status
│   │   │   └── HealthTimeline.tsx                   # Health status timeline
│   │   ├── analytics/
│   │   │   ├── PerformanceAnalytics.tsx             # Performance analytics dashboard
│   │   │   ├── UserExperienceMetrics.tsx            # UX metrics display
│   │   │   ├── ApplicationMetrics.tsx               # Application performance metrics
│   │   │   ├── TrendAnalysis.tsx                    # Performance trend analysis
│   │   │   └── PredictiveAnalytics.tsx              # Predictive performance analytics
│   │   └── reports/
│   │       ├── PerformanceReports.tsx               # Performance reports interface
│   │       ├── ReportBuilder.tsx                    # Report builder component
│   │       ├── ReportScheduler.tsx                  # Report scheduling interface
│   │       ├── ReportViewer.tsx                     # Report viewing component
│   │       └── ReportExport.tsx                     # Report export functionality
├── lib/
│   ├── monitoring/
│   │   ├── services/
│   │   │   ├── performance-service.ts               # Main performance service
│   │   │   ├── metrics-collector.ts                # Metrics collection service
│   │   │   ├── alert-service.ts                     # Alert management service
│   │   │   ├── health-service.ts                    # Health monitoring service
│   │   │   └── analytics-service.ts                 # Performance analytics service
│   │   ├── collectors/
│   │   │   ├── web-vitals-collector.ts              # Core Web Vitals collection
│   │   │   ├── performance-collector.ts             # Performance metrics collection
│   │   │   ├── error-collector.ts                   # Error tracking collection
│   │   │   ├── resource-collector.ts                # Resource usage collection
│   │   │   └── user-experience-collector.ts         # UX metrics collection
│   │   ├── processors/
│   │   │   ├── metrics-processor.ts                 # Metrics processing engine
│   │   │   ├── alert-processor.ts                   # Alert processing logic
│   │   │   ├── health-processor.ts                  # Health check processing
│   │   │   └── analytics-processor.ts               # Analytics data processing
│   │   ├── storage/
│   │   │   ├── metrics-storage.ts                   # Metrics data storage
│   │   │   ├── time-series-storage.ts               # Time-series data storage
│   │   │   ├── alert-storage.ts                     # Alert data storage
│   │   │   └── health-storage.ts                    # Health data storage
│   │   ├── alerts/
│   │   │   ├── alert-engine.ts                      # Alert processing engine
│   │   │   ├── notification-service.ts              # Alert notifications
│   │   │   ├── rule-engine.ts                       # Alert rules engine
│   │   │   └── escalation-service.ts                # Alert escalation handling
│   │   ├── health/
│   │   │   ├── health-checker.ts                    # Health check implementation
│   │   │   ├── system-monitor.ts                    # System monitoring
│   │   │   ├── service-monitor.ts                   # Service health monitoring
│   │   │   └── database-monitor.ts                  # Database health monitoring
│   │   └── analytics/
│   │       ├── performance-analyzer.ts              # Performance analysis
│   │       ├── trend-analyzer.ts                    # Trend analysis
│   │       ├── prediction-engine.ts                 # Predictive analytics
│   │       └── report-generator.ts                  # Report generation
│   └── integrations/
│       ├── monitoring-providers/
│       │   ├── datadog-integration.ts               # DataDog integration
│       │   ├── newrelic-integration.ts              # New Relic integration
│       │   ├── grafana-integration.ts               # Grafana integration
│       │   └── custom-provider.ts                   # Custom provider template
│       └── notification-providers/
│           ├── slack-notifications.ts               # Slack notifications
│           ├── email-notifications.ts               # Email notifications
│           ├── webhook-notifications.ts             # Webhook notifications
│           └── sms-notifications.ts                 # SMS notifications
```

### Core Performance Service

```typescript
// src/lib/monitoring/services/performance-service.ts
import { z } from 'zod';
import { createId } from '@paralleldrive/cuid2';
import { metricsCollector } from './metrics-collector';
import { alertService } from './alert-service';
import { healthService } from './health-service';
import { analyticsService } from './analytics-service';
import { auditLogger } from '@/lib/audit/audit-logger';

export const PerformanceMetricSchema = z.object({
  id: z.string(),
  type: z.enum(['web-vitals', 'api-response', 'database', 'custom']),
  name: z.string(),
  value: z.number(),
  unit: z.string(),
  timestamp: z.date(),
  labels: z.record(z.string()),
  tenantId: z.string(),
  userId: z.string().optional(),
});

export const AlertRuleSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  metric: z.string(),
  condition: z.enum(['gt', 'lt', 'eq', 'gte', 'lte']),
  threshold: z.number(),
  duration: z.number(), // in seconds
  severity: z.enum(['low', 'medium', 'high', 'critical']),
  enabled: z.boolean(),
  notifications: z.array(z.object({
    type: z.enum(['email', 'slack', 'webhook', 'sms']),
    target: z.string(),
  })),
});

export const MonitoringConfigSchema = z.object({
  tenantId: z.string(),
  enabled: z.boolean(),
  samplingRate: z.number().min(0).max(1),
  retentionDays: z.number().min(1).max(365),
  alertRules: z.array(AlertRuleSchema),
  customMetrics: z.array(z.object({
    name: z.string(),
    type: z.enum(['counter', 'gauge', 'histogram']),
    description: z.string(),
    labels: z.array(z.string()),
  })),
});

export type PerformanceMetric = z.infer<typeof PerformanceMetricSchema>;
export type AlertRule = z.infer<typeof AlertRuleSchema>;
export type MonitoringConfig = z.infer<typeof MonitoringConfigSchema>;

export class PerformanceService {
  private configs = new Map<string, MonitoringConfig>();
  private metrics = new Map<string, PerformanceMetric[]>();
  private realtimeConnections = new Map<string, WebSocket>();

  async initializeMonitoring(
    tenantId: string,
    config: MonitoringConfig
  ): Promise<void> {
    // Validate configuration
    const validatedConfig = MonitoringConfigSchema.parse(config);

    // Initialize metrics collection
    await metricsCollector.initialize(tenantId, {
      samplingRate: validatedConfig.samplingRate,
      customMetrics: validatedConfig.customMetrics,
    });

    // Set up alert rules
    for (const rule of validatedConfig.alertRules) {
      await alertService.createAlertRule(tenantId, rule);
    }

    // Initialize health checks
    await healthService.initialize(tenantId);

    // Cache configuration
    this.configs.set(tenantId, validatedConfig);

    // Log initialization
    await auditLogger.log({
      action: 'monitoring_initialized',
      tenantId,
      metadata: {
        samplingRate: validatedConfig.samplingRate,
        alertRulesCount: validatedConfig.alertRules.length,
        customMetricsCount: validatedConfig.customMetrics.length,
      },
    });
  }

  async collectMetric(
    tenantId: string,
    metric: Omit<PerformanceMetric, 'id' | 'timestamp'>
  ): Promise<void> {
    const config = this.configs.get(tenantId);
    if (!config || !config.enabled) {
      return;
    }

    // Apply sampling rate
    if (Math.random() > config.samplingRate) {
      return;
    }

    const performanceMetric: PerformanceMetric = {
      id: createId(),
      timestamp: new Date(),
      tenantId,
      ...metric,
    };

    // Validate metric
    const validatedMetric = PerformanceMetricSchema.parse(performanceMetric);

    // Store metric
    await this.storeMetric(validatedMetric);

    // Process alerts
    await this.processAlerts(tenantId, validatedMetric);

    // Send to real-time connections
    await this.broadcastMetric(tenantId, validatedMetric);
  }

  async getMetrics(
    tenantId: string,
    options: {
      type?: string;
      name?: string;
      startTime?: Date;
      endTime?: Date;
      aggregation?: 'avg' | 'sum' | 'min' | 'max' | 'count';
      interval?: string;
    }
  ): Promise<PerformanceMetric[]> {
    const config = this.configs.get(tenantId);
    if (!config) {
      throw new Error('Monitoring not initialized for tenant');
    }

    // Query metrics from storage
    const metrics = await this.queryMetrics(tenantId, options);

    // Apply aggregation if specified
    if (options.aggregation) {
      return this.aggregateMetrics(metrics, options.aggregation, options.interval);
    }

    return metrics;
  }

  async createAlert(
    tenantId: string,
    rule: AlertRule
  ): Promise<void> {
    const config = this.configs.get(tenantId);
    if (!config) {
      throw new Error('Monitoring not initialized for tenant');
    }

    // Validate rule
    const validatedRule = AlertRuleSchema.parse(rule);

    // Create alert rule
    await alertService.createAlertRule(tenantId, validatedRule);

    // Update configuration
    config.alertRules.push(validatedRule);
    this.configs.set(tenantId, config);

    // Log creation
    await auditLogger.log({
      action: 'alert_rule_created',
      tenantId,
      metadata: {
        ruleId: validatedRule.id,
        metric: validatedRule.metric,
        severity: validatedRule.severity,
      },
    });
  }

  async getSystemHealth(tenantId: string): Promise<{
    overall: 'healthy' | 'degraded' | 'unhealthy';
    checks: Array<{
      name: string;
      status: 'healthy' | 'degraded' | 'unhealthy';
      latency: number;
      error?: string;
    }>;
  }> {
    const config = this.configs.get(tenantId);
    if (!config) {
      throw new Error('Monitoring not initialized for tenant');
    }

    // Get health status from health service
    const healthStatus = await healthService.getHealthStatus(tenantId);

    return healthStatus;
  }

  async generatePerformanceReport(
    tenantId: string,
    options: {
      startTime: Date;
      endTime: Date;
      includeMetrics: string[];
      format: 'json' | 'csv' | 'pdf';
    }
  ): Promise<{ reportId: string; downloadUrl: string }> {
    const config = this.configs.get(tenantId);
    if (!config) {
      throw new Error('Monitoring not initialized for tenant');
    }

    // Generate report
    const report = await analyticsService.generateReport(tenantId, options);

    return {
      reportId: report.id,
      downloadUrl: report.downloadUrl,
    };
  }

  async startRealtimeMonitoring(
    tenantId: string,
    websocket: WebSocket
  ): Promise<void> {
    const config = this.configs.get(tenantId);
    if (!config) {
      throw new Error('Monitoring not initialized for tenant');
    }

    // Store WebSocket connection
    this.realtimeConnections.set(tenantId, websocket);

    // Send initial metrics
    const recentMetrics = await this.getMetrics(tenantId, {
      startTime: new Date(Date.now() - 5 * 60 * 1000), // Last 5 minutes
    });

    websocket.send(JSON.stringify({
      type: 'initial_metrics',
      metrics: recentMetrics,
    }));

    // Handle WebSocket close
    websocket.on('close', () => {
      this.realtimeConnections.delete(tenantId);
    });
  }

  async trackWebVitals(
    tenantId: string,
    userId: string,
    vitals: {
      lcp: number; // Largest Contentful Paint
      fid: number; // First Input Delay
      cls: number; // Cumulative Layout Shift
      fcp: number; // First Contentful Paint
      ttfb: number; // Time to First Byte
    }
  ): Promise<void> {
    const metrics = [
      {
        type: 'web-vitals' as const,
        name: 'lcp',
        value: vitals.lcp,
        unit: 'ms',
        labels: { metric: 'largest_contentful_paint' },
        userId,
      },
      {
        type: 'web-vitals' as const,
        name: 'fid',
        value: vitals.fid,
        unit: 'ms',
        labels: { metric: 'first_input_delay' },
        userId,
      },
      {
        type: 'web-vitals' as const,
        name: 'cls',
        value: vitals.cls,
        unit: 'score',
        labels: { metric: 'cumulative_layout_shift' },
        userId,
      },
      {
        type: 'web-vitals' as const,
        name: 'fcp',
        value: vitals.fcp,
        unit: 'ms',
        labels: { metric: 'first_contentful_paint' },
        userId,
      },
      {
        type: 'web-vitals' as const,
        name: 'ttfb',
        value: vitals.ttfb,
        unit: 'ms',
        labels: { metric: 'time_to_first_byte' },
        userId,
      },
    ];

    // Collect all metrics
    for (const metric of metrics) {
      await this.collectMetric(tenantId, metric);
    }
  }

  async trackApiPerformance(
    tenantId: string,
    apiCall: {
      method: string;
      path: string;
      duration: number;
      statusCode: number;
      userId?: string;
    }
  ): Promise<void> {
    await this.collectMetric(tenantId, {
      type: 'api-response',
      name: 'api_response_time',
      value: apiCall.duration,
      unit: 'ms',
      labels: {
        method: apiCall.method,
        path: apiCall.path,
        status_code: apiCall.statusCode.toString(),
      },
      userId: apiCall.userId,
    });
  }

  async trackDatabasePerformance(
    tenantId: string,
    query: {
      operation: string;
      table: string;
      duration: number;
      rowCount?: number;
    }
  ): Promise<void> {
    await this.collectMetric(tenantId, {
      type: 'database',
      name: 'database_query_time',
      value: query.duration,
      unit: 'ms',
      labels: {
        operation: query.operation,
        table: query.table,
        row_count: query.rowCount?.toString() || '0',
      },
    });
  }

  async trackCustomMetric(
    tenantId: string,
    metric: {
      name: string;
      value: number;
      unit: string;
      labels: Record<string, string>;
      userId?: string;
    }
  ): Promise<void> {
    await this.collectMetric(tenantId, {
      type: 'custom',
      name: metric.name,
      value: metric.value,
      unit: metric.unit,
      labels: metric.labels,
      userId: metric.userId,
    });
  }

  private async storeMetric(metric: PerformanceMetric): Promise<void> {
    // Store in time-series database
    // This would integrate with your chosen time-series database
    // (e.g., InfluxDB, TimescaleDB, etc.)
    
    const tenantMetrics = this.metrics.get(metric.tenantId) || [];
    tenantMetrics.push(metric);
    this.metrics.set(metric.tenantId, tenantMetrics);
  }

  private async processAlerts(
    tenantId: string,
    metric: PerformanceMetric
  ): Promise<void> {
    const config = this.configs.get(tenantId);
    if (!config) return;

    // Check alert rules
    for (const rule of config.alertRules) {
      if (rule.metric === metric.name && rule.enabled) {
        await alertService.processAlertRule(tenantId, rule, metric);
      }
    }
  }

  private async broadcastMetric(
    tenantId: string,
    metric: PerformanceMetric
  ): Promise<void> {
    const websocket = this.realtimeConnections.get(tenantId);
    if (websocket && websocket.readyState === WebSocket.OPEN) {
      websocket.send(JSON.stringify({
        type: 'metric_update',
        metric,
      }));
    }
  }

  private async queryMetrics(
    tenantId: string,
    options: any
  ): Promise<PerformanceMetric[]> {
    // Query implementation would depend on your time-series database
    const tenantMetrics = this.metrics.get(tenantId) || [];
    
    let filteredMetrics = tenantMetrics;

    // Apply filters
    if (options.type) {
      filteredMetrics = filteredMetrics.filter(m => m.type === options.type);
    }
    if (options.name) {
      filteredMetrics = filteredMetrics.filter(m => m.name === options.name);
    }
    if (options.startTime) {
      filteredMetrics = filteredMetrics.filter(m => m.timestamp >= options.startTime);
    }
    if (options.endTime) {
      filteredMetrics = filteredMetrics.filter(m => m.timestamp <= options.endTime);
    }

    return filteredMetrics;
  }

  private aggregateMetrics(
    metrics: PerformanceMetric[],
    aggregation: string,
    interval?: string
  ): PerformanceMetric[] {
    // Implement aggregation logic
    // This is a simplified implementation
    return metrics;
  }
}

export const performanceService = new PerformanceService();
```

### Web Vitals Collector

```typescript
// src/lib/monitoring/collectors/web-vitals-collector.ts
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';
import { performanceService } from '../services/performance-service';

export class WebVitalsCollector {
  private tenantId: string;
  private userId?: string;
  private vitals: Record<string, number> = {};

  constructor(tenantId: string, userId?: string) {
    this.tenantId = tenantId;
    this.userId = userId;
  }

  initialize(): void {
    // Collect Core Web Vitals
    getCLS(this.onCLS.bind(this));
    getFID(this.onFID.bind(this));
    getFCP(this.onFCP.bind(this));
    getLCP(this.onLCP.bind(this));
    getTTFB(this.onTTFB.bind(this));

    // Send metrics when page is about to unload
    window.addEventListener('beforeunload', () => {
      this.sendMetrics();
    });

    // Send metrics periodically
    setInterval(() => {
      this.sendMetrics();
    }, 30000); // Every 30 seconds
  }

  private onCLS(metric: any): void {
    this.vitals.cls = metric.value;
    this.trackVital('cls', metric.value, 'score');
  }

  private onFID(metric: any): void {
    this.vitals.fid = metric.value;
    this.trackVital('fid', metric.value, 'ms');
  }

  private onFCP(metric: any): void {
    this.vitals.fcp = metric.value;
    this.trackVital('fcp', metric.value, 'ms');
  }

  private onLCP(metric: any): void {
    this.vitals.lcp = metric.value;
    this.trackVital('lcp', metric.value, 'ms');
  }

  private onTTFB(metric: any): void {
    this.vitals.ttfb = metric.value;
    this.trackVital('ttfb', metric.value, 'ms');
  }

  private trackVital(name: string, value: number, unit: string): void {
    performanceService.collectMetric(this.tenantId, {
      type: 'web-vitals',
      name,
      value,
      unit,
      labels: {
        vital: name,
        page: window.location.pathname,
        userAgent: navigator.userAgent,
      },
      userId: this.userId,
    });
  }

  private sendMetrics(): void {
    if (Object.keys(this.vitals).length > 0) {
      performanceService.trackWebVitals(this.tenantId, this.userId || 'anonymous', {
        lcp: this.vitals.lcp || 0,
        fid: this.vitals.fid || 0,
        cls: this.vitals.cls || 0,
        fcp: this.vitals.fcp || 0,
        ttfb: this.vitals.ttfb || 0,
      });
    }
  }
}

export const initializeWebVitals = (tenantId: string, userId?: string): void => {
  const collector = new WebVitalsCollector(tenantId, userId);
  collector.initialize();
};
```

### Real-Time Monitoring Dashboard

```typescript
// src/components/monitoring/dashboard/MonitoringDashboard.tsx
'use client';

import React, { useState, useEffect } from 'react';
import {
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Box,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress,
  Tab,
  Tabs,
  Paper,
} from '@mui/material';
import { TrendingUp, TrendingDown, Warning, CheckCircle } from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { MetricsOverview } from './MetricsOverview';
import { SystemHealthStatus } from './SystemHealthStatus';
import { PerformanceCharts } from './PerformanceCharts';
import { AlertsPanel } from './AlertsPanel';
import { useMonitoring } from '@/hooks/useMonitoring';

interface MonitoringDashboardProps {
  tenantId: string;
}

export const MonitoringDashboard: React.FC<MonitoringDashboardProps> = ({
  tenantId,
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [realtimeEnabled, setRealtimeEnabled] = useState(true);
  const [timeRange, setTimeRange] = useState('1h');

  const {
    metrics,
    systemHealth,
    alerts,
    loading,
    error,
    startRealtimeMonitoring,
    stopRealtimeMonitoring,
    refreshMetrics,
  } = useMonitoring(tenantId);

  useEffect(() => {
    if (realtimeEnabled) {
      startRealtimeMonitoring();
    } else {
      stopRealtimeMonitoring();
    }

    return () => {
      stopRealtimeMonitoring();
    };
  }, [realtimeEnabled, startRealtimeMonitoring, stopRealtimeMonitoring]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleRealtimeToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRealtimeEnabled(event.target.checked);
  };

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'success';
      case 'degraded':
        return 'warning';
      case 'unhealthy':
        return 'error';
      default:
        return 'info';
    }
  };

  const getHealthStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle color="success" />;
      case 'degraded':
        return <Warning color="warning" />;
      case 'unhealthy':
        return <Warning color="error" />;
      default:
        return <CircularProgress size={24} />;
    }
  };

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '400px',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 0:
        return <MetricsOverview metrics={metrics} timeRange={timeRange} />;
      case 1:
        return <SystemHealthStatus systemHealth={systemHealth} />;
      case 2:
        return <PerformanceCharts metrics={metrics} timeRange={timeRange} />;
      case 3:
        return <AlertsPanel alerts={alerts} />;
      default:
        return null;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1">
          Performance Monitoring
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={realtimeEnabled}
                onChange={handleRealtimeToggle}
                color="primary"
              />
            }
            label="Real-time monitoring"
          />
        </Box>
      </Box>

      {/* System Health Overview */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {getHealthStatusIcon(systemHealth?.overall || 'unknown')}
                <Typography variant="h6">
                  System Health
                </Typography>
              </Box>
              <Typography
                variant="h4"
                sx={{ 
                  mt: 1,
                  color: `${getHealthStatusColor(systemHealth?.overall || 'unknown')}.main`,
                }}
              >
                {systemHealth?.overall || 'Unknown'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <TrendingUp color="primary" />
                <Typography variant="h6">
                  Response Time
                </Typography>
              </Box>
              <Typography variant="h4" sx={{ mt: 1 }}>
                {metrics?.averageResponseTime || 0}ms
              </Typography>
              <Typography variant="caption" color="textSecondary">
                Average last hour
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CheckCircle color="success" />
                <Typography variant="h6">
                  Uptime
                </Typography>
              </Box>
              <Typography variant="h4" sx={{ mt: 1, color: 'success.main' }}>
                {metrics?.uptime || 0}%
              </Typography>
              <Typography variant="caption" color="textSecondary">
                Last 30 days
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Warning color="warning" />
                <Typography variant="h6">
                  Active Alerts
                </Typography>
              </Box>
              <Typography variant="h4" sx={{ mt: 1, color: 'warning.main' }}>
                {alerts?.active?.length || 0}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                {alerts?.critical || 0} critical
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label="Metrics Overview" />
          <Tab label="System Health" />
          <Tab label="Performance Charts" />
          <Tab label="Alerts" />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      <Box sx={{ minHeight: '400px' }}>
        {renderTabContent()}
      </Box>
    </Box>
  );
};

export default MonitoringDashboard;
```

### Alert Management System

```typescript
// src/lib/monitoring/alerts/alert-engine.ts
import { z } from 'zod';
import { createId } from '@paralleldrive/cuid2';
import { notificationService } from './notification-service';
import { auditLogger } from '@/lib/audit/audit-logger';

export const AlertSchema = z.object({
  id: z.string(),
  ruleId: z.string(),
  tenantId: z.string(),
  metric: z.string(),
  value: z.number(),
  threshold: z.number(),
  severity: z.enum(['low', 'medium', 'high', 'critical']),
  status: z.enum(['open', 'acknowledged', 'resolved']),
  title: z.string(),
  description: z.string(),
  triggeredAt: z.date(),
  acknowledgedAt: z.date().optional(),
  resolvedAt: z.date().optional(),
  acknowledgedBy: z.string().optional(),
  resolvedBy: z.string().optional(),
});

export type Alert = z.infer<typeof AlertSchema>;

export class AlertEngine {
  private activeAlerts = new Map<string, Alert>();
  private alertHistory = new Map<string, Alert[]>();

  async processAlertRule(
    tenantId: string,
    rule: any,
    metric: any
  ): Promise<void> {
    const shouldTrigger = this.evaluateCondition(rule, metric);
    
    if (shouldTrigger) {
      await this.triggerAlert(tenantId, rule, metric);
    } else {
      await this.resolveAlert(tenantId, rule.id);
    }
  }

  private evaluateCondition(rule: any, metric: any): boolean {
    switch (rule.condition) {
      case 'gt':
        return metric.value > rule.threshold;
      case 'lt':
        return metric.value < rule.threshold;
      case 'gte':
        return metric.value >= rule.threshold;
      case 'lte':
        return metric.value <= rule.threshold;
      case 'eq':
        return metric.value === rule.threshold;
      default:
        return false;
    }
  }

  private async triggerAlert(
    tenantId: string,
    rule: any,
    metric: any
  ): Promise<void> {
    const alertId = createId();
    const alert: Alert = {
      id: alertId,
      ruleId: rule.id,
      tenantId,
      metric: metric.name,
      value: metric.value,
      threshold: rule.threshold,
      severity: rule.severity,
      status: 'open',
      title: `${rule.name} - ${metric.name} ${rule.condition} ${rule.threshold}`,
      description: `${metric.name} has ${rule.condition} ${rule.threshold} with value ${metric.value}`,
      triggeredAt: new Date(),
    };

    // Store alert
    this.activeAlerts.set(alertId, alert);
    
    // Add to history
    const history = this.alertHistory.get(tenantId) || [];
    history.push(alert);
    this.alertHistory.set(tenantId, history);

    // Send notifications
    await this.sendNotifications(alert, rule.notifications);

    // Log alert
    await auditLogger.log({
      action: 'alert_triggered',
      tenantId,
      metadata: {
        alertId,
        ruleId: rule.id,
        severity: rule.severity,
        metric: metric.name,
        value: metric.value,
        threshold: rule.threshold,
      },
    });
  }

  private async resolveAlert(tenantId: string, ruleId: string): Promise<void> {
    // Find and resolve active alerts for this rule
    for (const [alertId, alert] of this.activeAlerts) {
      if (alert.ruleId === ruleId && alert.status === 'open') {
        alert.status = 'resolved';
        alert.resolvedAt = new Date();
        alert.resolvedBy = 'system';

        // Remove from active alerts
        this.activeAlerts.delete(alertId);

        // Log resolution
        await auditLogger.log({
          action: 'alert_resolved',
          tenantId,
          metadata: {
            alertId,
            ruleId,
            resolvedBy: 'system',
          },
        });
      }
    }
  }

  private async sendNotifications(alert: Alert, notifications: any[]): Promise<void> {
    for (const notification of notifications) {
      await notificationService.sendNotification({
        type: notification.type,
        target: notification.target,
        alert,
      });
    }
  }

  async getActiveAlerts(tenantId: string): Promise<Alert[]> {
    return Array.from(this.activeAlerts.values())
      .filter(alert => alert.tenantId === tenantId && alert.status === 'open');
  }

  async getAlertHistory(
    tenantId: string,
    options: {
      limit?: number;
      severity?: string;
      status?: string;
    } = {}
  ): Promise<Alert[]> {
    const history = this.alertHistory.get(tenantId) || [];
    
    let filteredHistory = history;

    if (options.severity) {
      filteredHistory = filteredHistory.filter(alert => alert.severity === options.severity);
    }

    if (options.status) {
      filteredHistory = filteredHistory.filter(alert => alert.status === options.status);
    }

    if (options.limit) {
      filteredHistory = filteredHistory.slice(-options.limit);
    }

    return filteredHistory.sort((a, b) => b.triggeredAt.getTime() - a.triggeredAt.getTime());
  }

  async acknowledgeAlert(
    alertId: string,
    acknowledgedBy: string
  ): Promise<void> {
    const alert = this.activeAlerts.get(alertId);
    if (alert) {
      alert.status = 'acknowledged';
      alert.acknowledgedAt = new Date();
      alert.acknowledgedBy = acknowledgedBy;

      // Log acknowledgment
      await auditLogger.log({
        action: 'alert_acknowledged',
        tenantId: alert.tenantId,
        metadata: {
          alertId,
          acknowledgedBy,
        },
      });
    }
  }

  async resolveAlertManually(
    alertId: string,
    resolvedBy: string
  ): Promise<void> {
    const alert = this.activeAlerts.get(alertId);
    if (alert) {
      alert.status = 'resolved';
      alert.resolvedAt = new Date();
      alert.resolvedBy = resolvedBy;

      // Remove from active alerts
      this.activeAlerts.delete(alertId);

      // Log resolution
      await auditLogger.log({
        action: 'alert_resolved',
        tenantId: alert.tenantId,
        metadata: {
          alertId,
          resolvedBy,
        },
      });
    }
  }
}

export const alertEngine = new AlertEngine();
```

### API Route Implementation

```typescript
// src/app/api/v1/performance/metrics/route.ts
import { NextRequest } from 'next/server';
import { defineRoute } from 'next-openapi-route-handler';
import { z } from 'zod';
import { authMiddleware } from '@/lib/api/middleware/auth';
import { rateLimitMiddleware } from '@/lib/api/middleware/rate-limit';
import { performanceService, PerformanceMetricSchema } from '@/lib/monitoring/services/performance-service';
import { createResponse, createErrorResponse } from '@/lib/api/utils/responses';

export const POST = defineRoute({
  method: 'POST',
  path: '/api/v1/performance/metrics',
  tags: ['Performance Monitoring'],
  summary: 'Collect performance metric',
  description: 'Collect a performance metric for monitoring and analysis',
  requestSchema: PerformanceMetricSchema.omit({ id: true, timestamp: true }),
  responseSchema: z.object({
    success: z.boolean(),
    metricId: z.string(),
  }),
  middleware: [authMiddleware, rateLimitMiddleware],
  handler: async (req: NextRequest) => {
    try {
      const user = (req as any).user;
      const body = await req.json();
      
      const metric = {
        ...body,
        tenantId: user.tenantId,
        userId: user.id,
      };

      await performanceService.collectMetric(user.tenantId, metric);

      return createResponse({
        success: true,
        metricId: `${Date.now()}-${user.tenantId}`,
      });
    } catch (error) {
      console.error('Error collecting metric:', error);
      return createErrorResponse('Failed to collect metric', 500);
    }
  },
});

export const GET = defineRoute({
  method: 'GET',
  path: '/api/v1/performance/metrics',
  tags: ['Performance Monitoring'],
  summary: 'Get performance metrics',
  description: 'Retrieve performance metrics with optional filtering and aggregation',
  querySchema: z.object({
    type: z.string().optional(),
    name: z.string().optional(),
    startTime: z.coerce.date().optional(),
    endTime: z.coerce.date().optional(),
    aggregation: z.enum(['avg', 'sum', 'min', 'max', 'count']).optional(),
    interval: z.string().optional(),
  }),
  responseSchema: z.object({
    metrics: z.array(PerformanceMetricSchema),
    aggregation: z.string().optional(),
    interval: z.string().optional(),
  }),
  middleware: [authMiddleware, rateLimitMiddleware],
  handler: async (req: NextRequest) => {
    try {
      const user = (req as any).user;
      const searchParams = Object.fromEntries(req.nextUrl.searchParams);
      const query = z.object({
        type: z.string().optional(),
        name: z.string().optional(),
        startTime: z.coerce.date().optional(),
        endTime: z.coerce.date().optional(),
        aggregation: z.enum(['avg', 'sum', 'min', 'max', 'count']).optional(),
        interval: z.string().optional(),
      }).parse(searchParams);

      const metrics = await performanceService.getMetrics(user.tenantId, query);

      return createResponse({
        metrics,
        aggregation: query.aggregation,
        interval: query.interval,
      });
    } catch (error) {
      console.error('Error retrieving metrics:', error);
      return createErrorResponse('Failed to retrieve metrics', 500);
    }
  },
});
```

## Testing Strategy

### Unit Tests
```typescript
// src/lib/monitoring/services/__tests__/performance-service.test.ts
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { performanceService } from '../performance-service';
import { alertService } from '../alert-service';

vi.mock('../alert-service');

describe('PerformanceService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('collectMetric', () => {
    it('should collect and store metric', async () => {
      const config = {
        tenantId: 'tenant-123',
        enabled: true,
        samplingRate: 1.0,
        retentionDays: 30,
        alertRules: [],
        customMetrics: [],
      };

      await performanceService.initializeMonitoring('tenant-123', config);

      const metric = {
        type: 'api-response' as const,
        name: 'api_response_time',
        value: 150,
        unit: 'ms',
        labels: { method: 'GET', path: '/api/users' },
      };

      await performanceService.collectMetric('tenant-123', metric);

      const metrics = await performanceService.getMetrics('tenant-123', {
        type: 'api-response',
        name: 'api_response_time',
      });

      expect(metrics).toHaveLength(1);
      expect(metrics[0]).toMatchObject({
        type: 'api-response',
        name: 'api_response_time',
        value: 150,
        unit: 'ms',
      });
    });
  });

  describe('trackWebVitals', () => {
    it('should track web vitals metrics', async () => {
      const config = {
        tenantId: 'tenant-123',
        enabled: true,
        samplingRate: 1.0,
        retentionDays: 30,
        alertRules: [],
        customMetrics: [],
      };

      await performanceService.initializeMonitoring('tenant-123', config);

      const vitals = {
        lcp: 2500,
        fid: 100,
        cls: 0.1,
        fcp: 1800,
        ttfb: 600,
      };

      await performanceService.trackWebVitals('tenant-123', 'user-123', vitals);

      const metrics = await performanceService.getMetrics('tenant-123', {
        type: 'web-vitals',
      });

      expect(metrics).toHaveLength(5);
      expect(metrics.find(m => m.name === 'lcp')).toMatchObject({
        value: 2500,
        unit: 'ms',
      });
    });
  });
});
```

### Integration Tests
```typescript
// src/components/monitoring/dashboard/__tests__/MonitoringDashboard.integration.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import { MonitoringDashboard } from '../MonitoringDashboard';
import { MonitoringProvider } from '@/providers/MonitoringProvider';

const mockMetrics = [
  {
    id: '1',
    type: 'api-response',
    name: 'api_response_time',
    value: 150,
    unit: 'ms',
    timestamp: new Date(),
    labels: { method: 'GET' },
    tenantId: 'tenant-123',
  },
];

const mockSystemHealth = {
  overall: 'healthy',
  checks: [
    {
      name: 'Database',
      status: 'healthy',
      latency: 10,
    },
  ],
};

const renderWithProvider = (component: React.ReactNode) => {
  return render(
    <MonitoringProvider>
      {component}
    </MonitoringProvider>
  );
};

describe('MonitoringDashboard Integration', () => {
  it('should display performance metrics', async () => {
    renderWithProvider(
      <MonitoringDashboard tenantId="tenant-123" />
    );

    await waitFor(() => {
      expect(screen.getByText('Performance Monitoring')).toBeInTheDocument();
      expect(screen.getByText('System Health')).toBeInTheDocument();
    });
  });

  it('should handle real-time monitoring toggle', async () => {
    renderWithProvider(
      <MonitoringDashboard tenantId="tenant-123" />
    );

    const realtimeToggle = screen.getByLabelText('Real-time monitoring');
    expect(realtimeToggle).toBeInTheDocument();
    expect(realtimeToggle).toBeChecked();
  });
});
```

## Performance Considerations

### Metrics Collection Optimization
- **Sampling**: Implement intelligent sampling rates
- **Batching**: Batch metric collection to reduce overhead
- **Compression**: Compress metric data for efficient storage
- **Caching**: Cache frequently accessed metrics

### Real-Time Processing
- **WebSocket Optimization**: Efficient WebSocket connection management
- **Delta Updates**: Send only changed metrics
- **Connection Pooling**: Optimize connection usage
- **Rate Limiting**: Prevent excessive real-time updates

### Data Storage
- **Time-Series Database**: Use optimized time-series storage
- **Data Retention**: Implement automated data retention policies
- **Indexing**: Optimize database indexes for query performance
- **Partitioning**: Partition data by time and tenant

## Security Considerations

### Data Protection
- **Encryption**: Encrypt sensitive performance data
- **Access Control**: Role-based access to monitoring data
- **Audit Logging**: Log all monitoring access and changes
- **Data Masking**: Mask sensitive information in metrics

### Alert Security
- **Notification Security**: Secure alert notifications
- **Rate Limiting**: Prevent alert spam and abuse
- **Authentication**: Secure alert management interfaces
- **Audit Trail**: Complete audit trail for alert actions

## Success Metrics

### Performance Monitoring
- **Detection Speed**: Time to detect performance issues
- **False Positive Rate**: Accuracy of alert system
- **Coverage**: Percentage of system components monitored
- **Resolution Time**: Time to resolve performance issues

### User Experience
- **Dashboard Usage**: Frequency of dashboard access
- **Alert Response**: Response time to alerts
- **System Reliability**: Overall system uptime improvement
- **Performance Optimization**: Measurable performance improvements

This comprehensive Performance Monitoring implementation provides real-time system health monitoring, intelligent alerting, and detailed performance analytics that enable proactive system optimization and issue resolution for the NEXUS SaaS Starter.
