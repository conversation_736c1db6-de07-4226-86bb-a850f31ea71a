import { z } from "zod";

// Profile validation schemas
export const profileUpdateSchema = z.object({
  firstName: z.string().min(1).max(50).optional(),
  lastName: z.string().min(1).max(50).optional(),
  name: z.string().min(1).max(100).optional(),
  bio: z.string().max(500).optional(),
  location: z.string().max(100).optional(),
  website: z.string().url().optional().or(z.literal("")),
  company: z.string().max(100).optional(),
  jobTitle: z.string().max(100).optional(),
  username: z.string()
    .min(3)
    .max(30)
    .regex(/^[a-zA-Z0-9_-]+$/, "Username can only contain letters, numbers, underscores, and hyphens")
    .optional(),
});

export const preferencesUpdateSchema = z.object({
  language: z.enum(["en", "es", "fr", "de", "ja", "zh"]).optional(),
  timezone: z.string().optional(),
  theme: z.enum(["light", "dark", "system"]).optional(),
  emailNotifications: z.boolean().optional(),
  pushNotifications: z.boolean().optional(),
  marketingEmails: z.boolean().optional(),
});

export const privacyUpdateSchema = z.object({
  profileVisibility: z.enum(["public", "private", "team"]).optional(),
  showEmail: z.boolean().optional(),
  showLocation: z.boolean().optional(),
});

export const avatarUploadSchema = z.object({
  file: z.instanceof(File)
    .refine((file) => file.size <= 5 * 1024 * 1024, "File size must be less than 5MB")
    .refine(
      (file) => ["image/jpeg", "image/png", "image/webp"].includes(file.type),
      "File must be a JPEG, PNG, or WebP image"
    ),
  altText: z.string().max(100).optional(),
});

export const notificationSettingsSchema = z.object({
  email: z.object({
    security: z.boolean(),
    updates: z.boolean(),
    marketing: z.boolean(),
    mentions: z.boolean(),
    comments: z.boolean(),
  }),
  push: z.object({
    security: z.boolean(),
    updates: z.boolean(),
    mentions: z.boolean(),
    comments: z.boolean(),
  }),
  inApp: z.object({
    security: z.boolean(),
    updates: z.boolean(),
    mentions: z.boolean(),
    comments: z.boolean(),
  }),
});

// Validation functions
export const validateProfileUpdate = (data: unknown) => {
  return profileUpdateSchema.parse(data);
};

export const validatePreferencesUpdate = (data: unknown) => {
  return preferencesUpdateSchema.parse(data);
};

export const validatePrivacyUpdate = (data: unknown) => {
  return privacyUpdateSchema.parse(data);
};

export const validateAvatarUpload = (data: unknown) => {
  return avatarUploadSchema.parse(data);
};

export const validateNotificationSettings = (data: unknown) => {
  return notificationSettingsSchema.parse(data);
};

// Type exports
export type ProfileUpdateData = z.infer<typeof profileUpdateSchema>;
export type PreferencesUpdateData = z.infer<typeof preferencesUpdateSchema>;
export type PrivacyUpdateData = z.infer<typeof privacyUpdateSchema>;
export type AvatarUploadData = z.infer<typeof avatarUploadSchema>;
export type NotificationSettings = z.infer<typeof notificationSettingsSchema>;
