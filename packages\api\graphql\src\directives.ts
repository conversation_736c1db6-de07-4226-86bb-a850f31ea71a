import { mapSchema, getDirective, MapperKind } from "@graphql-tools/utils";
import { GraphQLSchema, defaultFieldResolver, GraphQLError } from "graphql";
import { GraphQLContext, AuthDirectiveArgs, RateLimitDirectiveArgs } from "./types";
import { accessControl } from "@nexus/rbac";

// Authentication directive
export const authDirective = (schema: GraphQLSchema) => {
  return mapSchema(schema, {
    [MapperKind.OBJECT_FIELD]: (fieldConfig) => {
      const authDirective = getDirective(schema, fieldConfig, "auth")?.[0] as AuthDirectiveArgs;
      
      if (authDirective) {
        const { resolve = defaultFieldResolver } = fieldConfig;
        
        fieldConfig.resolve = async function (source, args, context: GraphQLContext, info) {
          // Check if user is authenticated
          if (!context.user) {
            throw new GraphQLError("Authentication required", {
              extensions: { code: "UNAUTHENTICATED" },
            });
          }

          // Check required permissions if specified
          if (authDirective.requires && authDirective.requires.length > 0) {
            const hasPermission = await checkPermissions(
              context.user.id,
              authDirective.requires,
              context
            );

            if (!hasPermission) {
              throw new GraphQLError("Insufficient permissions", {
                extensions: { code: "FORBIDDEN" },
              });
            }
          }

          return resolve(source, args, context, info);
        };
      }

      return fieldConfig;
    },
  });
};

// Rate limiting directive
export const rateLimitDirective = (schema: GraphQLSchema) => {
  const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

  return mapSchema(schema, {
    [MapperKind.OBJECT_FIELD]: (fieldConfig) => {
      const rateLimitDirective = getDirective(schema, fieldConfig, "rateLimit")?.[0] as RateLimitDirectiveArgs;
      
      if (rateLimitDirective) {
        const { resolve = defaultFieldResolver } = fieldConfig;
        
        fieldConfig.resolve = async function (source, args, context: GraphQLContext, info) {
          const key = `${context.user?.id || context.ip}:${info.fieldName}`;
          const now = Date.now();
          const windowMs = parseTimeWindow(rateLimitDirective.window);
          
          const current = rateLimitMap.get(key);
          
          if (current) {
            if (now < current.resetTime) {
              if (current.count >= rateLimitDirective.max) {
                throw new GraphQLError("Rate limit exceeded", {
                  extensions: { 
                    code: "RATE_LIMITED",
                    retryAfter: Math.ceil((current.resetTime - now) / 1000),
                  },
                });
              }
              current.count++;
            } else {
              // Reset window
              rateLimitMap.set(key, { count: 1, resetTime: now + windowMs });
            }
          } else {
            rateLimitMap.set(key, { count: 1, resetTime: now + windowMs });
          }

          return resolve(source, args, context, info);
        };
      }

      return fieldConfig;
    },
  });
};

// Complexity directive
export const complexityDirective = (schema: GraphQLSchema) => {
  return mapSchema(schema, {
    [MapperKind.OBJECT_FIELD]: (fieldConfig) => {
      const complexityDirective = getDirective(schema, fieldConfig, "complexity")?.[0];
      
      if (complexityDirective) {
        // Add complexity metadata to field
        fieldConfig.extensions = {
          ...fieldConfig.extensions,
          complexity: complexityDirective.value,
        };
      }

      return fieldConfig;
    },
  });
};

// Helper function to check permissions
const checkPermissions = async (
  userId: string,
  requiredPermissions: string[],
  context: GraphQLContext
): Promise<boolean> => {
  try {
    for (const permission of requiredPermissions) {
      const [action, resource] = permission.split(":");
      
      const hasPermission = await accessControl.can(
        userId,
        action,
        resource,
        {
          tenantId: context.tenantId,
          workspaceId: context.workspaceId,
          teamId: context.teamId,
        }
      );

      if (!hasPermission) {
        return false;
      }
    }
    
    return true;
  } catch (error) {
    console.error("Permission check error:", error);
    return false;
  }
};

// Helper function to parse time window
const parseTimeWindow = (window: string): number => {
  const match = window.match(/^(\d+)([smhd])$/);
  if (!match) {
    throw new Error(`Invalid time window format: ${window}`);
  }

  const value = parseInt(match[1], 10);
  const unit = match[2];

  switch (unit) {
    case "s":
      return value * 1000;
    case "m":
      return value * 60 * 1000;
    case "h":
      return value * 60 * 60 * 1000;
    case "d":
      return value * 24 * 60 * 60 * 1000;
    default:
      throw new Error(`Invalid time unit: ${unit}`);
  }
};

// Apply all directives to schema
export const applyDirectives = (schema: GraphQLSchema): GraphQLSchema => {
  let transformedSchema = schema;
  
  transformedSchema = authDirective(transformedSchema);
  transformedSchema = rateLimitDirective(transformedSchema);
  transformedSchema = complexityDirective(transformedSchema);
  
  return transformedSchema;
};
