import {
  ApolloClient,
  InMemoryCache,
  createHttpLink,
  from,
  ApolloLink,
  Observable,
  FetchResult,
  Operation,
  NextLink,
} from "@apollo/client";
import { setContext } from "@apollo/client/link/context";
import { onError } from "@apollo/client/link/error";
import { RetryLink } from "@apollo/client/link/retry";
import { createClient } from "graphql-ws";
import { GraphQLWsLink } from "@apollo/client/link/subscriptions";
import { getMainDefinition } from "@apollo/client/utilities";
import { getConfig } from "./config";
import { TokenManager, AuthError, AUTH_EVENTS, AuthEventEmitter } from "./auth";

// Create GraphQL client
export class GraphQLApiClient {
  private client: ApolloClient<any>;
  private tokenManager: TokenManager;
  private authEventEmitter: AuthEventEmitter;
  private config = getConfig();
  private wsClient: any;

  constructor() {
    this.tokenManager = TokenManager.getInstance();
    this.authEventEmitter = AuthEventEmitter.getInstance();
    this.client = this.createApolloClient();
  }

  // Create Apollo Client with links
  private createApolloClient(): ApolloClient<any> {
    // HTTP Link for queries and mutations
    const httpLink = createHttpLink({
      uri: this.config.graphqlURL,
    });

    // WebSocket Link for subscriptions
    const wsUri = this.config.graphqlURL.replace(/^https?/, "ws");
    this.wsClient = createClient({
      url: wsUri,
      connectionParams: () => {
        const token = this.tokenManager.getAccessToken();
        return {
          authorization: token ? `Bearer ${token}` : "",
        };
      },
      on: {
        connected: () => console.log("GraphQL WebSocket connected"),
        closed: () => console.log("GraphQL WebSocket closed"),
        error: (error) => console.error("GraphQL WebSocket error:", error),
      },
    });

    const wsLink = new GraphQLWsLink(this.wsClient);

    // Split link - use WebSocket for subscriptions, HTTP for queries/mutations
    const splitLink = from([
      this.createErrorLink(),
      this.createRetryLink(),
      this.createAuthLink(),
      this.createContextLink(),
      this.createSplitLink(httpLink, wsLink),
    ]);

    return new ApolloClient({
      link: splitLink,
      cache: new InMemoryCache({
        typePolicies: {
          Query: {
            fields: {
              // Pagination handling
              users: {
                keyArgs: ["search", "filters"],
                merge(existing = { edges: [], pageInfo: {} }, incoming) {
                  return {
                    ...incoming,
                    edges: [...existing.edges, ...incoming.edges],
                  };
                },
              },
              workspaces: {
                keyArgs: ["search", "filters"],
                merge(existing = { edges: [], pageInfo: {} }, incoming) {
                  return {
                    ...incoming,
                    edges: [...existing.edges, ...incoming.edges],
                  };
                },
              },
              teams: {
                keyArgs: ["workspaceId", "search", "filters"],
                merge(existing = { edges: [], pageInfo: {} }, incoming) {
                  return {
                    ...incoming,
                    edges: [...existing.edges, ...incoming.edges],
                  };
                },
              },
              projects: {
                keyArgs: ["workspaceId", "teamId", "search", "filters"],
                merge(existing = { edges: [], pageInfo: {} }, incoming) {
                  return {
                    ...incoming,
                    edges: [...existing.edges, ...incoming.edges],
                  };
                },
              },
              files: {
                keyArgs: ["workspaceId", "projectId", "search", "filters"],
                merge(existing = { edges: [], pageInfo: {} }, incoming) {
                  return {
                    ...incoming,
                    edges: [...existing.edges, ...incoming.edges],
                  };
                },
              },
            },
          },
          User: {
            fields: {
              roles: {
                merge: false, // Replace instead of merging
              },
            },
          },
          Workspace: {
            fields: {
              members: {
                merge: false,
              },
              teams: {
                merge: false,
              },
              projects: {
                merge: false,
              },
            },
          },
        },
      }),
      defaultOptions: {
        watchQuery: {
          errorPolicy: "all",
          notifyOnNetworkStatusChange: true,
        },
        query: {
          errorPolicy: "all",
        },
        mutate: {
          errorPolicy: "all",
        },
      },
    });
  }

  // Create error handling link
  private createErrorLink(): ApolloLink {
    return onError(({ graphQLErrors, networkError, operation, forward }) => {
      if (graphQLErrors) {
        graphQLErrors.forEach(({ message, locations, path, extensions }) => {
          console.error(
            `GraphQL error: Message: ${message}, Location: ${locations}, Path: ${path}`
          );

          // Handle authentication errors
          if (extensions?.code === "UNAUTHENTICATED") {
            this.tokenManager.clearTokens();
            this.authEventEmitter.emit(AUTH_EVENTS.TOKEN_EXPIRED);
          }
        });
      }

      if (networkError) {
        console.error(`GraphQL network error: ${networkError}`);
        
        // Handle network authentication errors
        if ("statusCode" in networkError && networkError.statusCode === 401) {
          this.tokenManager.clearTokens();
          this.authEventEmitter.emit(AUTH_EVENTS.TOKEN_EXPIRED);
        }
      }
    });
  }

  // Create retry link
  private createRetryLink(): RetryLink {
    return new RetryLink({
      delay: {
        initial: 300,
        max: Infinity,
        jitter: true,
      },
      attempts: {
        max: 3,
        retryIf: (error, _operation) => {
          // Retry on network errors and server errors
          return !!error && (
            error.networkError?.message?.includes("fetch") ||
            error.networkError?.message?.includes("Network") ||
            (error.networkError as any)?.statusCode >= 500
          );
        },
      },
    });
  }

  // Create authentication link
  private createAuthLink(): ApolloLink {
    return setContext(async (_, { headers }) => {
      const token = this.tokenManager.getAccessToken();
      
      // Check if token needs refresh
      if (token && this.tokenManager.needsRefresh()) {
        try {
          await this.tokenManager.refreshTokens(async (refreshToken) => {
            // This would call the refresh endpoint
            // For now, we'll just return the existing tokens
            return {
              accessToken: token,
              refreshToken,
              expiresAt: Date.now() + 60 * 60 * 1000, // 1 hour
            };
          });
        } catch (error) {
          console.error("Token refresh failed:", error);
        }
      }

      return {
        headers: {
          ...headers,
          authorization: token ? `Bearer ${token}` : "",
        },
      };
    });
  }

  // Create context link for tenant/workspace/team
  private createContextLink(): ApolloLink {
    return new ApolloLink((operation, forward) => {
      const context = operation.getContext();
      const headers = context.headers || {};

      // Add context headers if provided
      if (context.tenantId) {
        headers["X-Tenant-ID"] = context.tenantId;
      }
      if (context.workspaceId) {
        headers["X-Workspace-ID"] = context.workspaceId;
      }
      if (context.teamId) {
        headers["X-Team-ID"] = context.teamId;
      }

      operation.setContext({
        ...context,
        headers,
      });

      return forward(operation);
    });
  }

  // Create split link for HTTP/WebSocket
  private createSplitLink(httpLink: ApolloLink, wsLink: ApolloLink): ApolloLink {
    return from([
      new ApolloLink((operation, forward) => {
        const definition = getMainDefinition(operation.query);
        
        // Use WebSocket for subscriptions, HTTP for everything else
        if (
          definition.kind === "OperationDefinition" &&
          definition.operation === "subscription"
        ) {
          return wsLink.request(operation) || null;
        }
        
        return forward(operation);
      }),
      httpLink,
    ]);
  }

  // Get Apollo Client instance
  getClient(): ApolloClient<any> {
    return this.client;
  }

  // Set context for requests
  setContext(context: {
    tenantId?: string;
    workspaceId?: string;
    teamId?: string;
  }): void {
    this.client.setLink(
      from([
        this.createErrorLink(),
        this.createRetryLink(),
        this.createAuthLink(),
        setContext((_, { headers }) => ({
          headers: {
            ...headers,
            ...(context.tenantId && { "X-Tenant-ID": context.tenantId }),
            ...(context.workspaceId && { "X-Workspace-ID": context.workspaceId }),
            ...(context.teamId && { "X-Team-ID": context.teamId }),
          },
        })),
        this.createSplitLink(
          createHttpLink({ uri: this.config.graphqlURL }),
          new GraphQLWsLink(this.wsClient)
        ),
      ])
    );
  }

  // Clear cache
  clearCache(): void {
    this.client.clearStore();
  }

  // Reset store
  resetStore(): void {
    this.client.resetStore();
  }

  // Close WebSocket connection
  closeWebSocket(): void {
    if (this.wsClient) {
      this.wsClient.dispose();
    }
  }
}

// Create singleton instance
export const graphqlApiClient = new GraphQLApiClient();
