import { Workspace } from "@nexus/database-schema";
import { createTenantClient } from "./client";
import { CreateWorkspace } from "@nexus/validation";

export class WorkspaceService {
  private tenantId: string;

  constructor(tenantId: string) {
    this.tenantId = tenantId;
  }

  // Create a new workspace
  async create(data: CreateWorkspace): Promise<Workspace> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.workspace.create({
      data,
    });
  }

  // Find workspace by ID
  async findById(id: string): Promise<Workspace | null> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.workspace.findUnique({
      where: { id },
    });
  }

  // Find workspace by slug
  async findBySlug(slug: string): Promise<Workspace | null> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.workspace.findUnique({
      where: { 
        tenantId_slug: {
          tenantId: this.tenantId,
          slug,
        },
      },
    });
  }

  // Update workspace
  async update(id: string, data: Partial<CreateWorkspace>): Promise<Workspace> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.workspace.update({
      where: { id },
      data,
    });
  }

  // Delete workspace
  async delete(id: string): Promise<Workspace> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.workspace.delete({
      where: { id },
    });
  }

  // List workspaces with pagination
  async list(page: number = 1, limit: number = 10) {
    const client = createTenantClient(this.tenantId);
    const skip = (page - 1) * limit;
    
    const [workspaces, total] = await Promise.all([
      client.tenant.workspace.findMany({
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      client.tenant.workspace.count(),
    ]);

    return {
      data: workspaces,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  // Check if slug is available
  async isSlugAvailable(slug: string): Promise<boolean> {
    const workspace = await this.findBySlug(slug);
    return !workspace;
  }

  // Get all workspaces for tenant
  async findAll(): Promise<Workspace[]> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.workspace.findMany({
      orderBy: { createdAt: "desc" },
    });
  }
}

export const createWorkspaceService = (tenantId: string): WorkspaceService => {
  return new WorkspaceService(tenantId);
};
