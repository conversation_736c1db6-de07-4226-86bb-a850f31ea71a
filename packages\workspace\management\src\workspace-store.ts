import { create } from "zustand";
import { WorkspaceData, WorkspaceMember, WorkspaceInvitation } from "./workspace-types";

interface WorkspaceStore {
  // State
  currentWorkspace: WorkspaceData | null;
  workspaces: WorkspaceData[];
  members: WorkspaceMember[];
  invitations: WorkspaceInvitation[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setCurrentWorkspace: (workspace: WorkspaceData | null) => void;
  setWorkspaces: (workspaces: WorkspaceData[]) => void;
  addWorkspace: (workspace: WorkspaceData) => void;
  updateWorkspace: (id: string, updates: Partial<WorkspaceData>) => void;
  removeWorkspace: (id: string) => void;
  setMembers: (members: WorkspaceMember[]) => void;
  addMember: (member: WorkspaceMember) => void;
  updateMember: (id: string, updates: Partial<WorkspaceMember>) => void;
  removeMember: (id: string) => void;
  setInvitations: (invitations: WorkspaceInvitation[]) => void;
  addInvitation: (invitation: WorkspaceInvitation) => void;
  updateInvitation: (id: string, updates: Partial<WorkspaceInvitation>) => void;
  removeInvitation: (id: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
}

export const useWorkspaceStore = create<WorkspaceStore>((set, get) => ({
  // Initial state
  currentWorkspace: null,
  workspaces: [],
  members: [],
  invitations: [],
  isLoading: false,
  error: null,
  
  // Actions
  setCurrentWorkspace: (workspace) => set({ currentWorkspace: workspace }),
  
  setWorkspaces: (workspaces) => set({ workspaces }),
  
  addWorkspace: (workspace) => set((state) => ({
    workspaces: [...state.workspaces, workspace],
  })),
  
  updateWorkspace: (id, updates) => set((state) => ({
    workspaces: state.workspaces.map((w) => 
      w.id === id ? { ...w, ...updates } : w
    ),
    currentWorkspace: state.currentWorkspace?.id === id 
      ? { ...state.currentWorkspace, ...updates }
      : state.currentWorkspace,
  })),
  
  removeWorkspace: (id) => set((state) => ({
    workspaces: state.workspaces.filter((w) => w.id !== id),
    currentWorkspace: state.currentWorkspace?.id === id 
      ? null 
      : state.currentWorkspace,
  })),
  
  setMembers: (members) => set({ members }),
  
  addMember: (member) => set((state) => ({
    members: [...state.members, member],
  })),
  
  updateMember: (id, updates) => set((state) => ({
    members: state.members.map((m) => 
      m.id === id ? { ...m, ...updates } : m
    ),
  })),
  
  removeMember: (id) => set((state) => ({
    members: state.members.filter((m) => m.id !== id),
  })),
  
  setInvitations: (invitations) => set({ invitations }),
  
  addInvitation: (invitation) => set((state) => ({
    invitations: [...state.invitations, invitation],
  })),
  
  updateInvitation: (id, updates) => set((state) => ({
    invitations: state.invitations.map((i) => 
      i.id === id ? { ...i, ...updates } : i
    ),
  })),
  
  removeInvitation: (id) => set((state) => ({
    invitations: state.invitations.filter((i) => i.id !== id),
  })),
  
  setLoading: (isLoading) => set({ isLoading }),
  
  setError: (error) => set({ error }),
  
  reset: () => set({
    currentWorkspace: null,
    workspaces: [],
    members: [],
    invitations: [],
    isLoading: false,
    error: null,
  }),
}));

// Selectors
export const selectCurrentWorkspace = (state: WorkspaceStore) => state.currentWorkspace;
export const selectWorkspaces = (state: WorkspaceStore) => state.workspaces;
export const selectMembers = (state: WorkspaceStore) => state.members;
export const selectInvitations = (state: WorkspaceStore) => state.invitations;
export const selectIsLoading = (state: WorkspaceStore) => state.isLoading;
export const selectError = (state: WorkspaceStore) => state.error;
