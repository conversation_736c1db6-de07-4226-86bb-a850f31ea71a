// Nexus SaaS Multi-Tenant Database Schema

generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Tenant {
  id        String           @id @default(cuid())
  name      String
  slug      String           @unique
  domain    String?          @unique
  status    TenantStatus     @default(ACTIVE)
  plan      SubscriptionPlan @default(STARTER)
  metadata  Json?
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  // Relationships
  users      User[]
  workspaces Workspace[]
  sessions   Session[]

  @@map("tenants")
}

model User {
  id        String     @id @default(cuid())
  tenantId  String // Multi-tenant isolation
  email     String
  name      String?
  avatar    String?
  role      UserRole   @default(MEMBER)
  status    UserStatus @default(ACTIVE)
  metadata  Json?
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  // Relationships
  tenant   Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  sessions Session[]

  @@unique([tenantId, email])
  @@index([tenantId])
  @@map("users")
}

model Workspace {
  id        String   @id @default(cuid())
  tenantId  String // Multi-tenant isolation
  name      String
  slug      String
  settings  Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, slug])
  @@index([tenantId])
  @@map("workspaces")
}

model Session {
  id        String   @id @default(cuid())
  tenantId  String // Multi-tenant isolation
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([userId])
  @@index([token])
  @@map("sessions")
}

// Enums
enum TenantStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum SubscriptionPlan {
  STARTER
  PROFESSIONAL
  ENTERPRISE
}

enum UserRole {
  OWNER
  ADMIN
  MEMBER
  VIEWER
}

enum UserStatus {
  ACTIVE
  INACTIVE
  INVITED
}
