"use client";

import React, { useState } from "react";
import { useRBAC, usePermission, useRole } from "./rbac-hooks";
import { ResourceType, ActionType, Role } from "./rbac-types";

// Permission gate component
export function PermissionGate({
  resource,
  action,
  resourceId,
  children,
  fallback,
  loading,
}: {
  resource: ResourceType;
  action: ActionType;
  resourceId?: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  loading?: React.ReactNode;
}) {
  const { hasPermission, isLoading } = usePermission(resource, action, resourceId);

  if (isLoading) {
    return loading || <div>Checking permissions...</div>;
  }

  if (!hasPermission) {
    return fallback || null;
  }

  return <>{children}</>;
}

// Role gate component
export function RoleGate({
  role,
  children,
  fallback,
}: {
  role: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const hasRole = useRole(role);

  if (!hasRole) {
    return fallback || null;
  }

  return <>{children}</>;
}

// Admin only component
export function AdminOnly({
  children,
  fallback,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  return (
    <RoleGate role="admin" fallback={fallback}>
      {children}
    </RoleGate>
  );
}

// Owner only component
export function OwnerOnly({
  children,
  fallback,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  return (
    <RoleGate role="owner" fallback={fallback}>
      {children}
    </RoleGate>
  );
}

// Role management component
export function RoleManager() {
  const { roles, createRole, isLoading, error } = useRBAC();
  const [showCreateForm, setShowCreateForm] = useState(false);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Role Management</h2>
        <button
          onClick={() => setShowCreateForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          Create Role
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {isLoading ? (
        <div>Loading roles...</div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {roles.map((role) => (
            <RoleCard key={role.id} role={role} />
          ))}
        </div>
      )}

      {showCreateForm && (
        <CreateRoleForm
          onClose={() => setShowCreateForm(false)}
          onSuccess={() => setShowCreateForm(false)}
        />
      )}
    </div>
  );
}

// Role card component
function RoleCard({ role }: { role: Role }) {
  return (
    <div className="bg-white border rounded-lg p-4">
      <div className="flex items-center space-x-3 mb-3">
        {role.icon && <span className="text-2xl">{role.icon}</span>}
        <div>
          <h3 className="font-semibold">{role.name}</h3>
          <p className="text-sm text-gray-500">{role.slug}</p>
        </div>
      </div>
      
      {role.description && (
        <p className="text-sm text-gray-600 mb-3">{role.description}</p>
      )}
      
      <div className="flex items-center justify-between">
        <span className={`px-2 py-1 text-xs rounded ${
          role.isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
        }`}>
          {role.isActive ? "Active" : "Inactive"}
        </span>
        
        <span className="text-xs text-gray-500">
          {role.permissions.length} permissions
        </span>
      </div>
    </div>
  );
}

// Create role form
function CreateRoleForm({
  onClose,
  onSuccess,
}: {
  onClose: () => void;
  onSuccess: () => void;
}) {
  const { createRole, isLoading } = useRBAC();
  const [formData, setFormData] = useState({
    name: "",
    slug: "",
    description: "",
    level: "workspace" as const,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createRole({
        ...formData,
        permissions: [],
        isSystem: false,
        isActive: true,
        metadata: {},
      });
      onSuccess();
    } catch (error) {
      console.error("Failed to create role:", error);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Create New Role</h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border rounded-md"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Slug</label>
            <input
              type="text"
              value={formData.slug}
              onChange={(e) => setFormData({ ...formData, slug: e.target.value })}
              className="w-full px-3 py-2 border rounded-md"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full px-3 py-2 border rounded-md"
              rows={3}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Level</label>
            <select
              value={formData.level}
              onChange={(e) => setFormData({ ...formData, level: e.target.value as any })}
              className="w-full px-3 py-2 border rounded-md"
            >
              <option value="system">System</option>
              <option value="organization">Organization</option>
              <option value="workspace">Workspace</option>
              <option value="team">Team</option>
              <option value="user">User</option>
            </select>
          </div>
          
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? "Creating..." : "Create Role"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Permission display component
export function PermissionDisplay({
  resource,
  action,
  resourceId,
}: {
  resource: ResourceType;
  action: ActionType;
  resourceId?: string;
}) {
  const { hasPermission, isLoading } = usePermission(resource, action, resourceId);

  if (isLoading) {
    return <span className="text-gray-400">Checking...</span>;
  }

  return (
    <span className={`px-2 py-1 text-xs rounded ${
      hasPermission 
        ? "bg-green-100 text-green-800" 
        : "bg-red-100 text-red-800"
    }`}>
      {hasPermission ? "Allowed" : "Denied"}
    </span>
  );
}

// Conditional render based on permission
export function IfCan({
  resource,
  action,
  resourceId,
  children,
}: {
  resource: ResourceType;
  action: ActionType;
  resourceId?: string;
  children: React.ReactNode;
}) {
  return (
    <PermissionGate resource={resource} action={action} resourceId={resourceId}>
      {children}
    </PermissionGate>
  );
}

// Conditional render based on role
export function IfRole({
  role,
  children,
}: {
  role: string;
  children: React.ReactNode;
}) {
  return (
    <RoleGate role={role}>
      {children}
    </RoleGate>
  );
}
