import { withFilter } from "graphql-subscriptions";
import { GraphQLContext, SubscriptionResolver } from "../types";
import { GraphQLAuthenticationError } from "../types";

export const subscriptionResolvers = {
  Subscription: {
    userCreated: {
      subscribe: withFilter(
        (parent: any, args: any, context: GraphQLContext) => {
          if (!context.user) {
            throw new GraphQLAuthenticationError();
          }
          return context.pubsub.asyncIterator(["USER_CREATED"]);
        },
        (payload: any, variables: any, context: GraphQLContext) => {
          // Filter by tenant if specified
          if (variables.tenantId) {
            return payload.tenantId === variables.tenantId;
          }
          // Default to user's tenant
          return payload.tenantId === context.tenantId;
        }
      ),
    },

    userUpdated: {
      subscribe: withFilter(
        (parent: any, args: any, context: GraphQLContext) => {
          if (!context.user) {
            throw new GraphQLAuthenticationError();
          }
          return context.pubsub.asyncIterator(["USER_UPDATED"]);
        },
        (payload: any, variables: any, context: GraphQLContext) => {
          // Filter by specific user if specified
          if (variables.userId) {
            return payload.userId === variables.userId;
          }
          return true;
        }
      ),
    },

    userDeleted: {
      subscribe: withFilter(
        (parent: any, args: any, context: GraphQLContext) => {
          if (!context.user) {
            throw new GraphQLAuthenticationError();
          }
          return context.pubsub.asyncIterator(["USER_DELETED"]);
        },
        (payload: any, variables: any, context: GraphQLContext) => {
          if (variables.userId) {
            return payload.userId === variables.userId;
          }
          return true;
        }
      ),
    },

    roleAssigned: {
      subscribe: withFilter(
        (parent: any, args: any, context: GraphQLContext) => {
          if (!context.user) {
            throw new GraphQLAuthenticationError();
          }
          return context.pubsub.asyncIterator(["ROLE_ASSIGNED"]);
        },
        (payload: any, variables: any, context: GraphQLContext) => {
          if (variables.userId) {
            return payload.userId === variables.userId;
          }
          return true;
        }
      ),
    },

    roleRevoked: {
      subscribe: withFilter(
        (parent: any, args: any, context: GraphQLContext) => {
          if (!context.user) {
            throw new GraphQLAuthenticationError();
          }
          return context.pubsub.asyncIterator(["ROLE_REVOKED"]);
        },
        (payload: any, variables: any, context: GraphQLContext) => {
          if (variables.userId) {
            return payload.userId === variables.userId;
          }
          return true;
        }
      ),
    },

    workspaceCreated: {
      subscribe: withFilter(
        (parent: any, args: any, context: GraphQLContext) => {
          if (!context.user) {
            throw new GraphQLAuthenticationError();
          }
          return context.pubsub.asyncIterator(["WORKSPACE_CREATED"]);
        },
        (payload: any, variables: any, context: GraphQLContext) => {
          if (variables.tenantId) {
            return payload.tenantId === variables.tenantId;
          }
          return payload.tenantId === context.tenantId;
        }
      ),
    },

    workspaceUpdated: {
      subscribe: withFilter(
        (parent: any, args: any, context: GraphQLContext) => {
          if (!context.user) {
            throw new GraphQLAuthenticationError();
          }
          return context.pubsub.asyncIterator(["WORKSPACE_UPDATED"]);
        },
        (payload: any, variables: any, context: GraphQLContext) => {
          if (variables.workspaceId) {
            return payload.workspaceId === variables.workspaceId;
          }
          return true;
        }
      ),
    },

    teamCreated: {
      subscribe: withFilter(
        (parent: any, args: any, context: GraphQLContext) => {
          if (!context.user) {
            throw new GraphQLAuthenticationError();
          }
          return context.pubsub.asyncIterator(["TEAM_CREATED"]);
        },
        (payload: any, variables: any, context: GraphQLContext) => {
          if (variables.workspaceId) {
            return payload.workspaceId === variables.workspaceId;
          }
          return true;
        }
      ),
    },

    teamUpdated: {
      subscribe: withFilter(
        (parent: any, args: any, context: GraphQLContext) => {
          if (!context.user) {
            throw new GraphQLAuthenticationError();
          }
          return context.pubsub.asyncIterator(["TEAM_UPDATED"]);
        },
        (payload: any, variables: any, context: GraphQLContext) => {
          if (variables.teamId) {
            return payload.teamId === variables.teamId;
          }
          return true;
        }
      ),
    },

    teamMemberAdded: {
      subscribe: withFilter(
        (parent: any, args: any, context: GraphQLContext) => {
          if (!context.user) {
            throw new GraphQLAuthenticationError();
          }
          return context.pubsub.asyncIterator(["TEAM_MEMBER_ADDED"]);
        },
        (payload: any, variables: any, context: GraphQLContext) => {
          if (variables.teamId) {
            return payload.teamId === variables.teamId;
          }
          return true;
        }
      ),
    },

    teamMemberRemoved: {
      subscribe: withFilter(
        (parent: any, args: any, context: GraphQLContext) => {
          if (!context.user) {
            throw new GraphQLAuthenticationError();
          }
          return context.pubsub.asyncIterator(["TEAM_MEMBER_REMOVED"]);
        },
        (payload: any, variables: any, context: GraphQLContext) => {
          if (variables.teamId) {
            return payload.teamId === variables.teamId;
          }
          return true;
        }
      ),
    },

    projectCreated: {
      subscribe: withFilter(
        (parent: any, args: any, context: GraphQLContext) => {
          if (!context.user) {
            throw new GraphQLAuthenticationError();
          }
          return context.pubsub.asyncIterator(["PROJECT_CREATED"]);
        },
        (payload: any, variables: any, context: GraphQLContext) => {
          if (variables.workspaceId && payload.workspaceId !== variables.workspaceId) {
            return false;
          }
          if (variables.teamId && payload.teamId !== variables.teamId) {
            return false;
          }
          return true;
        }
      ),
    },

    projectUpdated: {
      subscribe: withFilter(
        (parent: any, args: any, context: GraphQLContext) => {
          if (!context.user) {
            throw new GraphQLAuthenticationError();
          }
          return context.pubsub.asyncIterator(["PROJECT_UPDATED"]);
        },
        (payload: any, variables: any, context: GraphQLContext) => {
          if (variables.projectId) {
            return payload.projectId === variables.projectId;
          }
          return true;
        }
      ),
    },

    fileUploaded: {
      subscribe: withFilter(
        (parent: any, args: any, context: GraphQLContext) => {
          if (!context.user) {
            throw new GraphQLAuthenticationError();
          }
          return context.pubsub.asyncIterator(["FILE_UPLOADED"]);
        },
        (payload: any, variables: any, context: GraphQLContext) => {
          if (variables.workspaceId && payload.workspaceId !== variables.workspaceId) {
            return false;
          }
          if (variables.projectId && payload.projectId !== variables.projectId) {
            return false;
          }
          return true;
        }
      ),
    },

    fileUpdated: {
      subscribe: withFilter(
        (parent: any, args: any, context: GraphQLContext) => {
          if (!context.user) {
            throw new GraphQLAuthenticationError();
          }
          return context.pubsub.asyncIterator(["FILE_UPDATED"]);
        },
        (payload: any, variables: any, context: GraphQLContext) => {
          if (variables.fileId) {
            return payload.fileId === variables.fileId;
          }
          return true;
        }
      ),
    },

    fileDeleted: {
      subscribe: withFilter(
        (parent: any, args: any, context: GraphQLContext) => {
          if (!context.user) {
            throw new GraphQLAuthenticationError();
          }
          return context.pubsub.asyncIterator(["FILE_DELETED"]);
        },
        (payload: any, variables: any, context: GraphQLContext) => {
          if (variables.fileId) {
            return payload.fileId === variables.fileId;
          }
          return true;
        }
      ),
    },
  },
};
