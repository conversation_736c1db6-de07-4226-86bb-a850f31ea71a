import { UsageMetrics } from "./billing-types";

export class UsageAnalyticsService {
  private tenantId: string;

  constructor(tenantId: string) {
    this.tenantId = tenantId;
  }

  // Get current usage metrics
  async getCurrentUsage(): Promise<UsageMetrics> {
    const response = await fetch("/api/billing/usage/current", {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch current usage");
    }

    return response.json();
  }

  // Get usage history
  async getUsageHistory(
    period: { start: Date; end: Date },
    granularity: "day" | "week" | "month" = "day"
  ): Promise<Array<{
    date: Date;
    metrics: Array<{
      name: string;
      value: number;
      cost: number;
    }>;
    totalCost: number;
  }>> {
    const params = new URLSearchParams({
      startDate: period.start.toISOString(),
      endDate: period.end.toISOString(),
      granularity,
    });

    const response = await fetch(`/api/billing/usage/history?${params}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch usage history");
    }

    return response.json();
  }

  // Get usage projections
  async getUsageProjections(): Promise<{
    currentPeriod: {
      projected: number;
      actual: number;
      daysRemaining: number;
    };
    nextPeriod: {
      projected: number;
      confidence: number;
    };
    trends: Array<{
      metric: string;
      trend: "increasing" | "decreasing" | "stable";
      changePercent: number;
    }>;
  }> {
    const response = await fetch("/api/billing/usage/projections", {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch usage projections");
    }

    return response.json();
  }

  // Get usage alerts
  async getUsageAlerts(): Promise<Array<{
    id: string;
    metric: string;
    threshold: number;
    currentValue: number;
    severity: "warning" | "critical";
    message: string;
    createdAt: Date;
  }>> {
    const response = await fetch("/api/billing/usage/alerts", {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to fetch usage alerts");
    }

    return response.json();
  }

  // Set usage alert
  async setUsageAlert(data: {
    metric: string;
    threshold: number;
    type: "percentage" | "absolute";
    email: boolean;
    webhook?: string;
  }): Promise<void> {
    const response = await fetch("/api/billing/usage/alerts", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to set usage alert");
    }
  }

  // Delete usage alert
  async deleteUsageAlert(alertId: string): Promise<void> {
    const response = await fetch(`/api/billing/usage/alerts/${alertId}`, {
      method: "DELETE",
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to delete usage alert");
    }
  }

  // Export usage data
  async exportUsageData(
    period: { start: Date; end: Date },
    format: "csv" | "xlsx" = "csv"
  ): Promise<Blob> {
    const params = new URLSearchParams({
      startDate: period.start.toISOString(),
      endDate: period.end.toISOString(),
      format,
    });

    const response = await fetch(`/api/billing/usage/export?${params}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to export usage data");
    }

    return response.blob();
  }
}

export const createUsageAnalyticsService = (tenantId: string): UsageAnalyticsService => {
  return new UsageAnalyticsService(tenantId);
};
