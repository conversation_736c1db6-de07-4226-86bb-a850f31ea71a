import { FastifyInstance } from "fastify";
import { z } from "zod";
import { validate, authRateLimit } from "../middleware";
import { generateToken, generateRefreshToken } from "../middleware/auth";
import { ApiResponse, AuthenticationError, ValidationError } from "../types";

// Auth schemas
const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
});

const registerSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  name: z.string().min(2),
  tenantName: z.string().min(2).optional(),
});

const refreshSchema = z.object({
  refreshToken: z.string(),
});

export const authRoutes = async (fastify: FastifyInstance) => {
  // Login
  fastify.post("/login", {
    schema: {
      tags: ["Authentication"],
      summary: "User login",
      description: "Authenticate user with email and password",
      body: {
        type: "object",
        properties: {
          email: { type: "string", format: "email" },
          password: { type: "string", minLength: 8 },
        },
        required: ["email", "password"],
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                user: { $ref: "#/components/schemas/User" },
                accessToken: { type: "string" },
                refreshToken: { type: "string" },
                expiresIn: { type: "number" },
              },
            },
          },
        },
        400: { $ref: "#/components/responses/BadRequest" },
        401: { $ref: "#/components/responses/Unauthorized" },
        429: { $ref: "#/components/responses/TooManyRequests" },
      },
    },
    preHandler: [authRateLimit, validate({ body: loginSchema })],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { email, password } = request.body as z.infer<typeof loginSchema>;
      
      // TODO: Implement actual authentication logic
      // For now, mock authentication
      if (email === "<EMAIL>" && password === "password123") {
        const user = {
          id: "user_123",
          email,
          name: "Admin User",
          tenantId: "tenant_123",
          roles: ["admin"],
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        const accessToken = generateToken(fastify, {
          userId: user.id,
          email: user.email,
          tenantId: user.tenantId,
          roles: user.roles,
        });

        const refreshToken = generateRefreshToken(fastify, {
          userId: user.id,
          email: user.email,
          tenantId: user.tenantId,
          roles: user.roles,
        });

        return {
          success: true,
          data: {
            user,
            accessToken,
            refreshToken,
            expiresIn: 24 * 60 * 60, // 24 hours
          },
          meta: {
            timestamp: new Date().toISOString(),
            requestId: request.id,
            version: "1.0.0",
          },
        };
      }

      throw new AuthenticationError("Invalid credentials");
    },
  });

  // Register
  fastify.post("/register", {
    schema: {
      tags: ["Authentication"],
      summary: "User registration",
      description: "Register a new user account",
      body: {
        type: "object",
        properties: {
          email: { type: "string", format: "email" },
          password: { type: "string", minLength: 8 },
          name: { type: "string", minLength: 2 },
          tenantName: { type: "string", minLength: 2 },
        },
        required: ["email", "password", "name"],
      },
      response: {
        201: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                user: { $ref: "#/components/schemas/User" },
                accessToken: { type: "string" },
                refreshToken: { type: "string" },
                expiresIn: { type: "number" },
              },
            },
          },
        },
        400: { $ref: "#/components/responses/BadRequest" },
        409: {
          description: "Conflict - User already exists",
          content: {
            "application/json": {
              schema: { $ref: "#/components/schemas/Error" },
            },
          },
        },
        429: { $ref: "#/components/responses/TooManyRequests" },
      },
    },
    preHandler: [authRateLimit, validate({ body: registerSchema })],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { email, password, name, tenantName } = request.body as z.infer<typeof registerSchema>;
      
      // TODO: Implement actual registration logic
      const user = {
        id: `user_${Date.now()}`,
        email,
        name,
        tenantId: `tenant_${Date.now()}`,
        roles: ["owner"],
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const accessToken = generateToken(fastify, {
        userId: user.id,
        email: user.email,
        tenantId: user.tenantId,
        roles: user.roles,
      });

      const refreshToken = generateRefreshToken(fastify, {
        userId: user.id,
        email: user.email,
        tenantId: user.tenantId,
        roles: user.roles,
      });

      reply.status(201);
      return {
        success: true,
        data: {
          user,
          accessToken,
          refreshToken,
          expiresIn: 24 * 60 * 60,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Refresh token
  fastify.post("/refresh", {
    schema: {
      tags: ["Authentication"],
      summary: "Refresh access token",
      description: "Get a new access token using refresh token",
      body: {
        type: "object",
        properties: {
          refreshToken: { type: "string" },
        },
        required: ["refreshToken"],
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                accessToken: { type: "string" },
                expiresIn: { type: "number" },
              },
            },
          },
        },
        400: { $ref: "#/components/responses/BadRequest" },
        401: { $ref: "#/components/responses/Unauthorized" },
      },
    },
    preHandler: [validate({ body: refreshSchema })],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { refreshToken } = request.body as z.infer<typeof refreshSchema>;
      
      try {
        // TODO: Implement actual refresh token validation
        const payload = fastify.jwt.verify(refreshToken);
        
        const newAccessToken = generateToken(fastify, {
          userId: (payload as any).userId,
          email: (payload as any).email,
          tenantId: (payload as any).tenantId,
          roles: (payload as any).roles,
        });

        return {
          success: true,
          data: {
            accessToken: newAccessToken,
            expiresIn: 24 * 60 * 60,
          },
          meta: {
            timestamp: new Date().toISOString(),
            requestId: request.id,
            version: "1.0.0",
          },
        };
      } catch (error) {
        throw new AuthenticationError("Invalid refresh token");
      }
    },
  });

  // Logout
  fastify.post("/logout", {
    schema: {
      tags: ["Authentication"],
      summary: "User logout",
      description: "Logout user and invalidate tokens",
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                message: { type: "string" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
      },
    },
    preHandler: [fastify.authenticate],
    handler: async (request, reply): Promise<ApiResponse> => {
      // TODO: Implement token blacklisting
      
      return {
        success: true,
        data: {
          message: "Logged out successfully",
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Get current user
  fastify.get("/me", {
    schema: {
      tags: ["Authentication"],
      summary: "Get current user",
      description: "Get information about the authenticated user",
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                user: { $ref: "#/components/schemas/User" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
      },
    },
    preHandler: [fastify.authenticate],
    handler: async (request, reply): Promise<ApiResponse> => {
      const authRequest = request as any;
      
      return {
        success: true,
        data: {
          user: authRequest.user,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });
};
