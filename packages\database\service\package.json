{"name": "@nexus/database-service", "version": "0.1.0", "description": "Database service layer for Nexus SaaS", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "jest", "type-check": "tsc --noEmit"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "@nexus/validation": "workspace:*", "@nexus/database-schema": "workspace:*", "@prisma/client": "^5.20.0"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@types/jest": "^29.5.0", "jest": "^29.5.0", "typescript": "^5.8.0"}}