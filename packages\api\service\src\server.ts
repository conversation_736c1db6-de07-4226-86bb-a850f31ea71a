import Fastify, { FastifyInstance } from "fastify";
import { config, isDevelopment } from "./config";
import { registerPlugins } from "./plugins";
import { registerRoutes } from "./routes";
import { errorHandler } from "./middleware/error-handler";
import { requestLogger } from "./middleware/request-logger";

// Create Fastify instance
export const createServer = async (): Promise<FastifyInstance> => {
  const server = Fastify({
    logger: {
      level: config.LOG_LEVEL,
      ...(isDevelopment && {
        transport: {
          target: "pino-pretty",
          options: {
            colorize: true,
            translateTime: "HH:MM:ss Z",
            ignore: "pid,hostname",
          },
        },
      }),
    },
    genReqId: () => {
      return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    },
    trustProxy: true,
  });

  // Register request logger
  await server.register(requestLogger);

  // Register plugins
  await registerPlugins(server);

  // Register routes
  await registerRoutes(server);

  // Register error handler
  server.setErrorHandler(errorHandler);

  // Health check endpoint
  server.get("/health", async () => {
    return {
      status: "healthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || "unknown",
      environment: config.NODE_ENV,
      services: {
        database: "healthy", // TODO: Add actual database health check
      },
    };
  });

  // Ready check endpoint
  server.get("/ready", async () => {
    return {
      status: "ready",
      timestamp: new Date().toISOString(),
    };
  });

  return server;
};

// Start server
export const startServer = async (): Promise<FastifyInstance> => {
  try {
    const server = await createServer();

    // Graceful shutdown
    const gracefulShutdown = async (signal: string) => {
      server.log.info(`Received ${signal}, shutting down gracefully`);
      try {
        await server.close();
        process.exit(0);
      } catch (error) {
        server.log.error("Error during shutdown:", error);
        process.exit(1);
      }
    };

    process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
    process.on("SIGINT", () => gracefulShutdown("SIGINT"));

    // Start listening
    await server.listen({
      port: config.PORT,
      host: config.HOST,
    });

    server.log.info(`Server listening on ${config.HOST}:${config.PORT}`);
    
    if (config.ENABLE_SWAGGER) {
      server.log.info(`API documentation available at http://${config.HOST}:${config.PORT}${config.SWAGGER_PATH}`);
    }

    return server;
  } catch (error) {
    console.error("Failed to start server:", error);
    process.exit(1);
  }
};

// Export for testing
export { createServer as default };
