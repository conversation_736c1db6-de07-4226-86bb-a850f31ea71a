import { FastifyRequest, FastifyReply } from "fastify";
import { z } from "zod";

// Base API response schema
export const apiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.object({
    code: z.string(),
    message: z.string(),
    details: z.any().optional(),
  }).optional(),
  meta: z.object({
    timestamp: z.string(),
    requestId: z.string(),
    version: z.string(),
  }).optional(),
});

export type ApiResponse<T = any> = z.infer<typeof apiResponseSchema> & {
  data?: T;
};

// Pagination schema
export const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  total: z.number().optional(),
  totalPages: z.number().optional(),
});

export type Pagination = z.infer<typeof paginationSchema>;

// Query parameters schema
export const queryParamsSchema = z.object({
  page: z.string().transform(Number).optional(),
  limit: z.string().transform(Number).optional(),
  sort: z.string().optional(),
  order: z.enum(["asc", "desc"]).optional(),
  search: z.string().optional(),
  filter: z.string().optional(),
});

export type QueryParams = z.infer<typeof queryParamsSchema>;

// Request context
export interface RequestContext {
  userId?: string;
  tenantId?: string;
  workspaceId?: string;
  teamId?: string;
  roles?: string[];
  permissions?: string[];
  requestId: string;
  userAgent?: string;
  ip?: string;
}

// Extended Fastify request with context
export interface AuthenticatedRequest extends FastifyRequest {
  user?: {
    id: string;
    email: string;
    tenantId: string;
    roles: string[];
  };
  context: RequestContext;
}

// Route handler type
export type RouteHandler<T = any> = (
  request: AuthenticatedRequest,
  reply: FastifyReply
) => Promise<ApiResponse<T>>;

// Error types
export class ApiError extends Error {
  constructor(
    public statusCode: number,
    public code: string,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = "ApiError";
  }
}

export class ValidationError extends ApiError {
  constructor(message: string, details?: any) {
    super(400, "VALIDATION_ERROR", message, details);
  }
}

export class AuthenticationError extends ApiError {
  constructor(message: string = "Authentication required") {
    super(401, "AUTHENTICATION_ERROR", message);
  }
}

export class AuthorizationError extends ApiError {
  constructor(message: string = "Insufficient permissions") {
    super(403, "AUTHORIZATION_ERROR", message);
  }
}

export class NotFoundError extends ApiError {
  constructor(message: string = "Resource not found") {
    super(404, "NOT_FOUND", message);
  }
}

export class ConflictError extends ApiError {
  constructor(message: string = "Resource conflict") {
    super(409, "CONFLICT_ERROR", message);
  }
}

export class RateLimitError extends ApiError {
  constructor(message: string = "Rate limit exceeded") {
    super(429, "RATE_LIMIT_ERROR", message);
  }
}

export class InternalServerError extends ApiError {
  constructor(message: string = "Internal server error") {
    super(500, "INTERNAL_SERVER_ERROR", message);
  }
}

// Route options
export interface RouteOptions {
  requireAuth?: boolean;
  requirePermissions?: string[];
  rateLimit?: {
    max: number;
    timeWindow: string;
  };
  validation?: {
    body?: z.ZodSchema;
    query?: z.ZodSchema;
    params?: z.ZodSchema;
  };
  tags?: string[];
  summary?: string;
  description?: string;
}

// Plugin options
export interface PluginOptions {
  prefix?: string;
  tags?: string[];
}

// Health check response
export interface HealthCheckResponse {
  status: "healthy" | "unhealthy";
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  services: {
    database: "healthy" | "unhealthy";
    redis?: "healthy" | "unhealthy";
  };
}
