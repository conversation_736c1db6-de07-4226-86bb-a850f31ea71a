database_guidelines:
  id: supabase-database-guidelines
  name: Supabase Database Design Guidelines
  version: 1.0
  framework: nexus
  purpose: Instructions for creating production-ready database schemas

table_design_patterns:
  naming_conventions:
    tables: "Use snake_case for table names"
    columns: "Use snake_case for column names"
    constraints: "Use descriptive constraint names"
    indexes: "Prefix with idx_ followed by table and column"
  
  primary_keys:
    preferred: "XID using pg_idkit extension"
    alternative: "UUID with gen_random_uuid()"
    avoid: "Sequential integers for public-facing IDs"
  
  required_columns:
    audit_trail:
      - "created_at TIMESTAMPTZ DEFAULT NOW()"
      - "updated_at TIMESTAMPTZ DEFAULT NOW()"
      - "created_by UUID REFERENCES auth.users(id)"
      - "updated_by UUID REFERENCES auth.users(id)"
    
    soft_delete:
      - "deleted_at TIMESTAMPTZ (for soft delete pattern)"

data_types:
  text_fields:
    short: "VARCHAR(255) for known length limits"
    long: "TEXT for unlimited text content"
    enum: "CREATE TYPE for controlled values"
  
  numeric_fields:
    integers: "BIGINT for large numbers, INTEGER for smaller"
    decimals: "NUMERIC(precision, scale) for exact calculations"
    money: "NUMERIC(10,2) for currency"
  
  date_time:
    timestamps: "TIMESTAMPTZ for timezone-aware dates"
    dates: "DATE for date-only values"
    intervals: "INTERVAL for time periods"

row_level_security:
  enable_by_default: true
  
  common_policies:
    user_owned_data:
      - "Users can read their own data"
      - "Users can update their own data"
      - "Users can delete their own data"
    
    public_read:
      - "Anyone can read public data"
      - "Only authenticated users can write"
    
    admin_access:
      - "Admins have full access"
      - "Check user role in policies"

migration_patterns:
  file_naming: "YYYYMMDDHHMMSS_descriptive_name.sql"
  
  structure:
    - "Create tables with all constraints"
    - "Add indexes for performance"
    - "Enable RLS and create policies"
    - "Insert any required seed data"
    - "Add comments for documentation"

indexing_strategy:
  primary_indexes:
    - "Primary key (automatic)"
    - "Foreign key columns"
    - "Frequently queried columns"
  
  composite_indexes:
    - "Multi-column queries"
    - "Covering indexes for performance"
    - "Partial indexes for filtered queries"
  
  performance_considerations:
    - "Monitor query performance"
    - "Use EXPLAIN ANALYZE for optimization"
    - "Consider index maintenance overhead"

relationships:
  foreign_keys:
    - "Always define foreign key constraints"
    - "Use CASCADE or RESTRICT appropriately"
    - "Consider impact on delete operations"
  
  junction_tables:
    - "For many-to-many relationships"
    - "Include composite primary keys"
    - "Add audit columns"

security_considerations:
  data_protection:
    - "Enable RLS on all user data tables"
    - "Use policies to enforce access control"
    - "Audit sensitive data access"
  
  user_isolation:
    - "Filter by user_id in policies"
    - "Prevent cross-user data access"
    - "Validate user permissions"

implementation_checklist:
  - "Table names follow snake_case convention"
  - "Primary key using XID or UUID"
  - "Audit columns included"
  - "Foreign key constraints defined"
  - "Indexes created for performance"
  - "RLS enabled and policies created"
  - "Migration file properly named"
  - "Comments added for documentation"

examples:
  user_content: "Posts, comments, user-generated content"
  reference_data: "Categories, tags, configuration data"
  transactional: "Orders, payments, audit logs"
