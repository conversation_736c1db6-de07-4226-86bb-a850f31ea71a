"use client";

import React, { useState } from "react";
import { format } from "date-fns";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from "recharts";
import { useBilling, useBillingStats, useUsageMetrics } from "./billing-hooks";
import { Invoice, PaymentRecord } from "./billing-types";

// Billing dashboard overview
export function BillingDashboard() {
  const { stats, totalRevenue, pendingAmount, overdueInvoices, recentPayments, isLoading } = useBillingStats();

  if (isLoading) {
    return <div>Loading billing dashboard...</div>;
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Billing Dashboard</h1>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg border">
          <h3 className="text-sm font-medium text-gray-500">Total Revenue</h3>
          <p className="text-2xl font-bold">${(totalRevenue / 100).toFixed(2)}</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg border">
          <h3 className="text-sm font-medium text-gray-500">Pending Amount</h3>
          <p className="text-2xl font-bold">${(pendingAmount / 100).toFixed(2)}</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg border">
          <h3 className="text-sm font-medium text-gray-500">Overdue Invoices</h3>
          <p className="text-2xl font-bold text-red-600">{overdueInvoices.length}</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg border">
          <h3 className="text-sm font-medium text-gray-500">MRR</h3>
          <p className="text-2xl font-bold">${stats ? (stats.monthlyRecurringRevenue / 100).toFixed(2) : "0.00"}</p>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RecentInvoices />
        <RecentPayments payments={recentPayments} />
      </div>
    </div>
  );
}

// Invoice list component
export function InvoiceList() {
  const { invoices, loadInvoices, downloadInvoicePDF, sendInvoice, isLoading } = useBilling();
  const [filters, setFilters] = useState({});

  const handleDownload = async (invoiceId: string) => {
    try {
      await downloadInvoicePDF(invoiceId);
    } catch (error) {
      console.error("Failed to download invoice:", error);
    }
  };

  const handleSend = async (invoiceId: string) => {
    try {
      await sendInvoice(invoiceId);
      alert("Invoice sent successfully!");
    } catch (error) {
      console.error("Failed to send invoice:", error);
    }
  };

  if (isLoading) {
    return <div>Loading invoices...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Invoices</h2>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
          Create Invoice
        </button>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Invoice</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Customer</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {invoices.map((invoice) => (
              <tr key={invoice.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{invoice.number}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{invoice.customerName}</div>
                  <div className="text-sm text-gray-500">{invoice.customerEmail}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">${(invoice.total / 100).toFixed(2)}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    invoice.status === "paid" 
                      ? "bg-green-100 text-green-800"
                      : invoice.status === "open"
                      ? "bg-yellow-100 text-yellow-800"
                      : "bg-gray-100 text-gray-800"
                  }`}>
                    {invoice.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {format(new Date(invoice.issueDate), "MMM dd, yyyy")}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button
                    onClick={() => handleDownload(invoice.id)}
                    className="text-blue-600 hover:text-blue-900"
                  >
                    Download
                  </button>
                  <button
                    onClick={() => handleSend(invoice.id)}
                    className="text-green-600 hover:text-green-900"
                  >
                    Send
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

// Recent invoices widget
function RecentInvoices() {
  const { invoices } = useBilling();
  const recentInvoices = invoices.slice(0, 5);

  return (
    <div className="bg-white p-6 rounded-lg border">
      <h3 className="text-lg font-semibold mb-4">Recent Invoices</h3>
      <div className="space-y-3">
        {recentInvoices.map((invoice) => (
          <div key={invoice.id} className="flex items-center justify-between">
            <div>
              <p className="font-medium">{invoice.number}</p>
              <p className="text-sm text-gray-500">{invoice.customerName}</p>
            </div>
            <div className="text-right">
              <p className="font-medium">${(invoice.total / 100).toFixed(2)}</p>
              <p className={`text-sm ${
                invoice.status === "paid" ? "text-green-600" : "text-yellow-600"
              }`}>
                {invoice.status}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Recent payments widget
function RecentPayments({ payments }: { payments: PaymentRecord[] }) {
  return (
    <div className="bg-white p-6 rounded-lg border">
      <h3 className="text-lg font-semibold mb-4">Recent Payments</h3>
      <div className="space-y-3">
        {payments.map((payment) => (
          <div key={payment.id} className="flex items-center justify-between">
            <div>
              <p className="font-medium">${(payment.amount / 100).toFixed(2)}</p>
              <p className="text-sm text-gray-500">
                {payment.paymentMethod.type} •••• {payment.paymentMethod.card?.last4}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-500">
                {format(new Date(payment.createdAt), "MMM dd")}
              </p>
              <p className="text-sm text-green-600">Succeeded</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Usage analytics chart
export function UsageAnalyticsChart() {
  const usage = useUsageMetrics();

  if (!usage) {
    return <div>Loading usage data...</div>;
  }

  const chartData = usage.metrics.map((metric) => ({
    name: metric.name,
    value: metric.value,
    limit: metric.limit || 0,
    cost: metric.cost || 0,
  }));

  return (
    <div className="bg-white p-6 rounded-lg border">
      <h3 className="text-lg font-semibold mb-4">Usage Analytics</h3>
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Bar dataKey="value" fill="#3B82F6" />
          <Bar dataKey="limit" fill="#E5E7EB" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
