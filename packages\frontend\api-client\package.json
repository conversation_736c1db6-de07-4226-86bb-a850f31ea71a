{"name": "@nexus/api-client", "version": "0.1.0", "description": "API client for Nexus SaaS with REST and GraphQL support", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "jest", "type-check": "tsc --noEmit", "codegen": "graphql-codegen --config codegen.yml"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "@nexus/utils": "workspace:*", "@apollo/client": "^3.8.8", "axios": "^1.6.5", "axios-retry": "^4.0.0", "graphql": "^16.8.1", "graphql-ws": "^5.14.3", "zustand": "^4.4.7", "immer": "^10.0.3", "zod": "^4.0.5"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@graphql-codegen/cli": "^5.0.0", "@graphql-codegen/typescript": "^4.0.1", "@graphql-codegen/typescript-operations": "^4.0.1", "@graphql-codegen/typescript-react-apollo": "^4.0.0", "@types/jest": "^29.5.0", "@types/node": "^22.10.2", "jest": "^29.5.0", "typescript": "^5.8.0"}}