{"name": "@nexus/api-client", "version": "0.1.0", "description": "API client for Nexus SaaS with REST and GraphQL support", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "jest", "type-check": "tsc --noEmit", "codegen": "graphql-codegen --config codegen.yml"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "@nexus/utils": "workspace:*", "@apollo/client": "3.13.8", "axios": "1.10.0", "axios-retry": "4.5.0", "graphql": "16.11.0", "graphql-ws": "^5.14.3", "zustand": "5.0.6", "immer": "10.1.1", "zod": "4.0.5"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@graphql-codegen/cli": "5.0.7", "@graphql-codegen/typescript": "4.1.6", "@graphql-codegen/typescript-operations": "4.6.1", "@graphql-codegen/typescript-react-apollo": "4.3.3", "@types/jest": "30.0.0", "@types/node": "^22.10.2", "jest": "^29.5.0", "typescript": "5.8.3"}}