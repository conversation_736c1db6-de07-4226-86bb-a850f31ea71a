import sharp from "sharp";
import ffmpeg from "fluent-ffmpeg";
import ffmpegStatic from "ffmpeg-static";
import { PDFDocument } from "pdf-lib";
import mammoth from "mammoth";
import XLSX from "xlsx";
import { FileItem, ProcessingJob, ProcessingType, FileConfig } from "../types";
import { StorageProvider } from "../storage/providers";

// Set ffmpeg path
if (ffmpegStatic) {
  ffmpeg.setFfmpegPath(ffmpegStatic);
}

export class FileProcessor {
  private storageProvider: StorageProvider;
  private config: FileConfig["processing"];

  constructor(storageProvider: StorageProvider, config: FileConfig["processing"]) {
    this.storageProvider = storageProvider;
    this.config = config;
  }

  // Process file based on type
  async processFile(file: FileItem): Promise<ProcessingJob[]> {
    const jobs: ProcessingJob[] = [];

    if (!this.config.enabled) {
      return jobs;
    }

    // Generate thumbnails for images and videos
    if (this.shouldGenerateThumbnail(file.mimetype)) {
      jobs.push(await this.createThumbnailJob(file));
    }

    // Generate previews for documents
    if (this.shouldGeneratePreview(file.mimetype)) {
      jobs.push(await this.createPreviewJob(file));
    }

    // Compress images
    if (this.shouldCompress(file.mimetype)) {
      jobs.push(await this.createCompressionJob(file));
    }

    // Extract text content
    if (this.shouldExtractText(file.mimetype)) {
      jobs.push(await this.createTextExtractionJob(file));
    }

    // Analyze metadata
    jobs.push(await this.createAnalysisJob(file));

    return jobs;
  }

  // Generate thumbnail
  async generateThumbnail(file: FileItem, size: { width: number; height: number }): Promise<string> {
    const fileBuffer = await this.storageProvider.download(file.path);
    const outputPath = this.getThumbnailPath(file, size);

    if (file.mimetype.startsWith("image/")) {
      return this.generateImageThumbnail(fileBuffer, outputPath, size);
    } else if (file.mimetype.startsWith("video/")) {
      return this.generateVideoThumbnail(file.path, outputPath, size);
    } else if (file.mimetype === "application/pdf") {
      return this.generatePDFThumbnail(fileBuffer, outputPath, size);
    }

    throw new Error(`Thumbnail generation not supported for ${file.mimetype}`);
  }

  // Generate image thumbnail
  private async generateImageThumbnail(
    buffer: Buffer,
    outputPath: string,
    size: { width: number; height: number }
  ): Promise<string> {
    const thumbnailBuffer = await sharp(buffer)
      .resize(size.width, size.height, {
        fit: "cover",
        position: "center",
      })
      .jpeg({ quality: 80 })
      .toBuffer();

    await this.storageProvider.upload(
      { buffer: thumbnailBuffer } as Express.Multer.File,
      {
        path: outputPath,
        filename: `thumbnail_${size.width}x${size.height}.jpg`,
        contentType: "image/jpeg",
      }
    );

    return outputPath;
  }

  // Generate video thumbnail
  private async generateVideoThumbnail(
    inputPath: string,
    outputPath: string,
    size: { width: number; height: number }
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const tempOutputPath = `/tmp/thumbnail_${Date.now()}.jpg`;

      ffmpeg(inputPath)
        .screenshots({
          count: 1,
          folder: "/tmp",
          filename: `thumbnail_${Date.now()}.jpg`,
          size: `${size.width}x${size.height}`,
          timemarks: ["10%"],
        })
        .on("end", async () => {
          try {
            const thumbnailBuffer = await require("fs").promises.readFile(tempOutputPath);
            
            await this.storageProvider.upload(
              { buffer: thumbnailBuffer } as Express.Multer.File,
              {
                path: outputPath,
                filename: `thumbnail_${size.width}x${size.height}.jpg`,
                contentType: "image/jpeg",
              }
            );

            // Clean up temp file
            await require("fs").promises.unlink(tempOutputPath);
            
            resolve(outputPath);
          } catch (error) {
            reject(error);
          }
        })
        .on("error", reject);
    });
  }

  // Generate PDF thumbnail
  private async generatePDFThumbnail(
    buffer: Buffer,
    outputPath: string,
    size: { width: number; height: number }
  ): Promise<string> {
    // This would require pdf-poppler or similar library
    // For now, return a placeholder
    throw new Error("PDF thumbnail generation not implemented");
  }

  // Compress image
  async compressImage(file: FileItem): Promise<string> {
    const fileBuffer = await this.storageProvider.download(file.path);
    const outputPath = this.getCompressedPath(file);

    const compressedBuffer = await sharp(fileBuffer)
      .jpeg({
        quality: this.config.compression.quality,
        progressive: this.config.compression.progressive,
      })
      .toBuffer();

    await this.storageProvider.upload(
      { buffer: compressedBuffer } as Express.Multer.File,
      {
        path: outputPath,
        filename: `compressed_${file.filename}`,
        contentType: file.mimetype,
      }
    );

    return outputPath;
  }

  // Extract text from document
  async extractText(file: FileItem): Promise<string> {
    const fileBuffer = await this.storageProvider.download(file.path);

    switch (file.mimetype) {
      case "application/pdf":
        return this.extractTextFromPDF(fileBuffer);
      case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        return this.extractTextFromDocx(fileBuffer);
      case "application/vnd.ms-excel":
      case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        return this.extractTextFromExcel(fileBuffer);
      case "text/plain":
        return fileBuffer.toString("utf-8");
      default:
        throw new Error(`Text extraction not supported for ${file.mimetype}`);
    }
  }

  // Extract text from PDF
  private async extractTextFromPDF(buffer: Buffer): Promise<string> {
    // This would require pdf-parse or similar library
    // For now, return placeholder
    return "PDF text extraction not implemented";
  }

  // Extract text from DOCX
  private async extractTextFromDocx(buffer: Buffer): Promise<string> {
    const result = await mammoth.extractRawText({ buffer });
    return result.value;
  }

  // Extract text from Excel
  private async extractTextFromExcel(buffer: Buffer): Promise<string> {
    const workbook = XLSX.read(buffer, { type: "buffer" });
    let text = "";

    workbook.SheetNames.forEach(sheetName => {
      const sheet = workbook.Sheets[sheetName];
      text += XLSX.utils.sheet_to_txt(sheet) + "\n";
    });

    return text.trim();
  }

  // Analyze file metadata
  async analyzeFile(file: FileItem): Promise<Record<string, any>> {
    const fileBuffer = await this.storageProvider.download(file.path);
    const metadata: Record<string, any> = {};

    if (file.mimetype.startsWith("image/")) {
      const imageMetadata = await sharp(fileBuffer).metadata();
      metadata.width = imageMetadata.width;
      metadata.height = imageMetadata.height;
      metadata.format = imageMetadata.format;
      metadata.colorSpace = imageMetadata.space;
      metadata.channels = imageMetadata.channels;
      metadata.density = imageMetadata.density;
      metadata.exif = imageMetadata.exif;
      metadata.icc = imageMetadata.icc;
    }

    // Add file type detection
    const fileType = await import("file-type");
    const detectedType = await fileType.fileTypeFromBuffer(fileBuffer);
    if (detectedType) {
      metadata.detectedMimeType = detectedType.mime;
      metadata.detectedExtension = detectedType.ext;
    }

    return metadata;
  }

  // Helper methods
  private shouldGenerateThumbnail(mimetype: string): boolean {
    return this.config.thumbnail.enabled && (
      mimetype.startsWith("image/") ||
      mimetype.startsWith("video/") ||
      mimetype === "application/pdf"
    );
  }

  private shouldGeneratePreview(mimetype: string): boolean {
    return this.config.preview.enabled && (
      mimetype === "application/pdf" ||
      mimetype.includes("document") ||
      mimetype.includes("presentation") ||
      mimetype.includes("spreadsheet")
    );
  }

  private shouldCompress(mimetype: string): boolean {
    return this.config.compression.enabled && mimetype.startsWith("image/");
  }

  private shouldExtractText(mimetype: string): boolean {
    return this.config.ocr.enabled && (
      mimetype === "application/pdf" ||
      mimetype.includes("document") ||
      mimetype.includes("spreadsheet") ||
      mimetype === "text/plain"
    );
  }

  private getThumbnailPath(file: FileItem, size: { width: number; height: number }): string {
    const pathParts = file.path.split("/");
    const filename = pathParts.pop();
    const directory = pathParts.join("/");
    
    return `${directory}/thumbnails/${filename}_${size.width}x${size.height}.jpg`;
  }

  private getCompressedPath(file: FileItem): string {
    const pathParts = file.path.split("/");
    const filename = pathParts.pop();
    const directory = pathParts.join("/");
    
    return `${directory}/compressed/${filename}`;
  }

  // Create processing jobs
  private async createThumbnailJob(file: FileItem): Promise<ProcessingJob> {
    return {
      id: `${file.id}_thumbnail_${Date.now()}`,
      fileId: file.id,
      type: "thumbnail",
      status: "pending",
      input: {
        sourcePath: file.path,
        options: {
          sizes: this.config.thumbnail.sizes,
          formats: this.config.thumbnail.formats,
        },
      },
      progress: 0,
      createdAt: new Date(),
    };
  }

  private async createPreviewJob(file: FileItem): Promise<ProcessingJob> {
    return {
      id: `${file.id}_preview_${Date.now()}`,
      fileId: file.id,
      type: "preview",
      status: "pending",
      input: {
        sourcePath: file.path,
        options: {
          maxPages: this.config.preview.maxPages,
          quality: this.config.preview.quality,
        },
      },
      progress: 0,
      createdAt: new Date(),
    };
  }

  private async createCompressionJob(file: FileItem): Promise<ProcessingJob> {
    return {
      id: `${file.id}_compress_${Date.now()}`,
      fileId: file.id,
      type: "compress",
      status: "pending",
      input: {
        sourcePath: file.path,
        options: {
          quality: this.config.compression.quality,
          progressive: this.config.compression.progressive,
        },
      },
      progress: 0,
      createdAt: new Date(),
    };
  }

  private async createTextExtractionJob(file: FileItem): Promise<ProcessingJob> {
    return {
      id: `${file.id}_extract_${Date.now()}`,
      fileId: file.id,
      type: "extract",
      status: "pending",
      input: {
        sourcePath: file.path,
        options: {
          languages: this.config.ocr.languages,
        },
      },
      progress: 0,
      createdAt: new Date(),
    };
  }

  private async createAnalysisJob(file: FileItem): Promise<ProcessingJob> {
    return {
      id: `${file.id}_analyze_${Date.now()}`,
      fileId: file.id,
      type: "analyze",
      status: "pending",
      input: {
        sourcePath: file.path,
        options: {},
      },
      progress: 0,
      createdAt: new Date(),
    };
  }
}
