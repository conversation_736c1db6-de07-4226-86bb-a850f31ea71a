import { FastifyInstance } from "fastify";
import { z } from "zod";
import { validate, validatePagination, validateId } from "../middleware";
import { canRead, canCreate, canWrite } from "../middleware/rbac";
import { ApiResponse } from "../types";

const createSubscriptionSchema = z.object({
  planId: z.string(),
  billingCycle: z.enum(["monthly", "yearly"]),
  paymentMethodId: z.string().optional(),
});

const updateSubscriptionSchema = z.object({
  planId: z.string().optional(),
  billingCycle: z.enum(["monthly", "yearly"]).optional(),
});

export const billingRoutes = async (fastify: FastifyInstance) => {
  // Get subscription
  fastify.get("/subscription", {
    schema: {
      tags: ["Billing"],
      summary: "Get current subscription",
      description: "Get the current subscription details",
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                subscription: {
                  type: "object",
                  properties: {
                    id: { type: "string", format: "uuid" },
                    planId: { type: "string" },
                    planName: { type: "string" },
                    status: { type: "string", enum: ["active", "canceled", "past_due", "unpaid"] },
                    billingCycle: { type: "string", enum: ["monthly", "yearly"] },
                    currentPeriodStart: { type: "string", format: "date-time" },
                    currentPeriodEnd: { type: "string", format: "date-time" },
                    cancelAtPeriodEnd: { type: "boolean" },
                    amount: { type: "number" },
                    currency: { type: "string" },
                    createdAt: { type: "string", format: "date-time" },
                    updatedAt: { type: "string", format: "date-time" },
                  },
                },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [fastify.authenticate, canRead("subscription")],
    handler: async (request, reply): Promise<ApiResponse> => {
      // TODO: Implement actual subscription fetching
      const mockSubscription = {
        id: "sub_123",
        planId: "plan_pro",
        planName: "Pro Plan",
        status: "active",
        billingCycle: "monthly",
        currentPeriodStart: new Date().toISOString(),
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        cancelAtPeriodEnd: false,
        amount: 29.99,
        currency: "USD",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return {
        success: true,
        data: {
          subscription: mockSubscription,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Create subscription
  fastify.post("/subscription", {
    schema: {
      tags: ["Billing"],
      summary: "Create subscription",
      description: "Create a new subscription",
      security: [{ bearerAuth: [] }],
      body: {
        type: "object",
        properties: {
          planId: { type: "string" },
          billingCycle: { type: "string", enum: ["monthly", "yearly"] },
          paymentMethodId: { type: "string" },
        },
        required: ["planId", "billingCycle"],
      },
      response: {
        201: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                subscription: {
                  type: "object",
                  properties: {
                    id: { type: "string", format: "uuid" },
                    planId: { type: "string" },
                    planName: { type: "string" },
                    status: { type: "string" },
                    billingCycle: { type: "string" },
                    amount: { type: "number" },
                    currency: { type: "string" },
                    createdAt: { type: "string", format: "date-time" },
                  },
                },
              },
            },
          },
        },
        400: { $ref: "#/components/responses/BadRequest" },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
      },
    },
    preHandler: [fastify.authenticate, canCreate("subscription"), validate({ body: createSubscriptionSchema })],
    handler: async (request, reply): Promise<ApiResponse> => {
      const subscriptionData = request.body as z.infer<typeof createSubscriptionSchema>;
      
      // TODO: Implement actual subscription creation with payment processor
      const newSubscription = {
        id: `sub_${Date.now()}`,
        planId: subscriptionData.planId,
        planName: "Pro Plan", // TODO: Get from plan service
        status: "active",
        billingCycle: subscriptionData.billingCycle,
        amount: subscriptionData.billingCycle === "yearly" ? 299.99 : 29.99,
        currency: "USD",
        createdAt: new Date().toISOString(),
      };

      reply.status(201);
      return {
        success: true,
        data: {
          subscription: newSubscription,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Update subscription
  fastify.patch("/subscription", {
    schema: {
      tags: ["Billing"],
      summary: "Update subscription",
      description: "Update the current subscription",
      security: [{ bearerAuth: [] }],
      body: {
        type: "object",
        properties: {
          planId: { type: "string" },
          billingCycle: { type: "string", enum: ["monthly", "yearly"] },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                subscription: {
                  type: "object",
                  properties: {
                    id: { type: "string", format: "uuid" },
                    planId: { type: "string" },
                    planName: { type: "string" },
                    status: { type: "string" },
                    billingCycle: { type: "string" },
                    amount: { type: "number" },
                    currency: { type: "string" },
                    updatedAt: { type: "string", format: "date-time" },
                  },
                },
              },
            },
          },
        },
        400: { $ref: "#/components/responses/BadRequest" },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [fastify.authenticate, canWrite("subscription"), validate({ body: updateSubscriptionSchema })],
    handler: async (request, reply): Promise<ApiResponse> => {
      const updates = request.body as z.infer<typeof updateSubscriptionSchema>;
      
      // TODO: Implement actual subscription update
      const updatedSubscription = {
        id: "sub_123",
        planId: updates.planId || "plan_pro",
        planName: "Pro Plan",
        status: "active",
        billingCycle: updates.billingCycle || "monthly",
        amount: updates.billingCycle === "yearly" ? 299.99 : 29.99,
        currency: "USD",
        updatedAt: new Date().toISOString(),
      };

      return {
        success: true,
        data: {
          subscription: updatedSubscription,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Cancel subscription
  fastify.post("/subscription/cancel", {
    schema: {
      tags: ["Billing"],
      summary: "Cancel subscription",
      description: "Cancel the current subscription",
      security: [{ bearerAuth: [] }],
      body: {
        type: "object",
        properties: {
          cancelAtPeriodEnd: { type: "boolean", default: true },
          reason: { type: "string" },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                message: { type: "string" },
                canceledAt: { type: "string", format: "date-time" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [fastify.authenticate, canWrite("subscription")],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { cancelAtPeriodEnd, reason } = request.body as any;
      
      // TODO: Implement actual subscription cancellation
      
      return {
        success: true,
        data: {
          message: cancelAtPeriodEnd 
            ? "Subscription will be canceled at the end of the current period"
            : "Subscription canceled immediately",
          canceledAt: new Date().toISOString(),
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Get invoices
  fastify.get("/invoices", {
    schema: {
      tags: ["Billing"],
      summary: "List invoices",
      description: "Get a paginated list of invoices",
      security: [{ bearerAuth: [] }],
      querystring: {
        type: "object",
        properties: {
          page: { type: "integer", minimum: 1, default: 1 },
          limit: { type: "integer", minimum: 1, maximum: 100, default: 20 },
          status: { type: "string", enum: ["paid", "pending", "failed"] },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                invoices: {
                  type: "array",
                  items: {
                    type: "object",
                    properties: {
                      id: { type: "string", format: "uuid" },
                      number: { type: "string" },
                      status: { type: "string" },
                      amount: { type: "number" },
                      currency: { type: "string" },
                      dueDate: { type: "string", format: "date-time" },
                      paidAt: { type: "string", format: "date-time" },
                      createdAt: { type: "string", format: "date-time" },
                    },
                  },
                },
                pagination: { $ref: "#/components/schemas/Pagination" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
      },
    },
    preHandler: [fastify.authenticate, canRead("invoice"), validatePagination],
    handler: async (request, reply): Promise<ApiResponse> => {
      const query = request.query as any;
      
      // TODO: Implement actual invoice fetching
      const mockInvoices = [
        {
          id: "inv_123",
          number: "INV-2024-001",
          status: "paid",
          amount: 29.99,
          currency: "USD",
          dueDate: new Date().toISOString(),
          paidAt: new Date().toISOString(),
          createdAt: new Date().toISOString(),
        },
      ];

      return {
        success: true,
        data: {
          invoices: mockInvoices,
          pagination: {
            page: query.page || 1,
            limit: query.limit || 20,
            total: mockInvoices.length,
            totalPages: 1,
          },
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Get invoice by ID
  fastify.get("/invoices/:id", {
    schema: {
      tags: ["Billing"],
      summary: "Get invoice by ID",
      description: "Get a specific invoice by its ID",
      security: [{ bearerAuth: [] }],
      params: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
        },
        required: ["id"],
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                invoice: {
                  type: "object",
                  properties: {
                    id: { type: "string", format: "uuid" },
                    number: { type: "string" },
                    status: { type: "string" },
                    amount: { type: "number" },
                    currency: { type: "string" },
                    items: {
                      type: "array",
                      items: {
                        type: "object",
                        properties: {
                          description: { type: "string" },
                          amount: { type: "number" },
                          quantity: { type: "integer" },
                        },
                      },
                    },
                    dueDate: { type: "string", format: "date-time" },
                    paidAt: { type: "string", format: "date-time" },
                    createdAt: { type: "string", format: "date-time" },
                  },
                },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [fastify.authenticate, canRead("invoice"), validateId],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { id } = request.params as { id: string };
      
      // TODO: Implement actual invoice fetching
      const mockInvoice = {
        id,
        number: "INV-2024-001",
        status: "paid",
        amount: 29.99,
        currency: "USD",
        items: [
          {
            description: "Pro Plan - Monthly",
            amount: 29.99,
            quantity: 1,
          },
        ],
        dueDate: new Date().toISOString(),
        paidAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
      };

      return {
        success: true,
        data: {
          invoice: mockInvoice,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Download invoice
  fastify.get("/invoices/:id/download", {
    schema: {
      tags: ["Billing"],
      summary: "Download invoice",
      description: "Download invoice as PDF",
      security: [{ bearerAuth: [] }],
      params: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
        },
        required: ["id"],
      },
      response: {
        200: {
          description: "Invoice PDF",
          content: {
            "application/pdf": {
              schema: {
                type: "string",
                format: "binary",
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [fastify.authenticate, canRead("invoice"), validateId],
    handler: async (request, reply) => {
      const { id } = request.params as { id: string };
      
      // TODO: Implement actual PDF generation
      reply.type("application/pdf");
      reply.header("Content-Disposition", `attachment; filename="invoice_${id}.pdf"`);
      
      return "Mock PDF content";
    },
  });
};
