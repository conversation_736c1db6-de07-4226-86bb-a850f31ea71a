"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useTenantId } from "@nexus/tenant-context";
import { useIsAdmin, createAuditLogger } from "@nexus/rbac";
import { AuditLog, ResourceType } from "@nexus/rbac";
import { format, subDays, startOfDay, endOfDay } from "date-fns";

// Main audit log viewer component
export function AuditLogViewer() {
  const tenantId = useTenantId();
  const isAdmin = useIsAdmin();
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0,
  });
  const [stats, setStats] = useState<any>(null);

  const auditLogger = tenantId ? createAuditLogger(tenantId) : null;

  // Filter form
  const { register, handleSubmit, watch, reset } = useForm({
    defaultValues: {
      userId: "",
      resource: "",
      action: "",
      result: "",
      startDate: format(startOfDay(subDays(new Date(), 7)), "yyyy-MM-dd'T'HH:mm"),
      endDate: format(endOfDay(new Date()), "yyyy-MM-dd'T'HH:mm"),
    },
  });

  // Load audit logs
  const loadAuditLogs = async (filters?: any, page: number = 1) => {
    if (!auditLogger) return;

    setIsLoading(true);
    try {
      const result = await auditLogger.getAuditLogs(filters, page, pagination.limit);
      setAuditLogs(result.data);
      setPagination(result.pagination);
      setError(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to load audit logs");
    } finally {
      setIsLoading(false);
    }
  };

  // Load audit statistics
  const loadStats = async (filters?: any) => {
    if (!auditLogger) return;

    try {
      const period = filters?.startDate && filters?.endDate ? {
        start: new Date(filters.startDate),
        end: new Date(filters.endDate),
      } : undefined;

      const statsData = await auditLogger.getAuditStats(period);
      setStats(statsData);
    } catch (error) {
      console.error("Failed to load audit stats:", error);
    }
  };

  // Handle filter submission
  const onFilterSubmit = (data: any) => {
    const filters: any = {};
    
    if (data.userId) filters.userId = data.userId;
    if (data.resource) filters.resource = data.resource as ResourceType;
    if (data.action) filters.action = data.action;
    if (data.result) filters.result = data.result;
    if (data.startDate) filters.startDate = new Date(data.startDate);
    if (data.endDate) filters.endDate = new Date(data.endDate);

    loadAuditLogs(filters, 1);
    loadStats(filters);
  };

  // Export audit logs
  const handleExport = async (format: "csv" | "xlsx" = "csv") => {
    if (!auditLogger) return;

    try {
      const formData = watch();
      const filters: any = {};
      
      if (formData.userId) filters.userId = formData.userId;
      if (formData.resource) filters.resource = formData.resource as ResourceType;
      if (formData.action) filters.action = formData.action;
      if (formData.result) filters.result = formData.result;
      if (formData.startDate) filters.startDate = new Date(formData.startDate);
      if (formData.endDate) filters.endDate = new Date(formData.endDate);

      const blob = await auditLogger.exportAuditLogs(filters, format);
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `audit-logs-${format(new Date(), "yyyy-MM-dd")}.${format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to export audit logs");
    }
  };

  // Load initial data
  useEffect(() => {
    if (tenantId && isAdmin) {
      loadAuditLogs();
      loadStats();
    }
  }, [tenantId, isAdmin]);

  if (!isAdmin) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Access denied. Admin privileges required.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Audit Log Viewer</h1>
          <p className="text-gray-600">Monitor and analyze RBAC activity across your organization</p>
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={() => handleExport("csv")}
            className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
          >
            Export CSV
          </button>
          <button
            onClick={() => handleExport("xlsx")}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Export Excel
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Statistics */}
      {stats && <AuditStats stats={stats} />}

      {/* Filters */}
      <div className="bg-white p-6 rounded-lg border">
        <h2 className="text-lg font-semibold mb-4">Filters</h2>
        <form onSubmit={handleSubmit(onFilterSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">User ID</label>
              <input
                {...register("userId")}
                className="w-full px-3 py-2 border rounded-md"
                placeholder="Filter by user ID"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Resource</label>
              <select {...register("resource")} className="w-full px-3 py-2 border rounded-md">
                <option value="">All Resources</option>
                <option value="organization">Organization</option>
                <option value="workspace">Workspace</option>
                <option value="team">Team</option>
                <option value="member">Member</option>
                <option value="project">Project</option>
                <option value="document">Document</option>
                <option value="file">File</option>
                <option value="user">User</option>
                <option value="subscription">Subscription</option>
                <option value="invoice">Invoice</option>
                <option value="analytics">Analytics</option>
                <option value="integration">Integration</option>
                <option value="api">API</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Action</label>
              <select {...register("action")} className="w-full px-3 py-2 border rounded-md">
                <option value="">All Actions</option>
                <option value="permission_check">Permission Check</option>
                <option value="role_assign">Role Assign</option>
                <option value="role_revoke">Role Revoke</option>
                <option value="permission_grant">Permission Grant</option>
                <option value="permission_deny">Permission Deny</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Result</label>
              <select {...register("result")} className="w-full px-3 py-2 border rounded-md">
                <option value="">All Results</option>
                <option value="success">Success</option>
                <option value="failure">Failure</option>
                <option value="denied">Denied</option>
              </select>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Start Date</label>
              <input
                type="datetime-local"
                {...register("startDate")}
                className="w-full px-3 py-2 border rounded-md"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">End Date</label>
              <input
                type="datetime-local"
                {...register("endDate")}
                className="w-full px-3 py-2 border rounded-md"
              />
            </div>
          </div>
          
          <div className="flex space-x-3">
            <button
              type="submit"
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
            >
              Apply Filters
            </button>
            <button
              type="button"
              onClick={() => {
                reset();
                loadAuditLogs();
                loadStats();
              }}
              className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
            >
              Clear Filters
            </button>
          </div>
        </form>
      </div>

      {/* Audit Logs Table */}
      <AuditLogTable
        auditLogs={auditLogs}
        isLoading={isLoading}
        pagination={pagination}
        onPageChange={(page) => loadAuditLogs(watch(), page)}
      />
    </div>
  );
}

// Audit statistics component
function AuditStats({ stats }: { stats: any }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      <div className="bg-white p-6 rounded-lg border">
        <h3 className="text-sm font-medium text-gray-500">Total Logs</h3>
        <p className="text-2xl font-bold">{stats.totalLogs.toLocaleString()}</p>
      </div>
      
      <div className="bg-white p-6 rounded-lg border">
        <h3 className="text-sm font-medium text-gray-500">Permission Checks</h3>
        <p className="text-2xl font-bold">{stats.permissionChecks.toLocaleString()}</p>
      </div>
      
      <div className="bg-white p-6 rounded-lg border">
        <h3 className="text-sm font-medium text-gray-500">Role Changes</h3>
        <p className="text-2xl font-bold">{(stats.roleAssignments + stats.roleRevocations).toLocaleString()}</p>
      </div>
      
      <div className="bg-white p-6 rounded-lg border">
        <h3 className="text-sm font-medium text-gray-500">Denied Attempts</h3>
        <p className="text-2xl font-bold text-red-600">{stats.deniedAttempts.toLocaleString()}</p>
      </div>
    </div>
  );
}

// Audit log table component
function AuditLogTable({
  auditLogs,
  isLoading,
  pagination,
  onPageChange,
}: {
  auditLogs: AuditLog[];
  isLoading: boolean;
  pagination: any;
  onPageChange: (page: number) => void;
}) {
  if (isLoading) {
    return <div className="text-center py-8">Loading audit logs...</div>;
  }

  return (
    <div className="bg-white rounded-lg border">
      <div className="px-6 py-4 border-b">
        <h2 className="text-lg font-semibold">
          Audit Logs ({pagination.total.toLocaleString()})
        </h2>
      </div>
      
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Timestamp</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">User</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Action</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Resource</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Result</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Details</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {auditLogs.map((log) => (
              <tr key={log.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {format(new Date(log.createdAt), "MMM dd, yyyy HH:mm:ss")}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {log.userId}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {log.action.replace(/_/g, " ")}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {log.resource}
                  {log.resourceId && (
                    <div className="text-xs text-gray-500">ID: {log.resourceId}</div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 text-xs rounded ${
                    log.result === "success" ? "bg-green-100 text-green-800" :
                    log.result === "denied" ? "bg-red-100 text-red-800" :
                    "bg-yellow-100 text-yellow-800"
                  }`}>
                    {log.result}
                  </span>
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  <details className="cursor-pointer">
                    <summary className="text-blue-600 hover:text-blue-800">View Details</summary>
                    <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-w-xs">
                      {JSON.stringify(log.details, null, 2)}
                    </pre>
                  </details>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="px-6 py-4 border-t flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Showing {((pagination.page - 1) * pagination.limit) + 1} to{" "}
            {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
            {pagination.total} results
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={() => onPageChange(pagination.page - 1)}
              disabled={pagination.page === 1}
              className="px-3 py-1 border rounded disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            
            <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded">
              {pagination.page} of {pagination.totalPages}
            </span>
            
            <button
              onClick={() => onPageChange(pagination.page + 1)}
              disabled={pagination.page === pagination.totalPages}
              className="px-3 py-1 border rounded disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
