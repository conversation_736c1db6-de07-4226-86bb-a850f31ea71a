import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { restApiClient } from "../rest";
import { endpoints } from "../config";
import { WorkspaceStore, Workspace, CreateWorkspaceInput, UpdateWorkspaceInput, PaginationParams, SearchParams } from "../types";

interface WorkspaceState extends WorkspaceStore {
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  addWorkspace: (workspace: Workspace) => void;
  updateWorkspaceInList: (id: string, updates: Partial<Workspace>) => void;
  removeWorkspace: (id: string) => void;
}

export const useWorkspaceStore = create<WorkspaceState>()(
  immer((set, get) => ({
    workspaces: [],
    currentWorkspace: null,
    isLoading: false,
    error: null,

    setLoading: (loading) =>
      set((state) => {
        state.isLoading = loading;
      }),

    setError: (error) =>
      set((state) => {
        state.error = error;
      }),

    addWorkspace: (workspace) =>
      set((state) => {
        state.workspaces.push(workspace);
      }),

    updateWorkspaceInList: (id, updates) =>
      set((state) => {
        const index = state.workspaces.findIndex(w => w.id === id);
        if (index !== -1) {
          state.workspaces[index] = { ...state.workspaces[index], ...updates };
        }
        if (state.currentWorkspace?.id === id) {
          state.currentWorkspace = { ...state.currentWorkspace, ...updates };
        }
      }),

    removeWorkspace: (id) =>
      set((state) => {
        state.workspaces = state.workspaces.filter(w => w.id !== id);
        if (state.currentWorkspace?.id === id) {
          state.currentWorkspace = null;
        }
      }),

    fetchWorkspaces: async (pagination?: PaginationParams, search?: SearchParams) => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (pagination?.page) params.append("page", pagination.page.toString());
        if (pagination?.limit) params.append("limit", pagination.limit.toString());
        if (pagination?.sort) params.append("sort", pagination.sort);
        if (pagination?.order) params.append("order", pagination.order);
        if (search?.query) params.append("query", search.query);
        if (search?.filters) {
          Object.entries(search.filters).forEach(([key, value]) => {
            params.append(`filters[${key}]`, value.toString());
          });
        }

        const response = await restApiClient.get(
          `${endpoints.workspaces.list}?${params.toString()}`
        );

        set((state) => {
          state.workspaces = response.data.items || response.data;
        });
      } catch (error: any) {
        setError(error.message || "Failed to fetch workspaces");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    createWorkspace: async (input: CreateWorkspaceInput) => {
      const { setLoading, setError, addWorkspace } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.post(endpoints.workspaces.create, input);
        const workspace = response.data;

        addWorkspace(workspace);
        return workspace;
      } catch (error: any) {
        setError(error.message || "Failed to create workspace");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    updateWorkspace: async (id: string, input: UpdateWorkspaceInput) => {
      const { setLoading, setError, updateWorkspaceInList } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.put(endpoints.workspaces.update(id), input);
        const workspace = response.data;

        updateWorkspaceInList(id, workspace);
        return workspace;
      } catch (error: any) {
        setError(error.message || "Failed to update workspace");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    deleteWorkspace: async (id: string) => {
      const { setLoading, setError, removeWorkspace } = get();
      
      try {
        setLoading(true);
        setError(null);

        await restApiClient.delete(endpoints.workspaces.delete(id));
        removeWorkspace(id);
      } catch (error: any) {
        setError(error.message || "Failed to delete workspace");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    setCurrentWorkspace: (workspace) =>
      set((state) => {
        state.currentWorkspace = workspace;
        
        // Update API client context
        if (workspace) {
          restApiClient.setContext({
            tenantId: workspace.tenantId,
            workspaceId: workspace.id,
          });
        }
      }),
  }))
);

// Workspace utilities
export const workspaceUtils = {
  // Get workspace by ID
  getWorkspaceById: (id: string): Workspace | undefined => {
    const { workspaces } = useWorkspaceStore.getState();
    return workspaces.find(w => w.id === id);
  },

  // Get current workspace
  getCurrentWorkspace: (): Workspace | null => {
    return useWorkspaceStore.getState().currentWorkspace;
  },

  // Check if user is workspace owner
  isWorkspaceOwner: (workspaceId: string, userId: string): boolean => {
    const workspace = workspaceUtils.getWorkspaceById(workspaceId);
    return workspace?.ownerId === userId;
  },

  // Get workspace storage usage percentage
  getStorageUsagePercentage: (workspace: Workspace): number => {
    // Assuming 10GB limit for now - this should come from subscription/plan
    const limit = 10 * 1024 * 1024 * 1024; // 10GB in bytes
    return Math.round((workspace.storageUsed / limit) * 100);
  },

  // Format storage size
  formatStorageSize: (bytes: number): string => {
    const units = ["B", "KB", "MB", "GB", "TB"];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  },

  // Get workspace stats
  getWorkspaceStats: (workspace: Workspace) => ({
    members: workspace.memberCount,
    teams: workspace.teamCount,
    projects: workspace.projectCount,
    storage: workspaceUtils.formatStorageSize(workspace.storageUsed),
    storagePercentage: workspaceUtils.getStorageUsagePercentage(workspace),
  }),
};
