import { EventEmitter } from "events";
import { v4 as uuidv4 } from "uuid";
import { 
  Notification, 
  NotificationTemplate, 
  NotificationPreference, 
  NotificationDevice,
  NotificationSubscription,
  NotificationCampaign,
  NotificationConfig,
  NotificationType,
  NotificationChannel,
  NotificationPriority 
} from "./types";
import { NotificationQueue } from "./queue";

export class NotificationService extends EventEmitter {
  private queue: NotificationQueue;
  private config: NotificationConfig;
  private templates: Map<string, NotificationTemplate> = new Map();
  private preferences: Map<string, NotificationPreference[]> = new Map();
  private devices: Map<string, NotificationDevice[]> = new Map();
  private subscriptions: Map<string, NotificationSubscription[]> = new Map();

  constructor(config: NotificationConfig) {
    super();
    this.config = config;
    this.queue = new NotificationQueue(config);
    
    this.loadTemplates();
  }

  // Send notification
  async sendNotification(params: {
    userId: string;
    tenantId: string;
    type: NotificationType;
    title: string;
    message: string;
    data?: Record<string, any>;
    actionUrl?: string;
    priority?: NotificationPriority;
    channels?: NotificationChannel[];
    templateId?: string;
    variables?: Record<string, any>;
    scheduledAt?: Date;
  }): Promise<Notification> {
    const {
      userId,
      tenantId,
      type,
      title,
      message,
      data,
      actionUrl,
      priority = "normal",
      channels,
      templateId,
      variables = {},
      scheduledAt,
    } = params;

    // Create notification
    const notification: Notification = {
      id: uuidv4(),
      userId,
      tenantId,
      type,
      channel: channels || await this.getPreferredChannels(userId, type),
      title,
      message,
      data,
      actionUrl,
      priority,
      status: scheduledAt ? "scheduled" : "pending",
      scheduledAt,
      metadata: {
        templateId,
        source: "api",
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Get template
    const template = templateId 
      ? this.templates.get(templateId)
      : await this.getDefaultTemplate(type);

    if (!template) {
      throw new Error(`Template not found for type: ${type}`);
    }

    // Check rate limits
    await this.checkRateLimits(userId, notification.channel);

    // Get user devices for push notifications
    const devices = notification.channel.includes("push")
      ? this.devices.get(userId) || []
      : undefined;

    // Merge variables with notification data
    const allVariables = {
      ...variables,
      userId,
      tenantId,
      title,
      message,
      actionUrl,
      priority,
      type,
    };

    // Add to queue
    await this.queue.addNotification(notification, template, allVariables, devices);

    // Store notification (in real implementation, this would be in database)
    await this.storeNotification(notification);

    // Emit event
    this.emit("notification:created", notification);

    return notification;
  }

  // Send bulk notifications
  async sendBulkNotifications(params: {
    userIds: string[];
    tenantId: string;
    type: NotificationType;
    title: string;
    message: string;
    data?: Record<string, any>;
    actionUrl?: string;
    priority?: NotificationPriority;
    templateId?: string;
    variables?: Record<string, any>;
    scheduledAt?: Date;
  }): Promise<Notification[]> {
    const notifications: Notification[] = [];

    for (const userId of params.userIds) {
      try {
        const notification = await this.sendNotification({
          ...params,
          userId,
        });
        notifications.push(notification);
      } catch (error) {
        console.error(`Failed to send notification to user ${userId}:`, error);
      }
    }

    return notifications;
  }

  // Create campaign
  async createCampaign(params: {
    name: string;
    description?: string;
    tenantId?: string;
    templateId: string;
    audience: {
      type: "all" | "segment" | "users";
      userIds?: string[];
      segmentId?: string;
      filters?: Record<string, any>;
    };
    schedule: {
      type: "immediate" | "scheduled" | "recurring";
      scheduledAt?: Date;
      timezone?: string;
      recurring?: {
        frequency: "daily" | "weekly" | "monthly";
        interval: number;
        endDate?: Date;
      };
    };
  }): Promise<NotificationCampaign> {
    const campaign: NotificationCampaign = {
      id: uuidv4(),
      name: params.name,
      description: params.description,
      tenantId: params.tenantId,
      templateId: params.templateId,
      audience: params.audience,
      schedule: params.schedule,
      status: params.schedule.type === "immediate" ? "running" : "scheduled",
      stats: {
        totalRecipients: 0,
        sent: 0,
        delivered: 0,
        read: 0,
        clicked: 0,
        failed: 0,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Store campaign
    await this.storeCampaign(campaign);

    // Execute campaign if immediate
    if (params.schedule.type === "immediate") {
      await this.executeCampaign(campaign.id);
    }

    return campaign;
  }

  // Execute campaign
  async executeCampaign(campaignId: string): Promise<void> {
    // TODO: Implement campaign execution
    console.log(`Executing campaign ${campaignId}`);
  }

  // Update notification preferences
  async updatePreferences(
    userId: string,
    tenantId: string,
    preferences: Array<{
      type: NotificationType;
      channels: NotificationChannel[];
      enabled: boolean;
      frequency?: "immediate" | "hourly" | "daily" | "weekly";
      quietHours?: {
        enabled: boolean;
        start: string;
        end: string;
        timezone: string;
      };
    }>
  ): Promise<void> {
    const userPreferences: NotificationPreference[] = preferences.map(pref => ({
      id: uuidv4(),
      userId,
      tenantId,
      type: pref.type,
      channels: pref.channels,
      enabled: pref.enabled,
      frequency: pref.frequency,
      quietHours: pref.quietHours,
      createdAt: new Date(),
      updatedAt: new Date(),
    }));

    this.preferences.set(userId, userPreferences);
    
    // Store in database
    await this.storePreferences(userPreferences);

    this.emit("preferences:updated", { userId, preferences: userPreferences });
  }

  // Register device for push notifications
  async registerDevice(params: {
    userId: string;
    tenantId: string;
    type: "web" | "ios" | "android";
    token: string;
    endpoint?: string;
    keys?: {
      p256dh: string;
      auth: string;
    };
    userAgent?: string;
  }): Promise<NotificationDevice> {
    const device: NotificationDevice = {
      id: uuidv4(),
      userId: params.userId,
      tenantId: params.tenantId,
      type: params.type,
      token: params.token,
      endpoint: params.endpoint,
      keys: params.keys,
      userAgent: params.userAgent,
      isActive: true,
      lastUsed: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Store device
    const userDevices = this.devices.get(params.userId) || [];
    
    // Remove existing device with same token
    const filteredDevices = userDevices.filter(d => d.token !== params.token);
    filteredDevices.push(device);
    
    this.devices.set(params.userId, filteredDevices);

    await this.storeDevice(device);

    this.emit("device:registered", device);

    return device;
  }

  // Mark notification as read
  async markAsRead(notificationId: string, userId: string): Promise<void> {
    // TODO: Update notification in database
    console.log(`Marking notification ${notificationId} as read for user ${userId}`);
    
    this.emit("notification:read", { notificationId, userId, readAt: new Date() });
  }

  // Mark notification as clicked
  async markAsClicked(notificationId: string, userId: string): Promise<void> {
    // TODO: Update notification in database
    console.log(`Marking notification ${notificationId} as clicked for user ${userId}`);
    
    this.emit("notification:clicked", { notificationId, userId, clickedAt: new Date() });
  }

  // Get user notifications
  async getUserNotifications(
    userId: string,
    options: {
      limit?: number;
      offset?: number;
      type?: NotificationType;
      status?: string;
      unreadOnly?: boolean;
    } = {}
  ): Promise<{
    notifications: Notification[];
    total: number;
    unread: number;
  }> {
    // TODO: Implement database query
    return {
      notifications: [],
      total: 0,
      unread: 0,
    };
  }

  // Get preferred channels for user and notification type
  private async getPreferredChannels(
    userId: string,
    type: NotificationType
  ): Promise<NotificationChannel[]> {
    const userPreferences = this.preferences.get(userId) || [];
    const preference = userPreferences.find(p => p.type === type);
    
    if (preference && preference.enabled) {
      return preference.channels;
    }

    // Default channels based on type
    const defaultChannels: Record<NotificationType, NotificationChannel[]> = {
      welcome: ["email", "in_app"],
      mention: ["push", "in_app"],
      assignment: ["push", "email", "in_app"],
      comment: ["push", "in_app"],
      invitation: ["email", "in_app"],
      reminder: ["push", "in_app"],
      system: ["in_app"],
      security: ["email", "push", "in_app"],
      billing: ["email", "in_app"],
      marketing: ["email"],
      announcement: ["in_app"],
    };

    return defaultChannels[type] || ["in_app"];
  }

  // Get default template for notification type
  private async getDefaultTemplate(type: NotificationType): Promise<NotificationTemplate | null> {
    // Find template by type
    for (const template of this.templates.values()) {
      if (template.type === type && template.isActive) {
        return template;
      }
    }

    return null;
  }

  // Check rate limits
  private async checkRateLimits(
    userId: string,
    channels: NotificationChannel[]
  ): Promise<void> {
    // TODO: Implement rate limiting logic
    console.log(`Checking rate limits for user ${userId} and channels ${channels.join(", ")}`);
  }

  // Load templates
  private async loadTemplates(): Promise<void> {
    // TODO: Load templates from database or files
    console.log("Loading notification templates");
  }

  // Storage methods (these would interact with database in real implementation)
  private async storeNotification(notification: Notification): Promise<void> {
    console.log(`Storing notification ${notification.id}`);
  }

  private async storeCampaign(campaign: NotificationCampaign): Promise<void> {
    console.log(`Storing campaign ${campaign.id}`);
  }

  private async storePreferences(preferences: NotificationPreference[]): Promise<void> {
    console.log(`Storing preferences for ${preferences.length} types`);
  }

  private async storeDevice(device: NotificationDevice): Promise<void> {
    console.log(`Storing device ${device.id}`);
  }

  // Get service statistics
  async getStats(): Promise<{
    queue: any;
    notifications: {
      total: number;
      byType: Record<NotificationType, number>;
      byStatus: Record<string, number>;
    };
    devices: {
      total: number;
      byType: Record<string, number>;
    };
  }> {
    const queueStats = await this.queue.getStats();

    return {
      queue: queueStats,
      notifications: {
        total: 0,
        byType: {} as Record<NotificationType, number>,
        byStatus: {},
      },
      devices: {
        total: Array.from(this.devices.values()).flat().length,
        byType: {},
      },
    };
  }

  // Cleanup
  async cleanup(): Promise<void> {
    await this.queue.cleanup();
    this.removeAllListeners();
  }
}
