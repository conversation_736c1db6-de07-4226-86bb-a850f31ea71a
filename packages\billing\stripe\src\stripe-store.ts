import { create } from "zustand";
import { PaymentIntent, PaymentMethod, Subscription, Product, Customer } from "./stripe-types";

interface StripeStore {
  // State
  customer: Customer | null;
  paymentMethods: PaymentMethod[];
  currentSubscription: Subscription | null;
  products: Product[];
  paymentIntents: PaymentIntent[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setCustomer: (customer: Customer | null) => void;
  setPaymentMethods: (paymentMethods: PaymentMethod[]) => void;
  addPaymentMethod: (paymentMethod: PaymentMethod) => void;
  removePaymentMethod: (paymentMethodId: string) => void;
  updatePaymentMethod: (paymentMethodId: string, updates: Partial<PaymentMethod>) => void;
  setCurrentSubscription: (subscription: Subscription | null) => void;
  setProducts: (products: Product[]) => void;
  setPaymentIntents: (paymentIntents: PaymentIntent[]) => void;
  addPaymentIntent: (paymentIntent: PaymentIntent) => void;
  updatePaymentIntent: (paymentIntentId: string, updates: Partial<PaymentIntent>) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
  
  // Computed
  getDefaultPaymentMethod: () => PaymentMethod | null;
  hasActiveSubscription: () => boolean;
  getSubscriptionStatus: () => string | null;
}

export const useStripeStore = create<StripeStore>((set, get) => ({
  // Initial state
  customer: null,
  paymentMethods: [],
  currentSubscription: null,
  products: [],
  paymentIntents: [],
  isLoading: false,
  error: null,
  
  // Actions
  setCustomer: (customer) => set({ customer }),
  
  setPaymentMethods: (paymentMethods) => set({ paymentMethods }),
  
  addPaymentMethod: (paymentMethod) => set((state) => ({
    paymentMethods: [...state.paymentMethods, paymentMethod],
  })),
  
  removePaymentMethod: (paymentMethodId) => set((state) => ({
    paymentMethods: state.paymentMethods.filter((pm) => pm.id !== paymentMethodId),
  })),
  
  updatePaymentMethod: (paymentMethodId, updates) => set((state) => ({
    paymentMethods: state.paymentMethods.map((pm) =>
      pm.id === paymentMethodId ? { ...pm, ...updates } : pm
    ),
  })),
  
  setCurrentSubscription: (currentSubscription) => set({ currentSubscription }),
  
  setProducts: (products) => set({ products }),
  
  setPaymentIntents: (paymentIntents) => set({ paymentIntents }),
  
  addPaymentIntent: (paymentIntent) => set((state) => ({
    paymentIntents: [...state.paymentIntents, paymentIntent],
  })),
  
  updatePaymentIntent: (paymentIntentId, updates) => set((state) => ({
    paymentIntents: state.paymentIntents.map((pi) =>
      pi.id === paymentIntentId ? { ...pi, ...updates } : pi
    ),
  })),
  
  setLoading: (isLoading) => set({ isLoading }),
  
  setError: (error) => set({ error }),
  
  reset: () => set({
    customer: null,
    paymentMethods: [],
    currentSubscription: null,
    products: [],
    paymentIntents: [],
    isLoading: false,
    error: null,
  }),
  
  // Computed
  getDefaultPaymentMethod: () => {
    const { paymentMethods } = get();
    return paymentMethods.find((pm) => pm.isDefault) || null;
  },
  
  hasActiveSubscription: () => {
    const { currentSubscription } = get();
    return currentSubscription?.status === "active" || currentSubscription?.status === "trialing";
  },
  
  getSubscriptionStatus: () => {
    const { currentSubscription } = get();
    return currentSubscription?.status || null;
  },
}));

// Selectors
export const selectCustomer = (state: StripeStore) => state.customer;
export const selectPaymentMethods = (state: StripeStore) => state.paymentMethods;
export const selectCurrentSubscription = (state: StripeStore) => state.currentSubscription;
export const selectProducts = (state: StripeStore) => state.products;
export const selectPaymentIntents = (state: StripeStore) => state.paymentIntents;
export const selectIsLoading = (state: StripeStore) => state.isLoading;
export const selectError = (state: StripeStore) => state.error;
export const selectDefaultPaymentMethod = (state: StripeStore) => state.getDefaultPaymentMethod();
export const selectHasActiveSubscription = (state: StripeStore) => state.hasActiveSubscription();
export const selectSubscriptionStatus = (state: StripeStore) => state.getSubscriptionStatus();
