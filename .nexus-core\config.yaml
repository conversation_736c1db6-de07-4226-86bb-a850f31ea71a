# NEXUS Framework Configuration
# Single configuration file for streamlined framework management

framework:
  name: "NEXUS"
  version: "2.0.0"
  type: "micro-agent-system"
  description: "Context-engineering powered development framework"

# Technology Stack Configuration
tech_stack:
  primary:
    framework: "Next.js 15.4+"
    runtime: "React 19"
    language: "TypeScript 5.8+"
    backend: "Supabase"
    
  frontend:
    styling: "Tailwind CSS 4.1.11+"
    components: "Shadcn/ui + Radix UI"
    state_management: "Zustand 5+"
    data_fetching: "TanStack Query v5"
    validation: "Zod v4.0.5"
    
  development:
    package_manager: "npm"
    build_tool: "Next.js built-in"
    testing: "Jest + React Testing Library"
    deployment: "Vercel"

# Agent Configuration
agents:
  master_agent:
    name: "Nexus-Master"
    description: "Master agent with 125 senior developer capabilities"
    file: "agents/nexus-master.md"
    config: "agents/nexus-master.yaml"

# Context-Engineering Configuration
context_engineering:
  principles:
    - "Context windows over prompt engineering"
    - "Rich contextual information drives better results"
    - "Pattern recognition and memory integration"
    - "Feedback loops for continuous improvement"

# File Structure Configuration
structure:
  core_directory: ".nexus-core"
  agents_directory: "agents"
  templates_directory: ".nexus-core/templates-essential"
  enforcement_rules_directory: ".nexus-core/enforcement-rules"
  
  available_resources:
    context_memory: "context-memory.yaml"
    validation: "validation.yaml"
    enforcement_rules:
      - "enforcement-rules/ai-development-security.yaml"
      - "enforcement-rules/accessibility-architecture-experts.yaml"
      - "enforcement-rules/advanced-protocols-beast-mode.yaml"
      - "enforcement-rules/protocol-performance-quality.yaml"
    templates:
      - "templates-essential/component-template.yaml"
      - "templates-essential/api-template.yaml"
      - "templates-essential/page-template.yaml"
      - "templates-essential/database-template.yaml"

# Development Standards
standards:
  code_quality:
    max_file_lines: 250
    complexity_limit: 10
    typescript_strict: true
    typescript_ultra_strict: true
    no_any_types: true
    exact_optional_properties: true
    no_implicit_returns: true
    no_fallthrough_cases: true
    test_coverage: 80
    
  performance:
    bundle_size_limit: "200KB"
    core_web_vitals: true
    lighthouse_score: 98
    
  security:
    input_validation: "mandatory"
    output_sanitization: "mandatory"
    authentication: "required"
    authorization: "granular"
    
  accessibility:
    wcag_compliance: "AA"
    keyboard_navigation: "required"
    screen_reader_support: "mandatory"
    color_contrast: "4.5:1"

# Quality Gates
quality_gates:
  pre_commit:
    - "TypeScript compilation"
    - "ESLint validation"
    - "Prettier formatting"
    - "Unit tests"
    
  pre_deployment:
    - "Integration tests"
    - "Security scan"
    - "Performance audit"
    - "Accessibility check"

# Optimization Settings
optimization:
  file_consolidation: true
  pattern_merging: true
  template_essentials_only: true
  redundancy_elimination: true
  
  targets:
    file_reduction: "70%"
    setup_time: "<5 minutes"
    learning_curve: "single_agent"
    maintenance_overhead: "minimal"

# Monitoring and Analytics
monitoring:
  performance_tracking: true
  error_monitoring: true
  user_analytics: true
  security_monitoring: true
  
  metrics:
    - "Code generation accuracy"
    - "Bug-free deployment rate"
    - "Development speed"
    - "Quality compliance"

# Integration Settings
integrations:
  ide_support:
    - "VS Code with GitHub Copilot"
    - "Cursor IDE"
    - "Claude Code"
    
  deployment_platforms:
    - "Vercel"
    - "Netlify"
    - "AWS"
    
  monitoring_tools:
    - "Vercel Analytics"
    - "Sentry"
    - "LogRocket"

# Success Metrics
success_metrics:
  quantitative:
    file_count_reduction: "70%"
    setup_time_reduction: "67%"
    bug_rate_reduction: "90%"
    development_speed_increase: "300%"
    
  qualitative:
    developer_experience: "excellent"
    code_quality: "production_ready"
    maintainability: "high"
    scalability: "enterprise_grade"
