import { useCallback, useEffect, useState } from "react";
import { useAuth } from "@nexus/auth-client";
import { useTenantId } from "@nexus/tenant-context";
import { useRBACStore, createPermissionCacheKey } from "./rbac-store";
import { createRoleManager } from "./role-manager";
import { accessControl } from "./access-control";
import { ResourceType, ActionType, Role, UserRole } from "./rbac-types";

// Main RBAC hook
export function useRBAC() {
  const { user } = useAuth();
  const tenantId = useTenantId();
  const {
    roles,
    userRoles,
    permissions,
    isLoading,
    error,
    setRoles,
    setUserRoles,
    setPermissions,
    setLoading,
    setError,
    getUserRolesByUserId,
    getRoleById,
    getUserPermissions,
    hasRole,
  } = useRBACStore();

  const roleManager = tenantId ? createRoleManager(tenantId) : null;

  // Load RBAC data
  const loadRBACData = useCallback(async () => {
    if (!roleManager || !user?.id) return;
    
    setLoading(true);
    try {
      const [rolesData, userRolesData] = await Promise.all([
        roleManager.getRoles(),
        roleManager.getUserRoles(user.id),
      ]);
      
      setRoles(rolesData);
      setUserRoles(userRolesData);
      
      // Extract permissions from roles
      const allPermissions = rolesData.flatMap(role => role.permissions);
      setPermissions(allPermissions);
      
      setError(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to load RBAC data");
    } finally {
      setLoading(false);
    }
  }, [roleManager, user?.id, setRoles, setUserRoles, setPermissions, setLoading, setError]);

  // Create role
  const createRole = useCallback(async (roleData: Omit<Role, "id" | "createdAt" | "updatedAt">) => {
    if (!roleManager) throw new Error("No role manager available");
    
    setLoading(true);
    try {
      const newRole = await roleManager.createRole(roleData);
      setRoles([...roles, newRole]);
      setError(null);
      return newRole;
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to create role";
      setError(message);
      throw new Error(message);
    } finally {
      setLoading(false);
    }
  }, [roleManager, roles, setRoles, setLoading, setError]);

  // Assign role to user
  const assignRole = useCallback(async (userId: string, roleId: string, context?: {
    workspaceId?: string;
    teamId?: string;
    resourceId?: string;
    resourceType?: string;
  }) => {
    if (!roleManager) throw new Error("No role manager available");
    
    setLoading(true);
    try {
      const userRole = await roleManager.assignRole({
        userId,
        roleId,
        ...context,
      });
      setUserRoles([...userRoles, userRole]);
      setError(null);
      return userRole;
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to assign role";
      setError(message);
      throw new Error(message);
    } finally {
      setLoading(false);
    }
  }, [roleManager, userRoles, setUserRoles, setLoading, setError]);

  // Revoke role from user
  const revokeRole = useCallback(async (userRoleId: string) => {
    if (!roleManager) throw new Error("No role manager available");
    
    setLoading(true);
    try {
      await roleManager.revokeRole(userRoleId);
      setUserRoles(userRoles.filter(ur => ur.id !== userRoleId));
      setError(null);
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to revoke role";
      setError(message);
      throw new Error(message);
    } finally {
      setLoading(false);
    }
  }, [roleManager, userRoles, setUserRoles, setLoading, setError]);

  // Auto-load data when user/tenant changes
  useEffect(() => {
    if (user?.id && tenantId) {
      loadRBACData();
    }
  }, [user?.id, tenantId, loadRBACData]);

  return {
    roles,
    userRoles,
    permissions,
    isLoading,
    error,
    loadRBACData,
    createRole,
    assignRole,
    revokeRole,
    getUserRoles: (userId: string) => getUserRolesByUserId(userId),
    getRole: (roleId: string) => getRoleById(roleId),
    getUserPermissions: (userId: string) => getUserPermissions(userId),
    hasRole: (userId: string, roleSlug: string) => hasRole(userId, roleSlug),
  };
}

// Permission checking hook
export function usePermission(
  resource: ResourceType,
  action: ActionType,
  resourceId?: string
) {
  const { user } = useAuth();
  const tenantId = useTenantId();
  const { getCachedPermissionResult, cachePermissionResult } = useRBACStore();
  
  const [hasPermission, setHasPermission] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const checkPermission = useCallback(async () => {
    if (!user?.id || !tenantId) {
      setHasPermission(false);
      setIsLoading(false);
      return;
    }

    const cacheKey = createPermissionCacheKey(user.id, resource, action, resourceId);
    const cached = getCachedPermissionResult(cacheKey);
    
    if (cached) {
      setHasPermission(cached.granted);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const result = await accessControl.check(user.id, action, resource, {
        tenantId,
        resourceId,
      });
      
      cachePermissionResult(cacheKey, result);
      setHasPermission(result.granted);
      setError(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Permission check failed");
      setHasPermission(false);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, tenantId, resource, action, resourceId, getCachedPermissionResult, cachePermissionResult]);

  useEffect(() => {
    checkPermission();
  }, [checkPermission]);

  return { hasPermission, isLoading, error, recheck: checkPermission };
}

// Multiple permissions hook
export function usePermissions(
  permissions: Array<{
    resource: ResourceType;
    action: ActionType;
    resourceId?: string;
  }>
) {
  const { user } = useAuth();
  const tenantId = useTenantId();
  
  const [results, setResults] = useState<boolean[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const checkPermissions = useCallback(async () => {
    if (!user?.id || !tenantId) {
      setResults(new Array(permissions.length).fill(false));
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const permissionChecks = permissions.map(perm => ({
        action: perm.action,
        resource: perm.resource,
        resourceId: perm.resourceId,
      }));
      
      const results = await accessControl.canMultiple(user.id, permissionChecks, {
        tenantId,
      });
      
      setResults(results);
      setError(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Permission check failed");
      setResults(new Array(permissions.length).fill(false));
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, tenantId, permissions]);

  useEffect(() => {
    checkPermissions();
  }, [checkPermissions]);

  return { results, isLoading, error, recheck: checkPermissions };
}

// Role checking hook
export function useRole(roleSlug: string) {
  const { user } = useAuth();
  const { hasRole } = useRBACStore();
  
  return user?.id ? hasRole(user.id, roleSlug) : false;
}

// User roles hook
export function useUserRoles(userId?: string) {
  const { user } = useAuth();
  const { getUserRolesByUserId } = useRBACStore();
  
  const targetUserId = userId || user?.id;
  return targetUserId ? getUserRolesByUserId(targetUserId) : [];
}

// Convenience permission hooks
export function useCanRead(resource: ResourceType, resourceId?: string) {
  return usePermission(resource, "read", resourceId);
}

export function useCanWrite(resource: ResourceType, resourceId?: string) {
  return usePermission(resource, "update", resourceId);
}

export function useCanCreate(resource: ResourceType) {
  return usePermission(resource, "create");
}

export function useCanDelete(resource: ResourceType, resourceId?: string) {
  return usePermission(resource, "delete", resourceId);
}

export function useCanManage(resource: ResourceType, resourceId?: string) {
  return usePermission(resource, "manage", resourceId);
}

// Admin role checks
export function useIsAdmin() {
  return useRole("admin");
}

export function useIsOwner() {
  return useRole("owner");
}

export function useIsSystemAdmin() {
  return useRole("system_admin");
}
