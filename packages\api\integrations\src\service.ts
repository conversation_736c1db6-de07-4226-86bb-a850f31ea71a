import { EventEmitter } from "events";
import { v4 as uuidv4 } from "uuid";
import crypto from "crypto";
import { 
  Integration, 
  IntegrationConfig, 
  IntegrationCredentials, 
  IntegrationType, 
  IntegrationProvider, 
  SyncJob, 
  SyncResult, 
  Webhook, 
  WebhookEvent,
  IntegrationsConfig 
} from "./types";
import { OAuthManager } from "./oauth/providers";
import { GoogleAPIClient } from "./clients/google";
import { SlackAPIClient } from "./clients/slack";

export class IntegrationsService extends EventEmitter {
  private config: IntegrationsConfig;
  private oauthManager: OAuthManager;
  private integrations: Map<string, Integration> = new Map();
  private syncJobs: Map<string, SyncJob> = new Map();
  private webhooks: Map<string, Webhook> = new Map();

  constructor(config: IntegrationsConfig) {
    super();
    this.config = config;
    this.oauthManager = new OAuthManager(config.oauth);
  }

  // Create integration
  async createIntegration(params: {
    name: string;
    type: IntegrationType;
    provider: IntegrationProvider;
    tenantId: string;
    workspaceId?: string;
    userId: string;
    config: IntegrationConfig;
    credentials: IntegrationCredentials;
  }): Promise<Integration> {
    const integration: Integration = {
      id: uuidv4(),
      name: params.name,
      type: params.type,
      provider: params.provider,
      tenantId: params.tenantId,
      workspaceId: params.workspaceId,
      userId: params.userId,
      config: params.config,
      credentials: await this.encryptCredentials(params.credentials),
      status: "connected",
      syncCount: 0,
      errorCount: 0,
      metadata: {},
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Store integration
    this.integrations.set(integration.id, integration);
    await this.storeIntegration(integration);

    // Test connection
    try {
      await this.testConnection(integration.id);
    } catch (error) {
      integration.status = "error";
      integration.lastError = (error as Error).message;
      integration.lastErrorAt = new Date();
      await this.updateIntegration(integration);
    }

    // Emit event
    this.emit("integration:created", integration);

    return integration;
  }

  // Get integration by ID
  async getIntegration(integrationId: string): Promise<Integration | null> {
    const integration = this.integrations.get(integrationId);
    if (integration) {
      // Decrypt credentials before returning
      integration.credentials = await this.decryptCredentials(integration.credentials);
    }
    return integration || null;
  }

  // Update integration
  async updateIntegration(integration: Integration): Promise<Integration> {
    integration.updatedAt = new Date();
    
    // Encrypt credentials if they've been modified
    if (integration.credentials && !integration.credentials.encrypted) {
      integration.credentials = await this.encryptCredentials(integration.credentials);
    }

    this.integrations.set(integration.id, integration);
    await this.storeIntegration(integration);

    this.emit("integration:updated", integration);
    return integration;
  }

  // Delete integration
  async deleteIntegration(integrationId: string): Promise<void> {
    const integration = this.integrations.get(integrationId);
    if (!integration) {
      throw new Error("Integration not found");
    }

    // Revoke OAuth token if applicable
    if (integration.type === "oauth" && integration.credentials.accessToken) {
      try {
        await this.oauthManager.revokeToken(
          integration.provider,
          integration.credentials.accessToken
        );
      } catch (error) {
        console.error("Failed to revoke OAuth token:", error);
      }
    }

    // Delete webhooks
    const integrationWebhooks = Array.from(this.webhooks.values())
      .filter(webhook => webhook.integrationId === integrationId);
    
    for (const webhook of integrationWebhooks) {
      await this.deleteWebhook(webhook.id);
    }

    // Remove integration
    this.integrations.delete(integrationId);
    await this.removeIntegration(integrationId);

    this.emit("integration:deleted", { integrationId, integration });
  }

  // Test connection
  async testConnection(integrationId: string): Promise<boolean> {
    const integration = await this.getIntegration(integrationId);
    if (!integration) {
      throw new Error("Integration not found");
    }

    try {
      const client = this.createAPIClient(integration);
      
      // Provider-specific connection tests
      switch (integration.provider) {
        case "google":
          const googleClient = client as GoogleAPIClient;
          await googleClient.get("https://www.googleapis.com/oauth2/v1/userinfo");
          break;
        
        case "slack":
          const slackClient = client as SlackAPIClient;
          await slackClient.testAuth();
          break;
        
        default:
          // Generic test - try to make a simple API call
          await client.get("/");
      }

      // Update integration status
      integration.status = "connected";
      integration.lastError = undefined;
      integration.lastErrorAt = undefined;
      await this.updateIntegration(integration);

      return true;
    } catch (error) {
      // Update integration status
      integration.status = "error";
      integration.lastError = (error as Error).message;
      integration.lastErrorAt = new Date();
      integration.errorCount++;
      await this.updateIntegration(integration);

      this.emit("integration:error", { integration, error });
      return false;
    }
  }

  // Start sync job
  async startSync(params: {
    integrationId: string;
    type?: "full" | "incremental" | "manual";
    direction?: "import" | "export" | "bidirectional";
  }): Promise<SyncJob> {
    const integration = await this.getIntegration(params.integrationId);
    if (!integration) {
      throw new Error("Integration not found");
    }

    if (!integration.isActive) {
      throw new Error("Integration is not active");
    }

    const syncJob: SyncJob = {
      id: uuidv4(),
      integrationId: params.integrationId,
      type: params.type || "manual",
      status: "pending",
      direction: params.direction || integration.config.syncDirection || "import",
      progress: 0,
      processedRecords: 0,
      successfulRecords: 0,
      failedRecords: 0,
      errors: [],
      metadata: {},
      createdAt: new Date(),
    };

    this.syncJobs.set(syncJob.id, syncJob);
    await this.storeSyncJob(syncJob);

    // Start sync process
    this.processSyncJob(syncJob);

    this.emit("sync:started", syncJob);
    return syncJob;
  }

  // Process sync job
  private async processSyncJob(syncJob: SyncJob): Promise<void> {
    try {
      syncJob.status = "running";
      syncJob.startedAt = new Date();
      await this.updateSyncJob(syncJob);

      const integration = await this.getIntegration(syncJob.integrationId);
      if (!integration) {
        throw new Error("Integration not found");
      }

      const client = this.createAPIClient(integration);
      const result = await this.performSync(integration, client, syncJob);

      syncJob.status = "completed";
      syncJob.completedAt = new Date();
      syncJob.progress = 100;
      syncJob.successfulRecords = result.recordsSuccessful;
      syncJob.failedRecords = result.recordsFailed;
      syncJob.errors = result.errors;

      // Update integration sync count
      integration.syncCount++;
      integration.lastSyncAt = new Date();
      await this.updateIntegration(integration);

      this.emit("sync:completed", syncJob);
    } catch (error) {
      syncJob.status = "failed";
      syncJob.completedAt = new Date();
      syncJob.errors.push({
        record: {},
        error: (error as Error).message,
      });

      // Update integration error count
      const integration = await this.getIntegration(syncJob.integrationId);
      if (integration) {
        integration.errorCount++;
        integration.lastError = (error as Error).message;
        integration.lastErrorAt = new Date();
        await this.updateIntegration(integration);
      }

      this.emit("sync:failed", { syncJob, error });
    } finally {
      await this.updateSyncJob(syncJob);
    }
  }

  // Perform sync based on provider
  private async performSync(
    integration: Integration,
    client: any,
    syncJob: SyncJob
  ): Promise<SyncResult> {
    switch (integration.provider) {
      case "google":
        return this.performGoogleSync(integration, client, syncJob);
      case "slack":
        return this.performSlackSync(integration, client, syncJob);
      default:
        throw new Error(`Sync not implemented for provider: ${integration.provider}`);
    }
  }

  // Google sync implementation
  private async performGoogleSync(
    integration: Integration,
    client: GoogleAPIClient,
    syncJob: SyncJob
  ): Promise<SyncResult> {
    const result: SyncResult = {
      success: true,
      recordsProcessed: 0,
      recordsSuccessful: 0,
      recordsFailed: 0,
      errors: [],
      metadata: {},
    };

    // Example: Sync Google Drive files
    if (integration.config.settings?.syncDriveFiles) {
      try {
        const files = await client.listDriveFiles();
        
        for (const file of files.files || []) {
          try {
            // Process file (store in database, etc.)
            await this.processGoogleDriveFile(file, integration);
            result.recordsSuccessful++;
          } catch (error) {
            result.recordsFailed++;
            result.errors.push({
              record: file,
              error: (error as Error).message,
            });
          }
          
          result.recordsProcessed++;
          syncJob.progress = Math.round((result.recordsProcessed / (files.files?.length || 1)) * 100);
          await this.updateSyncJob(syncJob);
        }
      } catch (error) {
        result.success = false;
        result.errors.push({
          record: {},
          error: (error as Error).message,
        });
      }
    }

    return result;
  }

  // Slack sync implementation
  private async performSlackSync(
    integration: Integration,
    client: SlackAPIClient,
    syncJob: SyncJob
  ): Promise<SyncResult> {
    const result: SyncResult = {
      success: true,
      recordsProcessed: 0,
      recordsSuccessful: 0,
      recordsFailed: 0,
      errors: [],
      metadata: {},
    };

    // Example: Sync Slack channels
    if (integration.config.settings?.syncChannels) {
      try {
        const channels = await client.listChannels();
        
        for (const channel of channels.channels || []) {
          try {
            // Process channel (store in database, etc.)
            await this.processSlackChannel(channel, integration);
            result.recordsSuccessful++;
          } catch (error) {
            result.recordsFailed++;
            result.errors.push({
              record: channel,
              error: (error as Error).message,
            });
          }
          
          result.recordsProcessed++;
          syncJob.progress = Math.round((result.recordsProcessed / (channels.channels?.length || 1)) * 100);
          await this.updateSyncJob(syncJob);
        }
      } catch (error) {
        result.success = false;
        result.errors.push({
          record: {},
          error: (error as Error).message,
        });
      }
    }

    return result;
  }

  // Create webhook
  async createWebhook(params: {
    integrationId: string;
    url: string;
    events: string[];
    secret?: string;
    headers?: Record<string, string>;
  }): Promise<Webhook> {
    const webhook: Webhook = {
      id: uuidv4(),
      integrationId: params.integrationId,
      url: params.url,
      events: params.events,
      secret: params.secret,
      headers: params.headers,
      isActive: true,
      successCount: 0,
      failureCount: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.webhooks.set(webhook.id, webhook);
    await this.storeWebhook(webhook);

    this.emit("webhook:created", webhook);
    return webhook;
  }

  // Delete webhook
  async deleteWebhook(webhookId: string): Promise<void> {
    const webhook = this.webhooks.get(webhookId);
    if (!webhook) {
      throw new Error("Webhook not found");
    }

    this.webhooks.delete(webhookId);
    await this.removeWebhook(webhookId);

    this.emit("webhook:deleted", { webhookId, webhook });
  }

  // Create API client
  private createAPIClient(integration: Integration): any {
    switch (integration.provider) {
      case "google":
        const googleClient = new GoogleAPIClient();
        googleClient.setAuth(integration.credentials);
        return googleClient;
      
      case "slack":
        const slackClient = new SlackAPIClient();
        slackClient.setAuth(integration.credentials);
        return slackClient;
      
      default:
        throw new Error(`API client not implemented for provider: ${integration.provider}`);
    }
  }

  // Encryption/Decryption
  private async encryptCredentials(credentials: IntegrationCredentials): Promise<IntegrationCredentials> {
    const encrypted = { ...credentials, encrypted: true };
    
    if (credentials.accessToken) {
      encrypted.accessToken = this.encrypt(credentials.accessToken);
    }
    
    if (credentials.refreshToken) {
      encrypted.refreshToken = this.encrypt(credentials.refreshToken);
    }
    
    if (credentials.apiKey) {
      encrypted.apiKey = this.encrypt(credentials.apiKey);
    }
    
    if (credentials.secret) {
      encrypted.secret = this.encrypt(credentials.secret);
    }

    return encrypted;
  }

  private async decryptCredentials(credentials: IntegrationCredentials): Promise<IntegrationCredentials> {
    if (!credentials.encrypted) {
      return credentials;
    }

    const decrypted = { ...credentials, encrypted: false };
    
    if (credentials.accessToken) {
      decrypted.accessToken = this.decrypt(credentials.accessToken);
    }
    
    if (credentials.refreshToken) {
      decrypted.refreshToken = this.decrypt(credentials.refreshToken);
    }
    
    if (credentials.apiKey) {
      decrypted.apiKey = this.decrypt(credentials.apiKey);
    }
    
    if (credentials.secret) {
      decrypted.secret = this.decrypt(credentials.secret);
    }

    return decrypted;
  }

  private encrypt(text: string): string {
    const cipher = crypto.createCipher(this.config.encryption.algorithm, this.config.encryption.key);
    let encrypted = cipher.update(text, "utf8", "hex");
    encrypted += cipher.final("hex");
    return encrypted;
  }

  private decrypt(text: string): string {
    const decipher = crypto.createDecipher(this.config.encryption.algorithm, this.config.encryption.key);
    let decrypted = decipher.update(text, "hex", "utf8");
    decrypted += decipher.final("utf8");
    return decrypted;
  }

  // Storage methods (these would interact with database in real implementation)
  private async storeIntegration(integration: Integration): Promise<void> {
    console.log(`Storing integration ${integration.id}`);
  }

  private async removeIntegration(integrationId: string): Promise<void> {
    console.log(`Removing integration ${integrationId}`);
  }

  private async storeSyncJob(syncJob: SyncJob): Promise<void> {
    console.log(`Storing sync job ${syncJob.id}`);
  }

  private async updateSyncJob(syncJob: SyncJob): Promise<void> {
    console.log(`Updating sync job ${syncJob.id}`);
  }

  private async storeWebhook(webhook: Webhook): Promise<void> {
    console.log(`Storing webhook ${webhook.id}`);
  }

  private async removeWebhook(webhookId: string): Promise<void> {
    console.log(`Removing webhook ${webhookId}`);
  }

  // Processing methods
  private async processGoogleDriveFile(file: any, integration: Integration): Promise<void> {
    console.log(`Processing Google Drive file: ${file.name}`);
  }

  private async processSlackChannel(channel: any, integration: Integration): Promise<void> {
    console.log(`Processing Slack channel: ${channel.name}`);
  }
}
