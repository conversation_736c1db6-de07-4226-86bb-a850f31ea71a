import { Permission, ResourceType, ActionType, PermissionScope, PermissionMatrix } from "./rbac-types";

export class PermissionSystem {
  private static instance: PermissionSystem;
  private permissionMatrix: PermissionMatrix = {};

  private constructor() {
    this.initializePermissionMatrix();
  }

  static getInstance(): PermissionSystem {
    if (!PermissionSystem.instance) {
      PermissionSystem.instance = new PermissionSystem();
    }
    return PermissionSystem.instance;
  }

  private initializePermissionMatrix(): void {
    // Organization permissions
    this.permissionMatrix.organization = {
      create: { roles: ["system_admin"], scopes: ["system"] },
      read: { roles: ["owner", "admin", "member"], scopes: ["organization", "workspace", "team"] },
      update: { roles: ["owner", "admin"], scopes: ["organization", "workspace"] },
      delete: { roles: ["owner"], scopes: ["organization"] },
      manage: { roles: ["owner", "admin"], scopes: ["organization", "workspace"] },
    };

    // Workspace permissions
    this.permissionMatrix.workspace = {
      create: { roles: ["owner", "admin"], scopes: ["organization", "workspace"] },
      read: { roles: ["owner", "admin", "member", "viewer"], scopes: ["workspace", "team", "own"] },
      update: { roles: ["owner", "admin", "editor"], scopes: ["workspace", "team", "own"] },
      delete: { roles: ["owner", "admin"], scopes: ["workspace", "own"] },
      manage: { roles: ["owner", "admin"], scopes: ["workspace"] },
      share: { roles: ["owner", "admin", "editor"], scopes: ["workspace", "team"] },
    };

    // Team permissions
    this.permissionMatrix.team = {
      create: { roles: ["owner", "admin", "team_lead"], scopes: ["workspace", "team"] },
      read: { roles: ["owner", "admin", "member", "viewer"], scopes: ["team", "own"] },
      update: { roles: ["owner", "admin", "team_lead"], scopes: ["team", "own"] },
      delete: { roles: ["owner", "admin", "team_lead"], scopes: ["team", "own"] },
      join: { roles: ["member"], scopes: ["team"] },
      leave: { roles: ["member"], scopes: ["own"] },
      manage: { roles: ["owner", "admin", "team_lead"], scopes: ["team"] },
    };

    // Member permissions
    this.permissionMatrix.member = {
      create: { roles: ["owner", "admin", "team_lead"], scopes: ["workspace", "team"] },
      read: { roles: ["owner", "admin", "member", "viewer"], scopes: ["workspace", "team", "own"] },
      update: { roles: ["owner", "admin", "team_lead"], scopes: ["workspace", "team", "own"] },
      delete: { roles: ["owner", "admin"], scopes: ["workspace", "team"] },
      invite: { roles: ["owner", "admin", "team_lead"], scopes: ["workspace", "team"] },
      remove: { roles: ["owner", "admin", "team_lead"], scopes: ["workspace", "team"] },
      manage: { roles: ["owner", "admin"], scopes: ["workspace"] },
    };

    // Project permissions
    this.permissionMatrix.project = {
      create: { roles: ["owner", "admin", "editor", "team_lead"], scopes: ["workspace", "team"] },
      read: { roles: ["owner", "admin", "editor", "member", "viewer"], scopes: ["workspace", "team", "own"] },
      update: { roles: ["owner", "admin", "editor"], scopes: ["workspace", "team", "own"] },
      delete: { roles: ["owner", "admin"], scopes: ["workspace", "team", "own"] },
      share: { roles: ["owner", "admin", "editor"], scopes: ["workspace", "team", "own"] },
      collaborate: { roles: ["owner", "admin", "editor", "member"], scopes: ["workspace", "team"] },
      manage: { roles: ["owner", "admin"], scopes: ["workspace"] },
    };

    // Document permissions
    this.permissionMatrix.document = {
      create: { roles: ["owner", "admin", "editor", "member"], scopes: ["workspace", "team", "own"] },
      read: { roles: ["owner", "admin", "editor", "member", "viewer"], scopes: ["workspace", "team", "own"] },
      update: { roles: ["owner", "admin", "editor"], scopes: ["workspace", "team", "own"] },
      delete: { roles: ["owner", "admin", "editor"], scopes: ["workspace", "team", "own"] },
      share: { roles: ["owner", "admin", "editor", "member"], scopes: ["workspace", "team", "own"] },
      review: { roles: ["owner", "admin", "editor", "reviewer"], scopes: ["workspace", "team"] },
      publish: { roles: ["owner", "admin", "editor"], scopes: ["workspace", "team", "own"] },
    };

    // File permissions
    this.permissionMatrix.file = {
      create: { roles: ["owner", "admin", "editor", "member"], scopes: ["workspace", "team", "own"] },
      read: { roles: ["owner", "admin", "editor", "member", "viewer"], scopes: ["workspace", "team", "own"] },
      update: { roles: ["owner", "admin", "editor"], scopes: ["workspace", "team", "own"] },
      delete: { roles: ["owner", "admin", "editor"], scopes: ["workspace", "team", "own"] },
      share: { roles: ["owner", "admin", "editor", "member"], scopes: ["workspace", "team", "own"] },
      download: { roles: ["owner", "admin", "editor", "member", "viewer"], scopes: ["workspace", "team", "own"] },
      upload: { roles: ["owner", "admin", "editor", "member"], scopes: ["workspace", "team"] },
    };

    // User permissions
    this.permissionMatrix.user = {
      create: { roles: ["system_admin"], scopes: ["system"] },
      read: { roles: ["owner", "admin", "member"], scopes: ["organization", "workspace", "team", "own"] },
      update: { roles: ["owner", "admin"], scopes: ["organization", "workspace", "own"] },
      delete: { roles: ["system_admin", "owner"], scopes: ["system", "organization"] },
      ban: { roles: ["system_admin", "owner"], scopes: ["system", "organization"] },
      impersonate: { roles: ["system_admin"], scopes: ["system"] },
      manage: { roles: ["system_admin", "owner", "admin"], scopes: ["system", "organization", "workspace"] },
    };

    // Billing permissions
    this.permissionMatrix.subscription = {
      read: { roles: ["owner", "admin", "billing_admin"], scopes: ["organization", "workspace"] },
      create: { roles: ["owner", "billing_admin"], scopes: ["organization"] },
      update: { roles: ["owner", "billing_admin"], scopes: ["organization"] },
      cancel: { roles: ["owner"], scopes: ["organization"] },
      manage: { roles: ["owner", "billing_admin"], scopes: ["organization"] },
    };

    this.permissionMatrix.invoice = {
      read: { roles: ["owner", "admin", "billing_admin"], scopes: ["organization", "workspace"] },
      create: { roles: ["owner", "billing_admin"], scopes: ["organization"] },
      update: { roles: ["owner", "billing_admin"], scopes: ["organization"] },
      delete: { roles: ["owner"], scopes: ["organization"] },
      manage: { roles: ["owner", "billing_admin"], scopes: ["organization"] },
    };

    // Analytics permissions
    this.permissionMatrix.analytics = {
      read: { roles: ["owner", "admin", "analyst"], scopes: ["organization", "workspace", "team"] },
      create: { roles: ["owner", "admin", "analyst"], scopes: ["organization", "workspace"] },
      export: { roles: ["owner", "admin", "analyst"], scopes: ["organization", "workspace"] },
      manage: { roles: ["owner", "admin"], scopes: ["organization", "workspace"] },
    };

    // Integration permissions
    this.permissionMatrix.integration = {
      read: { roles: ["owner", "admin", "developer"], scopes: ["organization", "workspace"] },
      create: { roles: ["owner", "admin", "developer"], scopes: ["organization", "workspace"] },
      update: { roles: ["owner", "admin", "developer"], scopes: ["organization", "workspace"] },
      delete: { roles: ["owner", "admin"], scopes: ["organization", "workspace"] },
      manage: { roles: ["owner", "admin"], scopes: ["organization", "workspace"] },
    };

    // API permissions
    this.permissionMatrix.api = {
      read: { roles: ["owner", "admin", "developer"], scopes: ["organization", "workspace"] },
      create: { roles: ["owner", "admin", "developer"], scopes: ["organization", "workspace"] },
      update: { roles: ["owner", "admin", "developer"], scopes: ["organization", "workspace"] },
      delete: { roles: ["owner", "admin"], scopes: ["organization", "workspace"] },
      manage: { roles: ["owner", "admin"], scopes: ["organization", "workspace"] },
    };
  }

  // Get allowed roles for a resource/action combination
  getAllowedRoles(resource: ResourceType, action: ActionType): string[] {
    return this.permissionMatrix[resource]?.[action]?.roles || [];
  }

  // Get allowed scopes for a resource/action combination
  getAllowedScopes(resource: ResourceType, action: ActionType): PermissionScope[] {
    return this.permissionMatrix[resource]?.[action]?.scopes || [];
  }

  // Check if a role has permission for a resource/action
  hasPermission(role: string, resource: ResourceType, action: ActionType): boolean {
    const allowedRoles = this.getAllowedRoles(resource, action);
    return allowedRoles.includes(role);
  }

  // Get all permissions for a role
  getRolePermissions(role: string): Array<{ resource: ResourceType; actions: ActionType[] }> {
    const permissions: Array<{ resource: ResourceType; actions: ActionType[] }> = [];
    
    Object.entries(this.permissionMatrix).forEach(([resource, actions]) => {
      const allowedActions: ActionType[] = [];
      
      Object.entries(actions).forEach(([action, config]) => {
        if (config.roles.includes(role)) {
          allowedActions.push(action as ActionType);
        }
      });
      
      if (allowedActions.length > 0) {
        permissions.push({
          resource: resource as ResourceType,
          actions: allowedActions,
        });
      }
    });
    
    return permissions;
  }

  // Create a permission object
  createPermission(
    resource: ResourceType,
    action: ActionType,
    scope: PermissionScope,
    conditions?: Record<string, any>,
    attributes?: string[]
  ): Omit<Permission, "id" | "createdAt" | "updatedAt"> {
    return {
      resource,
      action,
      scope,
      conditions,
      attributes,
    };
  }

  // Validate permission structure
  validatePermission(permission: Partial<Permission>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!permission.resource) {
      errors.push("Resource is required");
    }

    if (!permission.action) {
      errors.push("Action is required");
    }

    if (!permission.scope) {
      errors.push("Scope is required");
    }

    if (permission.resource && permission.action) {
      const allowedRoles = this.getAllowedRoles(permission.resource, permission.action);
      if (allowedRoles.length === 0) {
        errors.push(`Invalid resource/action combination: ${permission.resource}:${permission.action}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

export const permissionSystem = PermissionSystem.getInstance();
