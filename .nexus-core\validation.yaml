# NEXUS Validation System
# Quality standards for APPLICATION development using NEXUS framework

validation_system:
  completion_bias_prevention:
    approach: "meta_cognitive_prompts"
    
    core_triggers:
      pre_completion_reflection:
        - "What would a world-class senior developer validate before marking this complete?"
        - "Generate specific validation steps for this task, then execute them"
      
      reality_check:
        - "If deployed now, what would break? Test those scenarios"
      
      proof_generation:
        - "How would I prove this works? Create and execute that proof"
      
      completion_definition:
        - "What does 'complete' mean for this specific task? Define and verify success criteria"
    
    validation_workflow:
      - "Analyze task type and generate appropriate validation criteria"
      - "Execute self-generated validation steps"
      - "Verify results and address any issues found"
      - "Confirm all success criteria are met"
    
    adaptive_prompts:
      - "Given the specific context, what unique validation requirements apply?"
      - "From the end user's perspective, what would prove this works correctly?"
      - "What are the top 3 ways this could fail? Test those scenarios"
      
  application_quality_standards:
    code_quality:
      typescript_standards:
        strict_mode: true
        no_any_types: true
        proper_typing: true
        interface_over_type: "preferred"
        
      react_standards:
        server_components_default: true
        client_components_minimal: true
        proper_error_boundaries: true
        accessibility_compliance: "WCAG AA"
        
      nextjs_standards:
        app_router_usage: true
        metadata_optimization: true
        performance_optimization: true
        seo_compliance: true
        
    security_validation:
      authentication:
        jwt_validation: "server-side only"
        session_management: "secure"
        password_requirements: "strong"
        
      authorization:
        rls_policies: "mandatory for all tables"
        principle_least_privilege: "enforced"
        role_based_access: "granular"
        
      input_validation:
        validation_library: "Zod v4.0.5"
        server_client_sync: true
        sanitization: "comprehensive"
        
    performance_standards:
      core_web_vitals:
        lcp: "< 2.5s"
        fid: "< 100ms"
        cls: "< 0.1"
        
      bundle_optimization:
        javascript_size: "< 200KB gzipped"
        css_size: "< 50KB gzipped"
        image_optimization: "automatic"
        
      caching_strategy:
        static_assets: "long-term caching"
        api_responses: "appropriate caching"
        database_queries: "optimized"
        
    accessibility_requirements:
      wcag_compliance:
        level: "AA"
        color_contrast: "4.5:1 minimum"
        keyboard_navigation: "full support"
        screen_reader: "comprehensive"
        
      semantic_html:
        proper_headings: "h1-h6 hierarchy"
        landmarks: "nav, main, aside"
        form_labels: "all inputs labeled"
        
    testing_standards:
      unit_tests:
        coverage: "80% minimum"
        component_testing: "React Testing Library"
        utility_testing: "Jest"
        
      integration_tests:
        api_testing: "comprehensive"
        database_testing: "with mocks"
        auth_flow_testing: "complete"
        
      e2e_tests:
        critical_paths: "user journeys"
        cross_browser: "modern browsers"
        performance_testing: "load testing"
      
  deployment_validation:
    pre_deployment_checks:
      - "All tests passing"
      - "Security scan clean"
      - "Performance audit passed"
      - "Accessibility compliance verified"
      
    production_readiness:
      error_handling: "comprehensive"
      logging: "appropriate"
      monitoring: "configured"
      rollback_plan: "prepared"
      
  success_criteria:
    quantitative_metrics:
      performance_score: "90+"
      accessibility_score: "100%"
      security_score: "A+"
      test_coverage: "80%+"
      
    qualitative_metrics:
      user_experience: "excellent"
      code_maintainability: "high"
      system_scalability: "enterprise"
      developer_productivity: "high"
