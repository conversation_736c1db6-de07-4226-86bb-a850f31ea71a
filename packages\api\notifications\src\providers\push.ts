import webpush from "web-push";
import admin from "firebase-admin";
import { PushConfig, Notification, NotificationTemplate, NotificationDevice } from "../types";

export interface PushProvider {
  send(
    notification: Notification,
    template: NotificationTemplate,
    variables: Record<string, any>,
    device: NotificationDevice
  ): Promise<void>;
  verify(): Promise<boolean>;
}

export class WebPushProvider implements PushProvider {
  private config: PushConfig;

  constructor(config: PushConfig) {
    this.config = config;
    
    if (!config.web?.vapidPublicKey || !config.web?.vapidPrivateKey) {
      throw new Error("VAPID keys are required for web push");
    }

    webpush.setVapidDetails(
      config.web.subject,
      config.web.vapidPublicKey,
      config.web.vapidPrivateKey
    );
  }

  async send(
    notification: Notification,
    template: NotificationTemplate,
    variables: Record<string, any>,
    device: NotificationDevice
  ): Promise<void> {
    try {
      if (device.type !== "web") {
        throw new Error("Device type must be 'web' for WebPush");
      }

      if (!device.endpoint || !device.keys) {
        throw new Error("Device endpoint and keys are required");
      }

      const pushContent = template.content.push;
      if (!pushContent) {
        throw new Error("Push content not found in template");
      }

      const title = this.replaceVariables(pushContent.title, variables);
      const body = this.replaceVariables(pushContent.body, variables);

      const payload = JSON.stringify({
        title,
        body,
        icon: pushContent.icon || "/icon-192x192.png",
        badge: pushContent.badge || "/badge-72x72.png",
        image: pushContent.image,
        data: {
          notificationId: notification.id,
          actionUrl: notification.actionUrl,
          ...notification.data,
        },
        actions: pushContent.actions?.map(action => ({
          action: action.action,
          title: this.replaceVariables(action.title, variables),
          icon: action.icon,
        })),
        tag: notification.type,
        requireInteraction: notification.priority === "urgent",
        silent: notification.priority === "low",
      });

      const subscription = {
        endpoint: device.endpoint,
        keys: device.keys,
      };

      await webpush.sendNotification(subscription, payload, {
        TTL: 24 * 60 * 60, // 24 hours
        urgency: this.getUrgency(notification.priority),
        topic: notification.type,
      });
    } catch (error: any) {
      throw new Error(`Failed to send web push notification: ${error.message}`);
    }
  }

  async verify(): Promise<boolean> {
    try {
      // Test VAPID configuration
      const testSubscription = {
        endpoint: "https://fcm.googleapis.com/fcm/send/test",
        keys: {
          p256dh: "test",
          auth: "test",
        },
      };

      // This will fail but validate the VAPID setup
      await webpush.sendNotification(testSubscription, "test", { TTL: 1 });
      return true;
    } catch (error: any) {
      // If error is about invalid subscription, VAPID is configured correctly
      return error.message.includes("invalid") || error.message.includes("subscription");
    }
  }

  private replaceVariables(content: string, variables: Record<string, any>): string {
    let result = content;
    
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      result = result.replace(regex, String(value));
    }
    
    return result;
  }

  private getUrgency(priority: string): "very-low" | "low" | "normal" | "high" {
    switch (priority) {
      case "urgent":
        return "high";
      case "high":
        return "high";
      case "normal":
        return "normal";
      case "low":
        return "low";
      default:
        return "normal";
    }
  }
}

export class FirebasePushProvider implements PushProvider {
  private app: admin.app.App;
  private config: PushConfig;

  constructor(config: PushConfig) {
    this.config = config;
    
    if (!config.firebase?.projectId || !config.firebase?.privateKey || !config.firebase?.clientEmail) {
      throw new Error("Firebase configuration is required");
    }

    this.app = admin.initializeApp({
      credential: admin.credential.cert({
        projectId: config.firebase.projectId,
        privateKey: config.firebase.privateKey.replace(/\\n/g, '\n'),
        clientEmail: config.firebase.clientEmail,
      }),
    });
  }

  async send(
    notification: Notification,
    template: NotificationTemplate,
    variables: Record<string, any>,
    device: NotificationDevice
  ): Promise<void> {
    try {
      if (device.type !== "android" && device.type !== "ios") {
        throw new Error("Device type must be 'android' or 'ios' for Firebase");
      }

      const pushContent = template.content.push;
      if (!pushContent) {
        throw new Error("Push content not found in template");
      }

      const title = this.replaceVariables(pushContent.title, variables);
      const body = this.replaceVariables(pushContent.body, variables);

      const message: admin.messaging.Message = {
        token: device.token,
        notification: {
          title,
          body,
          imageUrl: pushContent.image,
        },
        data: {
          notificationId: notification.id,
          actionUrl: notification.actionUrl || "",
          type: notification.type,
          priority: notification.priority,
          ...Object.fromEntries(
            Object.entries(notification.data || {}).map(([k, v]) => [k, String(v)])
          ),
        },
        android: {
          priority: this.getAndroidPriority(notification.priority),
          notification: {
            icon: pushContent.icon,
            color: "#007bff",
            tag: notification.type,
            clickAction: notification.actionUrl,
            channelId: notification.type,
          },
          ttl: 24 * 60 * 60 * 1000, // 24 hours
        },
        apns: {
          payload: {
            aps: {
              badge: 1,
              sound: notification.priority === "urgent" ? "default" : undefined,
              category: notification.type,
              threadId: notification.type,
            },
          },
          headers: {
            "apns-priority": this.getApnsPriority(notification.priority),
            "apns-expiration": String(Math.floor(Date.now() / 1000) + 24 * 60 * 60),
          },
        },
      };

      await admin.messaging().send(message);
    } catch (error: any) {
      throw new Error(`Failed to send Firebase push notification: ${error.message}`);
    }
  }

  async verify(): Promise<boolean> {
    try {
      // Test Firebase connection by getting app info
      await admin.messaging().send({
        token: "test-token",
        notification: {
          title: "Test",
          body: "Test",
        },
      }, true); // Dry run
      
      return true;
    } catch (error: any) {
      // If error is about invalid token, Firebase is configured correctly
      return error.code === "messaging/invalid-registration-token" ||
             error.code === "messaging/registration-token-not-registered";
    }
  }

  private replaceVariables(content: string, variables: Record<string, any>): string {
    let result = content;
    
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      result = result.replace(regex, String(value));
    }
    
    return result;
  }

  private getAndroidPriority(priority: string): "normal" | "high" {
    return priority === "urgent" || priority === "high" ? "high" : "normal";
  }

  private getApnsPriority(priority: string): "5" | "10" {
    return priority === "urgent" || priority === "high" ? "10" : "5";
  }
}

// Factory function to create push provider
export function createPushProvider(config: PushConfig, deviceType: string): PushProvider {
  if (deviceType === "web") {
    return new WebPushProvider(config);
  } else if (deviceType === "android" || deviceType === "ios") {
    return new FirebasePushProvider(config);
  } else {
    throw new Error(`Unsupported device type: ${deviceType}`);
  }
}
