overwrite: true
schema: "src/schema/index.ts"
documents: null
generates:
  src/generated/types.ts:
    plugins:
      - "typescript"
      - "typescript-resolvers"
    config:
      useIndexSignature: true
      contextType: "../types#GraphQLContext"
      mappers:
        User: "../types#User"
        Role: "../types#Role"
        Workspace: "../types#Workspace"
        Team: "../types#Team"
        Project: "../types#Project"
        File: "../types#File"
        Tenant: "../types#Tenant"
        Subscription: "../types#Subscription"
        Invoice: "../types#Invoice"
        Plan: "../types#Plan"
      scalars:
        DateTime: Date
        JSON: Record<string, any>
        Upload: any
        EmailAddress: string
        URL: string
        UUID: string
        PositiveInt: number
        NonNegativeInt: number
hooks:
  afterAllFileWrite:
    - prettier --write
