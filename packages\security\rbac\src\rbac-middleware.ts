import { NextRequest, NextResponse } from "next/server";
import { accessControl } from "./access-control";
import { ResourceType, ActionType } from "./rbac-types";

// Middleware configuration
export interface RBACMiddlewareConfig {
  resource: ResourceType;
  action: ActionType;
  resourceIdParam?: string;
  tenantIdHeader?: string;
  workspaceIdParam?: string;
  teamIdParam?: string;
  skipAuth?: boolean;
  customCheck?: (req: NextRequest, userId: string) => Promise<boolean>;
  onUnauthorized?: (req: NextRequest, reason: string) => NextResponse;
  onError?: (req: NextRequest, error: Error) => NextResponse;
}

// Default unauthorized response
const defaultUnauthorizedResponse = (reason: string) => {
  return NextResponse.json(
    { error: "Unauthorized", message: reason },
    { status: 403 }
  );
};

// Default error response
const defaultErrorResponse = (error: Error) => {
  return NextResponse.json(
    { error: "Internal Server Error", message: error.message },
    { status: 500 }
  );
};

// Main RBAC middleware function
export function withRBAC(config: RBACMiddlewareConfig) {
  return async function middleware(req: NextRequest): Promise<NextResponse | void> {
    try {
      // Skip auth if configured
      if (config.skipAuth) {
        return;
      }

      // Extract user ID from request (assuming it's set by auth middleware)
      const userId = req.headers.get("x-user-id");
      if (!userId) {
        const reason = "User ID not found in request headers";
        return config.onUnauthorized?.(req, reason) || defaultUnauthorizedResponse(reason);
      }

      // Extract tenant ID
      const tenantId = req.headers.get(config.tenantIdHeader || "x-tenant-id");
      if (!tenantId) {
        const reason = "Tenant ID not found in request headers";
        return config.onUnauthorized?.(req, reason) || defaultUnauthorizedResponse(reason);
      }

      // Extract resource ID from URL params if specified
      let resourceId: string | undefined;
      if (config.resourceIdParam) {
        const url = new URL(req.url);
        const pathSegments = url.pathname.split("/");
        const paramIndex = pathSegments.findIndex(segment => segment === config.resourceIdParam);
        if (paramIndex !== -1 && paramIndex + 1 < pathSegments.length) {
          resourceId = pathSegments[paramIndex + 1];
        }
      }

      // Extract workspace ID from URL params if specified
      let workspaceId: string | undefined;
      if (config.workspaceIdParam) {
        const url = new URL(req.url);
        const pathSegments = url.pathname.split("/");
        const paramIndex = pathSegments.findIndex(segment => segment === config.workspaceIdParam);
        if (paramIndex !== -1 && paramIndex + 1 < pathSegments.length) {
          workspaceId = pathSegments[paramIndex + 1];
        }
      }

      // Extract team ID from URL params if specified
      let teamId: string | undefined;
      if (config.teamIdParam) {
        const url = new URL(req.url);
        const pathSegments = url.pathname.split("/");
        const paramIndex = pathSegments.findIndex(segment => segment === config.teamIdParam);
        if (paramIndex !== -1 && paramIndex + 1 < pathSegments.length) {
          teamId = pathSegments[paramIndex + 1];
        }
      }

      // Custom check if provided
      if (config.customCheck) {
        const customResult = await config.customCheck(req, userId);
        if (!customResult) {
          const reason = "Custom authorization check failed";
          return config.onUnauthorized?.(req, reason) || defaultUnauthorizedResponse(reason);
        }
      }

      // Perform RBAC check
      const hasPermission = await accessControl.can(
        userId,
        config.action,
        config.resource,
        {
          tenantId,
          workspaceId,
          teamId,
          resourceId,
          requestData: {
            ip: req.ip,
            userAgent: req.headers.get("user-agent"),
            method: req.method,
            url: req.url,
          },
        }
      );

      if (!hasPermission) {
        const reason = `Insufficient permissions for ${config.action} on ${config.resource}`;
        return config.onUnauthorized?.(req, reason) || defaultUnauthorizedResponse(reason);
      }

      // Permission granted, continue to next middleware or handler
      return;
    } catch (error) {
      const err = error instanceof Error ? error : new Error("Unknown error");
      return config.onError?.(req, err) || defaultErrorResponse(err);
    }
  };
}

// Convenience middleware creators
export const requireRead = (resource: ResourceType, resourceIdParam?: string) =>
  withRBAC({ resource, action: "read", resourceIdParam });

export const requireWrite = (resource: ResourceType, resourceIdParam?: string) =>
  withRBAC({ resource, action: "update", resourceIdParam });

export const requireCreate = (resource: ResourceType) =>
  withRBAC({ resource, action: "create" });

export const requireDelete = (resource: ResourceType, resourceIdParam?: string) =>
  withRBAC({ resource, action: "delete", resourceIdParam });

export const requireManage = (resource: ResourceType, resourceIdParam?: string) =>
  withRBAC({ resource, action: "manage", resourceIdParam });

// API route wrapper for RBAC
export function withAPIRBAC<T extends any[]>(
  config: RBACMiddlewareConfig,
  handler: (req: NextRequest, ...args: T) => Promise<NextResponse>
) {
  return async (req: NextRequest, ...args: T): Promise<NextResponse> => {
    const middlewareResult = await withRBAC(config)(req);
    
    if (middlewareResult) {
      // Middleware returned a response (unauthorized or error)
      return middlewareResult;
    }
    
    // Permission granted, call the handler
    return handler(req, ...args);
  };
}

// React component wrapper for RBAC
export function withComponentRBAC<P extends object>(
  Component: React.ComponentType<P>,
  config: {
    resource: ResourceType;
    action: ActionType;
    fallback?: React.ComponentType<P>;
    loading?: React.ComponentType;
  }
) {
  return function RBACWrappedComponent(props: P) {
    // This would be implemented with React hooks
    // For now, just return the component
    return <Component {...props} />;
  };
}

// Hook for checking permissions in React components
export function usePermission(
  resource: ResourceType,
  action: ActionType,
  resourceId?: string
): {
  hasPermission: boolean;
  isLoading: boolean;
  error: string | null;
} {
  // This would be implemented with React hooks and context
  // For now, return a placeholder
  return {
    hasPermission: false,
    isLoading: true,
    error: null,
  };
}

// Decorator for class methods
export function RequirePermission(resource: ResourceType, action: ActionType) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      // Extract context from method arguments or class instance
      const context = this.getAuthContext?.() || {};
      
      if (!context.userId || !context.tenantId) {
        throw new Error("Authentication context not available");
      }
      
      const hasPermission = await accessControl.can(
        context.userId,
        action,
        resource,
        context
      );
      
      if (!hasPermission) {
        throw new Error(`Insufficient permissions for ${action} on ${resource}`);
      }
      
      return originalMethod.apply(this, args);
    };
    
    return descriptor;
  };
}
