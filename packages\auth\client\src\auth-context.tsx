"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { authClient } from "./auth-client";
import { AuthContextType, AuthState, LoginCredentials, RegisterCredentials } from "./auth-types";

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState<AuthState>({
    user: null,
    session: null,
    isLoading: true,
    isAuthenticated: false,
  });

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const session = await authClient.getSession();
        if (session?.data?.user) {
          setState({
            user: session.data.user as any,
            session: session.data as any,
            isLoading: false,
            isAuthenticated: true,
          });
        } else {
          setState({
            user: null,
            session: null,
            isLoading: false,
            isAuthenticated: false,
          });
        }
      } catch (error) {
        console.error("Failed to initialize auth:", error);
        setState({
          user: null,
          session: null,
          isLoading: false,
          isAuthenticated: false,
        });
      }
    };

    initializeAuth();
  }, []);

  const login = async (credentials: LoginCredentials) => {
    setState(prev => ({ ...prev, isLoading: true }));
    
    try {
      const result = await authClient.signIn.email({
        email: credentials.email,
        password: credentials.password,
        rememberMe: credentials.remember,
      });

      if (result.data?.user) {
        setState({
          user: result.data.user as any,
          session: result.data as any,
          isLoading: false,
          isAuthenticated: true,
        });
      }
    } catch (error) {
      setState(prev => ({ ...prev, isLoading: false }));
      throw error;
    }
  };

  const register = async (credentials: RegisterCredentials) => {
    setState(prev => ({ ...prev, isLoading: true }));
    
    try {
      const result = await authClient.signUp.email({
        email: credentials.email,
        password: credentials.password,
        name: credentials.name,
      });

      if (result.data?.user) {
        setState({
          user: result.data.user as any,
          session: result.data as any,
          isLoading: false,
          isAuthenticated: true,
        });
      }
    } catch (error) {
      setState(prev => ({ ...prev, isLoading: false }));
      throw error;
    }
  };

  const logout = async () => {
    setState(prev => ({ ...prev, isLoading: true }));
    
    try {
      await authClient.signOut();
      setState({
        user: null,
        session: null,
        isLoading: false,
        isAuthenticated: false,
      });
    } catch (error) {
      console.error("Logout failed:", error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const refreshSession = async () => {
    try {
      const session = await authClient.getSession();
      if (session?.data?.user) {
        setState(prev => ({
          ...prev,
          user: session.data!.user as any,
          session: session.data! as any,
          isAuthenticated: true,
        }));
      }
    } catch (error) {
      console.error("Failed to refresh session:", error);
    }
  };

  const value: AuthContextType = {
    ...state,
    login,
    register,
    logout,
    refreshSession,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
