import { useAuth } from "./auth-context";
import { AuthUser } from "./auth-types";

// Main auth hook
export { useAuth };

// Convenience hooks
export function useUser(): AuthUser | null {
  const { user } = useAuth();
  return user;
}

export function useIsAuthenticated(): boolean {
  const { isAuthenticated } = useAuth();
  return isAuthenticated;
}

export function useIsLoading(): boolean {
  const { isLoading } = useAuth();
  return isLoading;
}

// Role-based hooks
export function useHasRole(role: string): boolean {
  const { user } = useAuth();
  return user?.role === role;
}

export function useHasPermission(permission: string): boolean {
  const { user } = useAuth();
  return user?.permissions?.includes(permission) ?? false;
}

export function useIsOwner(): boolean {
  return useHasRole("OWNER");
}

export function useIsAdmin(): boolean {
  const { user } = useAuth();
  return user?.role === "OWNER" || user?.role === "ADMIN";
}

export function useCanRead(): boolean {
  return useHasPermission("read");
}

export function useCanWrite(): boolean {
  return useHasPermission("write");
}

export function useCanDelete(): boolean {
  return useHasPermission("delete");
}

// Tenant-based hooks
export function useTenantId(): string | null {
  const { user } = useAuth();
  return user?.tenantId ?? null;
}

// Auth actions hooks
export function useLogin() {
  const { login } = useAuth();
  return login;
}

export function useRegister() {
  const { register } = useAuth();
  return register;
}

export function useLogout() {
  const { logout } = useAuth();
  return logout;
}
