import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { persist } from "zustand/middleware";
import { restApiClient } from "../rest";
import { endpoints } from "../config";
import { TokenManager, userStorage, AUTH_EVENTS, AuthEventEmitter } from "../auth";
import { AuthStore, User, AuthTokens } from "../types";

interface AuthState extends AuthStore {
  error: string | null;
  setError: (error: string | null) => void;
  setLoading: (loading: boolean) => void;
  setUser: (user: User | null) => void;
  setTokens: (tokens: AuthTokens | null) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    immer((set, get) => ({
      user: null,
      tokens: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      setError: (error) =>
        set((state) => {
          state.error = error;
        }),

      setLoading: (loading) =>
        set((state) => {
          state.isLoading = loading;
        }),

      setUser: (user) =>
        set((state) => {
          state.user = user;
          state.isAuthenticated = !!user;
          if (user) {
            userStorage.save(user);
          } else {
            userStorage.clear();
          }
        }),

      setTokens: (tokens) =>
        set((state) => {
          state.tokens = tokens;
          if (tokens) {
            TokenManager.getInstance().setTokens(tokens);
          } else {
            TokenManager.getInstance().clearTokens();
          }
        }),

      login: async (email: string, password: string) => {
        const { setLoading, setError, setUser, setTokens } = get();
        
        try {
          setLoading(true);
          setError(null);

          const response = await restApiClient.post(endpoints.auth.login, {
            email,
            password,
          });

          const { user, tokens } = response.data;
          
          setUser(user);
          setTokens(tokens);
          
          // Set API client context
          restApiClient.setContext({
            tenantId: user.tenantId,
          });

          // Emit login event
          AuthEventEmitter.getInstance().emit(AUTH_EVENTS.LOGIN, { user, tokens });
        } catch (error: any) {
          setError(error.message || "Login failed");
          throw error;
        } finally {
          setLoading(false);
        }
      },

      logout: async () => {
        const { setUser, setTokens, setError } = get();
        
        try {
          // Call logout endpoint
          await restApiClient.post(endpoints.auth.logout);
        } catch (error) {
          console.error("Logout API call failed:", error);
        } finally {
          // Clear local state regardless of API call result
          setUser(null);
          setTokens(null);
          setError(null);
          
          // Emit logout event
          AuthEventEmitter.getInstance().emit(AUTH_EVENTS.LOGOUT);
        }
      },

      refreshToken: async () => {
        const { setTokens, setError, logout } = get();
        
        try {
          const tokenManager = TokenManager.getInstance();
          const refreshToken = tokenManager.getRefreshToken();
          
          if (!refreshToken) {
            throw new Error("No refresh token available");
          }

          const response = await restApiClient.post(endpoints.auth.refresh, {
            refreshToken,
          });

          const tokens = response.data;
          setTokens(tokens);
          
          // Emit token refresh event
          AuthEventEmitter.getInstance().emit(AUTH_EVENTS.TOKEN_REFRESH, tokens);
        } catch (error: any) {
          setError(error.message || "Token refresh failed");
          logout(); // Force logout on refresh failure
          throw error;
        }
      },

      updateUser: (updates: Partial<User>) =>
        set((state) => {
          if (state.user) {
            state.user = { ...state.user, ...updates };
            userStorage.save(state.user);
            
            // Emit user updated event
            AuthEventEmitter.getInstance().emit(AUTH_EVENTS.USER_UPDATED, state.user);
          }
        }),
    })),
    {
      name: "nexus-auth",
      partialize: (state) => ({
        user: state.user,
        tokens: state.tokens,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        if (state?.tokens) {
          // Restore tokens to TokenManager
          TokenManager.getInstance().setTokens(state.tokens);
          
          // Set API client context
          if (state.user?.tenantId) {
            restApiClient.setContext({
              tenantId: state.user.tenantId,
            });
          }
        }
      },
    }
  )
);

// Auth event listeners
const authEventEmitter = AuthEventEmitter.getInstance();

// Handle token expiration
authEventEmitter.on(AUTH_EVENTS.TOKEN_EXPIRED, () => {
  const { logout } = useAuthStore.getState();
  logout();
});

// Initialize auth state from storage
export const initializeAuth = () => {
  const tokenManager = TokenManager.getInstance();
  const storedUser = userStorage.load();
  
  if (tokenManager.isAuthenticated() && storedUser) {
    const { setUser, setTokens } = useAuthStore.getState();
    
    setUser(storedUser);
    setTokens({
      accessToken: tokenManager.getAccessToken()!,
      refreshToken: tokenManager.getRefreshToken()!,
      expiresAt: Date.now() + 60 * 60 * 1000, // Default 1 hour
    });
    
    // Set API client context
    restApiClient.setContext({
      tenantId: storedUser.tenantId,
    });
  }
};

// Auth utilities
export const authUtils = {
  // Check if user has specific role
  hasRole: (role: string): boolean => {
    const { user } = useAuthStore.getState();
    return user?.roles?.includes(role) || false;
  },

  // Check if user has any of the specified roles
  hasAnyRole: (roles: string[]): boolean => {
    const { user } = useAuthStore.getState();
    return roles.some(role => user?.roles?.includes(role)) || false;
  },

  // Check if user has all specified roles
  hasAllRoles: (roles: string[]): boolean => {
    const { user } = useAuthStore.getState();
    return roles.every(role => user?.roles?.includes(role)) || false;
  },

  // Check if user is admin
  isAdmin: (): boolean => {
    return authUtils.hasAnyRole(["admin", "system_admin"]);
  },

  // Check if user is owner
  isOwner: (): boolean => {
    return authUtils.hasRole("owner");
  },

  // Get current user
  getCurrentUser: (): User | null => {
    return useAuthStore.getState().user;
  },

  // Get current tenant ID
  getCurrentTenantId: (): string | null => {
    return useAuthStore.getState().user?.tenantId || null;
  },
};
