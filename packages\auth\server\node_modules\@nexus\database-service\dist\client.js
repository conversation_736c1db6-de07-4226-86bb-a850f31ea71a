import { PrismaClient } from "@nexus/database-schema";
import { setTenantContext } from "@nexus/database-schema";
// Global Prisma client instance
const globalForPrisma = globalThis;
// Create Prisma client with optimized configuration
export const prisma = globalForPrisma.prisma ??
    new PrismaClient({
        log: ["query", "error", "warn"],
        datasources: {
            db: {
                url: process.env.DATABASE_URL,
            },
        },
    });
if (process.env.NODE_ENV !== "production")
    globalForPrisma.prisma = prisma;
// Multi-tenant Prisma client wrapper
export class TenantPrismaClient {
    client;
    tenantId;
    constructor(tenantId, client = prisma) {
        this.client = client;
        this.tenantId = tenantId;
    }
    // Execute query with tenant context
    async withTenantContext(operation) {
        return this.client.$transaction(async (tx) => {
            // Set tenant context for RLS
            await tx.$executeRawUnsafe(setTenantContext(this.tenantId));
            return operation(tx);
        });
    }
    // Get tenant-scoped client
    get tenant() {
        return {
            user: {
                findMany: (args) => this.withTenantContext((tx) => tx.user.findMany({
                    ...args,
                    where: { ...args?.where, tenantId: this.tenantId },
                })),
                findUnique: (args) => this.withTenantContext((tx) => tx.user.findUnique({
                    ...args,
                    where: { ...args.where, tenantId: this.tenantId },
                })),
                create: (args) => this.withTenantContext((tx) => tx.user.create({
                    ...args,
                    data: { ...args.data, tenantId: this.tenantId },
                })),
                update: (args) => this.withTenantContext((tx) => tx.user.update({
                    ...args,
                    where: { ...args.where, tenantId: this.tenantId },
                })),
                delete: (args) => this.withTenantContext((tx) => tx.user.delete({
                    ...args,
                    where: { ...args.where, tenantId: this.tenantId },
                })),
            },
            workspace: {
                findMany: (args) => this.withTenantContext((tx) => tx.workspace.findMany({
                    ...args,
                    where: { ...args?.where, tenantId: this.tenantId },
                })),
                findUnique: (args) => this.withTenantContext((tx) => tx.workspace.findUnique({
                    ...args,
                    where: { ...args.where, tenantId: this.tenantId },
                })),
                create: (args) => this.withTenantContext((tx) => tx.workspace.create({
                    ...args,
                    data: { ...args.data, tenantId: this.tenantId },
                })),
                update: (args) => this.withTenantContext((tx) => tx.workspace.update({
                    ...args,
                    where: { ...args.where, tenantId: this.tenantId },
                })),
                delete: (args) => this.withTenantContext((tx) => tx.workspace.delete({
                    ...args,
                    where: { ...args.where, tenantId: this.tenantId },
                })),
            },
            session: {
                findMany: (args) => this.withTenantContext((tx) => tx.session.findMany({
                    ...args,
                    where: { ...args?.where, tenantId: this.tenantId },
                })),
                findUnique: (args) => this.withTenantContext((tx) => tx.session.findUnique({
                    ...args,
                    where: { ...args.where, tenantId: this.tenantId },
                })),
                create: (args) => this.withTenantContext((tx) => tx.session.create({
                    ...args,
                    data: { ...args.data, tenantId: this.tenantId },
                })),
                update: (args) => this.withTenantContext((tx) => tx.session.update({
                    ...args,
                    where: { ...args.where, tenantId: this.tenantId },
                })),
                delete: (args) => this.withTenantContext((tx) => tx.session.delete({
                    ...args,
                    where: { ...args.where, tenantId: this.tenantId },
                })),
            },
        };
    }
}
// Factory function to create tenant-scoped client
export const createTenantClient = (tenantId) => {
    return new TenantPrismaClient(tenantId);
};
// Database health check
export const checkDatabaseHealth = async () => {
    try {
        await prisma.$queryRaw `SELECT 1`;
        return true;
    }
    catch (error) {
        console.error("Database health check failed:", error);
        return false;
    }
};
// Graceful shutdown
export const disconnectDatabase = async () => {
    await prisma.$disconnect();
};
