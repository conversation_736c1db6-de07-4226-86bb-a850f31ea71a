import { createTenantClient } from "./client";
export class WorkspaceService {
    tenantId;
    constructor(tenantId) {
        this.tenantId = tenantId;
    }
    // Create a new workspace
    async create(data) {
        const client = createTenantClient(this.tenantId);
        return client.tenant.workspace.create({
            data,
        });
    }
    // Find workspace by ID
    async findById(id) {
        const client = createTenantClient(this.tenantId);
        return client.tenant.workspace.findUnique({
            where: { id },
        });
    }
    // Find workspace by slug
    async findBySlug(slug) {
        const client = createTenantClient(this.tenantId);
        return client.tenant.workspace.findUnique({
            where: {
                tenantId_slug: {
                    tenantId: this.tenantId,
                    slug,
                },
            },
        });
    }
    // Update workspace
    async update(id, data) {
        const client = createTenantClient(this.tenantId);
        return client.tenant.workspace.update({
            where: { id },
            data,
        });
    }
    // Delete workspace
    async delete(id) {
        const client = createTenantClient(this.tenantId);
        return client.tenant.workspace.delete({
            where: { id },
        });
    }
    // List workspaces with pagination
    async list(page = 1, limit = 10) {
        const client = createTenantClient(this.tenantId);
        const skip = (page - 1) * limit;
        const [workspaces, total] = await Promise.all([
            client.tenant.workspace.findMany({
                skip,
                take: limit,
                orderBy: { createdAt: "desc" },
            }),
            client.tenant.workspace.count(),
        ]);
        return {
            data: workspaces,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    // Check if slug is available
    async isSlugAvailable(slug) {
        const workspace = await this.findBySlug(slug);
        return !workspace;
    }
    // Get all workspaces for tenant
    async findAll() {
        const client = createTenantClient(this.tenantId);
        return client.tenant.workspace.findMany({
            orderBy: { createdAt: "desc" },
        });
    }
}
export const createWorkspaceService = (tenantId) => {
    return new WorkspaceService(tenantId);
};
