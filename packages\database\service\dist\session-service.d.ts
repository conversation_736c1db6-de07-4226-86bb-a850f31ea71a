export declare class SessionService {
    private tenantId;
    constructor(tenantId: string);
    create(userId: string, token: string, expiresAt: Date): Promise<Session>;
    findByToken(token: string): Promise<Session | null>;
    findByUserId(userId: string): Promise<Session[]>;
    updateExpiration(token: string, expiresAt: Date): Promise<Session>;
    delete(token: string): Promise<Session>;
    deleteAllForUser(userId: string): Promise<number>;
    deleteExpired(): Promise<number>;
    isValid(token: string): Promise<boolean>;
    refresh(token: string, extensionMs?: number): Promise<Session | null>;
}
export declare const createSessionService: (tenantId: string) => SessionService;
//# sourceMappingURL=session-service.d.ts.map