export declare class SessionService {
    private tenantId;
    constructor(tenantId: string);
    create(userId: string, token: string, expiresAt: Date): Promise<any>;
    findByToken(token: string): Promise<any | null>;
    findByUserId(userId: string): Promise<any[]>;
    updateExpiration(token: string, expiresAt: Date): Promise<any>;
    delete(token: string): Promise<any>;
    deleteAllForUser(userId: string): Promise<number>;
    deleteExpired(): Promise<number>;
    isValid(token: string): Promise<boolean>;
    refresh(token: string, extensionMs?: number): Promise<any | null>;
}
export declare const createSessionService: (tenantId: string) => SessionService;
//# sourceMappingURL=session-service.d.ts.map