import Bull from "bull";
import { createClient } from "redis";
import { 
  NotificationJob, 
  Notification, 
  NotificationTemplate, 
  NotificationDevice,
  NotificationConfig 
} from "./types";
import { createEmailProvider } from "./providers/email";
import { createSMSProvider } from "./providers/sms";
import { createPushProvider } from "./providers/push";

export class NotificationQueue {
  private emailQueue: Bull.Queue;
  private smsQueue: Bull.Queue;
  private pushQueue: Bull.Queue;
  private webhookQueue: Bull.Queue;
  private config: NotificationConfig;
  private redisClient: any;

  constructor(config: NotificationConfig) {
    this.config = config;

    // Create Redis client
    this.redisClient = createClient({
      host: config.redis.host,
      port: config.redis.port,
      password: config.redis.password,
      db: config.redis.db,
    });

    // Create queues
    this.emailQueue = new Bull("email notifications", {
      redis: config.redis,
      defaultJobOptions: {
        attempts: config.queue.retryAttempts,
        backoff: {
          type: "exponential",
          delay: config.queue.retryDelay,
        },
        removeOnComplete: 100,
        removeOnFail: 50,
      },
    });

    this.smsQueue = new Bull("sms notifications", {
      redis: config.redis,
      defaultJobOptions: {
        attempts: config.queue.retryAttempts,
        backoff: {
          type: "exponential",
          delay: config.queue.retryDelay,
        },
        removeOnComplete: 100,
        removeOnFail: 50,
      },
    });

    this.pushQueue = new Bull("push notifications", {
      redis: config.redis,
      defaultJobOptions: {
        attempts: config.queue.retryAttempts,
        backoff: {
          type: "exponential",
          delay: config.queue.retryDelay,
        },
        removeOnComplete: 100,
        removeOnFail: 50,
      },
    });

    this.webhookQueue = new Bull("webhook notifications", {
      redis: config.redis,
      defaultJobOptions: {
        attempts: config.queue.retryAttempts,
        backoff: {
          type: "exponential",
          delay: config.queue.retryDelay,
        },
        removeOnComplete: 100,
        removeOnFail: 50,
      },
    });

    this.setupProcessors();
    this.setupEventHandlers();
  }

  // Add notification to appropriate queue
  async addNotification(
    notification: Notification,
    template: NotificationTemplate,
    variables: Record<string, any>,
    devices?: NotificationDevice[]
  ): Promise<void> {
    const priority = this.getPriority(notification.priority);
    const delay = notification.scheduledAt 
      ? notification.scheduledAt.getTime() - Date.now()
      : 0;

    for (const channel of notification.channel) {
      switch (channel) {
        case "email":
          await this.emailQueue.add(
            "send-email",
            {
              notification,
              template,
              variables,
            },
            {
              priority,
              delay: Math.max(0, delay),
              jobId: `${notification.id}-email`,
            }
          );
          break;

        case "sms":
          await this.smsQueue.add(
            "send-sms",
            {
              notification,
              template,
              variables,
            },
            {
              priority,
              delay: Math.max(0, delay),
              jobId: `${notification.id}-sms`,
            }
          );
          break;

        case "push":
          if (devices) {
            for (const device of devices) {
              await this.pushQueue.add(
                "send-push",
                {
                  notification,
                  template,
                  variables,
                  device,
                },
                {
                  priority,
                  delay: Math.max(0, delay),
                  jobId: `${notification.id}-push-${device.id}`,
                }
              );
            }
          }
          break;

        case "webhook":
          await this.webhookQueue.add(
            "send-webhook",
            {
              notification,
              template,
              variables,
            },
            {
              priority,
              delay: Math.max(0, delay),
              jobId: `${notification.id}-webhook`,
            }
          );
          break;
      }
    }
  }

  // Setup queue processors
  private setupProcessors(): void {
    // Email processor
    this.emailQueue.process("send-email", this.config.queue.concurrency, async (job) => {
      const { notification, template, variables } = job.data;
      
      try {
        const emailProvider = createEmailProvider(this.config.email);
        await emailProvider.send(notification, template, variables);
        
        // Update notification status
        await this.updateNotificationStatus(notification.id, "sent");
        
        return { success: true, sentAt: new Date() };
      } catch (error: any) {
        await this.updateNotificationStatus(notification.id, "failed");
        throw error;
      }
    });

    // SMS processor
    this.smsQueue.process("send-sms", this.config.queue.concurrency, async (job) => {
      const { notification, template, variables } = job.data;
      
      try {
        const smsProvider = createSMSProvider(this.config.sms);
        await smsProvider.send(notification, template, variables);
        
        await this.updateNotificationStatus(notification.id, "sent");
        
        return { success: true, sentAt: new Date() };
      } catch (error: any) {
        await this.updateNotificationStatus(notification.id, "failed");
        throw error;
      }
    });

    // Push processor
    this.pushQueue.process("send-push", this.config.queue.concurrency, async (job) => {
      const { notification, template, variables, device } = job.data;
      
      try {
        const pushProvider = createPushProvider(this.config.push, device.type);
        await pushProvider.send(notification, template, variables, device);
        
        await this.updateNotificationStatus(notification.id, "sent");
        
        return { success: true, sentAt: new Date(), deviceId: device.id };
      } catch (error: any) {
        await this.updateNotificationStatus(notification.id, "failed");
        throw error;
      }
    });

    // Webhook processor
    this.webhookQueue.process("send-webhook", this.config.queue.concurrency, async (job) => {
      const { notification, template, variables } = job.data;
      
      try {
        await this.sendWebhook(notification, variables);
        
        await this.updateNotificationStatus(notification.id, "sent");
        
        return { success: true, sentAt: new Date() };
      } catch (error: any) {
        await this.updateNotificationStatus(notification.id, "failed");
        throw error;
      }
    });
  }

  // Setup event handlers
  private setupEventHandlers(): void {
    // Email queue events
    this.emailQueue.on("completed", (job, result) => {
      console.log(`Email notification ${job.id} completed:`, result);
    });

    this.emailQueue.on("failed", (job, err) => {
      console.error(`Email notification ${job.id} failed:`, err.message);
    });

    // SMS queue events
    this.smsQueue.on("completed", (job, result) => {
      console.log(`SMS notification ${job.id} completed:`, result);
    });

    this.smsQueue.on("failed", (job, err) => {
      console.error(`SMS notification ${job.id} failed:`, err.message);
    });

    // Push queue events
    this.pushQueue.on("completed", (job, result) => {
      console.log(`Push notification ${job.id} completed:`, result);
    });

    this.pushQueue.on("failed", (job, err) => {
      console.error(`Push notification ${job.id} failed:`, err.message);
    });

    // Webhook queue events
    this.webhookQueue.on("completed", (job, result) => {
      console.log(`Webhook notification ${job.id} completed:`, result);
    });

    this.webhookQueue.on("failed", (job, err) => {
      console.error(`Webhook notification ${job.id} failed:`, err.message);
    });
  }

  // Send webhook notification
  private async sendWebhook(
    notification: Notification,
    variables: Record<string, any>
  ): Promise<void> {
    const fetch = require('node-fetch');
    
    const payload = {
      id: notification.id,
      type: notification.type,
      userId: notification.userId,
      tenantId: notification.tenantId,
      title: notification.title,
      message: notification.message,
      data: notification.data,
      actionUrl: notification.actionUrl,
      priority: notification.priority,
      createdAt: notification.createdAt,
      variables,
    };

    const response = await fetch(this.config.webhook.url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Webhook-Signature": this.generateWebhookSignature(payload),
        ...this.config.webhook.headers,
      },
      body: JSON.stringify(payload),
      timeout: this.config.webhook.timeout,
    });

    if (!response.ok) {
      throw new Error(`Webhook failed with status ${response.status}`);
    }
  }

  // Generate webhook signature
  private generateWebhookSignature(payload: any): string {
    if (!this.config.webhook.secret) {
      return "";
    }

    const crypto = require('crypto');
    const hmac = crypto.createHmac('sha256', this.config.webhook.secret);
    hmac.update(JSON.stringify(payload));
    return `sha256=${hmac.digest('hex')}`;
  }

  // Update notification status
  private async updateNotificationStatus(
    notificationId: string,
    status: string
  ): Promise<void> {
    // TODO: Update notification status in database
    console.log(`Updating notification ${notificationId} status to ${status}`);
  }

  // Get priority number for Bull queue
  private getPriority(priority: string): number {
    switch (priority) {
      case "urgent":
        return 10;
      case "high":
        return 5;
      case "normal":
        return 0;
      case "low":
        return -5;
      default:
        return 0;
    }
  }

  // Get queue statistics
  async getStats(): Promise<{
    email: any;
    sms: any;
    push: any;
    webhook: any;
  }> {
    const [emailStats, smsStats, pushStats, webhookStats] = await Promise.all([
      this.getQueueStats(this.emailQueue),
      this.getQueueStats(this.smsQueue),
      this.getQueueStats(this.pushQueue),
      this.getQueueStats(this.webhookQueue),
    ]);

    return {
      email: emailStats,
      sms: smsStats,
      push: pushStats,
      webhook: webhookStats,
    };
  }

  // Get individual queue stats
  private async getQueueStats(queue: Bull.Queue): Promise<any> {
    const [waiting, active, completed, failed, delayed] = await Promise.all([
      queue.getWaiting(),
      queue.getActive(),
      queue.getCompleted(),
      queue.getFailed(),
      queue.getDelayed(),
    ]);

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
    };
  }

  // Clean up queues
  async cleanup(): Promise<void> {
    await Promise.all([
      this.emailQueue.close(),
      this.smsQueue.close(),
      this.pushQueue.close(),
      this.webhookQueue.close(),
    ]);

    if (this.redisClient) {
      await this.redisClient.quit();
    }
  }
}
