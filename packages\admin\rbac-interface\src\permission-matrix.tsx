"use client";

import React, { useState, useMemo } from "react";
import { useRBAC, useIsAdmin } from "@nexus/rbac";
import { Role, ResourceType, ActionType, permissionSystem } from "@nexus/rbac";

// Main permission matrix component
export function PermissionMatrix() {
  const { roles, isLoading } = useRBAC();
  const isAdmin = useIsAdmin();
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [filterResource, setFilterResource] = useState<ResourceType | "">("");
  const [viewMode, setViewMode] = useState<"grid" | "table">("grid");

  // Get all resources and actions
  const resources: ResourceType[] = [
    "organization", "workspace", "team", "member", "project", "document", 
    "file", "user", "subscription", "invoice", "analytics", "integration", "api"
  ];

  const actions: ActionType[] = [
    "create", "read", "update", "delete", "manage", "share", "invite", 
    "remove", "collaborate", "publish", "download", "upload", "export"
  ];

  // Filter roles to show
  const displayRoles = useMemo(() => {
    if (selectedRoles.length === 0) {
      return roles.filter(role => role.isActive);
    }
    return roles.filter(role => selectedRoles.includes(role.id));
  }, [roles, selectedRoles]);

  // Filter resources
  const displayResources = useMemo(() => {
    if (filterResource) {
      return [filterResource];
    }
    return resources;
  }, [filterResource, resources]);

  if (!isAdmin) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Access denied. Admin privileges required.</p>
      </div>
    );
  }

  if (isLoading) {
    return <div className="text-center py-8">Loading permission matrix...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Permission Matrix</h1>
          <p className="text-gray-600">Visual overview of role permissions across resources</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium">View:</label>
            <select
              value={viewMode}
              onChange={(e) => setViewMode(e.target.value as "grid" | "table")}
              className="px-3 py-1 border rounded-md text-sm"
            >
              <option value="grid">Grid</option>
              <option value="table">Table</option>
            </select>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg border">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Filter by Resource</label>
            <select
              value={filterResource}
              onChange={(e) => setFilterResource(e.target.value as ResourceType | "")}
              className="w-full px-3 py-2 border rounded-md"
            >
              <option value="">All Resources</option>
              {resources.map((resource) => (
                <option key={resource} value={resource}>
                  {resource.charAt(0).toUpperCase() + resource.slice(1)}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Select Roles</label>
            <div className="flex flex-wrap gap-2">
              {roles.filter(r => r.isActive).map((role) => (
                <label key={role.id} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={selectedRoles.includes(role.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedRoles([...selectedRoles, role.id]);
                      } else {
                        setSelectedRoles(selectedRoles.filter(id => id !== role.id));
                      }
                    }}
                    className="rounded"
                  />
                  <span className="text-sm">{role.name}</span>
                </label>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Permission Matrix */}
      {viewMode === "grid" ? (
        <PermissionGrid
          roles={displayRoles}
          resources={displayResources}
          actions={actions}
        />
      ) : (
        <PermissionTable
          roles={displayRoles}
          resources={displayResources}
          actions={actions}
        />
      )}
    </div>
  );
}

// Permission grid view
function PermissionGrid({
  roles,
  resources,
  actions,
}: {
  roles: Role[];
  resources: ResourceType[];
  actions: ActionType[];
}) {
  return (
    <div className="space-y-6">
      {resources.map((resource) => (
        <div key={resource} className="bg-white rounded-lg border">
          <div className="px-6 py-4 border-b bg-gray-50">
            <h3 className="text-lg font-semibold capitalize">{resource}</h3>
          </div>
          
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {actions.map((action) => {
                const allowedRoles = permissionSystem.getAllowedRoles(resource, action);
                if (allowedRoles.length === 0) return null;

                return (
                  <div key={action} className="border rounded-lg p-4">
                    <h4 className="font-medium capitalize mb-3">{action}</h4>
                    <div className="space-y-2">
                      {roles.map((role) => {
                        const hasPermission = allowedRoles.includes(role.slug);
                        return (
                          <div
                            key={role.id}
                            className={`flex items-center justify-between p-2 rounded text-sm ${
                              hasPermission 
                                ? "bg-green-50 text-green-800" 
                                : "bg-gray-50 text-gray-500"
                            }`}
                          >
                            <span>{role.name}</span>
                            <span className={`w-2 h-2 rounded-full ${
                              hasPermission ? "bg-green-500" : "bg-gray-300"
                            }`} />
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

// Permission table view
function PermissionTable({
  roles,
  resources,
  actions,
}: {
  roles: Role[];
  resources: ResourceType[];
  actions: ActionType[];
}) {
  return (
    <div className="bg-white rounded-lg border overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Resource / Action
              </th>
              {roles.map((role) => (
                <th
                  key={role.id}
                  className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {role.name}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {resources.map((resource) => (
              <React.Fragment key={resource}>
                <tr className="bg-gray-25">
                  <td
                    colSpan={roles.length + 1}
                    className="px-6 py-3 text-sm font-medium text-gray-900 capitalize bg-gray-100"
                  >
                    {resource}
                  </td>
                </tr>
                {actions.map((action) => {
                  const allowedRoles = permissionSystem.getAllowedRoles(resource, action);
                  if (allowedRoles.length === 0) return null;

                  return (
                    <tr key={`${resource}-${action}`}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">
                        {action}
                      </td>
                      {roles.map((role) => {
                        const hasPermission = allowedRoles.includes(role.slug);
                        return (
                          <td
                            key={role.id}
                            className="px-6 py-4 whitespace-nowrap text-center"
                          >
                            <span
                              className={`inline-flex w-3 h-3 rounded-full ${
                                hasPermission ? "bg-green-500" : "bg-gray-300"
                              }`}
                            />
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

// Permission summary component
export function PermissionSummary({ roleId }: { roleId: string }) {
  const { roles } = useRBAC();
  const role = roles.find(r => r.id === roleId);

  if (!role) return null;

  const resources: ResourceType[] = [
    "organization", "workspace", "team", "member", "project", "document", 
    "file", "user", "subscription", "invoice", "analytics", "integration", "api"
  ];

  const actions: ActionType[] = [
    "create", "read", "update", "delete", "manage", "share", "invite", 
    "remove", "collaborate", "publish", "download", "upload", "export"
  ];

  const permissions = resources.map(resource => {
    const allowedActions = actions.filter(action => {
      const allowedRoles = permissionSystem.getAllowedRoles(resource, action);
      return allowedRoles.includes(role.slug);
    });

    return {
      resource,
      actions: allowedActions,
      count: allowedActions.length,
    };
  }).filter(p => p.count > 0);

  return (
    <div className="bg-white rounded-lg border">
      <div className="px-6 py-4 border-b">
        <h3 className="text-lg font-semibold">Permission Summary: {role.name}</h3>
      </div>
      
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {permissions.map(({ resource, actions, count }) => (
            <div key={resource} className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium capitalize">{resource}</h4>
                <span className="text-sm text-gray-500">{count} actions</span>
              </div>
              <div className="flex flex-wrap gap-1">
                {actions.map((action) => (
                  <span
                    key={action}
                    className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded"
                  >
                    {action}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
