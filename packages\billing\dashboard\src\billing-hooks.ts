import { useCallback, useEffect } from "react";
import { useTenantId } from "@nexus/tenant-context";
import { useBillingStore } from "./billing-store";
import { createInvoiceService } from "./invoice-service";
import { createPaymentHistoryService } from "./payment-history";
import { createBillingService } from "./billing-service";
import { createUsageAnalyticsService } from "./usage-analytics";
import { InvoiceFilters, PaymentFilters } from "./billing-types";

// Main billing hook
export function useBilling() {
  const tenantId = useTenantId();
  const {
    invoices,
    payments,
    settings,
    stats,
    usage,
    isLoading,
    error,
    setInvoices,
    setPayments,
    setSettings,
    setStats,
    setUsage,
    setLoading,
    setError,
    getTotalRevenue,
    getPendingAmount,
    getOverdueInvoices,
    getRecentPayments,
  } = useBillingStore();

  const invoiceService = tenantId ? createInvoiceService(tenantId) : null;
  const paymentService = tenantId ? createPaymentHistoryService(tenantId) : null;
  const billingService = tenantId ? createBillingService(tenantId) : null;
  const usageService = tenantId ? createUsageAnalyticsService(tenantId) : null;

  // Load all billing data
  const loadBillingData = useCallback(async () => {
    if (!invoiceService || !paymentService || !billingService || !usageService) return;
    
    setLoading(true);
    try {
      const [invoicesData, paymentsData, settingsData, statsData, usageData] = await Promise.all([
        invoiceService.getInvoices(),
        paymentService.getPayments(),
        billingService.getBillingSettings(),
        billingService.getBillingStats(),
        usageService.getCurrentUsage(),
      ]);
      
      setInvoices(invoicesData.data, invoicesData.pagination);
      setPayments(paymentsData.data, paymentsData.pagination);
      setSettings(settingsData);
      setStats(statsData);
      setUsage(usageData);
      setError(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to load billing data");
    } finally {
      setLoading(false);
    }
  }, [invoiceService, paymentService, billingService, usageService, setInvoices, setPayments, setSettings, setStats, setUsage, setLoading, setError]);

  // Load invoices with filters
  const loadInvoices = useCallback(async (filters?: InvoiceFilters, page?: number) => {
    if (!invoiceService) return;
    
    setLoading(true);
    try {
      const result = await invoiceService.getInvoices(filters, page);
      setInvoices(result.data, result.pagination);
      setError(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to load invoices");
    } finally {
      setLoading(false);
    }
  }, [invoiceService, setInvoices, setLoading, setError]);

  // Load payments with filters
  const loadPayments = useCallback(async (filters?: PaymentFilters, page?: number) => {
    if (!paymentService) return;
    
    setLoading(true);
    try {
      const result = await paymentService.getPayments(filters, page);
      setPayments(result.data, result.pagination);
      setError(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to load payments");
    } finally {
      setLoading(false);
    }
  }, [paymentService, setPayments, setLoading, setError]);

  // Download invoice PDF
  const downloadInvoicePDF = useCallback(async (invoiceId: string) => {
    if (!invoiceService) throw new Error("No invoice service available");
    
    try {
      const blob = await invoiceService.downloadInvoicePDF(invoiceId);
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `invoice-${invoiceId}.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to download invoice");
      throw error;
    }
  }, [invoiceService, setError]);

  // Send invoice
  const sendInvoice = useCallback(async (invoiceId: string, email?: string) => {
    if (!invoiceService) throw new Error("No invoice service available");
    
    setLoading(true);
    try {
      await invoiceService.sendInvoice(invoiceId, email);
      setError(null);
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to send invoice";
      setError(message);
      throw new Error(message);
    } finally {
      setLoading(false);
    }
  }, [invoiceService, setLoading, setError]);

  // Update subscription plan
  const updateSubscriptionPlan = useCallback(async (subscriptionId: string, newPriceId: string) => {
    if (!billingService) throw new Error("No billing service available");
    
    setLoading(true);
    try {
      await billingService.updateSubscriptionPlan(subscriptionId, newPriceId);
      await loadBillingData(); // Reload data
      setError(null);
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to update subscription plan";
      setError(message);
      throw new Error(message);
    } finally {
      setLoading(false);
    }
  }, [billingService, loadBillingData, setLoading, setError]);

  // Cancel subscription
  const cancelSubscription = useCallback(async (subscriptionId: string, cancelAtPeriodEnd: boolean = true) => {
    if (!billingService) throw new Error("No billing service available");
    
    setLoading(true);
    try {
      await billingService.cancelSubscription(subscriptionId, cancelAtPeriodEnd);
      await loadBillingData(); // Reload data
      setError(null);
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to cancel subscription";
      setError(message);
      throw new Error(message);
    } finally {
      setLoading(false);
    }
  }, [billingService, loadBillingData, setLoading, setError]);

  // Auto-load data when tenant changes
  useEffect(() => {
    if (tenantId) {
      loadBillingData();
    }
  }, [tenantId, loadBillingData]);

  return {
    invoices,
    payments,
    settings,
    stats,
    usage,
    isLoading,
    error,
    totalRevenue: getTotalRevenue(),
    pendingAmount: getPendingAmount(),
    overdueInvoices: getOverdueInvoices(),
    recentPayments: getRecentPayments(),
    loadBillingData,
    loadInvoices,
    loadPayments,
    downloadInvoicePDF,
    sendInvoice,
    updateSubscriptionPlan,
    cancelSubscription,
  };
}

// Convenience hooks
export function useInvoices() {
  const { invoices, loadInvoices, downloadInvoicePDF, sendInvoice } = useBilling();
  return { invoices, loadInvoices, downloadInvoicePDF, sendInvoice };
}

export function usePayments() {
  const { payments, loadPayments } = useBilling();
  return { payments, loadPayments };
}

export function useBillingStats() {
  const { stats, totalRevenue, pendingAmount, overdueInvoices } = useBilling();
  return { stats, totalRevenue, pendingAmount, overdueInvoices };
}

export function useUsageMetrics() {
  const { usage } = useBilling();
  return usage;
}
