{"name": "@nexus/constants", "version": "0.1.0", "description": "Application constants for Nexus SaaS", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "typescript": "^5.8.0"}}