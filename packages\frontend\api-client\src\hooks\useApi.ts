import { useState, useCallback, useRef, useEffect } from "react";
import { ApiError } from "../types";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RetryManager } from "../utils";

interface UseApiOptions<T> {
  onSuccess?: (data: T) => void;
  onError?: (error: ApiError) => void;
  retry?: boolean;
  retryAttempts?: number;
  retryDelay?: number;
}

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: ApiError | null;
}

interface UseApiReturn<T, P extends any[] = []> extends UseApiState<T> {
  execute: (...params: P) => Promise<T>;
  reset: () => void;
  cancel: () => void;
}

export function useApi<T, P extends any[] = []>(
  apiFunction: (...params: P) => Promise<T>,
  options: UseApiOptions<T> = {}
): UseApiReturn<T, P> {
  const {
    onSuccess,
    onError,
    retry = false,
    retryAttempts = 3,
    retryDelay = 1000,
  } = options;

  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const cancelRef = useRef<AbortController | null>(null);
  const errorHandler = ErrorHandler.getInstance();
  const retryManager = RetryManager.getInstance();

  const execute = useCallback(
    async (...params: P): Promise<T> => {
      // Cancel previous request
      if (cancelRef.current) {
        cancelRef.current.abort();
      }

      // Create new abort controller
      cancelRef.current = new AbortController();

      setState(prev => ({
        ...prev,
        loading: true,
        error: null,
      }));

      try {
        let result: T;

        if (retry) {
          result = await retryManager.retry(
            () => apiFunction(...params),
            {
              maxAttempts: retryAttempts,
              baseDelay: retryDelay,
              retryCondition: (error) => retryManager.isRetryableError(error),
            }
          );
        } else {
          result = await apiFunction(...params);
        }

        // Check if request was cancelled
        if (cancelRef.current?.signal.aborted) {
          throw new Error("Request cancelled");
        }

        setState({
          data: result,
          loading: false,
          error: null,
        });

        onSuccess?.(result);
        return result;
      } catch (error: any) {
        // Don't update state if request was cancelled
        if (cancelRef.current?.signal.aborted) {
          return Promise.reject(error);
        }

        const apiError: ApiError = {
          message: error.message || "An error occurred",
          code: error.code || "API_ERROR",
          status: error.status,
          details: error.details,
        };

        setState({
          data: null,
          loading: false,
          error: apiError,
        });

        errorHandler.handleError(apiError);
        onError?.(apiError);
        
        throw apiError;
      }
    },
    [apiFunction, onSuccess, onError, retry, retryAttempts, retryDelay, errorHandler, retryManager]
  );

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    });
  }, []);

  const cancel = useCallback(() => {
    if (cancelRef.current) {
      cancelRef.current.abort();
      setState(prev => ({
        ...prev,
        loading: false,
      }));
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (cancelRef.current) {
        cancelRef.current.abort();
      }
    };
  }, []);

  return {
    ...state,
    execute,
    reset,
    cancel,
  };
}

// Hook for automatic API calls
export function useApiAuto<T, P extends any[] = []>(
  apiFunction: (...params: P) => Promise<T>,
  params: P,
  options: UseApiOptions<T> & {
    enabled?: boolean;
    refetchInterval?: number;
    refetchOnWindowFocus?: boolean;
  } = {}
): UseApiReturn<T, P> {
  const {
    enabled = true,
    refetchInterval,
    refetchOnWindowFocus = false,
    ...apiOptions
  } = options;

  const api = useApi(apiFunction, apiOptions);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-execute on mount and when params change
  useEffect(() => {
    if (enabled) {
      api.execute(...params);
    }
  }, [enabled, ...params]);

  // Set up refetch interval
  useEffect(() => {
    if (refetchInterval && enabled) {
      intervalRef.current = setInterval(() => {
        api.execute(...params);
      }, refetchInterval);

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [refetchInterval, enabled, ...params]);

  // Refetch on window focus
  useEffect(() => {
    if (refetchOnWindowFocus && enabled) {
      const handleFocus = () => {
        api.execute(...params);
      };

      window.addEventListener("focus", handleFocus);
      return () => window.removeEventListener("focus", handleFocus);
    }
  }, [refetchOnWindowFocus, enabled, ...params]);

  return api;
}

// Hook for mutations (POST, PUT, DELETE operations)
export function useMutation<T, P extends any[] = []>(
  mutationFunction: (...params: P) => Promise<T>,
  options: UseApiOptions<T> & {
    invalidateQueries?: string[];
  } = {}
): UseApiReturn<T, P> & {
  mutate: (...params: P) => Promise<T>;
  mutateAsync: (...params: P) => Promise<T>;
} {
  const { invalidateQueries, ...apiOptions } = options;
  const api = useApi(mutationFunction, {
    ...apiOptions,
    onSuccess: (data) => {
      // TODO: Implement query invalidation
      // This would typically invalidate React Query or SWR cache
      options.onSuccess?.(data);
    },
  });

  return {
    ...api,
    mutate: api.execute,
    mutateAsync: api.execute,
  };
}

// Hook for paginated data
export function usePaginatedApi<T>(
  apiFunction: (page: number, limit: number, ...params: any[]) => Promise<{
    items: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }>,
  params: any[] = [],
  options: {
    initialPage?: number;
    pageSize?: number;
    enabled?: boolean;
  } = {}
) {
  const { initialPage = 1, pageSize = 20, enabled = true } = options;
  
  const [page, setPage] = useState(initialPage);
  const [allItems, setAllItems] = useState<T[]>([]);
  const [pagination, setPagination] = useState<any>(null);

  const api = useApi(
    useCallback(
      (currentPage: number) => apiFunction(currentPage, pageSize, ...params),
      [apiFunction, pageSize, ...params]
    ),
    {
      onSuccess: (data) => {
        if (page === 1) {
          setAllItems(data.items);
        } else {
          setAllItems(prev => [...prev, ...data.items]);
        }
        setPagination(data.pagination);
      },
    }
  );

  const loadMore = useCallback(() => {
    if (pagination?.hasNext) {
      const nextPage = page + 1;
      setPage(nextPage);
      api.execute(nextPage);
    }
  }, [pagination?.hasNext, page, api]);

  const refresh = useCallback(() => {
    setPage(1);
    setAllItems([]);
    setPagination(null);
    api.execute(1);
  }, [api]);

  // Auto-execute on mount
  useEffect(() => {
    if (enabled) {
      api.execute(page);
    }
  }, [enabled]);

  return {
    items: allItems,
    pagination,
    loading: api.loading,
    error: api.error,
    loadMore,
    refresh,
    hasMore: pagination?.hasNext || false,
  };
}
