# NEXUS SaaS Starter - Plugin Architecture Implementation

**PRP Name**: Plugin Architecture - Extensible Plugin System  
**Version**: 1.0  
**Date**: July 18, 2025  
**Type**: Ecosystem & Extensions Implementation PRP  
**Phase**: 06-Ecosystem  
**Framework**: Next.js 15.4+ / TypeScript 5.8+ / Better-Auth / Multi-Tenant  

---

## Purpose

Build a comprehensive, secure, and extensible plugin architecture that allows developers to extend the NEXUS SaaS Starter with custom functionality while maintaining multi-tenant isolation, security boundaries, and seamless integration with existing systems.

## Core Principles

- **Multi-Tenant Aware**: Every plugin operation respects workspace isolation
- **Security First**: Sandboxed execution with capability-based permissions
- **Type Safety**: Full TypeScript support with plugin contracts and interfaces
- **Developer Experience**: Rich SDK, CLI tools, and development environment
- **Performance**: Resource monitoring, limits, and efficient plugin loading
- **Integration**: Seamless integration with existing better-auth, webhook, and middleware systems

---

## Research & Documentation

### Context7-Verified Patterns (CRITICAL)

```yaml
# Plugin Architecture Patterns (Context7 Verified)
dotnet_core_plugins:
  - url: /natemcmaster/dotnetcoreplugins
    why: "Production-ready plugin loading, shared types, and isolation patterns"
    critical: "PluginLoader.CreateFromAssemblyFile, shared types, contextual reflection"
    patterns: ["Plugin discovery", "Type unification", "Assembly isolation", "Plugin lifecycle"]

software_architecture_patterns:
  - url: /mehdihadeli/awesome-software-architecture
    why: "Enterprise plugin architecture patterns and best practices"
    critical: "Clean architecture, dependency injection, plugin boundaries"
    patterns: ["Plugin interfaces", "Dependency injection", "Event-driven architecture"]

better_auth_plugins:
  - url: https://better-auth.com/docs/plugins
    why: "Existing plugin system foundation in current codebase"
    critical: "Hook system, middleware integration, plugin registration"
    patterns: ["Auth hooks", "Plugin lifecycle", "Middleware integration"]
```

### Current Codebase Integration Points

```typescript
// Existing better-auth plugin pattern (CRITICAL FOUNDATION)
// From: PRPs/features/01-foundation/authentication-middleware-implementation.md
export const authenticationPlugin = () => {
  return {
    id: "authentication-middleware",
    hooks: {
      before: [
        {
          matcher: (context) => context.path.startsWith("/sign-in"),
          handler: async (context) => {
            // Plugin hook implementation
          }
        }
      ]
    }
  }
}

// Multi-tenant context pattern (CRITICAL FOR ISOLATION)
// From: PRPs/features/01-foundation/multi-tenant-database-architecture-implementation.md
interface TenantContext {
  tenantId: string;
  workspaceId: string;
  userId: string;
  permissions: string[];
}

// Webhook system integration (CRITICAL FOR EVENTS)
// From: PRPs/features/03-enterprise/webhook-system-implementation.md
interface WebhookConfig {
  id: string;
  tenantId: string;
  url: string;
  events: string[];
  secret: string;
  active: boolean;
}
```

### Technology Stack Context

```yaml
Core Framework:
  - Next.js: 15.4+ (App Router, Server Components, Middleware)
  - React: 19 (Server Components, Concurrent Features)
  - TypeScript: 5.8+ (Advanced type system, decorators)
  - Tailwind CSS: 4.1.11+ (Styling system)

Backend Services:
  - PostgreSQL: Multi-tenant database with row-level security
  - Prisma: ORM with multi-tenant extensions
  - Better-Auth: Authentication with existing plugin system
  - Supabase: Real-time subscriptions and edge functions

Plugin Runtime:
  - Node.js: VM module for sandboxing
  - Worker Threads: Isolated plugin execution
  - Dynamic Imports: Runtime plugin loading
  - TypeScript Compiler API: Plugin validation and compilation
```

---

## Data Models and Structure

### Database Schema (Prisma)

```typescript
// Multi-tenant plugin registry
model Plugin {
  id          String   @id @default(cuid())
  
  // Plugin Identity
  name        String   // e.g., "slack-integration"
  displayName String   // e.g., "Slack Integration"
  version     String   // Semantic versioning
  description String
  
  // Plugin Metadata
  author      String
  homepage    String?
  repository  String?
  license     String   @default("MIT")
  
  // Plugin Configuration
  manifest    Json     // Plugin manifest with capabilities, dependencies
  config      Json?    // Default configuration schema
  
  // Plugin Status
  isPublic    Boolean  @default(false)
  isVerified  Boolean  @default(false)
  isActive    Boolean  @default(true)
  
  // Plugin Files
  packageUrl  String?  // URL to plugin package (npm, GitHub, etc.)
  sourceCode  String?  // Inline source code for simple plugins
  
  // Audit
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  installations PluginInstallation[]
  
  @@unique([name, version])
  @@index([isPublic, isActive])
  @@index([author])
}

// Workspace-specific plugin installations
model PluginInstallation {
  id          String   @id @default(cuid())
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  pluginId    String
  plugin      Plugin   @relation(fields: [pluginId], references: [id], onDelete: Cascade)
  
  // Installation Configuration
  config      Json?    // Workspace-specific plugin configuration
  isEnabled   Boolean  @default(true)
  
  // Installation Metadata
  installedBy String   // User ID who installed the plugin
  installedAt DateTime @default(now())
  
  // Plugin Permissions
  permissions Json     // Granted permissions for this installation
  
  // Audit
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@unique([workspaceId, pluginId])
  @@index([workspaceId, isEnabled])
  @@index([pluginId])
}

// Plugin execution logs and metrics
model PluginExecution {
  id          String   @id @default(cuid())
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  pluginId    String
  plugin      Plugin   @relation(fields: [pluginId], references: [id], onDelete: Cascade)
  
  // Execution Context
  executionId String   // Unique execution identifier
  hookName    String?  // Hook that triggered execution
  eventType   String?  // Event type that triggered execution
  
  // Execution Results
  status      String   // success, error, timeout
  duration    Int      // Execution time in milliseconds
  memoryUsage Int?     // Memory usage in bytes
  
  // Error Information
  error       String?  // Error message if failed
  stackTrace  String?  // Stack trace for debugging
  
  // Audit
  executedAt  DateTime @default(now())
  
  @@index([workspaceId, pluginId])
  @@index([status])
  @@index([executedAt])
}
```

### Plugin Manifest Schema

```typescript
// Plugin manifest structure (plugin.json)
interface PluginManifest {
  // Plugin Identity
  name: string;
  version: string;
  displayName: string;
  description: string;
  
  // Plugin Metadata
  author: string;
  homepage?: string;
  repository?: string;
  license: string;
  
  // Plugin Capabilities
  capabilities: {
    // API capabilities
    api?: {
      endpoints?: string[];      // Custom API endpoints
      middleware?: string[];     // Middleware hooks
      webhooks?: string[];       // Webhook event subscriptions
    };
    
    // UI capabilities
    ui?: {
      dashboardWidgets?: string[];  // Dashboard widget components
      pages?: string[];             // Custom pages
      components?: string[];        // Reusable components
    };
    
    // System capabilities
    system?: {
      hooks?: string[];          // System hooks (auth, billing, etc.)
      jobs?: string[];           // Background job handlers
      events?: string[];         // Event listeners
    };
  };
  
  // Plugin Dependencies
  dependencies?: {
    core?: string;               // Minimum core version
    plugins?: Record<string, string>;  // Required plugins
    npm?: Record<string, string>;      // NPM dependencies
  };
  
  // Plugin Configuration Schema
  configSchema?: {
    type: "object";
    properties: Record<string, any>;
    required?: string[];
  };
  
  // Plugin Permissions
  permissions: {
    database?: string[];         // Database access permissions
    api?: string[];             // API access permissions
    ui?: string[];              // UI access permissions
    external?: string[];        // External service permissions
  };
  
  // Plugin Entry Points
  entryPoints: {
    server?: string;            // Server-side entry point
    client?: string;            // Client-side entry point
    worker?: string;            // Worker thread entry point
  };
}
```

---

## Implementation Blueprint

### Task Breakdown (Information-Dense Implementation)

**Phase 1: Core Plugin System (4-6 hours)**

Task 1: Plugin Registry and Database Schema
CREATE src/lib/plugins/plugin-registry.ts:
  - IMPLEMENT PluginRegistry class with CRUD operations
  - CREATE plugin discovery and validation logic
  - IMPLEMENT version management and compatibility checking
  - ADD plugin manifest parsing and validation
  - SETUP plugin installation and uninstallation workflows

CREATE prisma/migrations/add-plugin-system.sql:
  - ADD Plugin, PluginInstallation, PluginExecution tables
  - IMPLEMENT multi-tenant indexes and constraints
  - CREATE plugin audit triggers
  - SETUP plugin cleanup procedures

Task 2: Plugin Runtime and Execution Engine
CREATE src/lib/plugins/plugin-runtime.ts:
  - IMPLEMENT secure plugin execution environment
  - CREATE plugin sandboxing with VM module
  - ADD resource monitoring and limits
  - IMPLEMENT plugin lifecycle management
  - CREATE plugin error handling and fault isolation

CREATE src/lib/plugins/plugin-loader.ts:
  - IMPLEMENT dynamic plugin loading with import()
  - CREATE plugin compilation and validation
  - ADD plugin dependency resolution
  - IMPLEMENT plugin hot-reloading for development
  - CREATE plugin cache management

**Phase 2: Plugin API and Integration (3-4 hours)**

Task 3: Plugin API Framework
CREATE src/lib/plugins/plugin-api.ts:
  - IMPLEMENT PluginAPI base class with core utilities
  - CREATE plugin context injection (tenant, user, permissions)
  - ADD plugin communication and event system
  - IMPLEMENT plugin data storage and retrieval
  - CREATE plugin configuration management

CREATE src/lib/plugins/plugin-hooks.ts:
  - EXTEND existing better-auth hook system
  - IMPLEMENT system hooks (auth, billing, user management)
  - CREATE UI hooks (dashboard, components, pages)
  - ADD API hooks (middleware, endpoints, validation)
  - IMPLEMENT webhook integration for plugin events

Task 4: Plugin SDK and Developer Tools
CREATE packages/plugin-sdk/:
  - IMPLEMENT PluginSDK with TypeScript definitions
  - CREATE plugin base classes and interfaces
  - ADD utility functions and helpers
  - IMPLEMENT plugin testing framework
  - CREATE plugin development CLI tools

CREATE packages/plugin-sdk/src/base-plugin.ts:
  - IMPLEMENT BasePlugin abstract class
  - CREATE plugin lifecycle methods (install, enable, disable)
  - ADD plugin configuration and validation
  - IMPLEMENT plugin error handling
  - CREATE plugin logging and metrics

**Phase 3: Plugin Management UI (3-4 hours)**

Task 5: Plugin Management Dashboard
CREATE src/app/(dashboard)/plugins/:
  - IMPLEMENT plugin marketplace browser
  - CREATE plugin installation and configuration UI
  - ADD plugin status and monitoring dashboard
  - IMPLEMENT plugin permissions management
  - CREATE plugin development tools interface

CREATE src/components/plugins/:
  - IMPLEMENT PluginCard component for plugin display
  - CREATE PluginInstaller component with progress tracking
  - ADD PluginConfigurator for plugin settings
  - IMPLEMENT PluginMetrics for performance monitoring
  - CREATE PluginPermissions for security management

Task 6: Plugin Widget System
CREATE src/lib/plugins/widget-system.ts:
  - IMPLEMENT dashboard widget registration
  - CREATE widget rendering and lifecycle management
  - ADD widget configuration and customization
  - IMPLEMENT widget data binding and updates
  - CREATE widget security and isolation

**Phase 4: Security and Multi-Tenancy (2-3 hours)**

Task 7: Plugin Security Framework
CREATE src/lib/plugins/plugin-security.ts:
  - IMPLEMENT plugin permission system
  - CREATE capability-based access control
  - ADD plugin sandboxing and resource limits
  - IMPLEMENT plugin audit logging
  - CREATE security scanning and validation

Task 8: Multi-Tenant Plugin Isolation
CREATE src/lib/plugins/tenant-isolation.ts:
  - IMPLEMENT workspace-aware plugin execution
  - CREATE tenant data isolation for plugins
  - ADD plugin configuration per workspace
  - IMPLEMENT plugin billing and usage tracking
  - CREATE tenant-specific plugin permissions

**Phase 5: Plugin Marketplace Integration (2-3 hours)**

Task 9: Plugin Marketplace API
CREATE src/app/api/plugins/:
  - IMPLEMENT plugin discovery and search API
  - CREATE plugin installation and management endpoints
  - ADD plugin metrics and analytics API
  - IMPLEMENT plugin review and rating system
  - CREATE plugin publishing and distribution API

Task 10: Plugin Development Workflow
CREATE tools/plugin-cli/:
  - IMPLEMENT plugin scaffolding and templates
  - CREATE plugin build and packaging tools
  - ADD plugin testing and validation
  - IMPLEMENT plugin publishing workflow
  - CREATE plugin documentation generation

---

## Integration Points

### 1. Better-Auth Plugin System Integration

```typescript
// Extend existing better-auth plugin system
// src/lib/plugins/auth-integration.ts
import { betterAuth } from "better-auth";
import { PluginRegistry } from "./plugin-registry";

export function createAuthPluginIntegration() {
  return {
    id: "nexus-plugin-system",

    hooks: {
      before: [
        {
          matcher: (context) => true, // All auth operations
          handler: async (context) => {
            // Execute auth-related plugins
            const plugins = await PluginRegistry.getEnabledPlugins(
              context.workspaceId,
              'auth'
            );

            for (const plugin of plugins) {
              await plugin.executeHook('auth.before', context);
            }
          }
        }
      ],

      after: [
        {
          matcher: (context) => true,
          handler: async (context) => {
            // Execute post-auth plugins
            const plugins = await PluginRegistry.getEnabledPlugins(
              context.workspaceId,
              'auth'
            );

            for (const plugin of plugins) {
              await plugin.executeHook('auth.after', context);
            }
          }
        }
      ]
    }
  }
}
```

### 2. Webhook System Integration

```typescript
// Integrate with existing webhook system
// src/lib/plugins/webhook-integration.ts
import { WebhookService } from "../webhooks/webhook-service";
import { PluginRegistry } from "./plugin-registry";

export class PluginWebhookIntegration {
  static async handleWebhookEvent(
    workspaceId: string,
    eventType: string,
    payload: any
  ) {
    // Get plugins subscribed to this event type
    const plugins = await PluginRegistry.getPluginsByEvent(
      workspaceId,
      eventType
    );

    // Execute plugins in parallel with error isolation
    const results = await Promise.allSettled(
      plugins.map(plugin =>
        plugin.executeWebhookHandler(eventType, payload)
      )
    );

    // Log plugin execution results
    await this.logPluginExecutions(workspaceId, eventType, results);
  }

  static async registerPluginWebhooks(
    workspaceId: string,
    pluginId: string,
    webhookEvents: string[]
  ) {
    // Register plugin webhook subscriptions
    for (const eventType of webhookEvents) {
      await WebhookService.subscribe(workspaceId, eventType, {
        handler: 'plugin',
        pluginId,
        metadata: { source: 'plugin-system' }
      });
    }
  }
}
```

### 3. Multi-Tenant Middleware Integration

```typescript
// Integrate with existing tenant middleware
// src/middleware.ts (Enhanced)
import { NextRequest, NextResponse } from 'next/server';
import { PluginMiddleware } from './lib/plugins/plugin-middleware';

export async function middleware(request: NextRequest) {
  // Existing middleware logic...
  const tenantContext = await getTenantContext(request);

  // Execute plugin middleware
  const pluginResponse = await PluginMiddleware.execute(
    request,
    tenantContext
  );

  if (pluginResponse) {
    return pluginResponse; // Plugin handled the request
  }

  // Continue with normal middleware flow
  return NextResponse.next();
}
```

### 4. API Route Plugin Integration

```typescript
// Dynamic API route registration for plugins
// src/lib/plugins/api-integration.ts
export class PluginAPIIntegration {
  static async registerPluginRoutes(
    workspaceId: string,
    pluginId: string,
    routes: PluginRoute[]
  ) {
    for (const route of routes) {
      // Register dynamic API route
      await this.createDynamicRoute(workspaceId, pluginId, route);
    }
  }

  private static async createDynamicRoute(
    workspaceId: string,
    pluginId: string,
    route: PluginRoute
  ) {
    // Create Next.js API route handler
    const handler = async (request: Request) => {
      // Validate plugin permissions
      await this.validatePluginPermissions(workspaceId, pluginId, route);

      // Execute plugin route handler
      const plugin = await PluginRegistry.getPlugin(pluginId);
      return await plugin.executeRouteHandler(route.path, request);
    };

    // Register route with Next.js router
    await this.registerRoute(route.path, route.method, handler);
  }
}
```

---

## Security Implementation

### 1. Plugin Sandboxing and Isolation

```typescript
// Secure plugin execution environment
// src/lib/plugins/plugin-sandbox.ts
import { Worker } from 'worker_threads';
import { VM } from 'vm2';

export class PluginSandbox {
  private static readonly MEMORY_LIMIT = 128 * 1024 * 1024; // 128MB
  private static readonly TIMEOUT = 30000; // 30 seconds

  static async executePlugin(
    pluginCode: string,
    context: PluginExecutionContext,
    permissions: PluginPermissions
  ): Promise<PluginExecutionResult> {
    // Create isolated VM context
    const vm = new VM({
      timeout: this.TIMEOUT,
      sandbox: this.createSandbox(context, permissions),
      eval: false,
      wasm: false,
      fixAsync: true
    });

    try {
      // Execute plugin in sandbox
      const result = await vm.run(pluginCode);

      return {
        success: true,
        result,
        memoryUsage: process.memoryUsage().heapUsed,
        duration: Date.now() - context.startTime
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        stackTrace: error.stack,
        duration: Date.now() - context.startTime
      };
    }
  }

  private static createSandbox(
    context: PluginExecutionContext,
    permissions: PluginPermissions
  ) {
    return {
      // Provide safe APIs based on permissions
      console: this.createSafeConsole(context.pluginId),
      fetch: permissions.external ? this.createSafeFetch() : undefined,
      database: permissions.database ? this.createSafeDatabase(context) : undefined,

      // Plugin context
      context: {
        workspaceId: context.workspaceId,
        userId: context.userId,
        pluginId: context.pluginId,
        config: context.config
      }
    };
  }
}
```

### 2. Plugin Permission System

```typescript
// Capability-based plugin permissions
// src/lib/plugins/plugin-permissions.ts
export class PluginPermissionSystem {
  static async validatePermissions(
    workspaceId: string,
    pluginId: string,
    requiredPermissions: string[]
  ): Promise<boolean> {
    const installation = await this.getPluginInstallation(workspaceId, pluginId);
    const grantedPermissions = installation.permissions as string[];

    // Check if all required permissions are granted
    return requiredPermissions.every(permission =>
      grantedPermissions.includes(permission)
    );
  }

  static async grantPermissions(
    workspaceId: string,
    pluginId: string,
    permissions: string[],
    grantedBy: string
  ): Promise<void> {
    // Validate user has permission to grant these permissions
    await this.validateUserCanGrantPermissions(workspaceId, grantedBy, permissions);

    // Update plugin installation permissions
    await prisma.pluginInstallation.update({
      where: {
        workspaceId_pluginId: { workspaceId, pluginId }
      },
      data: {
        permissions: permissions,
        updatedAt: new Date()
      }
    });

    // Log permission change for audit
    await this.logPermissionChange(workspaceId, pluginId, permissions, grantedBy);
  }

  static readonly PERMISSION_CATEGORIES = {
    DATABASE: ['read', 'write', 'admin'],
    API: ['read', 'write', 'webhook'],
    UI: ['dashboard', 'pages', 'components'],
    EXTERNAL: ['http', 'email', 'storage'],
    SYSTEM: ['hooks', 'jobs', 'events']
  } as const;
}
```

---

## Validation Gates (Executable Testing)

### Level 1: Syntax & Style Validation
```bash
# TypeScript compilation and linting
npm run lint                    # ESLint checks for plugin system
npx tsc --noEmit               # TypeScript type checking
npm run type-check             # Plugin SDK type validation

# Plugin-specific validation
npm run validate-plugins       # Plugin manifest validation
npm run security-scan          # Plugin security scanning
```

### Level 2: Unit Testing
```bash
# Core plugin system tests
npm test src/lib/plugins/      # Plugin registry and runtime tests
npm test packages/plugin-sdk/  # Plugin SDK tests
npm test src/components/plugins/ # Plugin UI component tests

# Plugin isolation tests
npm run test:sandbox           # Plugin sandboxing tests
npm run test:permissions       # Permission system tests
npm run test:multi-tenant      # Multi-tenant isolation tests
```

### Level 3: Integration Testing
```bash
# Start development server
npm run dev

# Test plugin installation and execution
curl -X POST http://localhost:3000/api/plugins/install \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{"pluginId": "test-plugin", "workspaceId": "workspace-123"}'

# Test plugin API endpoints
curl -X GET http://localhost:3000/api/plugins/workspace-123/test-plugin/status

# Test plugin webhook integration
curl -X POST http://localhost:3000/api/webhooks/plugin-event \
  -H "Content-Type: application/json" \
  -d '{"eventType": "user.created", "workspaceId": "workspace-123"}'
```

### Level 4: End-to-End Plugin Testing
```bash
# Production build validation
npm run build                  # Build with plugin system
npm run start                  # Production server testing

# Plugin marketplace testing
npm run test:e2e:plugins       # Playwright E2E tests for plugin system
npm run test:performance       # Plugin performance testing
npm run test:security          # Security penetration testing
```

### Level 5: Plugin Development Testing
```bash
# Plugin SDK testing
cd packages/plugin-sdk
npm run test                   # SDK unit tests
npm run build                  # SDK build validation

# Plugin CLI testing
npx @nexus-saas/plugin-cli create test-plugin
cd test-plugin
npm run build                  # Plugin build testing
npm run test                   # Plugin-specific tests
npm run validate               # Plugin validation
```

---

## Quality Standards Checklist

- [x] **Multi-tenant isolation**: All plugin operations include workspace context
- [x] **Security boundaries**: Plugin sandboxing and permission system
- [x] **Type safety**: Full TypeScript support with plugin contracts
- [x] **Performance monitoring**: Resource limits and execution metrics
- [x] **Developer experience**: Rich SDK, CLI tools, and documentation
- [x] **Integration**: Seamless integration with existing systems
- [x] **Scalability**: Efficient plugin loading and execution
- [x] **Audit compliance**: Comprehensive logging and monitoring
- [x] **Error handling**: Fault isolation and graceful degradation
- [x] **Testing coverage**: Unit, integration, and E2E testing

---

**Framework**: NEXUS SaaS Starter Multi-Tenant Plugin Architecture
**Technology Stack**: Next.js 15.4+ / TypeScript 5.8+ / Better-Auth / PostgreSQL
**Optimization**: Production-ready, enterprise-grade, extensible plugin ecosystem
