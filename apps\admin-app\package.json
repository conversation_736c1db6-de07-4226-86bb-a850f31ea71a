{"name": "@nexus/admin-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3001", "build": "next build", "start": "next start --port 3001", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "@nexus/utils": "workspace:*", "@nexus/validation": "workspace:*", "@nexus/database-schema": "workspace:*", "@nexus/database-service": "workspace:*", "@nexus/auth-client": "workspace:*", "@nexus/auth-server": "workspace:*", "@nexus/tenant-context": "workspace:*", "@radix-ui/react-accordion": "1.2.11", "@radix-ui/react-alert-dialog": "1.1.14", "@radix-ui/react-avatar": "1.1.10", "@radix-ui/react-checkbox": "1.3.2", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-popover": "1.1.14", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-switch": "1.2.5", "@radix-ui/react-tabs": "1.1.12", "@radix-ui/react-tooltip": "1.2.7", "@tanstack/react-query": "^5.83.0", "@tanstack/react-table": "^8.21.3", "better-auth": "^1.2.12", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.1.1", "date-fns": "4.1.0", "framer-motion": "^12.23.6", "lucide-react": "0.525.0", "next": "15.4.2", "next-themes": "0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "7.60.0", "recharts": "3.1.0", "sonner": "2.0.6", "tailwind-merge": "3.3.1", "zod": "4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@nexus/eslint-config": "workspace:*", "@nexus/prettier-config": "workspace:*", "@nexus/tsconfig": "workspace:*", "@eslint/eslintrc": "3.3.1", "@tailwindcss/postcss": "4.1.11", "@types/node": "24.0.15", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "eslint": "9.31.0", "eslint-config-next": "15.4.2", "tailwindcss": "4.1.11", "tw-animate-css": "1.3.5", "typescript": "5.8.3"}}