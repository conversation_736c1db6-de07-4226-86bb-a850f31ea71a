// Search and analytics types

export interface SearchDocument {
  id: string;
  type: DocumentType;
  tenantId: string;
  workspaceId: string;
  projectId?: string;
  title: string;
  content: string;
  summary?: string;
  author: {
    id: string;
    name: string;
    email: string;
  };
  tags: string[];
  categories: string[];
  metadata: Record<string, any>;
  permissions: {
    public: boolean;
    users: string[];
    teams: string[];
    workspaces: string[];
  };
  status: DocumentStatus;
  language: string;
  createdAt: Date;
  updatedAt: Date;
  indexedAt: Date;
}

export type DocumentType = 
  | "file"
  | "project"
  | "task"
  | "comment"
  | "message"
  | "user"
  | "team"
  | "workspace"
  | "wiki"
  | "note";

export type DocumentStatus = 
  | "active"
  | "archived"
  | "deleted"
  | "draft";

export interface SearchQuery {
  query: string;
  filters?: SearchFilters;
  sort?: SearchSort[];
  pagination?: {
    page: number;
    size: number;
  };
  highlight?: {
    enabled: boolean;
    fields: string[];
    fragmentSize?: number;
    numberOfFragments?: number;
  };
  aggregations?: SearchAggregation[];
  suggestions?: {
    enabled: boolean;
    field: string;
  };
}

export interface SearchFilters {
  types?: DocumentType[];
  tenantId?: string;
  workspaceId?: string;
  projectId?: string;
  authorId?: string;
  tags?: string[];
  categories?: string[];
  status?: DocumentStatus[];
  language?: string[];
  dateRange?: {
    field: "createdAt" | "updatedAt";
    start?: Date;
    end?: Date;
  };
  permissions?: {
    userId: string;
    includePublic?: boolean;
  };
}

export interface SearchSort {
  field: string;
  order: "asc" | "desc";
  mode?: "min" | "max" | "sum" | "avg" | "median";
}

export interface SearchAggregation {
  name: string;
  type: "terms" | "date_histogram" | "range" | "stats";
  field: string;
  size?: number;
  interval?: string;
  ranges?: Array<{ from?: number; to?: number; key?: string }>;
}

export interface SearchResult {
  documents: SearchResultDocument[];
  total: {
    value: number;
    relation: "eq" | "gte";
  };
  maxScore: number;
  aggregations: Record<string, SearchAggregationResult>;
  suggestions: SearchSuggestion[];
  took: number;
}

export interface SearchResultDocument {
  id: string;
  type: DocumentType;
  score: number;
  source: SearchDocument;
  highlight?: Record<string, string[]>;
  sort?: any[];
}

export interface SearchAggregationResult {
  buckets?: Array<{
    key: string | number;
    docCount: number;
    [key: string]: any;
  }>;
  value?: number;
  values?: Record<string, number>;
}

export interface SearchSuggestion {
  text: string;
  offset: number;
  length: number;
  options: Array<{
    text: string;
    score: number;
    freq?: number;
  }>;
}

// Analytics types
export interface AnalyticsEvent {
  id: string;
  type: AnalyticsEventType;
  tenantId: string;
  workspaceId?: string;
  projectId?: string;
  userId?: string;
  sessionId?: string;
  properties: Record<string, any>;
  context: {
    userAgent?: string;
    ip?: string;
    referrer?: string;
    page?: string;
    timestamp: Date;
  };
  createdAt: Date;
}

export type AnalyticsEventType = 
  | "page_view"
  | "search_query"
  | "search_click"
  | "file_download"
  | "file_upload"
  | "project_create"
  | "team_join"
  | "user_login"
  | "user_signup"
  | "feature_use"
  | "error_occurred";

export interface AnalyticsQuery {
  eventTypes?: AnalyticsEventType[];
  tenantId?: string;
  workspaceId?: string;
  projectId?: string;
  userId?: string;
  dateRange: {
    start: Date;
    end: Date;
  };
  groupBy?: string[];
  metrics?: AnalyticsMetric[];
  filters?: Record<string, any>;
}

export interface AnalyticsMetric {
  name: string;
  type: "count" | "sum" | "avg" | "min" | "max" | "unique";
  field?: string;
}

export interface AnalyticsResult {
  data: Array<{
    timestamp?: Date;
    dimensions: Record<string, string>;
    metrics: Record<string, number>;
  }>;
  total: Record<string, number>;
  metadata: {
    query: AnalyticsQuery;
    executionTime: number;
    totalEvents: number;
  };
}

// Search analytics
export interface SearchAnalytics {
  totalQueries: number;
  uniqueUsers: number;
  averageResultsPerQuery: number;
  clickThroughRate: number;
  zeroResultQueries: number;
  topQueries: Array<{
    query: string;
    count: number;
    avgResults: number;
    clickThroughRate: number;
  }>;
  topResults: Array<{
    documentId: string;
    title: string;
    clicks: number;
    impressions: number;
    clickThroughRate: number;
  }>;
  queryTrends: Array<{
    date: string;
    queries: number;
    uniqueUsers: number;
  }>;
}

// Index management
export interface IndexConfig {
  name: string;
  settings: {
    numberOfShards: number;
    numberOfReplicas: number;
    refreshInterval: string;
    maxResultWindow: number;
    analysis: {
      analyzers: Record<string, any>;
      tokenizers: Record<string, any>;
      filters: Record<string, any>;
    };
  };
  mappings: {
    properties: Record<string, IndexFieldMapping>;
  };
}

export interface IndexFieldMapping {
  type: "text" | "keyword" | "date" | "long" | "double" | "boolean" | "object" | "nested";
  analyzer?: string;
  searchAnalyzer?: string;
  index?: boolean;
  store?: boolean;
  fields?: Record<string, IndexFieldMapping>;
  properties?: Record<string, IndexFieldMapping>;
}

export interface IndexStats {
  name: string;
  health: "green" | "yellow" | "red";
  status: "open" | "close";
  uuid: string;
  primaryShards: number;
  replicaShards: number;
  documentsCount: number;
  documentsDeleted: number;
  storeSize: string;
  primaryStoreSize: string;
}

// Configuration
export interface SearchConfig {
  elasticsearch: {
    nodes: string[];
    auth?: {
      username: string;
      password: string;
    };
    ssl?: {
      ca?: string;
      cert?: string;
      key?: string;
      rejectUnauthorized?: boolean;
    };
    requestTimeout: number;
    pingTimeout: number;
    maxRetries: number;
  };
  indices: {
    documents: string;
    analytics: string;
    suggestions: string;
  };
  search: {
    defaultSize: number;
    maxSize: number;
    highlightFragmentSize: number;
    highlightNumberOfFragments: number;
    suggestionSize: number;
    timeout: string;
  };
  indexing: {
    batchSize: number;
    flushInterval: number;
    refreshInterval: string;
    numberOfShards: number;
    numberOfReplicas: number;
  };
  analytics: {
    retentionDays: number;
    aggregationInterval: string;
    realTimeEnabled: boolean;
  };
  cache: {
    enabled: boolean;
    ttl: number;
    maxSize: number;
  };
}

// Text processing
export interface TextProcessor {
  extractKeywords(text: string, options?: KeywordExtractionOptions): string[];
  analyzeSentiment(text: string): SentimentAnalysis;
  extractEntities(text: string): EntityExtraction[];
  summarize(text: string, options?: SummarizationOptions): string;
  detectLanguage(text: string): LanguageDetection;
  tokenize(text: string, options?: TokenizationOptions): string[];
}

export interface KeywordExtractionOptions {
  maxKeywords?: number;
  minLength?: number;
  removeStopwords?: boolean;
  language?: string;
}

export interface SentimentAnalysis {
  score: number; // -1 to 1
  comparative: number;
  calculation: Array<{
    word: string;
    score: number;
  }>;
  tokens: string[];
  words: string[];
  positive: string[];
  negative: string[];
}

export interface EntityExtraction {
  text: string;
  type: "person" | "organization" | "location" | "date" | "money" | "misc";
  confidence: number;
  startOffset: number;
  endOffset: number;
}

export interface SummarizationOptions {
  maxSentences?: number;
  maxLength?: number;
  algorithm?: "textrank" | "frequency" | "position";
}

export interface LanguageDetection {
  language: string;
  confidence: number;
  alternatives: Array<{
    language: string;
    confidence: number;
  }>;
}

export interface TokenizationOptions {
  removeStopwords?: boolean;
  stemming?: boolean;
  lowercase?: boolean;
  removePunctuation?: boolean;
  language?: string;
}
