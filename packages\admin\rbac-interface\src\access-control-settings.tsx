"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { useIsAdmin } from "@nexus/rbac";

// Access control settings component
export function AccessControlSettings() {
  const isAdmin = useIsAdmin();
  const [settings, setSettings] = useState({
    enableAuditLogging: true,
    auditRetentionDays: 90,
    enablePermissionCaching: true,
    cacheExpirationMinutes: 15,
    enableRoleInheritance: true,
    maxRoleDepth: 5,
    enableSessionTimeout: true,
    sessionTimeoutMinutes: 480,
    enableMFA: false,
    mfaRequired: false,
    enableIPRestrictions: false,
    allowedIPs: "",
    enableTimeRestrictions: false,
    allowedHours: "09:00-17:00",
    enableFailedLoginLockout: true,
    maxFailedAttempts: 5,
    lockoutDurationMinutes: 30,
  });

  const { register, handleSubmit, formState: { errors } } = useForm({
    defaultValues: settings,
  });

  const onSubmit = async (data: any) => {
    try {
      // Save settings to API
      console.log("Saving settings:", data);
      setSettings(data);
      alert("Settings saved successfully!");
    } catch (error) {
      console.error("Failed to save settings:", error);
      alert("Failed to save settings");
    }
  };

  if (!isAdmin) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Access denied. Admin privileges required.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Access Control Settings</h1>
        <p className="text-gray-600">Configure security and access control policies for your organization</p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Audit & Logging */}
        <SettingsSection title="Audit & Logging" description="Configure audit logging and retention policies">
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register("enableAuditLogging")}
                className="mr-3"
              />
              <label className="text-sm font-medium">Enable audit logging</label>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Audit log retention (days)</label>
              <input
                type="number"
                {...register("auditRetentionDays", { min: 1, max: 365 })}
                className="w-32 px-3 py-2 border rounded-md"
              />
              <p className="text-xs text-gray-500 mt-1">How long to keep audit logs (1-365 days)</p>
            </div>
          </div>
        </SettingsSection>

        {/* Performance */}
        <SettingsSection title="Performance" description="Configure caching and performance optimizations">
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register("enablePermissionCaching")}
                className="mr-3"
              />
              <label className="text-sm font-medium">Enable permission caching</label>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Cache expiration (minutes)</label>
              <input
                type="number"
                {...register("cacheExpirationMinutes", { min: 1, max: 60 })}
                className="w-32 px-3 py-2 border rounded-md"
              />
              <p className="text-xs text-gray-500 mt-1">How long to cache permission results</p>
            </div>
          </div>
        </SettingsSection>

        {/* Role Management */}
        <SettingsSection title="Role Management" description="Configure role hierarchy and inheritance">
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register("enableRoleInheritance")}
                className="mr-3"
              />
              <label className="text-sm font-medium">Enable role inheritance</label>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Maximum role depth</label>
              <input
                type="number"
                {...register("maxRoleDepth", { min: 1, max: 10 })}
                className="w-32 px-3 py-2 border rounded-md"
              />
              <p className="text-xs text-gray-500 mt-1">Maximum levels of role inheritance</p>
            </div>
          </div>
        </SettingsSection>

        {/* Session Management */}
        <SettingsSection title="Session Management" description="Configure session timeouts and security">
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register("enableSessionTimeout")}
                className="mr-3"
              />
              <label className="text-sm font-medium">Enable session timeout</label>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Session timeout (minutes)</label>
              <input
                type="number"
                {...register("sessionTimeoutMinutes", { min: 15, max: 1440 })}
                className="w-32 px-3 py-2 border rounded-md"
              />
              <p className="text-xs text-gray-500 mt-1">Automatic logout after inactivity</p>
            </div>
          </div>
        </SettingsSection>

        {/* Multi-Factor Authentication */}
        <SettingsSection title="Multi-Factor Authentication" description="Configure MFA requirements">
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register("enableMFA")}
                className="mr-3"
              />
              <label className="text-sm font-medium">Enable MFA support</label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register("mfaRequired")}
                className="mr-3"
              />
              <label className="text-sm font-medium">Require MFA for all users</label>
            </div>
          </div>
        </SettingsSection>

        {/* Access Restrictions */}
        <SettingsSection title="Access Restrictions" description="Configure IP and time-based restrictions">
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register("enableIPRestrictions")}
                className="mr-3"
              />
              <label className="text-sm font-medium">Enable IP restrictions</label>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Allowed IP addresses</label>
              <textarea
                {...register("allowedIPs")}
                className="w-full px-3 py-2 border rounded-md"
                rows={3}
                placeholder="***********/24&#10;10.0.0.0/8"
              />
              <p className="text-xs text-gray-500 mt-1">One IP address or CIDR block per line</p>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register("enableTimeRestrictions")}
                className="mr-3"
              />
              <label className="text-sm font-medium">Enable time restrictions</label>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Allowed hours</label>
              <input
                type="text"
                {...register("allowedHours")}
                className="w-48 px-3 py-2 border rounded-md"
                placeholder="09:00-17:00"
              />
              <p className="text-xs text-gray-500 mt-1">Format: HH:MM-HH:MM (24-hour)</p>
            </div>
          </div>
        </SettingsSection>

        {/* Failed Login Protection */}
        <SettingsSection title="Failed Login Protection" description="Configure account lockout policies">
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register("enableFailedLoginLockout")}
                className="mr-3"
              />
              <label className="text-sm font-medium">Enable failed login lockout</label>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Maximum failed attempts</label>
              <input
                type="number"
                {...register("maxFailedAttempts", { min: 3, max: 10 })}
                className="w-32 px-3 py-2 border rounded-md"
              />
              <p className="text-xs text-gray-500 mt-1">Number of failed attempts before lockout</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Lockout duration (minutes)</label>
              <input
                type="number"
                {...register("lockoutDurationMinutes", { min: 5, max: 1440 })}
                className="w-32 px-3 py-2 border rounded-md"
              />
              <p className="text-xs text-gray-500 mt-1">How long to lock the account</p>
            </div>
          </div>
        </SettingsSection>

        {/* Save Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
          >
            Save Settings
          </button>
        </div>
      </form>
    </div>
  );
}

// Settings section component
function SettingsSection({
  title,
  description,
  children,
}: {
  title: string;
  description: string;
  children: React.ReactNode;
}) {
  return (
    <div className="bg-white border rounded-lg p-6">
      <div className="mb-4">
        <h3 className="text-lg font-semibold">{title}</h3>
        <p className="text-gray-600 text-sm">{description}</p>
      </div>
      {children}
    </div>
  );
}
