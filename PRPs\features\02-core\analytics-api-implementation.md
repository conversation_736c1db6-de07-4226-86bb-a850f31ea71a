# Analytics API Implementation

## Overview
Implement a comprehensive GraphQL Analytics API for the NEXUS SaaS Starter that provides type-safe data access, real-time subscriptions, and powerful query capabilities for analytics data. This implementation leverages Pothos GraphQL schema builder patterns to create a robust, scalable, and developer-friendly analytics API that integrates seamlessly with the Business Intelligence Features and Performance Monitoring systems.

## Context Research Summary

### GraphQL API Architecture (Pothos)
- **Type-Safe Schema Definition**: Strongly typed GraphQL schema with automatic validation
- **Code Generation**: Client-side type generation for consistent API contracts
- **Real-Time Subscriptions**: Live data updates through GraphQL subscriptions
- **Plugin Architecture**: Extensible system for adding custom functionality
- **Error Handling**: Structured error responses and validation
- **Performance Optimization**: Built-in caching and optimization strategies

### Analytics Integration (davidwells/analytics)
- **Multi-Provider Support**: Unified API for multiple analytics providers
- **Event Tracking**: Comprehensive event lifecycle management
- **Real-Time Processing**: Event streaming and data aggregation
- **Storage Utilities**: Efficient data storage and retrieval
- **Plugin System**: Extensible analytics capabilities

### Performance Monitoring Integration (Shopify React Native Performance)
- **Metrics Collection**: Real-time performance data collection
- **Data Aggregation**: Efficient data processing and aggregation
- **Query Optimization**: Optimized data access patterns
- **Caching Strategies**: Performance-optimized caching mechanisms
- **Error Handling**: Comprehensive error tracking and reporting

## Implementation Plan

### Phase 1: Core GraphQL Schema
1. **Base Schema Definition**
   - Analytics data types and interfaces
   - Query and mutation operations
   - Subscription definitions
   - Error handling and validation

2. **Type-Safe Code Generation**
   - Automatic TypeScript type generation
   - Client-side query builders
   - Schema validation and testing
   - Documentation generation

### Phase 2: Advanced Query Capabilities
1. **Flexible Data Queries**
   - Dynamic filtering and sorting
   - Aggregation and grouping
   - Time-based queries and ranges
   - Cross-metric correlations

2. **Real-Time Subscriptions**
   - Live analytics updates
   - Event-driven notifications
   - Connection management
   - Subscription filtering

### Phase 3: Integration & Optimization
1. **Performance Optimization**
   - Query optimization and caching
   - Connection pooling
   - Rate limiting and throttling
   - Batch processing capabilities

2. **Developer Experience**
   - Interactive GraphQL playground
   - API documentation and examples
   - SDK generation for multiple languages
   - Testing and debugging tools

## Technical Implementation

### File Structure
```
src/
├── app/
│   ├── api/
│   │   ├── graphql/
│   │   │   └── route.ts                             # GraphQL endpoint
│   │   └── v1/
│   │       ├── analytics/
│   │       │   ├── schema/
│   │       │   │   └── route.ts                     # Schema introspection API
│   │       │   ├── playground/
│   │       │   │   └── route.ts                     # GraphQL playground
│   │       │   └── codegen/
│   │       │       └── route.ts                     # Code generation API
│   │       └── subscriptions/
│   │           └── route.ts                         # WebSocket subscriptions
│   └── analytics/
│       ├── playground/
│       │   └── page.tsx                             # GraphQL playground page
│       ├── schema/
│       │   └── page.tsx                             # Schema documentation
│       └── docs/
│           └── page.tsx                             # API documentation
├── lib/
│   ├── graphql/
│   │   ├── schema/
│   │   │   ├── index.ts                             # Main schema definition
│   │   │   ├── types/
│   │   │   │   ├── analytics.ts                     # Analytics types
│   │   │   │   ├── metrics.ts                       # Metrics types
│   │   │   │   ├── events.ts                        # Event types
│   │   │   │   ├── reports.ts                       # Report types
│   │   │   │   ├── dashboards.ts                    # Dashboard types
│   │   │   │   └── subscriptions.ts                 # Subscription types
│   │   │   ├── queries/
│   │   │   │   ├── analytics.ts                     # Analytics queries
│   │   │   │   ├── metrics.ts                       # Metrics queries
│   │   │   │   ├── events.ts                        # Event queries
│   │   │   │   ├── reports.ts                       # Report queries
│   │   │   │   └── dashboards.ts                    # Dashboard queries
│   │   │   ├── mutations/
│   │   │   │   ├── analytics.ts                     # Analytics mutations
│   │   │   │   ├── events.ts                        # Event mutations
│   │   │   │   ├── reports.ts                       # Report mutations
│   │   │   │   └── dashboards.ts                    # Dashboard mutations
│   │   │   └── subscriptions/
│   │   │       ├── analytics.ts                     # Analytics subscriptions
│   │   │       ├── metrics.ts                       # Metrics subscriptions
│   │   │       ├── events.ts                        # Event subscriptions
│   │   │       └── reports.ts                       # Report subscriptions
│   │   ├── resolvers/
│   │   │   ├── analytics-resolver.ts                # Analytics resolver
│   │   │   ├── metrics-resolver.ts                  # Metrics resolver
│   │   │   ├── events-resolver.ts                   # Events resolver
│   │   │   ├── reports-resolver.ts                  # Reports resolver
│   │   │   └── dashboards-resolver.ts               # Dashboards resolver
│   │   ├── plugins/
│   │   │   ├── auth-plugin.ts                       # Authentication plugin
│   │   │   ├── validation-plugin.ts                 # Validation plugin
│   │   │   ├── caching-plugin.ts                    # Caching plugin
│   │   │   ├── rate-limiting-plugin.ts              # Rate limiting plugin
│   │   │   └── tracing-plugin.ts                    # Tracing plugin
│   │   ├── middleware/
│   │   │   ├── auth-middleware.ts                   # Authentication middleware
│   │   │   ├── rate-limit-middleware.ts             # Rate limiting middleware
│   │   │   ├── validation-middleware.ts             # Validation middleware
│   │   │   └── error-handling-middleware.ts         # Error handling middleware
│   │   └── utils/
│   │       ├── query-builder.ts                     # Query builder utilities
│   │       ├── subscription-manager.ts              # Subscription management
│   │       ├── cache-manager.ts                     # Cache management
│   │       └── schema-generator.ts                  # Schema generation utilities
│   ├── analytics/
│   │   ├── api/
│   │   │   ├── analytics-api.ts                     # Analytics API service
│   │   │   ├── metrics-api.ts                       # Metrics API service
│   │   │   ├── events-api.ts                        # Events API service
│   │   │   └── reports-api.ts                       # Reports API service
│   │   ├── services/
│   │   │   ├── data-service.ts                      # Data access service
│   │   │   ├── aggregation-service.ts               # Data aggregation service
│   │   │   ├── filtering-service.ts                 # Data filtering service
│   │   │   └── subscription-service.ts              # Subscription service
│   │   ├── processors/
│   │   │   ├── query-processor.ts                   # Query processing
│   │   │   ├── mutation-processor.ts                # Mutation processing
│   │   │   ├── subscription-processor.ts            # Subscription processing
│   │   │   └── batch-processor.ts                   # Batch processing
│   │   └── validators/
│   │       ├── query-validator.ts                   # Query validation
│   │       ├── mutation-validator.ts                # Mutation validation
│   │       └── subscription-validator.ts            # Subscription validation
│   └── integrations/
│       ├── analytics-providers/
│       │   ├── google-analytics-api.ts              # Google Analytics API
│       │   ├── mixpanel-api.ts                      # Mixpanel API
│       │   ├── segment-api.ts                       # Segment API
│       │   └── custom-provider-api.ts               # Custom provider API
│       └── external-apis/
│           ├── third-party-integrations.ts          # Third-party integrations
│           ├── webhook-handlers.ts                  # Webhook handlers
│           └── data-sync-services.ts                # Data synchronization
├── types/
│   ├── generated/
│   │   ├── graphql.ts                               # Generated GraphQL types
│   │   ├── analytics.ts                             # Generated analytics types
│   │   └── client.ts                                # Generated client types
│   └── analytics/
│       ├── api-types.ts                             # API-specific types
│       ├── query-types.ts                           # Query-specific types
│       └── subscription-types.ts                    # Subscription-specific types
```

### Core GraphQL Schema

```typescript
// src/lib/graphql/schema/index.ts
import { createSchema } from 'graphql-yoga';
import { builder } from './builder';
import { authPlugin } from '../plugins/auth-plugin';
import { validationPlugin } from '../plugins/validation-plugin';
import { cachingPlugin } from '../plugins/caching-plugin';
import { rateLimitingPlugin } from '../plugins/rate-limiting-plugin';
import { tracingPlugin } from '../plugins/tracing-plugin';

// Import types
import './types/analytics';
import './types/metrics';
import './types/events';
import './types/reports';
import './types/dashboards';
import './types/subscriptions';

// Import queries
import './queries/analytics';
import './queries/metrics';
import './queries/events';
import './queries/reports';
import './queries/dashboards';

// Import mutations
import './mutations/analytics';
import './mutations/events';
import './mutations/reports';
import './mutations/dashboards';

// Import subscriptions
import './subscriptions/analytics';
import './subscriptions/metrics';
import './subscriptions/events';
import './subscriptions/reports';

// Register plugins
builder.use(authPlugin);
builder.use(validationPlugin);
builder.use(cachingPlugin);
builder.use(rateLimitingPlugin);
builder.use(tracingPlugin);

export const schema = builder.toSchema();

export const graphqlConfig = {
  schema,
  context: async ({ request }: { request: Request }) => {
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');
    
    // Validate token and get user context
    const user = await validateToken(token);
    
    return {
      user,
      tenantId: user?.tenantId,
      permissions: user?.permissions || [],
    };
  },
  plugins: [
    // Custom plugins for analytics
    {
      onExecute: ({ args }) => {
        // Log GraphQL operations
        console.log('GraphQL Operation:', args.operationName);
      },
    },
  ],
};

async function validateToken(token?: string) {
  if (!token) return null;
  
  // Implementation would validate JWT token
  // and return user context
  return {
    id: 'user-123',
    tenantId: 'tenant-123',
    permissions: ['analytics:read', 'metrics:read'],
  };
}
```

### Analytics Schema Types

```typescript
// src/lib/graphql/schema/types/analytics.ts
import { builder } from '../builder';
import { z } from 'zod';

// Input types
export const AnalyticsFilterInput = builder.inputType('AnalyticsFilterInput', {
  fields: (t) => ({
    dateRange: t.field({
      type: 'DateRangeInput',
      required: false,
    }),
    metrics: t.stringList({
      required: false,
    }),
    dimensions: t.stringList({
      required: false,
    }),
    filters: t.field({
      type: 'FilterConditionInput',
      required: false,
    }),
  }),
});

export const DateRangeInput = builder.inputType('DateRangeInput', {
  fields: (t) => ({
    start: t.field({
      type: 'DateTime',
      required: true,
    }),
    end: t.field({
      type: 'DateTime',
      required: true,
    }),
  }),
});

export const FilterConditionInput = builder.inputType('FilterConditionInput', {
  fields: (t) => ({
    field: t.string({ required: true }),
    operator: t.field({
      type: 'FilterOperator',
      required: true,
    }),
    value: t.string({ required: true }),
  }),
});

export const FilterOperator = builder.enumType('FilterOperator', {
  values: ['EQUALS', 'NOT_EQUALS', 'GREATER_THAN', 'LESS_THAN', 'CONTAINS', 'IN', 'NOT_IN'],
});

export const AggregationType = builder.enumType('AggregationType', {
  values: ['SUM', 'COUNT', 'AVG', 'MIN', 'MAX', 'DISTINCT_COUNT'],
});

// Object types
export const AnalyticsData = builder.objectType('AnalyticsData', {
  fields: (t) => ({
    id: t.exposeString('id'),
    tenantId: t.exposeString('tenantId'),
    timestamp: t.field({
      type: 'DateTime',
      resolve: (data) => data.timestamp,
    }),
    metrics: t.field({
      type: [MetricValue],
      resolve: (data) => data.metrics,
    }),
    dimensions: t.field({
      type: [DimensionValue],
      resolve: (data) => data.dimensions,
    }),
    properties: t.field({
      type: 'JSON',
      resolve: (data) => data.properties,
    }),
  }),
});

export const MetricValue = builder.objectType('MetricValue', {
  fields: (t) => ({
    name: t.exposeString('name'),
    value: t.exposeFloat('value'),
    unit: t.exposeString('unit'),
    type: t.exposeString('type'),
  }),
});

export const DimensionValue = builder.objectType('DimensionValue', {
  fields: (t) => ({
    name: t.exposeString('name'),
    value: t.exposeString('value'),
    type: t.exposeString('type'),
  }),
});

export const AnalyticsAggregation = builder.objectType('AnalyticsAggregation', {
  fields: (t) => ({
    field: t.exposeString('field'),
    type: t.field({
      type: AggregationType,
      resolve: (data) => data.type,
    }),
    value: t.exposeFloat('value'),
    count: t.exposeInt('count'),
  }),
});

export const AnalyticsQueryResult = builder.objectType('AnalyticsQueryResult', {
  fields: (t) => ({
    data: t.field({
      type: [AnalyticsData],
      resolve: (result) => result.data,
    }),
    aggregations: t.field({
      type: [AnalyticsAggregation],
      resolve: (result) => result.aggregations,
    }),
    totalCount: t.exposeInt('totalCount'),
    hasMore: t.exposeBoolean('hasMore'),
    cursor: t.exposeString('cursor', { nullable: true }),
  }),
});

// Custom scalar types
builder.scalarType('DateTime', {
  serialize: (value) => value.toISOString(),
  parseValue: (value) => new Date(value as string),
});

builder.scalarType('JSON', {
  serialize: (value) => value,
  parseValue: (value) => value,
});
```

### Analytics Queries

```typescript
// src/lib/graphql/schema/queries/analytics.ts
import { builder } from '../builder';
import { analyticsResolver } from '../../resolvers/analytics-resolver';
import { AnalyticsQueryResult, AnalyticsFilterInput, AggregationType } from '../types/analytics';

builder.queryField('analytics', (t) =>
  t.field({
    type: AnalyticsQueryResult,
    args: {
      filter: t.arg({
        type: AnalyticsFilterInput,
        required: false,
      }),
      limit: t.arg.int({
        required: false,
        defaultValue: 100,
      }),
      offset: t.arg.int({
        required: false,
        defaultValue: 0,
      }),
      orderBy: t.arg.string({
        required: false,
      }),
      orderDirection: t.arg({
        type: 'OrderDirection',
        required: false,
        defaultValue: 'ASC',
      }),
    },
    resolve: async (root, args, ctx) => {
      return analyticsResolver.getAnalytics(args, ctx);
    },
  })
);

builder.queryField('analyticsAggregation', (t) =>
  t.field({
    type: [AnalyticsAggregation],
    args: {
      filter: t.arg({
        type: AnalyticsFilterInput,
        required: false,
      }),
      groupBy: t.arg.stringList({
        required: true,
      }),
      aggregations: t.arg({
        type: [AggregationInput],
        required: true,
      }),
    },
    resolve: async (root, args, ctx) => {
      return analyticsResolver.getAnalyticsAggregation(args, ctx);
    },
  })
);

builder.queryField('analyticsTimeSeries', (t) =>
  t.field({
    type: TimeSeriesResult,
    args: {
      filter: t.arg({
        type: AnalyticsFilterInput,
        required: false,
      }),
      interval: t.arg({
        type: 'TimeInterval',
        required: true,
      }),
      metrics: t.arg.stringList({
        required: true,
      }),
    },
    resolve: async (root, args, ctx) => {
      return analyticsResolver.getAnalyticsTimeSeries(args, ctx);
    },
  })
);

builder.queryField('analyticsInsights', (t) =>
  t.field({
    type: [AnalyticsInsight],
    args: {
      filter: t.arg({
        type: AnalyticsFilterInput,
        required: false,
      }),
      insightTypes: t.arg({
        type: [InsightType],
        required: false,
      }),
      confidenceThreshold: t.arg.float({
        required: false,
        defaultValue: 0.7,
      }),
    },
    resolve: async (root, args, ctx) => {
      return analyticsResolver.getAnalyticsInsights(args, ctx);
    },
  })
);

// Additional input types
const AggregationInput = builder.inputType('AggregationInput', {
  fields: (t) => ({
    field: t.string({ required: true }),
    type: t.field({
      type: AggregationType,
      required: true,
    }),
  }),
});

const TimeInterval = builder.enumType('TimeInterval', {
  values: ['MINUTE', 'HOUR', 'DAY', 'WEEK', 'MONTH', 'QUARTER', 'YEAR'],
});

const InsightType = builder.enumType('InsightType', {
  values: ['TREND', 'ANOMALY', 'FORECAST', 'CORRELATION', 'SEGMENTATION'],
});

const OrderDirection = builder.enumType('OrderDirection', {
  values: ['ASC', 'DESC'],
});

// Additional result types
const TimeSeriesResult = builder.objectType('TimeSeriesResult', {
  fields: (t) => ({
    data: t.field({
      type: [TimeSeriesDataPoint],
      resolve: (result) => result.data,
    }),
    interval: t.field({
      type: TimeInterval,
      resolve: (result) => result.interval,
    }),
    metrics: t.stringList({
      resolve: (result) => result.metrics,
    }),
  }),
});

const TimeSeriesDataPoint = builder.objectType('TimeSeriesDataPoint', {
  fields: (t) => ({
    timestamp: t.field({
      type: 'DateTime',
      resolve: (data) => data.timestamp,
    }),
    values: t.field({
      type: [MetricValue],
      resolve: (data) => data.values,
    }),
  }),
});

const AnalyticsInsight = builder.objectType('AnalyticsInsight', {
  fields: (t) => ({
    id: t.exposeString('id'),
    type: t.field({
      type: InsightType,
      resolve: (insight) => insight.type,
    }),
    title: t.exposeString('title'),
    description: t.exposeString('description'),
    confidence: t.exposeFloat('confidence'),
    impact: t.exposeString('impact'),
    recommendations: t.stringList({
      resolve: (insight) => insight.recommendations,
    }),
    data: t.field({
      type: 'JSON',
      resolve: (insight) => insight.data,
    }),
    createdAt: t.field({
      type: 'DateTime',
      resolve: (insight) => insight.createdAt,
    }),
  }),
});
```

### Analytics Subscriptions

```typescript
// src/lib/graphql/schema/subscriptions/analytics.ts
import { builder } from '../builder';
import { analyticsResolver } from '../../resolvers/analytics-resolver';
import { AnalyticsData, AnalyticsFilterInput } from '../types/analytics';

builder.subscriptionField('analyticsUpdates', (t) =>
  t.field({
    type: AnalyticsData,
    args: {
      filter: t.arg({
        type: AnalyticsFilterInput,
        required: false,
      }),
      tenantId: t.arg.string({
        required: true,
      }),
    },
    subscribe: async (root, args, ctx) => {
      return analyticsResolver.subscribeToAnalyticsUpdates(args, ctx);
    },
    resolve: (payload) => payload,
  })
);

builder.subscriptionField('metricsUpdates', (t) =>
  t.field({
    type: [MetricValue],
    args: {
      metrics: t.arg.stringList({
        required: true,
      }),
      tenantId: t.arg.string({
        required: true,
      }),
    },
    subscribe: async (root, args, ctx) => {
      return analyticsResolver.subscribeToMetricsUpdates(args, ctx);
    },
    resolve: (payload) => payload,
  })
);

builder.subscriptionField('alertsUpdates', (t) =>
  t.field({
    type: AnalyticsAlert,
    args: {
      severity: t.arg({
        type: [AlertSeverity],
        required: false,
      }),
      tenantId: t.arg.string({
        required: true,
      }),
    },
    subscribe: async (root, args, ctx) => {
      return analyticsResolver.subscribeToAlertsUpdates(args, ctx);
    },
    resolve: (payload) => payload,
  })
);

builder.subscriptionField('insightsUpdates', (t) =>
  t.field({
    type: AnalyticsInsight,
    args: {
      insightTypes: t.arg({
        type: [InsightType],
        required: false,
      }),
      tenantId: t.arg.string({
        required: true,
      }),
    },
    subscribe: async (root, args, ctx) => {
      return analyticsResolver.subscribeToInsightsUpdates(args, ctx);
    },
    resolve: (payload) => payload,
  })
);

// Additional types for subscriptions
const AnalyticsAlert = builder.objectType('AnalyticsAlert', {
  fields: (t) => ({
    id: t.exposeString('id'),
    type: t.exposeString('type'),
    severity: t.field({
      type: AlertSeverity,
      resolve: (alert) => alert.severity,
    }),
    title: t.exposeString('title'),
    description: t.exposeString('description'),
    metric: t.exposeString('metric'),
    value: t.exposeFloat('value'),
    threshold: t.exposeFloat('threshold'),
    triggeredAt: t.field({
      type: 'DateTime',
      resolve: (alert) => alert.triggeredAt,
    }),
    status: t.field({
      type: AlertStatus,
      resolve: (alert) => alert.status,
    }),
  }),
});

const AlertSeverity = builder.enumType('AlertSeverity', {
  values: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'],
});

const AlertStatus = builder.enumType('AlertStatus', {
  values: ['OPEN', 'ACKNOWLEDGED', 'RESOLVED'],
});
```

### Analytics Resolver

```typescript
// src/lib/graphql/resolvers/analytics-resolver.ts
import { z } from 'zod';
import { createAsyncIterator } from '@graphql-tools/utils';
import { PubSub } from 'graphql-subscriptions';
import { analyticsService } from '../../analytics/services/data-service';
import { performanceService } from '../../monitoring/services/performance-service';
import { businessIntelligenceService } from '../../business-intelligence/services/bi-service';
import { auditLogger } from '../../audit/audit-logger';

const pubsub = new PubSub();

export class AnalyticsResolver {
  async getAnalytics(args: any, ctx: any) {
    const { filter, limit, offset, orderBy, orderDirection } = args;
    const { tenantId, user } = ctx;

    // Validate permissions
    await this.validatePermissions(ctx, 'analytics:read');

    // Build query
    const query = {
      tenantId,
      filter: filter || {},
      limit: Math.min(limit || 100, 1000), // Max 1000 items
      offset: offset || 0,
      orderBy: orderBy || 'timestamp',
      orderDirection: orderDirection || 'ASC',
    };

    // Execute query
    const result = await analyticsService.queryAnalytics(query);

    // Log query
    await auditLogger.log({
      action: 'analytics_query',
      userId: user?.id,
      tenantId,
      metadata: {
        filter,
        limit,
        offset,
        resultCount: result.data.length,
      },
    });

    return {
      data: result.data,
      aggregations: result.aggregations || [],
      totalCount: result.totalCount,
      hasMore: result.hasMore,
      cursor: result.cursor,
    };
  }

  async getAnalyticsAggregation(args: any, ctx: any) {
    const { filter, groupBy, aggregations } = args;
    const { tenantId, user } = ctx;

    // Validate permissions
    await this.validatePermissions(ctx, 'analytics:read');

    // Build aggregation query
    const query = {
      tenantId,
      filter: filter || {},
      groupBy,
      aggregations,
    };

    // Execute aggregation
    const result = await analyticsService.aggregateAnalytics(query);

    // Log aggregation
    await auditLogger.log({
      action: 'analytics_aggregation',
      userId: user?.id,
      tenantId,
      metadata: {
        filter,
        groupBy,
        aggregations: aggregations.map((agg: any) => `${agg.field}:${agg.type}`),
        resultCount: result.length,
      },
    });

    return result;
  }

  async getAnalyticsTimeSeries(args: any, ctx: any) {
    const { filter, interval, metrics } = args;
    const { tenantId, user } = ctx;

    // Validate permissions
    await this.validatePermissions(ctx, 'analytics:read');

    // Build time series query
    const query = {
      tenantId,
      filter: filter || {},
      interval,
      metrics,
    };

    // Execute time series query
    const result = await analyticsService.getTimeSeries(query);

    // Log time series query
    await auditLogger.log({
      action: 'analytics_time_series',
      userId: user?.id,
      tenantId,
      metadata: {
        filter,
        interval,
        metrics,
        dataPoints: result.data.length,
      },
    });

    return result;
  }

  async getAnalyticsInsights(args: any, ctx: any) {
    const { filter, insightTypes, confidenceThreshold } = args;
    const { tenantId, user } = ctx;

    // Validate permissions
    await this.validatePermissions(ctx, 'analytics:read');

    // Generate insights
    const insights = await businessIntelligenceService.generateInsights(
      tenantId,
      {
        from: filter?.dateRange?.start || new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        to: filter?.dateRange?.end || new Date(),
      },
      {
        types: insightTypes || ['TREND', 'ANOMALY'],
        includeRecommendations: true,
        confidenceThreshold: confidenceThreshold || 0.7,
      }
    );

    // Log insights query
    await auditLogger.log({
      action: 'analytics_insights',
      userId: user?.id,
      tenantId,
      metadata: {
        filter,
        insightTypes,
        confidenceThreshold,
        insightCount: insights.length,
      },
    });

    return insights;
  }

  async subscribeToAnalyticsUpdates(args: any, ctx: any) {
    const { filter, tenantId } = args;
    const { user } = ctx;

    // Validate permissions
    await this.validatePermissions(ctx, 'analytics:read');

    // Create subscription
    const subscriptionId = `analytics_${tenantId}_${user?.id}_${Date.now()}`;
    
    // Log subscription start
    await auditLogger.log({
      action: 'analytics_subscription_start',
      userId: user?.id,
      tenantId,
      metadata: {
        subscriptionId,
        filter,
      },
    });

    // Return async iterator
    return createAsyncIterator(pubsub.asyncIterator(`analytics_updates_${tenantId}`));
  }

  async subscribeToMetricsUpdates(args: any, ctx: any) {
    const { metrics, tenantId } = args;
    const { user } = ctx;

    // Validate permissions
    await this.validatePermissions(ctx, 'metrics:read');

    // Create metrics subscription
    const subscriptionId = `metrics_${tenantId}_${user?.id}_${Date.now()}`;
    
    // Log subscription start
    await auditLogger.log({
      action: 'metrics_subscription_start',
      userId: user?.id,
      tenantId,
      metadata: {
        subscriptionId,
        metrics,
      },
    });

    // Return async iterator
    return createAsyncIterator(pubsub.asyncIterator(`metrics_updates_${tenantId}`));
  }

  async subscribeToAlertsUpdates(args: any, ctx: any) {
    const { severity, tenantId } = args;
    const { user } = ctx;

    // Validate permissions
    await this.validatePermissions(ctx, 'alerts:read');

    // Create alerts subscription
    const subscriptionId = `alerts_${tenantId}_${user?.id}_${Date.now()}`;
    
    // Log subscription start
    await auditLogger.log({
      action: 'alerts_subscription_start',
      userId: user?.id,
      tenantId,
      metadata: {
        subscriptionId,
        severity,
      },
    });

    // Return async iterator
    return createAsyncIterator(pubsub.asyncIterator(`alerts_updates_${tenantId}`));
  }

  async subscribeToInsightsUpdates(args: any, ctx: any) {
    const { insightTypes, tenantId } = args;
    const { user } = ctx;

    // Validate permissions
    await this.validatePermissions(ctx, 'insights:read');

    // Create insights subscription
    const subscriptionId = `insights_${tenantId}_${user?.id}_${Date.now()}`;
    
    // Log subscription start
    await auditLogger.log({
      action: 'insights_subscription_start',
      userId: user?.id,
      tenantId,
      metadata: {
        subscriptionId,
        insightTypes,
      },
    });

    // Return async iterator
    return createAsyncIterator(pubsub.asyncIterator(`insights_updates_${tenantId}`));
  }

  // Utility method to publish updates
  async publishAnalyticsUpdate(tenantId: string, data: any) {
    await pubsub.publish(`analytics_updates_${tenantId}`, data);
  }

  async publishMetricsUpdate(tenantId: string, metrics: any) {
    await pubsub.publish(`metrics_updates_${tenantId}`, metrics);
  }

  async publishAlertsUpdate(tenantId: string, alert: any) {
    await pubsub.publish(`alerts_updates_${tenantId}`, alert);
  }

  async publishInsightsUpdate(tenantId: string, insight: any) {
    await pubsub.publish(`insights_updates_${tenantId}`, insight);
  }

  private async validatePermissions(ctx: any, permission: string) {
    const { user, permissions } = ctx;
    
    if (!user) {
      throw new Error('Authentication required');
    }

    if (!permissions.includes(permission)) {
      throw new Error(`Permission denied: ${permission}`);
    }
  }
}

export const analyticsResolver = new AnalyticsResolver();
```

### GraphQL API Route

```typescript
// src/app/api/graphql/route.ts
import { createYoga } from 'graphql-yoga';
import { NextRequest } from 'next/server';
import { schema, graphqlConfig } from '@/lib/graphql/schema';
import { rateLimitMiddleware } from '@/lib/api/middleware/rate-limit';
import { auditLogger } from '@/lib/audit/audit-logger';

const yoga = createYoga<{
  req: NextRequest;
}>({
  schema,
  context: graphqlConfig.context,
  plugins: [
    ...graphqlConfig.plugins,
    {
      onExecute: ({ args }) => {
        // Log all GraphQL operations
        auditLogger.log({
          action: 'graphql_operation',
          metadata: {
            operationName: args.operationName,
            operationType: args.document?.definitions?.[0]?.kind,
            variables: args.variableValues,
          },
        });
      },
    },
  ],
  cors: {
    origin: process.env.NODE_ENV === 'development' ? true : process.env.CORS_ORIGIN,
    credentials: true,
  },
  graphiql: process.env.NODE_ENV === 'development',
});

export async function GET(request: NextRequest) {
  return yoga.handleRequest(request, {
    req: request,
  });
}

export async function POST(request: NextRequest) {
  // Apply rate limiting
  await rateLimitMiddleware(request);
  
  return yoga.handleRequest(request, {
    req: request,
  });
}

export async function OPTIONS(request: NextRequest) {
  return yoga.handleRequest(request, {
    req: request,
  });
}
```

### Client SDK Generation

```typescript
// src/lib/graphql/utils/schema-generator.ts
import { codegen } from '@graphql-codegen/core';
import { plugin as typescriptPlugin } from '@graphql-codegen/typescript';
import { plugin as typescriptOperationsPlugin } from '@graphql-codegen/typescript-operations';
import { plugin as typescriptReactQueryPlugin } from '@graphql-codegen/typescript-react-query';
import { printSchema } from 'graphql';
import { schema } from '../schema';

export class SchemaGenerator {
  async generateTypeScriptTypes(): Promise<string> {
    const schemaString = printSchema(schema);
    
    const config = {
      documents: [],
      config: {
        scalars: {
          DateTime: 'Date',
          JSON: 'any',
        },
        skipTypename: true,
        enumsAsTypes: true,
      },
      plugins: [
        {
          typescript: {},
        },
      ],
      pluginMap: {
        typescript: typescriptPlugin,
      },
      schema: schemaString,
    };

    const output = await codegen(config);
    return output;
  }

  async generateReactQueryHooks(documents: string[]): Promise<string> {
    const schemaString = printSchema(schema);
    
    const config = {
      documents,
      config: {
        scalars: {
          DateTime: 'Date',
          JSON: 'any',
        },
        skipTypename: true,
        enumsAsTypes: true,
        withHooks: true,
        withSubscriptionHooks: true,
      },
      plugins: [
        {
          typescript: {},
        },
        {
          'typescript-operations': {},
        },
        {
          'typescript-react-query': {},
        },
      ],
      pluginMap: {
        typescript: typescriptPlugin,
        'typescript-operations': typescriptOperationsPlugin,
        'typescript-react-query': typescriptReactQueryPlugin,
      },
      schema: schemaString,
    };

    const output = await codegen(config);
    return output;
  }

  async generateDocumentation(): Promise<string> {
    const schemaString = printSchema(schema);
    
    // Generate markdown documentation
    const docs = this.generateMarkdownDocs(schemaString);
    
    return docs;
  }

  private generateMarkdownDocs(schemaString: string): string {
    // Implementation to generate markdown documentation
    // from GraphQL schema
    return `# Analytics API Documentation

## Overview
This API provides access to analytics data, metrics, and insights.

## Schema
\`\`\`graphql
${schemaString}
\`\`\`

## Queries
- \`analytics\`: Get analytics data with filtering and pagination
- \`analyticsAggregation\`: Get aggregated analytics data
- \`analyticsTimeSeries\`: Get time-series analytics data
- \`analyticsInsights\`: Get AI-powered insights

## Subscriptions
- \`analyticsUpdates\`: Real-time analytics updates
- \`metricsUpdates\`: Real-time metrics updates
- \`alertsUpdates\`: Real-time alert updates
- \`insightsUpdates\`: Real-time insights updates

## Examples

### Get Analytics Data
\`\`\`graphql
query GetAnalytics($filter: AnalyticsFilterInput) {
  analytics(filter: $filter) {
    data {
      id
      timestamp
      metrics {
        name
        value
        unit
      }
      dimensions {
        name
        value
      }
    }
    totalCount
    hasMore
  }
}
\`\`\`

### Subscribe to Real-time Updates
\`\`\`graphql
subscription AnalyticsUpdates($tenantId: String!) {
  analyticsUpdates(tenantId: $tenantId) {
    id
    timestamp
    metrics {
      name
      value
      unit
    }
  }
}
\`\`\`
`;
  }
}

export const schemaGenerator = new SchemaGenerator();
```

### Testing Strategy

```typescript
// src/lib/graphql/schema/__tests__/analytics.test.ts
import { buildSchema, graphql } from 'graphql';
import { schema } from '../index';

describe('Analytics GraphQL Schema', () => {
  it('should execute analytics query', async () => {
    const query = `
      query TestAnalytics {
        analytics(limit: 10) {
          data {
            id
            timestamp
            metrics {
              name
              value
              unit
            }
          }
          totalCount
          hasMore
        }
      }
    `;

    const result = await graphql({
      schema,
      source: query,
      contextValue: {
        user: { id: 'user-123', tenantId: 'tenant-123' },
        tenantId: 'tenant-123',
        permissions: ['analytics:read'],
      },
    });

    expect(result.errors).toBeUndefined();
    expect(result.data?.analytics).toBeDefined();
    expect(result.data?.analytics.data).toBeInstanceOf(Array);
  });

  it('should handle aggregation queries', async () => {
    const query = `
      query TestAggregation {
        analyticsAggregation(
          groupBy: ["metric_name"]
          aggregations: [
            { field: "value", type: SUM }
            { field: "value", type: COUNT }
          ]
        ) {
          field
          type
          value
          count
        }
      }
    `;

    const result = await graphql({
      schema,
      source: query,
      contextValue: {
        user: { id: 'user-123', tenantId: 'tenant-123' },
        tenantId: 'tenant-123',
        permissions: ['analytics:read'],
      },
    });

    expect(result.errors).toBeUndefined();
    expect(result.data?.analyticsAggregation).toBeDefined();
  });

  it('should handle subscription setup', async () => {
    const subscription = `
      subscription TestSubscription {
        analyticsUpdates(tenantId: "tenant-123") {
          id
          timestamp
          metrics {
            name
            value
          }
        }
      }
    `;

    const result = await graphql({
      schema,
      source: subscription,
      contextValue: {
        user: { id: 'user-123', tenantId: 'tenant-123' },
        tenantId: 'tenant-123',
        permissions: ['analytics:read'],
      },
    });

    expect(result.errors).toBeUndefined();
  });
});
```

### Performance Considerations

#### Query Optimization
- **DataLoader**: Implement DataLoader for efficient data fetching
- **Query Complexity**: Limit query complexity and depth
- **Caching**: Implement query result caching
- **Pagination**: Enforce pagination limits

#### Subscription Management
- **Connection Pooling**: Optimize WebSocket connections
- **Memory Management**: Prevent memory leaks in subscriptions
- **Rate Limiting**: Limit subscription frequency
- **Connection Limits**: Limit concurrent connections per tenant

#### Security Considerations
- **Authentication**: JWT token validation
- **Authorization**: Role-based access control
- **Rate Limiting**: Prevent API abuse
- **Input Validation**: Validate all inputs
- **Query Depth**: Prevent deep query attacks

## Success Metrics

### API Performance
- **Response Time**: Average GraphQL query response time
- **Throughput**: Queries per second handling capacity
- **Error Rate**: Percentage of failed queries
- **Subscription Latency**: Real-time update delivery time

### Developer Experience
- **API Usage**: Number of active API consumers
- **Documentation Usage**: API documentation page views
- **SDK Downloads**: Generated SDK usage statistics
- **Support Tickets**: API-related support requests

This comprehensive Analytics API implementation provides a powerful, type-safe, and scalable GraphQL interface for accessing analytics data, with real-time capabilities and extensive customization options for the NEXUS SaaS Starter.
