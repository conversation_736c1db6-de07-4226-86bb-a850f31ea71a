// RBAC system types

export type ResourceType = 
  | "organization" 
  | "workspace" 
  | "team" 
  | "member" 
  | "project" 
  | "document" 
  | "file" 
  | "user" 
  | "session" 
  | "subscription" 
  | "invoice" 
  | "analytics" 
  | "report" 
  | "integration" 
  | "webhook" 
  | "api";

export type ActionType = 
  | "create" 
  | "read" 
  | "update" 
  | "delete" 
  | "manage" 
  | "share" 
  | "invite" 
  | "remove" 
  | "join" 
  | "leave" 
  | "collaborate" 
  | "review" 
  | "publish" 
  | "download" 
  | "upload" 
  | "ban" 
  | "impersonate" 
  | "revoke" 
  | "cancel" 
  | "export" 
  | "schedule";

export type PermissionScope = "own" | "team" | "workspace" | "organization" | "system";

export type RoleLevel = "system" | "organization" | "workspace" | "team" | "user";

export interface Permission {
  id: string;
  resource: ResourceType;
  action: ActionType;
  scope: PermissionScope;
  conditions?: Record<string, any>;
  attributes?: string[];
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Role {
  id: string;
  name: string;
  slug: string;
  level: RoleLevel;
  tenantId?: string;
  workspaceId?: string;
  teamId?: string;
  description?: string;
  color?: string;
  icon?: string;
  isSystem: boolean;
  isActive: boolean;
  permissions: Permission[];
  inherits?: string[];
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserRole {
  id: string;
  userId: string;
  roleId: string;
  tenantId?: string;
  workspaceId?: string;
  teamId?: string;
  resourceId?: string;
  resourceType?: ResourceType;
  isActive: boolean;
  expiresAt?: Date;
  assignedBy: string;
  assignedAt: Date;
  metadata: Record<string, any>;
}

export interface PermissionCheck {
  userId: string;
  resource: ResourceType;
  action: ActionType;
  resourceId?: string;
  tenantId?: string;
  workspaceId?: string;
  teamId?: string;
  context?: Record<string, any>;
}

export interface PermissionResult {
  granted: boolean;
  reason?: string;
  role?: string;
  permission?: Permission;
  conditions?: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface PolicyRule {
  id: string;
  name: string;
  description?: string;
  resource: ResourceType;
  action: ActionType;
  effect: "allow" | "deny";
  conditions: Array<{
    field: string;
    operator: "eq" | "ne" | "gt" | "gte" | "lt" | "lte" | "in" | "nin" | "contains" | "exists";
    value: any;
  }>;
  priority: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface AccessContext {
  userId: string;
  tenantId?: string;
  workspaceId?: string;
  teamId?: string;
  resourceId?: string;
  resourceType?: ResourceType;
  userRoles: UserRole[];
  sessionData?: Record<string, any>;
  requestData?: Record<string, any>;
}

export interface RoleTemplate {
  id: string;
  name: string;
  slug: string;
  description: string;
  level: RoleLevel;
  permissions: Array<{
    resource: ResourceType;
    actions: ActionType[];
    scope: PermissionScope;
  }>;
  isPublic: boolean;
  category: string;
  tags: string[];
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuditLog {
  id: string;
  userId: string;
  action: "permission_check" | "role_assign" | "role_revoke" | "permission_grant" | "permission_deny";
  resource: ResourceType;
  resourceId?: string;
  details: Record<string, any>;
  result: "success" | "failure" | "denied";
  ipAddress?: string;
  userAgent?: string;
  tenantId?: string;
  workspaceId?: string;
  createdAt: Date;
}

export interface RoleHierarchy {
  [roleName: string]: {
    permissions: Permission[];
    inherits?: string[];
    level: RoleLevel;
    description: string;
  };
}

export interface PermissionMatrix {
  [resource: string]: {
    [action: string]: {
      roles: string[];
      scopes: PermissionScope[];
      conditions?: Record<string, any>;
    };
  };
}
