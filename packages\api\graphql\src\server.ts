#!/usr/bin/env node

import { ApolloServer } from "@apollo/server";
import { startStandaloneServer } from "@apollo/server/standalone";
import { ApolloServerPluginDrainHttpServer } from "@apollo/server/plugin/drainHttpServer";
import { ApolloServerPluginLandingPageLocalDefault } from "@apollo/server/plugin/landingPage/default";
import { createServer } from "http";
import { GraphQLError, GraphQLFormattedError } from "graphql";
import depthLimit from "graphql-depth-limit";
import costAnalysis from "graphql-query-complexity";
import { shield, rule, and, or } from "graphql-shield";

import { schema } from "./schema";
import { createContext } from "./context";
import { applyDirectives } from "./directives";
import { createSubscriptionServer } from "./subscriptions";
import { createFederatedSchema } from "./federation";
import { GraphQLContext } from "./types";

// Environment configuration
const PORT = parseInt(process.env.GRAPHQL_PORT || "4000", 10);
const HOST = process.env.GRAPHQL_HOST || "localhost";
const NODE_ENV = process.env.NODE_ENV || "development";
const ENABLE_SUBSCRIPTIONS = process.env.ENABLE_SUBSCRIPTIONS === "true";
const ENABLE_FEDERATION = process.env.ENABLE_FEDERATION === "true";
const ENABLE_PLAYGROUND = NODE_ENV === "development";
const MAX_QUERY_DEPTH = parseInt(process.env.MAX_QUERY_DEPTH || "10", 10);
const MAX_QUERY_COMPLEXITY = parseInt(process.env.MAX_QUERY_COMPLEXITY || "1000", 10);

// Security rules for GraphQL Shield
const isAuthenticated = rule({ cache: "contextual" })(
  async (parent, args, context: GraphQLContext) => {
    return context.user !== null;
  }
);

const isAdmin = rule({ cache: "contextual" })(
  async (parent, args, context: GraphQLContext) => {
    return context.user?.roles?.includes("admin") || 
           context.user?.roles?.includes("system_admin") || false;
  }
);

const isOwner = rule({ cache: "contextual" })(
  async (parent, args, context: GraphQLContext) => {
    return context.user?.roles?.includes("owner") || 
           context.user?.roles?.includes("system_admin") || false;
  }
);

// Shield permissions
const permissions = shield(
  {
    Query: {
      me: isAuthenticated,
      systemStats: isAdmin,
      allTenants: isAdmin,
      systemHealth: isAdmin,
      auditLogs: isAdmin,
    },
    Mutation: {
      suspendTenant: isAdmin,
      reactivateTenant: isAdmin,
      updateSystemSettings: isAdmin,
    },
  },
  {
    allowExternalErrors: true,
    fallbackError: new GraphQLError("Access denied", {
      extensions: { code: "FORBIDDEN" },
    }),
  }
);

// Create Apollo Server
export const createApolloServer = async () => {
  // Choose schema based on federation setting
  const graphqlSchema = ENABLE_FEDERATION ? createFederatedSchema() : schema;
  
  // Apply directives and security
  const finalSchema = applyDirectives(graphqlSchema);

  // Create HTTP server for subscriptions
  const httpServer = createServer();

  const server = new ApolloServer<GraphQLContext>({
    schema: finalSchema,
    
    // Security plugins
    plugins: [
      ApolloServerPluginDrainHttpServer({ httpServer }),
      
      // Landing page
      ENABLE_PLAYGROUND
        ? ApolloServerPluginLandingPageLocalDefault({ embed: true })
        : ApolloServerPluginLandingPageLocalDefault({ embed: false }),
      
      // Query complexity analysis
      {
        requestDidStart() {
          return {
            didResolveOperation({ request, document }) {
              const complexity = costAnalysis({
                maximumComplexity: MAX_QUERY_COMPLEXITY,
                variables: request.variables,
                createError: (max: number, actual: number) => {
                  return new GraphQLError(
                    `Query complexity limit exceeded: ${actual} > ${max}`,
                    {
                      extensions: { 
                        code: "QUERY_COMPLEXITY_EXCEEDED",
                        maxComplexity: max,
                        actualComplexity: actual,
                      },
                    }
                  );
                },
                scalarCost: 1,
                objectCost: 2,
                listFactor: 10,
                introspectionCost: 1000,
              });

              return complexity(document);
            },
          };
        },
      },

      // Request logging
      {
        requestDidStart() {
          return {
            didResolveOperation({ request, operationName }) {
              console.log(`GraphQL Operation: ${operationName || "Anonymous"}`);
            },
            didEncounterErrors({ errors }) {
              errors.forEach((error) => {
                console.error("GraphQL Error:", error);
              });
            },
          };
        },
      },
    ],

    // Validation rules
    validationRules: [
      depthLimit(MAX_QUERY_DEPTH),
    ],

    // Error formatting
    formatError: (formattedError: GraphQLFormattedError, error: unknown) => {
      // Log error details in development
      if (NODE_ENV === "development") {
        console.error("GraphQL Error Details:", error);
      }

      // Don't expose internal errors in production
      if (NODE_ENV === "production" && !formattedError.extensions?.code) {
        return {
          message: "Internal server error",
          extensions: { code: "INTERNAL_ERROR" },
        };
      }

      return formattedError;
    },

    // Introspection and playground
    introspection: NODE_ENV === "development",
  });

  return { server, httpServer };
};

// Start GraphQL server
export const startGraphQLServer = async () => {
  try {
    const { server, httpServer } = await createApolloServer();

    // Start subscription server if enabled
    let subscriptionServer;
    if (ENABLE_SUBSCRIPTIONS) {
      subscriptionServer = createSubscriptionServer({
        schema: ENABLE_FEDERATION ? createFederatedSchema() : schema,
        port: PORT + 1,
        path: "/graphql",
      });
    }

    // Start Apollo Server
    const { url } = await startStandaloneServer(server, {
      listen: { port: PORT, host: HOST },
      context: async ({ req }) => {
        return createContext(req);
      },
    });

    console.log(`🚀 GraphQL server ready at ${url}`);
    
    if (ENABLE_SUBSCRIPTIONS) {
      console.log(`🔄 GraphQL subscriptions ready at ws://${HOST}:${PORT + 1}/graphql`);
    }

    if (ENABLE_FEDERATION) {
      console.log(`🌐 Federation mode enabled`);
    }

    // Graceful shutdown
    const gracefulShutdown = async (signal: string) => {
      console.log(`Received ${signal}, shutting down GraphQL server gracefully`);
      
      try {
        await server.stop();
        httpServer.close();
        
        if (subscriptionServer) {
          subscriptionServer.close();
        }
        
        process.exit(0);
      } catch (error) {
        console.error("Error during GraphQL server shutdown:", error);
        process.exit(1);
      }
    };

    process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
    process.on("SIGINT", () => gracefulShutdown("SIGINT"));

    return { server, httpServer, subscriptionServer };
  } catch (error) {
    console.error("Failed to start GraphQL server:", error);
    process.exit(1);
  }
};

// Health check endpoint
export const healthCheck = async () => {
  return {
    status: "healthy",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || "unknown",
    environment: NODE_ENV,
    features: {
      subscriptions: ENABLE_SUBSCRIPTIONS,
      federation: ENABLE_FEDERATION,
      playground: ENABLE_PLAYGROUND,
    },
    limits: {
      maxQueryDepth: MAX_QUERY_DEPTH,
      maxQueryComplexity: MAX_QUERY_COMPLEXITY,
    },
  };
};

// Export for testing
export { createApolloServer as default };

// Start server if this file is run directly
if (require.main === module) {
  startGraphQLServer().catch((error) => {
    console.error("Failed to start GraphQL server:", error);
    process.exit(1);
  });
}
