// Integration types

export interface Integration {
  id: string;
  name: string;
  type: IntegrationType;
  provider: IntegrationProvider;
  tenantId: string;
  workspaceId?: string;
  userId: string;
  config: IntegrationConfig;
  credentials: IntegrationCredentials;
  status: IntegrationStatus;
  lastSyncAt?: Date;
  lastErrorAt?: Date;
  lastError?: string;
  syncCount: number;
  errorCount: number;
  metadata: Record<string, any>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export type IntegrationType = 
  | "oauth"
  | "api_key"
  | "webhook"
  | "database"
  | "file_sync"
  | "messaging"
  | "crm"
  | "payment"
  | "analytics"
  | "storage";

export type IntegrationProvider = 
  | "google"
  | "microsoft"
  | "github"
  | "slack"
  | "discord"
  | "zoom"
  | "stripe"
  | "paypal"
  | "salesforce"
  | "hubspot"
  | "mailchimp"
  | "twilio"
  | "sendgrid"
  | "aws"
  | "azure"
  | "gcp"
  | "dropbox"
  | "onedrive"
  | "zapier"
  | "custom";

export type IntegrationStatus = 
  | "connected"
  | "disconnected"
  | "error"
  | "syncing"
  | "pending"
  | "expired";

export interface IntegrationConfig {
  scopes?: string[];
  webhookUrl?: string;
  syncInterval?: number;
  autoSync?: boolean;
  syncDirection?: "import" | "export" | "bidirectional";
  fieldMappings?: FieldMapping[];
  filters?: IntegrationFilter[];
  settings?: Record<string, any>;
}

export interface IntegrationCredentials {
  accessToken?: string;
  refreshToken?: string;
  apiKey?: string;
  secret?: string;
  expiresAt?: Date;
  tokenType?: string;
  scope?: string;
  encrypted?: boolean;
}

export interface FieldMapping {
  sourceField: string;
  targetField: string;
  transform?: string;
  required?: boolean;
  defaultValue?: any;
}

export interface IntegrationFilter {
  field: string;
  operator: "equals" | "contains" | "starts_with" | "ends_with" | "greater_than" | "less_than";
  value: any;
}

// OAuth types
export interface OAuthProvider {
  name: string;
  clientId: string;
  clientSecret: string;
  authorizationUrl: string;
  tokenUrl: string;
  userInfoUrl: string;
  scopes: string[];
  redirectUri: string;
}

export interface OAuthToken {
  accessToken: string;
  refreshToken?: string;
  tokenType: string;
  expiresIn?: number;
  expiresAt?: Date;
  scope?: string;
}

export interface OAuthUser {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  provider: string;
  providerData: Record<string, any>;
}

// Webhook types
export interface Webhook {
  id: string;
  integrationId: string;
  url: string;
  events: string[];
  secret?: string;
  headers?: Record<string, string>;
  isActive: boolean;
  lastTriggeredAt?: Date;
  successCount: number;
  failureCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface WebhookEvent {
  id: string;
  webhookId: string;
  event: string;
  payload: Record<string, any>;
  headers: Record<string, string>;
  signature?: string;
  status: "pending" | "delivered" | "failed";
  attempts: number;
  maxAttempts: number;
  nextAttemptAt?: Date;
  deliveredAt?: Date;
  error?: string;
  createdAt: Date;
}

// Sync types
export interface SyncJob {
  id: string;
  integrationId: string;
  type: "full" | "incremental" | "manual";
  status: "pending" | "running" | "completed" | "failed" | "cancelled";
  direction: "import" | "export" | "bidirectional";
  startedAt?: Date;
  completedAt?: Date;
  progress: number;
  totalRecords?: number;
  processedRecords: number;
  successfulRecords: number;
  failedRecords: number;
  errors: SyncError[];
  metadata: Record<string, any>;
  createdAt: Date;
}

export interface SyncError {
  record: any;
  error: string;
  field?: string;
  code?: string;
}

export interface SyncResult {
  success: boolean;
  recordsProcessed: number;
  recordsSuccessful: number;
  recordsFailed: number;
  errors: SyncError[];
  metadata: Record<string, any>;
}

// Provider-specific types
export interface GoogleIntegration {
  type: "google";
  services: ("drive" | "gmail" | "calendar" | "sheets" | "docs")[];
  config: {
    driveFolder?: string;
    calendarId?: string;
    sheetId?: string;
  };
}

export interface SlackIntegration {
  type: "slack";
  config: {
    channels: string[];
    botToken?: string;
    signingSecret?: string;
    notificationTypes: string[];
  };
}

export interface GitHubIntegration {
  type: "github";
  config: {
    repositories: string[];
    events: string[];
    organization?: string;
  };
}

export interface StripeIntegration {
  type: "stripe";
  config: {
    webhookEndpoint?: string;
    events: string[];
    accountId?: string;
  };
}

export interface SalesforceIntegration {
  type: "salesforce";
  config: {
    instanceUrl: string;
    objects: string[];
    fieldMappings: FieldMapping[];
  };
}

// API client types
export interface APIClient {
  get(url: string, params?: Record<string, any>): Promise<any>;
  post(url: string, data?: any): Promise<any>;
  put(url: string, data?: any): Promise<any>;
  patch(url: string, data?: any): Promise<any>;
  delete(url: string): Promise<any>;
  setAuth(credentials: IntegrationCredentials): void;
  refreshToken?(): Promise<OAuthToken>;
}

// Configuration
export interface IntegrationsConfig {
  oauth: {
    providers: Record<string, OAuthProvider>;
    callbackUrl: string;
    sessionSecret: string;
  };
  webhooks: {
    baseUrl: string;
    secret: string;
    maxAttempts: number;
    retryDelay: number;
  };
  sync: {
    defaultInterval: number;
    maxConcurrentJobs: number;
    batchSize: number;
    timeout: number;
  };
  encryption: {
    algorithm: string;
    key: string;
    iv: string;
  };
  redis: {
    host: string;
    port: number;
    password?: string;
    db?: number;
  };
}

// Events
export interface IntegrationEvent {
  id: string;
  type: IntegrationEventType;
  integrationId: string;
  tenantId: string;
  userId?: string;
  data: Record<string, any>;
  metadata: Record<string, any>;
  createdAt: Date;
}

export type IntegrationEventType = 
  | "integration.connected"
  | "integration.disconnected"
  | "integration.error"
  | "sync.started"
  | "sync.completed"
  | "sync.failed"
  | "webhook.received"
  | "webhook.delivered"
  | "webhook.failed"
  | "oauth.token_refreshed"
  | "oauth.token_expired";

// Analytics
export interface IntegrationAnalytics {
  totalIntegrations: number;
  activeIntegrations: number;
  integrationsByProvider: Record<string, number>;
  integrationsByType: Record<string, number>;
  syncStats: {
    totalSyncs: number;
    successfulSyncs: number;
    failedSyncs: number;
    averageSyncTime: number;
  };
  webhookStats: {
    totalWebhooks: number;
    successfulDeliveries: number;
    failedDeliveries: number;
    averageResponseTime: number;
  };
  errorStats: {
    totalErrors: number;
    errorsByType: Record<string, number>;
    errorsByProvider: Record<string, number>;
  };
}

// Rate limiting
export interface RateLimit {
  provider: string;
  endpoint: string;
  limit: number;
  remaining: number;
  resetAt: Date;
  windowSize: number;
}

// Marketplace
export interface IntegrationTemplate {
  id: string;
  name: string;
  description: string;
  provider: IntegrationProvider;
  type: IntegrationType;
  category: string;
  icon: string;
  screenshots: string[];
  config: IntegrationConfig;
  fieldMappings: FieldMapping[];
  isOfficial: boolean;
  isPublic: boolean;
  rating: number;
  installCount: number;
  author: {
    id: string;
    name: string;
    email: string;
  };
  documentation: string;
  changelog: string;
  version: string;
  createdAt: Date;
  updatedAt: Date;
}
