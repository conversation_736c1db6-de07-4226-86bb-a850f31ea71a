# NEXUS SaaS Starter - Migration Tools Implementation

**PRP Name**: Migration Tools - Import/Export and Migration Utilities  
**Version**: 1.0  
**Date**: July 18, 2025  
**Type**: Ecosystem & Extensions Implementation PRP  
**Phase**: 06-Ecosystem  
**Framework**: Next.js 15.4+ / TypeScript 5.8+ / Multi-Tenant / Data Migration  

---

## Purpose

Build comprehensive migration tools that enable seamless data import/export, platform migrations, and data transformation utilities for the NEXUS SaaS Starter. This includes automated migration workflows, data validation, rollback capabilities, and support for various data formats and external platforms.

## Core Principles

- **Zero-Downtime Migrations**: All migrations execute without service interruption
- **Data Integrity First**: Comprehensive validation, checksums, and rollback capabilities
- **Multi-Tenant Aware**: All migration operations respect workspace isolation
- **Format Agnostic**: Support for JSON, CSV, XML, SQL, and custom formats
- **Platform Interoperability**: Import/export from major SaaS platforms
- **Audit Compliance**: Complete migration tracking and audit trails

---

## Research & Documentation

### Context7-Verified Patterns (CRITICAL)

```yaml
# Migration Platform Patterns (Context7 Verified)
atlassian_migration_platform:
  - url: /context7/developer_atlassian-platform-app-migration
    why: "Enterprise-grade migration platform with transfer management, progress tracking, and rollback"
    critical: "Migration transfers, progress reporting, data validation, rollback mechanisms"
    patterns: ["Transfer lifecycle", "Progress tracking", "Data validation", "Rollback support"]

data_migrate_gem:
  - url: /ilyakatz/data-migrate
    why: "Production-ready data migration framework with versioning and rollback capabilities"
    critical: "Data migration versioning, rollback support, migration status tracking"
    patterns: ["Migration versioning", "Rollback mechanisms", "Status tracking", "Data validation"]

pentaho_data_integration:
  - url: /pentaho/pentaho-kettle
    why: "Enterprise ETL platform with comprehensive data transformation and migration capabilities"
    critical: "ETL pipelines, data transformation, format conversion, validation"
    patterns: ["ETL workflows", "Data transformation", "Format conversion", "Pipeline management"]
```

### Current Codebase Integration Points

```typescript
// Multi-tenant Context (CRITICAL FOR MIGRATION ISOLATION)
// From: PRPs/features/01-foundation/multi-tenant-database-architecture-implementation.md
interface TenantContext {
  tenantId: string;
  workspaceId: string;
  userId: string;
  permissions: string[];
}

// Database Schema (CRITICAL FOR DATA MIGRATION)
// From: PRPs/features/01-foundation/multi-tenant-database-architecture-implementation.md
interface DatabaseSchema {
  tables: TableDefinition[];
  relationships: RelationshipDefinition[];
  indexes: IndexDefinition[];
  constraints: ConstraintDefinition[];
}

// Audit System (CRITICAL FOR MIGRATION TRACKING)
// From: PRPs/features/03-enterprise/audit-logging-implementation.md
interface AuditLog {
  id: string;
  workspaceId: string;
  userId: string;
  action: string;
  resourceType: string;
  resourceId: string;
  changes: Record<string, any>;
  timestamp: Date;
}

// File Storage (CRITICAL FOR MIGRATION ASSETS)
// From: PRPs/features/02-core/file-storage-implementation.md
interface FileStorage {
  uploadFile(file: File, path: string): Promise<string>;
  downloadFile(path: string): Promise<Buffer>;
  deleteFile(path: string): Promise<void>;
  getFileMetadata(path: string): Promise<FileMetadata>;
}
```

### Technology Stack Context

```yaml
Core Framework:
  - Next.js: 15.4+ (App Router, Server Actions, Streaming)
  - React: 19 (Server Components, Concurrent Features)
  - TypeScript: 5.8+ (Advanced type system, strict mode)
  - Prisma: ORM with migration support and schema management

Data Processing:
  - Node.js Streams: For large file processing
  - CSV Parser: Fast CSV parsing and generation
  - XML Parser: XML data transformation
  - JSON Schema: Data validation and transformation
  - SQL Parser: SQL query parsing and generation

Migration Infrastructure:
  - Redis: Migration job queuing and progress tracking
  - Bull Queue: Background job processing
  - Zod: Runtime data validation
  - Sharp: Image processing and optimization
  - Archiver: Archive creation and extraction
```

---

## Data Models and Structure

### Database Schema (Prisma)

```typescript
// Migration Jobs and Tracking
model MigrationJob {
  id          String   @id @default(cuid())
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  // Job Identity
  name        String   // Human-readable job name
  type        String   // import, export, platform_migration, data_transformation
  
  // Job Configuration
  source      Json?    // Source configuration (file, API, database)
  destination Json?    // Destination configuration
  mapping     Json?    // Field mapping and transformation rules
  options     Json?    // Additional job options
  
  // Job Status
  status      String   // pending, running, completed, failed, cancelled
  progress    Int      @default(0) // Progress percentage (0-100)
  
  // Job Results
  totalRecords    Int?     // Total records to process
  processedRecords Int?    // Records processed so far
  successRecords  Int?     // Successfully processed records
  errorRecords    Int?     // Records with errors
  
  // Job Metadata
  startedAt   DateTime?
  completedAt DateTime?
  estimatedDuration Int? // Estimated duration in seconds
  
  // Error Information
  errorMessage String?
  errorDetails Json?
  
  // Job Assets
  sourceFileId String?  // Reference to uploaded source file
  resultFileId String?  // Reference to generated result file
  logFileId    String?  // Reference to detailed log file
  
  // Rollback Information
  rollbackData Json?    // Data needed for rollback
  canRollback  Boolean  @default(false)
  rolledBack   Boolean  @default(false)
  
  // Audit
  createdBy   String   // User ID who created the job
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  steps       MigrationStep[]
  logs        MigrationLog[]
  
  @@index([workspaceId, status])
  @@index([type, status])
  @@index([createdAt])
}

// Migration Steps (for complex multi-step migrations)
model MigrationStep {
  id          String   @id @default(cuid())
  
  migrationJobId String
  migrationJob   MigrationJob @relation(fields: [migrationJobId], references: [id], onDelete: Cascade)
  
  // Step Identity
  name        String   // Step name
  order       Int      // Execution order
  type        String   // validation, transformation, import, export
  
  // Step Configuration
  config      Json     // Step-specific configuration
  
  // Step Status
  status      String   // pending, running, completed, failed, skipped
  progress    Int      @default(0)
  
  // Step Results
  recordsProcessed Int?
  recordsSuccess   Int?
  recordsError     Int?
  
  // Step Timing
  startedAt   DateTime?
  completedAt DateTime?
  duration    Int?     // Duration in milliseconds
  
  // Error Information
  errorMessage String?
  errorDetails Json?
  
  // Audit
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([migrationJobId, order])
  @@index([status])
}

// Migration Logs (detailed logging for debugging)
model MigrationLog {
  id          String   @id @default(cuid())
  
  migrationJobId String
  migrationJob   MigrationJob @relation(fields: [migrationJobId], references: [id], onDelete: Cascade)
  
  // Log Entry
  level       String   // info, warn, error, debug
  message     String   // Log message
  details     Json?    // Additional log details
  
  // Log Context
  stepName    String?  // Associated step name
  recordIndex Int?     // Record index being processed
  
  // Audit
  timestamp   DateTime @default(now())
  
  @@index([migrationJobId, timestamp])
  @@index([level])
}

// Migration Templates (reusable migration configurations)
model MigrationTemplate {
  id          String   @id @default(cuid())
  workspaceId String?  // Optional: workspace-specific templates
  workspace   Workspace? @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  // Template Identity
  name        String   // Template name
  description String?  // Template description
  category    String   // import, export, platform_migration
  
  // Template Configuration
  config      Json     // Template configuration
  mapping     Json?    // Default field mapping
  validation  Json?    // Validation rules
  
  // Template Metadata
  isPublic    Boolean  @default(false) // Public templates available to all workspaces
  isActive    Boolean  @default(true)
  
  // Template Usage
  usageCount  Int      @default(0)
  
  // Audit
  createdBy   String   // User ID who created the template
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([category, isPublic])
  @@index([workspaceId])
}

// Platform Connectors (for external platform integrations)
model PlatformConnector {
  id          String   @id @default(cuid())
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  // Connector Identity
  name        String   // Connector name
  platform    String   // slack, github, salesforce, etc.
  type        String   // import, export, bidirectional
  
  // Connector Configuration
  config      Json     // Platform-specific configuration
  credentials Json     // Encrypted credentials
  
  // Connector Status
  isActive    Boolean  @default(true)
  lastSync    DateTime?
  
  // Connector Metadata
  supportedFormats String[] // Supported data formats
  capabilities     String[] // Supported operations
  
  // Rate Limiting
  rateLimitPerHour Int?
  rateLimitPerDay  Int?
  
  // Audit
  createdBy   String   // User ID who created the connector
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@unique([workspaceId, name])
  @@index([platform, type])
}
```

### Migration Configuration Schema

```typescript
// Migration Job Configuration
interface MigrationJobConfig {
  // Job Type
  type: 'import' | 'export' | 'platform_migration' | 'data_transformation';
  
  // Source Configuration
  source: {
    type: 'file' | 'api' | 'database' | 'platform';
    config: FileSourceConfig | APISourceConfig | DatabaseSourceConfig | PlatformSourceConfig;
  };
  
  // Destination Configuration
  destination: {
    type: 'database' | 'file' | 'api' | 'platform';
    config: DatabaseDestConfig | FileDestConfig | APIDestConfig | PlatformDestConfig;
  };
  
  // Data Mapping
  mapping: {
    fields: FieldMapping[];
    transformations: DataTransformation[];
    validation: ValidationRule[];
  };
  
  // Job Options
  options: {
    batchSize: number;
    parallelism: number;
    skipErrors: boolean;
    createBackup: boolean;
    dryRun: boolean;
    rollbackEnabled: boolean;
  };
}

// Field Mapping Configuration
interface FieldMapping {
  source: string;
  destination: string;
  type: 'direct' | 'transform' | 'computed' | 'constant';
  transformation?: {
    function: string;
    parameters: Record<string, any>;
  };
  validation?: {
    required: boolean;
    type: string;
    format?: string;
    constraints?: Record<string, any>;
  };
}

// Data Transformation Rules
interface DataTransformation {
  name: string;
  type: 'filter' | 'map' | 'reduce' | 'validate' | 'normalize';
  condition?: string; // JavaScript expression
  function: string;   // Transformation function
  parameters: Record<string, any>;
}

// Platform-Specific Configurations
interface PlatformSourceConfig {
  platform: string;
  endpoint: string;
  authentication: {
    type: 'oauth' | 'api_key' | 'basic' | 'bearer';
    credentials: Record<string, string>;
  };
  query?: Record<string, any>;
  pagination?: {
    type: 'offset' | 'cursor' | 'page';
    size: number;
  };
}
```

---

## Implementation Blueprint

### Task Breakdown (Information-Dense Implementation)

**Phase 1: Migration Infrastructure (4-6 hours)**

Task 1: Migration Job Engine
CREATE src/lib/migration/migration-engine.ts:
  - IMPLEMENT MigrationEngine class with job lifecycle management
  - CREATE job queuing system with Redis and Bull Queue
  - ADD progress tracking and real-time status updates
  - IMPLEMENT error handling and recovery mechanisms
  - SETUP job scheduling and batch processing

CREATE src/lib/migration/migration-executor.ts:
  - IMPLEMENT MigrationExecutor for step-by-step execution
  - CREATE data streaming and chunked processing
  - ADD memory-efficient large file handling
  - IMPLEMENT parallel processing and worker management
  - SETUP rollback and recovery mechanisms

Task 2: Data Validation and Transformation
CREATE src/lib/migration/data-validator.ts:
  - IMPLEMENT comprehensive data validation framework
  - CREATE schema validation with Zod and JSON Schema
  - ADD data type conversion and normalization
  - IMPLEMENT custom validation rules and constraints
  - SETUP validation reporting and error handling

CREATE src/lib/migration/data-transformer.ts:
  - IMPLEMENT data transformation pipeline
  - CREATE field mapping and value transformation
  - ADD data enrichment and computed fields
  - IMPLEMENT conditional transformations and filters
  - SETUP transformation logging and debugging

**Phase 2: Import/Export Engines (5-7 hours)**

Task 3: File Format Processors
CREATE src/lib/migration/processors/:
  - IMPLEMENT CSVProcessor for CSV import/export
  - CREATE JSONProcessor for JSON data handling
  - ADD XMLProcessor for XML transformation
  - IMPLEMENT SQLProcessor for SQL script generation
  - SETUP ExcelProcessor for spreadsheet handling

CREATE src/lib/migration/processors/csv-processor.ts:
  - IMPLEMENT streaming CSV parser and generator
  - CREATE configurable delimiter and encoding support
  - ADD header mapping and validation
  - IMPLEMENT large file chunked processing
  - SETUP CSV format detection and validation

Task 4: Database Migration Engine
CREATE src/lib/migration/database-migrator.ts:
  - IMPLEMENT database schema migration
  - CREATE data migration with referential integrity
  - ADD incremental migration and delta sync
  - IMPLEMENT migration versioning and rollback
  - SETUP migration conflict resolution

CREATE src/lib/migration/schema-analyzer.ts:
  - IMPLEMENT database schema analysis
  - CREATE schema comparison and diff generation
  - ADD dependency analysis and migration ordering
  - IMPLEMENT schema validation and compatibility checking
  - SETUP schema documentation generation

**Phase 3: Platform Integrations (4-5 hours)**

Task 5: External Platform Connectors
CREATE src/lib/migration/connectors/:
  - IMPLEMENT SlackConnector for Slack data migration
  - CREATE GitHubConnector for repository data
  - ADD SalesforceConnector for CRM data
  - IMPLEMENT NotionConnector for knowledge base
  - SETUP TrelloConnector for project management data

CREATE src/lib/migration/connectors/base-connector.ts:
  - IMPLEMENT BaseConnector abstract class
  - CREATE authentication and rate limiting
  - ADD pagination and data streaming
  - IMPLEMENT error handling and retry logic
  - SETUP connector health monitoring

Task 6: API Integration Framework
CREATE src/lib/migration/api-client.ts:
  - IMPLEMENT generic API client with authentication
  - CREATE rate limiting and request throttling
  - ADD response caching and optimization
  - IMPLEMENT retry logic with exponential backoff
  - SETUP API health monitoring and alerting

CREATE src/lib/migration/webhook-processor.ts:
  - IMPLEMENT webhook-based data synchronization
  - CREATE real-time data streaming
  - ADD webhook validation and security
  - IMPLEMENT event-driven migration triggers
  - SETUP webhook delivery and retry mechanisms

**Phase 4: Migration UI and Management (3-4 hours)**

Task 7: Migration Dashboard
CREATE src/app/(dashboard)/migrations/:
  - IMPLEMENT migration job management interface
  - CREATE migration wizard with step-by-step guidance
  - ADD real-time progress monitoring
  - IMPLEMENT migration history and analytics
  - SETUP migration template management

CREATE src/components/migration/:
  - IMPLEMENT MigrationWizard for guided setup
  - CREATE ProgressTracker for real-time monitoring
  - ADD DataMapper for field mapping interface
  - IMPLEMENT ValidationResults for error display
  - CREATE MigrationHistory for job tracking

Task 8: Migration Templates and Presets
CREATE src/lib/migration/templates/:
  - IMPLEMENT migration template system
  - CREATE platform-specific migration presets
  - ADD custom template creation and sharing
  - IMPLEMENT template validation and testing
  - SETUP template marketplace integration

CREATE src/components/migration/template-builder.ts:
  - IMPLEMENT TemplateBuilder for custom templates
  - CREATE drag-and-drop field mapping
  - ADD transformation rule builder
  - IMPLEMENT template preview and testing
  - SETUP template export and sharing

**Phase 5: Advanced Migration Features (2-3 hours)**

Task 9: Rollback and Recovery System
CREATE src/lib/migration/rollback-manager.ts:
  - IMPLEMENT comprehensive rollback system
  - CREATE snapshot-based recovery
  - ADD incremental rollback capabilities
  - IMPLEMENT rollback validation and testing
  - SETUP rollback audit and compliance

CREATE src/lib/migration/backup-manager.ts:
  - IMPLEMENT automated backup creation
  - CREATE backup validation and integrity checking
  - ADD backup compression and optimization
  - IMPLEMENT backup retention and cleanup
  - SETUP backup restoration and recovery

Task 10: Migration Analytics and Reporting
CREATE src/lib/migration/analytics.ts:
  - IMPLEMENT migration performance analytics
  - CREATE data quality metrics and reporting
  - ADD migration success rate tracking
  - IMPLEMENT cost analysis and optimization
  - SETUP migration insights and recommendations

---

## Integration Points

### 1. Multi-Tenant Migration Isolation

```typescript
// Multi-tenant migration framework
// src/lib/migration/tenant-migration.ts
export class TenantMigrationFramework {
  static async executeMigration(
    workspaceId: string,
    migrationConfig: MigrationJobConfig
  ): Promise<MigrationResult> {
    // Validate workspace access and permissions
    await this.validateWorkspaceAccess(workspaceId, migrationConfig);

    // Create isolated migration context
    const migrationContext = await this.createMigrationContext(workspaceId);

    // Execute migration with tenant isolation
    const result = await this.executeTenantIsolatedMigration(
      migrationContext,
      migrationConfig
    );

    // Audit migration execution
    await this.auditMigrationExecution(workspaceId, migrationConfig, result);

    return result;
  }

  static async createMigrationContext(
    workspaceId: string
  ): Promise<MigrationContext> {
    return {
      workspaceId,
      tenantId: await this.getTenantId(workspaceId),
      databaseSchema: await this.getTenantSchema(workspaceId),
      permissions: await this.getTenantPermissions(workspaceId),
      isolation: {
        dataPrefix: `tenant_${workspaceId}_`,
        schemaName: `workspace_${workspaceId}`,
        connectionPool: await this.getTenantConnection(workspaceId)
      }
    };
  }

  static async validateDataIntegrity(
    workspaceId: string,
    migrationData: any[]
  ): Promise<ValidationResult> {
    // Validate data against tenant schema
    const schema = await this.getTenantSchema(workspaceId);
    const validationResults = await this.validateAgainstSchema(migrationData, schema);

    // Check for data conflicts and duplicates
    const conflictCheck = await this.checkDataConflicts(workspaceId, migrationData);

    // Validate referential integrity
    const integrityCheck = await this.validateReferentialIntegrity(
      workspaceId,
      migrationData
    );

    return {
      valid: validationResults.valid && conflictCheck.valid && integrityCheck.valid,
      errors: [
        ...validationResults.errors,
        ...conflictCheck.errors,
        ...integrityCheck.errors
      ],
      warnings: [
        ...validationResults.warnings,
        ...conflictCheck.warnings,
        ...integrityCheck.warnings
      ]
    };
  }
}
```

### 2. File Storage Integration

```typescript
// Integration with existing file storage system
// src/lib/migration/file-migration.ts
import { FileStorageService } from '../storage/file-storage-service';

export class FileMigrationIntegration {
  static async processMigrationFile(
    workspaceId: string,
    fileId: string,
    migrationConfig: MigrationJobConfig
  ): Promise<MigrationResult> {
    // Download migration file from storage
    const fileBuffer = await FileStorageService.downloadFile(fileId);
    const fileMetadata = await FileStorageService.getFileMetadata(fileId);

    // Validate file format and size
    await this.validateMigrationFile(fileBuffer, fileMetadata, migrationConfig);

    // Process file based on format
    const processor = this.getFileProcessor(fileMetadata.mimeType);
    const migrationData = await processor.parse(fileBuffer, migrationConfig);

    // Execute migration with processed data
    const result = await TenantMigrationFramework.executeMigration(
      workspaceId,
      {
        ...migrationConfig,
        source: {
          type: 'data',
          data: migrationData
        }
      }
    );

    // Store migration results
    if (result.success) {
      const resultFileId = await this.storeMigrationResults(workspaceId, result);
      result.resultFileId = resultFileId;
    }

    return result;
  }

  static async exportToFile(
    workspaceId: string,
    exportConfig: ExportConfig
  ): Promise<string> {
    // Extract data based on export configuration
    const exportData = await this.extractExportData(workspaceId, exportConfig);

    // Transform data to target format
    const processor = this.getFileProcessor(exportConfig.format);
    const fileBuffer = await processor.generate(exportData, exportConfig);

    // Upload to file storage
    const fileName = `export_${workspaceId}_${Date.now()}.${exportConfig.format}`;
    const fileId = await FileStorageService.uploadFile(
      fileBuffer,
      `migrations/exports/${fileName}`,
      {
        workspaceId,
        type: 'migration_export',
        metadata: exportConfig
      }
    );

    return fileId;
  }
}
```

### 3. Audit System Integration

```typescript
// Integration with existing audit logging
// src/lib/migration/migration-audit.ts
import { AuditService } from '../audit/audit-service';

export class MigrationAuditIntegration {
  static async auditMigrationStart(
    workspaceId: string,
    userId: string,
    migrationJob: MigrationJob
  ): Promise<void> {
    await AuditService.log({
      workspaceId,
      userId,
      action: 'migration.started',
      resourceType: 'migration_job',
      resourceId: migrationJob.id,
      details: {
        jobName: migrationJob.name,
        jobType: migrationJob.type,
        sourceType: migrationJob.source?.type,
        destinationType: migrationJob.destination?.type,
        estimatedRecords: migrationJob.totalRecords
      },
      metadata: {
        migrationConfig: migrationJob.mapping,
        jobOptions: migrationJob.options
      }
    });
  }

  static async auditMigrationProgress(
    workspaceId: string,
    migrationJob: MigrationJob,
    progress: MigrationProgress
  ): Promise<void> {
    await AuditService.log({
      workspaceId,
      userId: migrationJob.createdBy,
      action: 'migration.progress',
      resourceType: 'migration_job',
      resourceId: migrationJob.id,
      details: {
        progress: progress.percentage,
        recordsProcessed: progress.recordsProcessed,
        recordsSuccess: progress.recordsSuccess,
        recordsError: progress.recordsError,
        currentStep: progress.currentStep
      }
    });
  }

  static async auditMigrationCompletion(
    workspaceId: string,
    migrationJob: MigrationJob,
    result: MigrationResult
  ): Promise<void> {
    await AuditService.log({
      workspaceId,
      userId: migrationJob.createdBy,
      action: result.success ? 'migration.completed' : 'migration.failed',
      resourceType: 'migration_job',
      resourceId: migrationJob.id,
      details: {
        success: result.success,
        totalRecords: result.totalRecords,
        successRecords: result.successRecords,
        errorRecords: result.errorRecords,
        duration: result.duration,
        errorMessage: result.errorMessage
      },
      metadata: {
        migrationSummary: result.summary,
        performanceMetrics: result.metrics
      }
    });
  }

  static async auditDataChanges(
    workspaceId: string,
    userId: string,
    changes: DataChange[]
  ): Promise<void> {
    for (const change of changes) {
      await AuditService.log({
        workspaceId,
        userId,
        action: `migration.data.${change.operation}`,
        resourceType: change.resourceType,
        resourceId: change.resourceId,
        changes: {
          before: change.before,
          after: change.after
        },
        metadata: {
          migrationJobId: change.migrationJobId,
          migrationStep: change.step
        }
      });
    }
  }
}
```

### 4. Webhook System Integration

```typescript
// Integration with existing webhook system
// src/lib/migration/migration-webhooks.ts
import { WebhookService } from '../webhooks/webhook-service';

export class MigrationWebhookIntegration {
  static async triggerMigrationWebhooks(
    workspaceId: string,
    event: MigrationEvent
  ): Promise<void> {
    // Get webhooks subscribed to migration events
    const webhooks = await WebhookService.getWebhooksByEvent(
      workspaceId,
      `migration.${event.type}`
    );

    // Prepare webhook payload
    const payload = {
      event: `migration.${event.type}`,
      data: {
        migrationJobId: event.migrationJobId,
        workspaceId,
        status: event.status,
        progress: event.progress,
        metadata: event.metadata
      },
      timestamp: new Date().toISOString()
    };

    // Deliver webhooks in parallel
    await Promise.allSettled(
      webhooks.map(webhook =>
        WebhookService.deliverWebhook(webhook, payload)
      )
    );
  }

  static async registerMigrationEventTypes(): Promise<void> {
    // Register migration-specific event types
    const migrationEvents = [
      'migration.started',
      'migration.progress',
      'migration.step_completed',
      'migration.completed',
      'migration.failed',
      'migration.cancelled',
      'migration.rollback_started',
      'migration.rollback_completed'
    ];

    for (const eventType of migrationEvents) {
      await WebhookService.registerEventType(eventType, {
        description: `Triggered when ${eventType.split('.').slice(1).join(' ')}`,
        schema: this.getMigrationEventSchema(eventType)
      });
    }
  }

  static async setupMigrationWebhookEndpoints(
    workspaceId: string,
    migrationJobId: string
  ): Promise<void> {
    // Setup webhook endpoints for external platform integrations
    const webhookUrl = `${process.env.APP_URL}/api/webhooks/migration/${migrationJobId}`;

    // Register webhook with external platforms if needed
    const migrationJob = await this.getMigrationJob(migrationJobId);

    if (migrationJob.source?.type === 'platform') {
      await this.registerPlatformWebhook(
        migrationJob.source.platform,
        webhookUrl,
        migrationJob.source.config
      );
    }
  }
}
```

---

## Security Implementation

### 1. Migration Security Framework

```typescript
// Comprehensive security for migration operations
// src/lib/migration/migration-security.ts
export class MigrationSecurityFramework {
  static async validateMigrationPermissions(
    workspaceId: string,
    userId: string,
    migrationConfig: MigrationJobConfig
  ): Promise<boolean> {
    // Check workspace-level migration permissions
    const hasWorkspaceAccess = await this.checkWorkspacePermissions(
      workspaceId,
      userId,
      'migration'
    );

    if (!hasWorkspaceAccess) {
      throw new SecurityError('Insufficient workspace permissions for migration');
    }

    // Check resource-specific permissions
    const resourcePermissions = await this.checkResourcePermissions(
      workspaceId,
      userId,
      migrationConfig
    );

    if (!resourcePermissions.allowed) {
      throw new SecurityError(
        `Insufficient permissions for resources: ${resourcePermissions.deniedResources.join(', ')}`
      );
    }

    // Validate data access permissions
    await this.validateDataAccessPermissions(workspaceId, userId, migrationConfig);

    return true;
  }

  static async encryptSensitiveData(
    migrationData: any[],
    encryptionConfig: EncryptionConfig
  ): Promise<any[]> {
    const encryptedData = [];

    for (const record of migrationData) {
      const encryptedRecord = { ...record };

      // Encrypt sensitive fields
      for (const field of encryptionConfig.sensitiveFields) {
        if (encryptedRecord[field]) {
          encryptedRecord[field] = await this.encryptField(
            encryptedRecord[field],
            encryptionConfig.key
          );
        }
      }

      encryptedData.push(encryptedRecord);
    }

    return encryptedData;
  }

  static async validateMigrationSource(
    source: MigrationSource
  ): Promise<ValidationResult> {
    switch (source.type) {
      case 'file':
        return await this.validateFileSource(source);
      case 'api':
        return await this.validateAPISource(source);
      case 'platform':
        return await this.validatePlatformSource(source);
      default:
        throw new SecurityError(`Unsupported migration source type: ${source.type}`);
    }
  }

  private static async validateFileSource(
    source: FileSource
  ): Promise<ValidationResult> {
    // Validate file type and size
    const allowedTypes = ['text/csv', 'application/json', 'text/xml', 'application/vnd.ms-excel'];
    if (!allowedTypes.includes(source.mimeType)) {
      return {
        valid: false,
        errors: [`File type ${source.mimeType} is not allowed`]
      };
    }

    // Check file size limits
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (source.size > maxSize) {
      return {
        valid: false,
        errors: [`File size ${source.size} exceeds maximum allowed size of ${maxSize}`]
      };
    }

    // Scan file for malicious content
    const scanResult = await this.scanFileForMalware(source.fileId);
    if (!scanResult.clean) {
      return {
        valid: false,
        errors: ['File contains potentially malicious content']
      };
    }

    return { valid: true, errors: [] };
  }
}
```

### 2. Data Privacy and Compliance

```typescript
// Data privacy and compliance framework
// src/lib/migration/privacy-compliance.ts
export class MigrationPrivacyCompliance {
  static async applyDataPrivacyRules(
    workspaceId: string,
    migrationData: any[],
    privacyConfig: PrivacyConfig
  ): Promise<any[]> {
    const processedData = [];

    for (const record of migrationData) {
      let processedRecord = { ...record };

      // Apply GDPR compliance rules
      if (privacyConfig.gdprCompliance) {
        processedRecord = await this.applyGDPRRules(processedRecord, privacyConfig);
      }

      // Apply data anonymization
      if (privacyConfig.anonymization) {
        processedRecord = await this.anonymizeData(processedRecord, privacyConfig);
      }

      // Apply data masking
      if (privacyConfig.dataMasking) {
        processedRecord = await this.maskSensitiveData(processedRecord, privacyConfig);
      }

      processedData.push(processedRecord);
    }

    return processedData;
  }

  static async validateDataRetentionPolicies(
    workspaceId: string,
    migrationData: any[]
  ): Promise<ValidationResult> {
    const retentionPolicies = await this.getWorkspaceRetentionPolicies(workspaceId);
    const violations = [];

    for (const record of migrationData) {
      for (const policy of retentionPolicies) {
        const violation = await this.checkRetentionViolation(record, policy);
        if (violation) {
          violations.push(violation);
        }
      }
    }

    return {
      valid: violations.length === 0,
      errors: violations.map(v => v.message),
      warnings: violations.filter(v => v.severity === 'warning').map(v => v.message)
    };
  }

  static async generateComplianceReport(
    migrationJobId: string
  ): Promise<ComplianceReport> {
    const migrationJob = await this.getMigrationJob(migrationJobId);
    const complianceChecks = await this.runComplianceChecks(migrationJob);

    return {
      migrationJobId,
      timestamp: new Date(),
      complianceStatus: complianceChecks.every(c => c.passed) ? 'compliant' : 'non_compliant',
      checks: complianceChecks,
      recommendations: await this.generateComplianceRecommendations(complianceChecks),
      auditTrail: await this.generateAuditTrail(migrationJobId)
    };
  }
}
```

---

## Validation Gates (Executable Testing)

### Level 1: Syntax & Style Validation
```bash
# TypeScript compilation and linting
npm run lint                    # ESLint checks for migration system
npx tsc --noEmit               # TypeScript type checking
npm run type-check             # Migration framework type validation

# Migration-specific validation
npm run validate-migrations    # Migration configuration validation
npm run security-scan          # Security scanning for migration tools
```

### Level 2: Unit Testing
```bash
# Core migration system tests
npm test src/lib/migration/    # Migration engine and processor tests
npm test src/components/migration/ # Migration UI component tests
npm test src/app/api/migration/ # Migration API endpoint tests

# Data processing tests
npm run test:processors        # File processor tests
npm run test:validators        # Data validation tests
npm run test:transformers      # Data transformation tests
```

### Level 3: Integration Testing
```bash
# Start development server
npm run dev

# Test migration API endpoints
curl -X POST http://localhost:3000/api/migration/jobs \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{"name": "Test Migration", "type": "import", "workspaceId": "workspace-123"}'

# Test file upload for migration
curl -X POST http://localhost:3000/api/migration/upload \
  -H "Authorization: Bearer $API_KEY" \
  -F "file=@test-data.csv" \
  -F "workspaceId=workspace-123"

# Test migration status
curl -X GET http://localhost:3000/api/migration/jobs/job-123/status \
  -H "Authorization: Bearer $API_KEY"
```

### Level 4: End-to-End Migration Testing
```bash
# Production build validation
npm run build                  # Build with migration system
npm run start                  # Production server testing

# Migration E2E testing
npm run test:e2e:migration     # Playwright E2E tests for migration
npm run test:migration-flows   # Complete migration workflow tests
npm run test:rollback          # Rollback functionality tests
```

### Level 5: Data Migration Testing
```bash
# Migration data validation
npm run test:data-integrity    # Data integrity validation tests
npm run test:performance       # Migration performance tests
npm run test:large-datasets    # Large dataset migration tests

# Platform integration testing
npm run test:platform-connectors # External platform connector tests
npm run test:api-integrations  # API integration tests
npm run test:webhook-delivery  # Webhook delivery tests
```

---

## Quality Standards Checklist

- [x] **Multi-tenant isolation**: All migration operations include workspace context and data isolation
- [x] **Data integrity**: Comprehensive validation, checksums, and referential integrity checking
- [x] **Zero-downtime migrations**: All migrations execute without service interruption
- [x] **Rollback capabilities**: Complete rollback system with snapshot-based recovery
- [x] **Security framework**: Encryption, access control, and audit compliance
- [x] **Format support**: Comprehensive support for JSON, CSV, XML, SQL, and custom formats
- [x] **Platform integrations**: Connectors for major SaaS platforms and APIs
- [x] **Performance optimization**: Streaming, chunked processing, and parallel execution
- [x] **Audit compliance**: Complete migration tracking and audit trails
- [x] **Error handling**: Comprehensive error handling, recovery, and reporting

---

**Framework**: NEXUS SaaS Starter Multi-Tenant Migration Platform
**Technology Stack**: Next.js 15.4+ / TypeScript 5.8+ / Prisma / Redis / Bull Queue
**Optimization**: Production-ready, enterprise-grade, zero-downtime migration system
