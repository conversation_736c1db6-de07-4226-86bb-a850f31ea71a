import { FastifyRequest, FastifyReply } from "fastify";
import { z } from "zod";
import { ValidationError } from "../types";

// Validation middleware factory
export const validate = (schema: {
  body?: z.ZodSchema;
  query?: z.ZodSchema;
  params?: z.ZodSchema;
  headers?: z.ZodSchema;
}) => {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // Validate body
      if (schema.body) {
        request.body = schema.body.parse(request.body);
      }

      // Validate query parameters
      if (schema.query) {
        request.query = schema.query.parse(request.query);
      }

      // Validate route parameters
      if (schema.params) {
        request.params = schema.params.parse(request.params);
      }

      // Validate headers
      if (schema.headers) {
        request.headers = schema.headers.parse(request.headers);
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const details = error.errors.map(err => ({
          field: err.path.join("."),
          message: err.message,
          code: err.code,
          received: err.received,
        }));

        throw new ValidationError("Validation failed", details);
      }
      throw error;
    }
  };
};

// Common validation schemas
export const commonSchemas = {
  // Pagination
  pagination: z.object({
    page: z.string().transform(Number).pipe(z.number().min(1)).optional().default("1"),
    limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).optional().default("20"),
    sort: z.string().optional(),
    order: z.enum(["asc", "desc"]).optional().default("asc"),
  }),

  // Search
  search: z.object({
    q: z.string().min(1).optional(),
    filter: z.string().optional(),
  }),

  // ID parameter
  idParam: z.object({
    id: z.string().uuid("Invalid ID format"),
  }),

  // Tenant context
  tenantHeaders: z.object({
    "x-tenant-id": z.string().uuid("Invalid tenant ID").optional(),
    "x-workspace-id": z.string().uuid("Invalid workspace ID").optional(),
    "x-team-id": z.string().uuid("Invalid team ID").optional(),
  }),

  // File upload
  fileUpload: z.object({
    filename: z.string().min(1),
    mimetype: z.string().min(1),
    encoding: z.string().min(1),
  }),

  // Date range
  dateRange: z.object({
    startDate: z.string().datetime().optional(),
    endDate: z.string().datetime().optional(),
  }),
};

// Validation helpers
export const validateBody = (schema: z.ZodSchema) => validate({ body: schema });
export const validateQuery = (schema: z.ZodSchema) => validate({ query: schema });
export const validateParams = (schema: z.ZodSchema) => validate({ params: schema });
export const validateHeaders = (schema: z.ZodSchema) => validate({ headers: schema });

// Common validations
export const validatePagination = validateQuery(commonSchemas.pagination);
export const validateSearch = validateQuery(commonSchemas.search);
export const validateId = validateParams(commonSchemas.idParam);
export const validateTenantHeaders = validateHeaders(commonSchemas.tenantHeaders);
export const validateDateRange = validateQuery(commonSchemas.dateRange);

// Sanitization helpers
export const sanitizeString = (str: string): string => {
  return str.trim().replace(/[<>]/g, "");
};

export const sanitizeEmail = (email: string): string => {
  return email.toLowerCase().trim();
};

export const sanitizeObject = (obj: any): any => {
  if (typeof obj === "string") {
    return sanitizeString(obj);
  }
  
  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject);
  }
  
  if (obj && typeof obj === "object") {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      sanitized[key] = sanitizeObject(value);
    }
    return sanitized;
  }
  
  return obj;
};

// Content type validation
export const validateContentType = (allowedTypes: string[]) => {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    const contentType = request.headers["content-type"];
    
    if (!contentType) {
      throw new ValidationError("Content-Type header is required");
    }

    const isAllowed = allowedTypes.some(type => 
      contentType.startsWith(type)
    );

    if (!isAllowed) {
      throw new ValidationError(
        `Invalid Content-Type. Allowed types: ${allowedTypes.join(", ")}`
      );
    }
  };
};

// JSON content type validation
export const validateJsonContentType = validateContentType(["application/json"]);

// Multipart content type validation
export const validateMultipartContentType = validateContentType(["multipart/form-data"]);

// File size validation
export const validateFileSize = (maxSize: number) => {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    const contentLength = request.headers["content-length"];
    
    if (contentLength && parseInt(contentLength) > maxSize) {
      throw new ValidationError(
        `File size exceeds maximum allowed size of ${maxSize} bytes`
      );
    }
  };
};
