# NEXUS SaaS Starter - White-Label Solution Implementation

**PRP Name**: White-Label Solution - Complete White-Label Package  
**Version**: 1.0  
**Date**: July 18, 2025  
**Type**: Ecosystem & Extensions Implementation PRP  
**Phase**: 06-Ecosystem  
**Framework**: Next.js 15.4+ / TypeScript 5.8+ / Multi-Tenant / White-Label  

---

## Purpose

Build a comprehensive white-label solution that enables complete customization and rebranding of the NEXUS SaaS Starter for partners and resellers. This includes dynamic theming, custom domains, branded interfaces, partner management, and complete white-label deployment capabilities.

## Core Principles

- **Complete Customization**: Full control over branding, themes, and user experience
- **Partner-Centric**: Dedicated partner management and onboarding systems
- **Domain Flexibility**: Custom domain support with SSL and DNS management
- **Brand Isolation**: Complete separation of partner brands and customer data
- **Deployment Ready**: Automated deployment and configuration for partners
- **Revenue Sharing**: Built-in partner revenue tracking and commission systems

---

## Research & Documentation

### Context7-Verified Patterns (CRITICAL)

```yaml
# White-Label Platform Patterns (Context7 Verified)
channex_white_label:
  - url: /channexio/wl_docs
    why: "Production white-label platform with API management, partner onboarding, and multi-tenant architecture"
    critical: "White-label API management, partner configuration, tenant isolation, custom branding"
    patterns: ["White-label APIs", "Partner management", "Custom branding", "Multi-tenant isolation"]

finbuckle_multitenant:
  - url: /finbuckle/finbuckle.multitenant
    why: "Enterprise multi-tenant framework with per-tenant configuration, data isolation, and white-label support"
    critical: "Multi-tenant strategies, per-tenant options, data isolation, tenant resolution"
    patterns: ["Tenant strategies", "Data isolation", "Per-tenant config", "White-label support"]

laravel_multitenancy:
  - url: /spatie/laravel-multitenancy
    why: "Production multi-tenant framework with white-label capabilities and tenant management"
    critical: "Tenant management, database isolation, custom domains, white-label features"
    patterns: ["Tenant management", "Database isolation", "Custom domains", "White-label"]
```

### Current Codebase Integration Points

```typescript
// Multi-tenant Foundation (CRITICAL FOR WHITE-LABEL)
// From: PRPs/features/01-foundation/multi-tenant-database-architecture-implementation.md
interface TenantContext {
  tenantId: string;
  workspaceId: string;
  userId: string;
  permissions: string[];
}

// Theme System (CRITICAL FOR BRANDING)
// From: PRPs/features/02-core/theme-system-implementation.md
interface ThemeConfig {
  id: string;
  name: string;
  colors: ColorPalette;
  typography: TypographyConfig;
  components: ComponentTheme;
  customCSS?: string;
}

// Billing Integration (CRITICAL FOR PARTNER REVENUE)
// From: PRPs/features/02-core/stripe-integration-implementation.md
interface SubscriptionContext {
  customerId: string;
  subscriptionId: string;
  planId: string;
  status: string;
  partnerId?: string;
}

// File Storage (CRITICAL FOR BRAND ASSETS)
// From: PRPs/features/02-core/file-storage-implementation.md
interface FileStorage {
  uploadFile(file: File, path: string): Promise<string>;
  downloadFile(path: string): Promise<Buffer>;
  deleteFile(path: string): Promise<void>;
  getFileMetadata(path: string): Promise<FileMetadata>;
}
```

### Technology Stack Context

```yaml
Core Framework:
  - Next.js: 15.4+ (App Router, Server Actions, Dynamic routing)
  - React: 19 (Server Components, Concurrent Features)
  - TypeScript: 5.8+ (Advanced type system, strict mode)
  - Tailwind CSS: 4.1.11+ (Dynamic theming and customization)

White-Label Infrastructure:
  - DNS Management: Cloudflare API for custom domain setup
  - SSL Certificates: Automated SSL provisioning and renewal
  - CDN: Global content delivery for partner assets
  - Image Processing: Dynamic logo and asset optimization

Partner Management:
  - Partner Portal: Dedicated partner management interface
  - Revenue Tracking: Commission and revenue sharing systems
  - Onboarding: Automated partner setup and configuration
  - Support: Partner-specific support and documentation

Customization Engine:
  - Dynamic Theming: Real-time theme switching and customization
  - Brand Assets: Logo, favicon, and asset management
  - Custom CSS: Advanced styling and layout customization
  - Component Overrides: Custom component implementations
```

---

## Data Models and Structure

### Database Schema (Prisma)

```typescript
// Partner Management and White-Label Configuration
model Partner {
  id          String   @id @default(cuid())
  
  // Partner Identity
  name        String   // Partner company name
  slug        String   @unique // URL-friendly identifier
  email       String   @unique // Primary contact email
  
  // Partner Status
  status      String   // active, pending, suspended, cancelled
  tier        String   // basic, professional, enterprise
  
  // Partner Configuration
  config      Json     // Partner-specific configuration
  features    String[] // Enabled features for this partner
  limits      Json     // Usage limits and quotas
  
  // Branding Configuration
  branding    Json     // Brand colors, logos, themes
  customDomain String? // Custom domain (e.g., partner.com)
  subdomain   String?  // Subdomain (e.g., partner.nexus.com)
  
  // Revenue Sharing
  commissionRate Float @default(0.0) // Commission percentage
  revenueModel   String @default("percentage") // percentage, fixed, hybrid
  
  // Partner Contacts
  primaryContactId String?
  primaryContact   User? @relation("PartnerPrimaryContact", fields: [primaryContactId], references: [id])
  
  // Technical Configuration
  apiKey      String   @unique // Partner API key
  webhookUrl  String?  // Partner webhook endpoint
  
  // Onboarding
  onboardingStatus String @default("pending") // pending, in_progress, completed
  onboardingData   Json?  // Onboarding progress and data
  
  // Audit
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  workspaces  Workspace[]
  users       User[]
  transactions PartnerTransaction[]
  domains     PartnerDomain[]
  themes      PartnerTheme[]
  
  @@index([status])
  @@index([tier])
  @@index([slug])
}

// Partner Custom Domains
model PartnerDomain {
  id          String   @id @default(cuid())
  
  partnerId   String
  partner     Partner  @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  
  // Domain Configuration
  domain      String   @unique // Custom domain
  subdomain   String?  // Optional subdomain
  
  // DNS Configuration
  dnsStatus   String   @default("pending") // pending, active, failed
  sslStatus   String   @default("pending") // pending, active, failed, expired
  
  // SSL Certificate
  sslCertId   String?  // SSL certificate identifier
  sslExpiry   DateTime? // SSL certificate expiry
  
  // Domain Verification
  verificationToken String @unique
  verificationStatus String @default("pending") // pending, verified, failed
  
  // Domain Status
  isActive    Boolean  @default(false)
  isPrimary   Boolean  @default(false)
  
  // Audit
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([partnerId])
  @@index([domain])
  @@index([verificationStatus])
}

// Partner Custom Themes
model PartnerTheme {
  id          String   @id @default(cuid())
  
  partnerId   String
  partner     Partner  @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  
  // Theme Identity
  name        String   // Theme name
  description String?  // Theme description
  
  // Theme Configuration
  config      Json     // Complete theme configuration
  colors      Json     // Color palette
  typography  Json     // Typography settings
  components  Json     // Component customizations
  
  // Custom Assets
  logoUrl     String?  // Custom logo URL
  faviconUrl  String?  // Custom favicon URL
  backgroundUrl String? // Custom background URL
  
  // Custom CSS
  customCSS   String?  // Additional custom CSS
  
  // Theme Status
  isActive    Boolean  @default(false)
  isDefault   Boolean  @default(false)
  
  // Version Control
  version     String   @default("1.0.0")
  parentThemeId String? // Base theme this extends
  
  // Audit
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@unique([partnerId, name])
  @@index([partnerId, isActive])
}

// Partner Revenue and Transactions
model PartnerTransaction {
  id          String   @id @default(cuid())
  
  partnerId   String
  partner     Partner  @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  
  // Transaction Details
  type        String   // commission, bonus, adjustment, payout
  amount      Decimal  @db.Decimal(10, 2)
  currency    String   @default("USD")
  
  // Source Information
  sourceType  String   // subscription, usage, one_time
  sourceId    String?  // Related subscription or transaction ID
  workspaceId String?  // Related workspace
  
  // Commission Details
  baseAmount  Decimal? @db.Decimal(10, 2) // Original transaction amount
  commissionRate Float? // Commission rate applied
  
  // Transaction Status
  status      String   @default("pending") // pending, processed, failed, cancelled
  
  // Payment Information
  payoutId    String?  // Payout batch ID
  payoutDate  DateTime? // When commission was paid out
  
  // Metadata
  description String?  // Transaction description
  metadata    Json?    // Additional transaction data
  
  // Audit
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([partnerId, status])
  @@index([type, status])
  @@index([createdAt])
}

// White-Label Workspace Configuration
model WhiteLabelConfig {
  id          String   @id @default(cuid())
  workspaceId String   @unique
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  partnerId   String?
  partner     Partner? @relation(fields: [partnerId], references: [id])
  
  // Branding Configuration
  brandName   String?  // Custom brand name
  brandLogo   String?  // Brand logo URL
  brandFavicon String? // Brand favicon URL
  brandColors Json?    // Custom brand colors
  
  // Domain Configuration
  customDomain String? // Custom domain for this workspace
  subdomain   String?  // Custom subdomain
  
  // Feature Configuration
  features    Json     // Enabled/disabled features
  customization Json   // UI customization settings
  
  // Email Branding
  emailFromName String? // Custom "from" name for emails
  emailLogo     String? // Logo for email templates
  emailFooter   String? // Custom email footer
  
  // Legal and Compliance
  termsUrl    String?  // Custom terms of service URL
  privacyUrl  String?  // Custom privacy policy URL
  supportEmail String? // Custom support email
  
  // Advanced Customization
  customCSS   String?  // Custom CSS overrides
  customJS    String?  // Custom JavaScript (if allowed)
  
  // White-Label Status
  isActive    Boolean  @default(false)
  
  // Audit
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([partnerId])
  @@index([customDomain])
}

// Partner User Management
model PartnerUser {
  id          String   @id @default(cuid())
  
  partnerId   String
  partner     Partner  @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Partner Role
  role        String   // admin, manager, user, viewer
  
  // Permissions
  permissions String[] // Specific permissions within partner context
  
  // Access Control
  isActive    Boolean  @default(true)
  
  // Audit
  joinedAt    DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@unique([partnerId, userId])
  @@index([partnerId, role])
}
```

### White-Label Configuration Schema

```typescript
// White-Label System Configuration
interface WhiteLabelSystemConfig {
  // Partner Configuration
  partner: {
    onboardingFlow: OnboardingStep[];
    defaultFeatures: string[];
    tierLimits: Record<string, TierLimits>;
    commissionStructure: CommissionStructure;
  };
  
  // Domain Management
  domains: {
    allowCustomDomains: boolean;
    subdomainPattern: string;
    dnsProvider: 'cloudflare' | 'route53' | 'custom';
    sslProvider: 'letsencrypt' | 'cloudflare' | 'custom';
    verificationMethods: string[];
  };
  
  // Branding Configuration
  branding: {
    allowedAssetTypes: string[];
    maxLogoSize: number;
    maxFaviconSize: number;
    customCSSLimit: number;
    themeCustomization: ThemeCustomizationConfig;
  };
  
  // Revenue Configuration
  revenue: {
    defaultCommissionRate: number;
    payoutSchedule: 'weekly' | 'monthly' | 'quarterly';
    minimumPayout: number;
    payoutMethods: string[];
  };
}

// Partner Onboarding Configuration
interface OnboardingStep {
  id: string;
  name: string;
  description: string;
  type: 'form' | 'verification' | 'configuration' | 'review';
  required: boolean;
  fields?: OnboardingField[];
  validation?: ValidationRule[];
  dependencies?: string[];
}

// Theme Customization Configuration
interface ThemeCustomizationConfig {
  allowColorCustomization: boolean;
  allowTypographyCustomization: boolean;
  allowLayoutCustomization: boolean;
  allowComponentOverrides: boolean;
  customCSSEnabled: boolean;
  presetThemes: PresetTheme[];
}

// Commission Structure Configuration
interface CommissionStructure {
  type: 'percentage' | 'fixed' | 'tiered' | 'hybrid';
  rates: CommissionRate[];
  bonuses?: CommissionBonus[];
  minimums?: CommissionMinimum[];
}

interface CommissionRate {
  tier?: string;
  productType?: string;
  rate: number;
  threshold?: number;
}
```

---

## Implementation Blueprint

### Task Breakdown (Information-Dense Implementation)

**Phase 1: Partner Management System (5-7 hours)**

Task 1: Partner Registration and Onboarding
CREATE src/lib/white-label/partner-management.ts:
  - IMPLEMENT PartnerService with full CRUD operations
  - CREATE partner registration and approval workflows
  - ADD automated onboarding process with step tracking
  - IMPLEMENT partner tier management and feature gating
  - SETUP partner API key generation and management

CREATE src/lib/white-label/partner-onboarding.ts:
  - IMPLEMENT multi-step onboarding wizard
  - CREATE onboarding progress tracking
  - ADD document upload and verification
  - IMPLEMENT automated setup and configuration
  - SETUP onboarding email sequences and notifications

Task 2: Partner Portal and Dashboard
CREATE src/app/(partner-portal)/:
  - IMPLEMENT comprehensive partner dashboard
  - CREATE partner analytics and reporting
  - ADD revenue tracking and commission management
  - IMPLEMENT partner user management
  - SETUP partner support and documentation

CREATE src/components/partner/:
  - IMPLEMENT PartnerDashboard with key metrics
  - CREATE RevenueChart for commission tracking
  - ADD PartnerSettings for configuration management
  - IMPLEMENT UserManagement for partner team
  - CREATE SupportTickets for partner support

**Phase 2: Custom Domain and DNS Management (4-5 hours)**

Task 3: Domain Management System
CREATE src/lib/white-label/domain-manager.ts:
  - IMPLEMENT custom domain registration and verification
  - CREATE DNS record management and validation
  - ADD SSL certificate provisioning and renewal
  - IMPLEMENT domain health monitoring
  - SETUP automated domain configuration

CREATE src/lib/white-label/dns-provider.ts:
  - IMPLEMENT Cloudflare DNS integration
  - CREATE Route53 DNS integration
  - ADD DNS record creation and management
  - IMPLEMENT DNS propagation checking
  - SETUP DNS health monitoring and alerts

Task 4: SSL Certificate Management
CREATE src/lib/white-label/ssl-manager.ts:
  - IMPLEMENT Let's Encrypt integration
  - CREATE SSL certificate generation and installation
  - ADD certificate renewal automation
  - IMPLEMENT SSL health monitoring
  - SETUP certificate expiry notifications

CREATE src/lib/white-label/cdn-manager.ts:
  - IMPLEMENT CDN configuration for custom domains
  - CREATE asset optimization and delivery
  - ADD cache management and purging
  - IMPLEMENT global edge distribution
  - SETUP performance monitoring and analytics

**Phase 3: Dynamic Branding and Theming (4-6 hours)**

Task 5: Brand Asset Management
CREATE src/lib/white-label/brand-manager.ts:
  - IMPLEMENT brand asset upload and processing
  - CREATE logo and favicon optimization
  - ADD brand color palette management
  - IMPLEMENT asset versioning and rollback
  - SETUP brand asset CDN distribution

CREATE src/lib/white-label/theme-engine.ts:
  - IMPLEMENT dynamic theme generation
  - CREATE real-time theme switching
  - ADD custom CSS injection and validation
  - IMPLEMENT theme inheritance and overrides
  - SETUP theme performance optimization

Task 6: UI Customization System
CREATE src/components/white-label/:
  - IMPLEMENT DynamicThemeProvider for runtime theming
  - CREATE BrandAssetManager for asset management
  - ADD CustomCSSEditor for advanced styling
  - IMPLEMENT ThemePreview for real-time preview
  - CREATE ComponentOverrides for custom components

CREATE src/lib/white-label/customization-engine.ts:
  - IMPLEMENT UI component customization
  - CREATE layout and structure modifications
  - ADD feature toggle management
  - IMPLEMENT custom navigation and menus
  - SETUP customization validation and security

**Phase 4: White-Label Deployment (3-4 hours)**

Task 7: Automated Deployment System
CREATE src/lib/white-label/deployment-manager.ts:
  - IMPLEMENT automated white-label deployment
  - CREATE environment provisioning and configuration
  - ADD database setup and migration
  - IMPLEMENT application configuration and customization
  - SETUP deployment monitoring and rollback

CREATE src/lib/white-label/infrastructure-manager.ts:
  - IMPLEMENT cloud infrastructure provisioning
  - CREATE container orchestration and scaling
  - ADD load balancer and CDN configuration
  - IMPLEMENT monitoring and alerting setup
  - SETUP backup and disaster recovery

Task 8: Configuration Management
CREATE src/lib/white-label/config-manager.ts:
  - IMPLEMENT dynamic configuration management
  - CREATE environment-specific settings
  - ADD feature flag management
  - IMPLEMENT configuration validation and testing
  - SETUP configuration deployment and rollback

CREATE src/lib/white-label/environment-manager.ts:
  - IMPLEMENT multi-environment management
  - CREATE staging and production environments
  - ADD environment synchronization
  - IMPLEMENT environment-specific customizations
  - SETUP environment monitoring and health checks

**Phase 5: Revenue and Analytics (2-3 hours)**

Task 9: Revenue Tracking System
CREATE src/lib/white-label/revenue-tracker.ts:
  - IMPLEMENT commission calculation and tracking
  - CREATE revenue reporting and analytics
  - ADD payout management and automation
  - IMPLEMENT revenue forecasting and insights
  - SETUP revenue audit and compliance

CREATE src/lib/white-label/commission-engine.ts:
  - IMPLEMENT flexible commission structures
  - CREATE tiered and performance-based commissions
  - ADD bonus and incentive management
  - IMPLEMENT commission dispute resolution
  - SETUP commission reporting and transparency

Task 10: Partner Analytics and Insights
CREATE src/lib/white-label/partner-analytics.ts:
  - IMPLEMENT comprehensive partner analytics
  - CREATE customer acquisition and retention metrics
  - ADD usage and engagement analytics
  - IMPLEMENT performance benchmarking
  - SETUP predictive analytics and recommendations

---

## Integration Points

### 1. Multi-Tenant White-Label Integration

```typescript
// Multi-tenant white-label framework integration
// src/lib/white-label/tenant-white-label.ts
export class TenantWhiteLabelIntegration {
  static async resolveWhiteLabelConfig(
    request: Request
  ): Promise<WhiteLabelConfig | null> {
    // Extract domain from request
    const host = request.headers.get('host');
    const domain = this.extractDomain(host);

    // Check for custom domain configuration
    const customDomain = await this.getCustomDomainConfig(domain);
    if (customDomain) {
      return await this.getWhiteLabelConfig(customDomain.partnerId);
    }

    // Check for subdomain configuration
    const subdomain = this.extractSubdomain(host);
    if (subdomain) {
      const partner = await this.getPartnerBySubdomain(subdomain);
      if (partner) {
        return await this.getWhiteLabelConfig(partner.id);
      }
    }

    // Check for workspace-specific white-label
    const workspaceId = await this.extractWorkspaceFromRequest(request);
    if (workspaceId) {
      return await this.getWorkspaceWhiteLabelConfig(workspaceId);
    }

    return null;
  }

  static async applyWhiteLabelContext(
    config: WhiteLabelConfig,
    context: TenantContext
  ): Promise<WhiteLabelContext> {
    // Apply branding configuration
    const branding = await this.resolveBrandingConfig(config);

    // Apply theme configuration
    const theme = await this.resolveThemeConfig(config);

    // Apply feature configuration
    const features = await this.resolveFeatureConfig(config, context);

    // Apply domain configuration
    const domain = await this.resolveDomainConfig(config);

    return {
      partnerId: config.partnerId,
      workspaceId: config.workspaceId,
      branding,
      theme,
      features,
      domain,
      customization: config.customization
    };
  }

  static async validateWhiteLabelAccess(
    partnerId: string,
    workspaceId: string,
    userId: string
  ): Promise<boolean> {
    // Validate partner access to workspace
    const partnerAccess = await this.validatePartnerWorkspaceAccess(
      partnerId,
      workspaceId
    );

    if (!partnerAccess) {
      return false;
    }

    // Validate user access within partner context
    const userAccess = await this.validatePartnerUserAccess(
      partnerId,
      userId
    );

    return userAccess;
  }
}
```

### 2. Dynamic Theme Integration

```typescript
// Integration with existing theme system
// src/lib/white-label/theme-integration.ts
import { ThemeService } from '../theme/theme-service';

export class WhiteLabelThemeIntegration {
  static async generatePartnerTheme(
    partnerId: string,
    baseTheme: ThemeConfig,
    customization: PartnerCustomization
  ): Promise<ThemeConfig> {
    // Start with base theme
    let partnerTheme = { ...baseTheme };

    // Apply partner color customizations
    if (customization.colors) {
      partnerTheme.colors = this.mergeColorPalette(
        partnerTheme.colors,
        customization.colors
      );
    }

    // Apply typography customizations
    if (customization.typography) {
      partnerTheme.typography = this.mergeTypography(
        partnerTheme.typography,
        customization.typography
      );
    }

    // Apply component customizations
    if (customization.components) {
      partnerTheme.components = this.mergeComponentTheme(
        partnerTheme.components,
        customization.components
      );
    }

    // Add custom CSS
    if (customization.customCSS) {
      partnerTheme.customCSS = this.validateAndSanitizeCSS(
        customization.customCSS
      );
    }

    // Generate theme assets
    await this.generateThemeAssets(partnerId, partnerTheme);

    return partnerTheme;
  }

  static async deployPartnerTheme(
    partnerId: string,
    theme: ThemeConfig
  ): Promise<void> {
    // Generate CSS files
    const cssFiles = await this.generateThemeCSS(theme);

    // Upload to CDN
    const cdnUrls = await this.uploadThemeAssets(partnerId, cssFiles);

    // Update partner configuration
    await this.updatePartnerThemeConfig(partnerId, {
      themeId: theme.id,
      cssUrls: cdnUrls,
      version: theme.version,
      deployedAt: new Date()
    });

    // Invalidate CDN cache
    await this.invalidateThemeCache(partnerId);
  }

  static async getPartnerThemeCSS(
    partnerId: string,
    workspaceId?: string
  ): Promise<string> {
    // Get partner theme configuration
    const partnerTheme = await this.getPartnerTheme(partnerId);

    // Get workspace-specific overrides
    const workspaceOverrides = workspaceId
      ? await this.getWorkspaceThemeOverrides(workspaceId)
      : null;

    // Merge configurations
    const finalTheme = workspaceOverrides
      ? this.mergeThemeConfigs(partnerTheme, workspaceOverrides)
      : partnerTheme;

    // Generate CSS
    return await this.generateDynamicCSS(finalTheme);
  }
}
```

### 3. Billing Integration for Partners

```typescript
// Integration with existing billing system for partner revenue
// src/lib/white-label/partner-billing.ts
import { StripeService } from '../billing/stripe-service';

export class PartnerBillingIntegration {
  static async trackPartnerRevenue(
    partnerId: string,
    workspaceId: string,
    transaction: BillingTransaction
  ): Promise<void> {
    // Get partner commission configuration
    const partner = await this.getPartner(partnerId);
    const commissionRate = partner.commissionRate;

    // Calculate commission
    const commissionAmount = this.calculateCommission(
      transaction.amount,
      commissionRate,
      partner.revenueModel
    );

    // Create partner transaction record
    await this.createPartnerTransaction({
      partnerId,
      type: 'commission',
      amount: commissionAmount,
      currency: transaction.currency,
      sourceType: transaction.type,
      sourceId: transaction.id,
      workspaceId,
      baseAmount: transaction.amount,
      commissionRate,
      status: 'pending'
    });

    // Update partner revenue metrics
    await this.updatePartnerMetrics(partnerId, {
      totalRevenue: commissionAmount,
      transactionCount: 1,
      lastTransactionDate: new Date()
    });
  }

  static async processPartnerPayouts(
    partnerId: string,
    payoutPeriod: PayoutPeriod
  ): Promise<PayoutResult> {
    // Get pending partner transactions
    const pendingTransactions = await this.getPendingTransactions(
      partnerId,
      payoutPeriod
    );

    if (pendingTransactions.length === 0) {
      return { success: true, amount: 0, transactionCount: 0 };
    }

    // Calculate total payout amount
    const totalAmount = pendingTransactions.reduce(
      (sum, tx) => sum + tx.amount,
      0
    );

    // Check minimum payout threshold
    const partner = await this.getPartner(partnerId);
    if (totalAmount < partner.minimumPayout) {
      return {
        success: false,
        reason: 'Below minimum payout threshold',
        amount: totalAmount
      };
    }

    // Process payout via Stripe Connect
    const payoutResult = await StripeService.createPayout({
      amount: totalAmount,
      currency: 'USD',
      destination: partner.stripeAccountId,
      metadata: {
        partnerId,
        payoutPeriod: payoutPeriod.id,
        transactionCount: pendingTransactions.length
      }
    });

    // Update transaction statuses
    await this.markTransactionsAsPaid(
      pendingTransactions.map(tx => tx.id),
      payoutResult.id
    );

    return {
      success: true,
      amount: totalAmount,
      transactionCount: pendingTransactions.length,
      payoutId: payoutResult.id
    };
  }

  static async setupPartnerStripeAccount(
    partnerId: string,
    partnerInfo: PartnerAccountInfo
  ): Promise<string> {
    // Create Stripe Connect account for partner
    const account = await StripeService.createConnectAccount({
      type: 'express',
      country: partnerInfo.country,
      email: partnerInfo.email,
      business_profile: {
        name: partnerInfo.businessName,
        url: partnerInfo.website
      },
      metadata: {
        partnerId,
        partnerName: partnerInfo.businessName
      }
    });

    // Update partner with Stripe account ID
    await this.updatePartner(partnerId, {
      stripeAccountId: account.id,
      payoutEnabled: false // Will be enabled after onboarding
    });

    return account.id;
  }
}
```

### 4. File Storage Integration for Brand Assets

```typescript
// Integration with existing file storage for brand assets
// src/lib/white-label/brand-asset-storage.ts
import { FileStorageService } from '../storage/file-storage-service';

export class BrandAssetStorageIntegration {
  static async uploadBrandAsset(
    partnerId: string,
    assetType: 'logo' | 'favicon' | 'background' | 'custom',
    file: File,
    options?: AssetUploadOptions
  ): Promise<BrandAsset> {
    // Validate asset type and size
    await this.validateAssetUpload(assetType, file);

    // Generate asset path
    const assetPath = this.generateAssetPath(partnerId, assetType, file.name);

    // Process asset based on type
    const processedFile = await this.processAsset(assetType, file, options);

    // Upload to storage
    const fileId = await FileStorageService.uploadFile(
      processedFile,
      assetPath,
      {
        partnerId,
        assetType,
        originalName: file.name,
        metadata: options?.metadata
      }
    );

    // Generate CDN URLs
    const urls = await this.generateAssetUrls(fileId, assetType);

    // Create brand asset record
    const brandAsset = await this.createBrandAsset({
      partnerId,
      assetType,
      fileId,
      originalName: file.name,
      urls,
      metadata: {
        size: processedFile.size,
        mimeType: processedFile.type,
        dimensions: await this.getImageDimensions(processedFile)
      }
    });

    return brandAsset;
  }

  static async generateAssetVariants(
    partnerId: string,
    assetId: string
  ): Promise<AssetVariant[]> {
    const asset = await this.getBrandAsset(assetId);
    const originalFile = await FileStorageService.downloadFile(asset.fileId);

    const variants: AssetVariant[] = [];

    // Generate different sizes for logos
    if (asset.assetType === 'logo') {
      const sizes = [32, 64, 128, 256, 512];

      for (const size of sizes) {
        const resizedFile = await this.resizeImage(originalFile, size, size);
        const variantPath = this.generateVariantPath(partnerId, asset.assetType, size);

        const variantFileId = await FileStorageService.uploadFile(
          resizedFile,
          variantPath,
          {
            partnerId,
            assetType: asset.assetType,
            variant: `${size}x${size}`,
            parentAssetId: assetId
          }
        );

        variants.push({
          size: `${size}x${size}`,
          fileId: variantFileId,
          url: await this.generateAssetUrl(variantFileId)
        });
      }
    }

    // Generate favicon variants
    if (asset.assetType === 'favicon') {
      const faviconSizes = [16, 32, 48, 64];

      for (const size of faviconSizes) {
        const faviconFile = await this.generateFavicon(originalFile, size);
        const variantPath = this.generateVariantPath(partnerId, 'favicon', size);

        const variantFileId = await FileStorageService.uploadFile(
          faviconFile,
          variantPath,
          {
            partnerId,
            assetType: 'favicon',
            variant: `${size}x${size}`,
            parentAssetId: assetId
          }
        );

        variants.push({
          size: `${size}x${size}`,
          fileId: variantFileId,
          url: await this.generateAssetUrl(variantFileId)
        });
      }
    }

    return variants;
  }

  static async optimizeAssetDelivery(
    partnerId: string
  ): Promise<void> {
    // Get all partner assets
    const assets = await this.getPartnerAssets(partnerId);

    // Optimize images
    for (const asset of assets) {
      if (this.isImageAsset(asset)) {
        await this.optimizeImage(asset);
      }
    }

    // Setup CDN caching
    await this.configureCDNCaching(partnerId, assets);

    // Generate WebP variants
    await this.generateWebPVariants(partnerId, assets);

    // Update asset URLs with optimized versions
    await this.updateAssetUrls(partnerId, assets);
  }
}
```

---

## Security Implementation

### 1. White-Label Security Framework

```typescript
// Comprehensive security for white-label operations
// src/lib/white-label/security-framework.ts
export class WhiteLabelSecurityFramework {
  static async validatePartnerAccess(
    partnerId: string,
    userId: string,
    action: string,
    resourceId?: string
  ): Promise<boolean> {
    // Check partner status
    const partner = await this.getPartner(partnerId);
    if (partner.status !== 'active') {
      throw new SecurityError('Partner account is not active');
    }

    // Check user's partner membership
    const partnerUser = await this.getPartnerUser(partnerId, userId);
    if (!partnerUser || !partnerUser.isActive) {
      throw new SecurityError('User is not an active partner member');
    }

    // Check action permissions
    const hasPermission = await this.checkPartnerPermissions(
      partnerUser.role,
      partnerUser.permissions,
      action
    );

    if (!hasPermission) {
      throw new SecurityError(`Insufficient permissions for action: ${action}`);
    }

    // Check resource-specific access
    if (resourceId) {
      const hasResourceAccess = await this.checkResourceAccess(
        partnerId,
        userId,
        resourceId
      );

      if (!hasResourceAccess) {
        throw new SecurityError('Access denied to resource');
      }
    }

    return true;
  }

  static async validateCustomDomain(
    partnerId: string,
    domain: string
  ): Promise<ValidationResult> {
    // Check domain format
    if (!this.isValidDomainFormat(domain)) {
      return {
        valid: false,
        errors: ['Invalid domain format']
      };
    }

    // Check domain availability
    const isAvailable = await this.checkDomainAvailability(domain);
    if (!isAvailable) {
      return {
        valid: false,
        errors: ['Domain is already in use']
      };
    }

    // Check domain ownership
    const ownershipVerified = await this.verifyDomainOwnership(partnerId, domain);
    if (!ownershipVerified) {
      return {
        valid: false,
        errors: ['Domain ownership verification failed']
      };
    }

    // Check domain reputation
    const reputationCheck = await this.checkDomainReputation(domain);
    if (!reputationCheck.safe) {
      return {
        valid: false,
        errors: ['Domain has reputation issues'],
        warnings: reputationCheck.warnings
      };
    }

    return { valid: true, errors: [] };
  }

  static async sanitizeCustomCSS(
    css: string,
    partnerId: string
  ): Promise<string> {
    // Remove potentially dangerous CSS
    let sanitizedCSS = css;

    // Remove @import statements
    sanitizedCSS = sanitizedCSS.replace(/@import\s+[^;]+;/gi, '');

    // Remove external URLs
    sanitizedCSS = sanitizedCSS.replace(/url\s*\(\s*['"]*https?:\/\/[^)]+\)/gi, '');

    // Remove JavaScript expressions
    sanitizedCSS = sanitizedCSS.replace(/expression\s*\([^)]*\)/gi, '');

    // Remove behavior properties
    sanitizedCSS = sanitizedCSS.replace(/behavior\s*:[^;]+;/gi, '');

    // Validate CSS syntax
    const validationResult = await this.validateCSSyntax(sanitizedCSS);
    if (!validationResult.valid) {
      throw new SecurityError('Invalid CSS syntax detected');
    }

    // Check CSS size limits
    const maxCSSSize = await this.getPartnerCSSLimit(partnerId);
    if (sanitizedCSS.length > maxCSSSize) {
      throw new SecurityError(`CSS exceeds maximum size limit of ${maxCSSSize} characters`);
    }

    return sanitizedCSS;
  }

  static async validateBrandAsset(
    file: File,
    assetType: string,
    partnerId: string
  ): Promise<ValidationResult> {
    // Check file type
    const allowedTypes = this.getAllowedAssetTypes(assetType);
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        errors: [`File type ${file.type} is not allowed for ${assetType}`]
      };
    }

    // Check file size
    const maxSize = this.getMaxAssetSize(assetType);
    if (file.size > maxSize) {
      return {
        valid: false,
        errors: [`File size exceeds maximum allowed size of ${maxSize} bytes`]
      };
    }

    // Scan for malware
    const scanResult = await this.scanFileForMalware(file);
    if (!scanResult.clean) {
      return {
        valid: false,
        errors: ['File contains potentially malicious content']
      };
    }

    // Validate image dimensions (for image assets)
    if (this.isImageAsset(assetType)) {
      const dimensions = await this.getImageDimensions(file);
      const validDimensions = this.validateImageDimensions(assetType, dimensions);

      if (!validDimensions.valid) {
        return validDimensions;
      }
    }

    return { valid: true, errors: [] };
  }
}
```

### 2. Partner Data Isolation

```typescript
// Data isolation and security for partner operations
// src/lib/white-label/partner-isolation.ts
export class PartnerDataIsolation {
  static async enforcePartnerDataIsolation(
    partnerId: string,
    operation: DatabaseOperation
  ): Promise<DatabaseOperation> {
    // Add partner filter to all queries
    const isolatedOperation = { ...operation };

    // Add partner ID filter
    if (isolatedOperation.where) {
      isolatedOperation.where = {
        ...isolatedOperation.where,
        partnerId
      };
    } else {
      isolatedOperation.where = { partnerId };
    }

    // Validate operation doesn't access other partner data
    await this.validatePartnerDataAccess(partnerId, isolatedOperation);

    return isolatedOperation;
  }

  static async validatePartnerDataAccess(
    partnerId: string,
    operation: DatabaseOperation
  ): Promise<void> {
    // Check for attempts to access other partner data
    if (operation.where?.partnerId && operation.where.partnerId !== partnerId) {
      throw new SecurityError('Attempted access to other partner data');
    }

    // Check for attempts to remove partner filters
    if (operation.includeGlobal && !this.isGlobalAccessAllowed(partnerId)) {
      throw new SecurityError('Global data access not allowed for this partner');
    }

    // Validate related data access
    if (operation.include) {
      await this.validateRelatedDataAccess(partnerId, operation.include);
    }
  }

  static async createPartnerDataScope(
    partnerId: string,
    userId: string
  ): Promise<DataScope> {
    // Get partner configuration
    const partner = await this.getPartner(partnerId);

    // Get user's partner role and permissions
    const partnerUser = await this.getPartnerUser(partnerId, userId);

    // Create data scope based on partner tier and user role
    const dataScope: DataScope = {
      partnerId,
      allowedTables: this.getAllowedTables(partner.tier, partnerUser.role),
      allowedOperations: this.getAllowedOperations(partnerUser.permissions),
      dataFilters: this.getDataFilters(partnerId, partnerUser.role),
      rateLimits: this.getRateLimits(partner.tier)
    };

    return dataScope;
  }
}
```

---

## Validation Gates (Executable Testing)

### Level 1: Syntax & Style Validation
```bash
# TypeScript compilation and linting
npm run lint                    # ESLint checks for white-label system
npx tsc --noEmit               # TypeScript type checking
npm run type-check             # White-label framework type validation

# White-label specific validation
npm run validate-white-label   # White-label configuration validation
npm run security-scan          # Security scanning for white-label system
```

### Level 2: Unit Testing
```bash
# Core white-label system tests
npm test src/lib/white-label/  # White-label engine and service tests
npm test src/components/partner/ # Partner portal component tests
npm test src/app/api/white-label/ # White-label API endpoint tests

# Partner management tests
npm run test:partner-management # Partner onboarding and management tests
npm run test:domain-management  # Custom domain and DNS tests
npm run test:theme-engine       # Dynamic theming and branding tests
```

### Level 3: Integration Testing
```bash
# Start development server
npm run dev

# Test white-label API endpoints
curl -X POST http://localhost:3000/api/white-label/partners \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{"name": "Test Partner", "email": "<EMAIL>", "tier": "professional"}'

# Test custom domain setup
curl -X POST http://localhost:3000/api/white-label/domains \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{"partnerId": "partner-123", "domain": "custom.example.com"}'

# Test theme customization
curl -X POST http://localhost:3000/api/white-label/themes \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{"partnerId": "partner-123", "colors": {"primary": "#ff0000"}}'
```

### Level 4: End-to-End White-Label Testing
```bash
# Production build validation
npm run build                  # Build with white-label system
npm run start                  # Production server testing

# White-label E2E testing
npm run test:e2e:white-label   # Playwright E2E tests for white-label
npm run test:partner-onboarding # Partner onboarding flow tests
npm run test:custom-domains    # Custom domain setup and verification
```

### Level 5: White-Label Performance Testing
```bash
# White-label system performance
npm run test:performance       # White-label system performance tests
npm run test:theme-switching   # Dynamic theme switching performance
npm run test:asset-delivery    # Brand asset delivery performance

# Partner portal testing
npm run test:partner-portal    # Partner portal performance tests
npm run test:revenue-tracking  # Revenue tracking and analytics tests
npm run test:multi-tenant      # Multi-tenant isolation tests
```

---

## Quality Standards Checklist

- [x] **Complete customization**: Full control over branding, themes, and user experience
- [x] **Partner management**: Comprehensive partner onboarding, management, and analytics
- [x] **Custom domains**: Full custom domain support with SSL and DNS management
- [x] **Dynamic theming**: Real-time theme switching and customization capabilities
- [x] **Revenue tracking**: Built-in partner revenue tracking and commission systems
- [x] **Data isolation**: Complete separation of partner brands and customer data
- [x] **Security framework**: Comprehensive access control, validation, and audit compliance
- [x] **Deployment automation**: Automated white-label deployment and configuration
- [x] **Performance optimization**: Optimized asset delivery and CDN integration
- [x] **Scalable architecture**: Designed for enterprise-scale white-label operations

---

**Framework**: NEXUS SaaS Starter Multi-Tenant White-Label Platform
**Technology Stack**: Next.js 15.4+ / TypeScript 5.8+ / Dynamic Theming / Partner Management
**Optimization**: Production-ready, enterprise-grade, complete white-label solution
