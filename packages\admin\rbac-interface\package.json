{"name": "@nexus/admin-rbac-interface", "version": "0.1.0", "description": "Admin interface for RBAC management in Nexus SaaS", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "jest", "type-check": "tsc --noEmit"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "@nexus/utils": "workspace:*", "@nexus/validation": "workspace:*", "@nexus/rbac": "workspace:*", "@nexus/auth-client": "workspace:*", "@nexus/tenant-context": "workspace:*", "react": "19.1.0", "react-hook-form": "7.60.0", "react-table": "7.8.0", "zustand": "5.0.6", "zod": "4.0.5", "date-fns": "4.1.0", "recharts": "3.1.0", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@types/jest": "30.0.0", "@types/react": "19.1.8", "@types/react-table": "7.7.20", "jest": "30.0.4", "typescript": "5.8.3"}}