# User Data Export & Deletion Implementation

## Overview
Implement comprehensive GDPR-compliant user data export and deletion features for the NEXUS SaaS Starter, providing users with complete control over their personal data through secure, efficient export mechanisms and permanent data deletion capabilities.

## Context Research Summary

### Fast-CSV Library Patterns
- **Streaming CSV Export**: Using `fast-csv` for memory-efficient large dataset exports with streaming capabilities
- **Headers Management**: Automatic header discovery from objects and custom header configuration
- **Data Transformation**: Synchronous and asynchronous row transformations during export
- **Multiple Output Formats**: Support for CSV strings, buffers, files, and streams
- **Performance Optimization**: Streaming approach for handling large datasets without memory constraints

### Node.js Best Practices
- **Stateless Operations**: Avoiding local file storage, using streams for data processing
- **Error Handling**: Comprehensive error handling for file operations and data processing
- **Security**: Input validation, sanitization, and secure file handling
- **Performance**: Memory-efficient processing of large datasets
- **Logging**: Comprehensive audit logging for compliance and debugging

### GDPR Compliance Requirements
- **Right to Portability**: Structured data export in machine-readable format
- **Right to Erasure**: Complete data deletion with verification
- **Data Minimization**: Export only necessary data, secure deletion of all related data
- **User Consent**: Clear user consent mechanisms for data operations
- **Audit Trail**: Complete logging of all data operations for compliance

## Implementation Plan

### Phase 1: Data Export Infrastructure
1. **Export Service Architecture**
   - Create modular export service with pluggable format support
   - Implement streaming-based export for memory efficiency
   - Set up background job processing for large exports
   - Create secure temporary file handling with automatic cleanup

2. **Data Mapping & Transformation**
   - Define comprehensive user data schema for export
   - Create data transformation pipelines for different formats
   - Implement relationship mapping for related data
   - Set up data sanitization and privacy filtering

### Phase 2: Export Formats & Delivery
1. **CSV Export Implementation**
   - Implement streaming CSV export using fast-csv
   - Support custom field selection and ordering
   - Include metadata and relationship data
   - Optimize for large datasets with pagination

2. **JSON Export Implementation**
   - Structured JSON export with nested relationships
   - Include complete user data graph
   - Support incremental exports for large datasets
   - Implement data compression for efficient delivery

3. **PDF Export Implementation**
   - Human-readable PDF reports with user data
   - Include data visualizations and summaries
   - Support custom templates and branding
   - Implement accessibility features

### Phase 3: Data Deletion & Compliance
1. **Soft Deletion Implementation**
   - Mark data as deleted while preserving for legal requirements
   - Implement cascading deletion for related data
   - Set up automated hard deletion after retention period
   - Create deletion verification mechanisms

2. **Hard Deletion Implementation**
   - Permanent data removal from all systems
   - Cascade deletion across all related tables
   - Remove file system artifacts and backups
   - Implement deletion verification and certification

3. **Compliance & Audit**
   - Complete audit logging for all operations
   - Generate compliance reports and certificates
   - Implement data retention policy enforcement
   - Create regulatory reporting mechanisms

### Phase 4: User Interface & Experience
1. **Export Request Management**
   - User-friendly data export request interface
   - Export progress tracking and notifications
   - Download management and secure access
   - Export history and re-request capabilities

2. **Data Deletion Interface**
   - Clear deletion request workflows
   - Confirmation mechanisms and waiting periods
   - Deletion status tracking and notifications
   - Data restoration capabilities (before hard deletion)

## Technical Implementation

### File Structure
```
src/
├── app/
│   ├── api/
│   │   └── v1/
│   │       ├── user-data/
│   │       │   ├── export/
│   │       │   │   ├── route.ts              # Export request endpoint
│   │       │   │   └── [requestId]/
│   │       │   │       ├── route.ts          # Export status/download
│   │       │   │       └── download/
│   │       │   │           └── route.ts      # Secure download endpoint
│   │       │   └── deletion/
│   │       │       ├── route.ts              # Deletion request endpoint
│   │       │       └── [requestId]/
│   │       │           └── route.ts          # Deletion status
│   │       └── gdpr/
│   │           ├── export-status/
│   │           │   └── route.ts              # Export status tracking
│   │           └── deletion-status/
│   │               └── route.ts              # Deletion status tracking
├── lib/
│   ├── data-export/
│   │   ├── services/
│   │   │   ├── export-service.ts             # Main export orchestrator
│   │   │   ├── csv-export.ts                 # CSV export implementation
│   │   │   ├── json-export.ts                # JSON export implementation
│   │   │   └── pdf-export.ts                 # PDF export implementation
│   │   ├── transformers/
│   │   │   ├── user-data-transformer.ts      # User data transformation
│   │   │   ├── relationship-mapper.ts        # Relationship mapping
│   │   │   └── privacy-filter.ts             # Data privacy filtering
│   │   ├── formats/
│   │   │   ├── csv-formatter.ts              # CSV formatting logic
│   │   │   ├── json-formatter.ts             # JSON formatting logic
│   │   │   └── pdf-formatter.ts              # PDF formatting logic
│   │   └── storage/
│   │       ├── export-storage.ts             # Export file management
│   │       ├── secure-download.ts            # Secure download handling
│   │       └── cleanup-service.ts            # Temporary file cleanup
│   ├── data-deletion/
│   │   ├── services/
│   │   │   ├── deletion-service.ts           # Main deletion orchestrator
│   │   │   ├── soft-deletion.ts              # Soft deletion implementation
│   │   │   ├── hard-deletion.ts              # Hard deletion implementation
│   │   │   └── cascade-deletion.ts           # Cascading deletion logic
│   │   ├── validators/
│   │   │   ├── deletion-validator.ts         # Deletion request validation
│   │   │   ├── retention-checker.ts          # Retention policy checking
│   │   │   └── compliance-validator.ts       # Compliance validation
│   │   └── verification/
│   │       ├── deletion-verifier.ts          # Deletion verification
│   │       ├── certificate-generator.ts      # Compliance certificates
│   │       └── audit-logger.ts               # Audit logging
│   ├── gdpr/
│   │   ├── compliance/
│   │   │   ├── gdpr-service.ts               # GDPR compliance service
│   │   │   ├── consent-manager.ts            # Consent management
│   │   │   ├── data-mapping.ts               # Data mapping for GDPR
│   │   │   └── retention-policy.ts           # Data retention policies
│   │   ├── notifications/
│   │   │   ├── export-notifications.ts       # Export status notifications
│   │   │   ├── deletion-notifications.ts     # Deletion status notifications
│   │   │   └── compliance-notifications.ts   # Compliance notifications
│   │   └── reporting/
│   │       ├── compliance-reporter.ts        # Compliance reporting
│   │       ├── audit-reporter.ts             # Audit reporting
│   │       └── metrics-collector.ts          # Metrics collection
│   └── jobs/
│       ├── export-processor.ts               # Background export processing
│       ├── deletion-processor.ts             # Background deletion processing
│       └── cleanup-processor.ts              # Cleanup job processing
```

### Core Export Service Implementation

```typescript
// src/lib/data-export/services/export-service.ts
import { z } from 'zod';
import { createId } from '@paralleldrive/cuid2';
import { csvExporter } from './csv-export';
import { jsonExporter } from './json-export';
import { pdfExporter } from './pdf-export';
import { exportStorage } from '../storage/export-storage';
import { auditLogger } from '@/lib/audit/audit-logger';
import { emailNotifications } from '@/lib/notifications/email-notifications';

export const ExportRequestSchema = z.object({
  format: z.enum(['csv', 'json', 'pdf']),
  includeFields: z.array(z.string()).optional(),
  excludeFields: z.array(z.string()).optional(),
  includeRelationships: z.boolean().default(true),
  dateRange: z.object({
    from: z.date().optional(),
    to: z.date().optional(),
  }).optional(),
  compressionLevel: z.enum(['none', 'low', 'medium', 'high']).default('medium'),
  notificationEmail: z.string().email().optional(),
});

export const ExportStatusSchema = z.object({
  id: z.string(),
  userId: z.string(),
  tenantId: z.string(),
  status: z.enum(['pending', 'processing', 'completed', 'failed', 'expired']),
  format: z.enum(['csv', 'json', 'pdf']),
  progress: z.number().min(0).max(100),
  recordCount: z.number().optional(),
  fileSize: z.number().optional(),
  downloadUrl: z.string().optional(),
  expiresAt: z.date(),
  createdAt: z.date(),
  completedAt: z.date().optional(),
  error: z.string().optional(),
});

export type ExportRequest = z.infer<typeof ExportRequestSchema>;
export type ExportStatus = z.infer<typeof ExportStatusSchema>;

export class ExportService {
  private readonly exporters = {
    csv: csvExporter,
    json: jsonExporter,
    pdf: pdfExporter,
  };

  async requestExport(
    userId: string,
    tenantId: string,
    request: ExportRequest
  ): Promise<ExportStatus> {
    const exportId = createId();
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

    const exportStatus: ExportStatus = {
      id: exportId,
      userId,
      tenantId,
      status: 'pending',
      format: request.format,
      progress: 0,
      expiresAt,
      createdAt: new Date(),
    };

    // Store export request
    await exportStorage.saveExportStatus(exportStatus);

    // Log audit event
    await auditLogger.log({
      action: 'data_export_requested',
      userId,
      tenantId,
      metadata: {
        exportId,
        format: request.format,
        fields: request.includeFields,
      },
    });

    // Queue background job
    await this.queueExportJob(exportId, request);

    return exportStatus;
  }

  async processExport(exportId: string, request: ExportRequest): Promise<void> {
    try {
      // Update status to processing
      await exportStorage.updateExportStatus(exportId, {
        status: 'processing',
        progress: 0,
      });

      const exportStatus = await exportStorage.getExportStatus(exportId);
      if (!exportStatus) {
        throw new Error('Export request not found');
      }

      // Get exporter for format
      const exporter = this.exporters[request.format];
      if (!exporter) {
        throw new Error(`Unsupported export format: ${request.format}`);
      }

      // Create export stream with progress tracking
      const exportStream = await exporter.createExportStream(
        exportStatus.userId,
        exportStatus.tenantId,
        request,
        (progress) => this.updateProgress(exportId, progress)
      );

      // Generate export file
      const { filePath, recordCount, fileSize } = await exportStorage.saveExport(
        exportId,
        exportStream,
        request.format,
        request.compressionLevel
      );

      // Generate secure download URL
      const downloadUrl = await exportStorage.generateDownloadUrl(exportId);

      // Update status to completed
      await exportStorage.updateExportStatus(exportId, {
        status: 'completed',
        progress: 100,
        recordCount,
        fileSize,
        downloadUrl,
        completedAt: new Date(),
      });

      // Send notification
      if (request.notificationEmail) {
        await emailNotifications.sendExportCompletedEmail(
          request.notificationEmail,
          {
            exportId,
            format: request.format,
            recordCount,
            fileSize,
            downloadUrl,
            expiresAt: exportStatus.expiresAt,
          }
        );
      }

      // Log completion
      await auditLogger.log({
        action: 'data_export_completed',
        userId: exportStatus.userId,
        tenantId: exportStatus.tenantId,
        metadata: {
          exportId,
          format: request.format,
          recordCount,
          fileSize,
        },
      });

    } catch (error) {
      await this.handleExportError(exportId, error);
    }
  }

  private async updateProgress(exportId: string, progress: number): Promise<void> {
    await exportStorage.updateExportStatus(exportId, { progress });
  }

  private async handleExportError(exportId: string, error: any): Promise<void> {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    await exportStorage.updateExportStatus(exportId, {
      status: 'failed',
      error: errorMessage,
    });

    const exportStatus = await exportStorage.getExportStatus(exportId);
    if (exportStatus) {
      await auditLogger.log({
        action: 'data_export_failed',
        userId: exportStatus.userId,
        tenantId: exportStatus.tenantId,
        metadata: {
          exportId,
          error: errorMessage,
        },
      });
    }
  }

  private async queueExportJob(exportId: string, request: ExportRequest): Promise<void> {
    // Implementation depends on your job queue system
    // This could be Redis Bull, AWS SQS, or another queue system
    console.log(`Queuing export job for ${exportId}`);
  }
}

export const exportService = new ExportService();
```

### CSV Export Implementation

```typescript
// src/lib/data-export/services/csv-export.ts
import { format } from '@fast-csv/format';
import { Readable } from 'stream';
import { userDataTransformer } from '../transformers/user-data-transformer';
import { relationshipMapper } from '../transformers/relationship-mapper';
import { privacyFilter } from '../transformers/privacy-filter';
import { userRepository } from '@/lib/database/repositories/user-repository';
import type { ExportRequest } from './export-service';

export class CsvExporter {
  async createExportStream(
    userId: string,
    tenantId: string,
    request: ExportRequest,
    onProgress?: (progress: number) => void
  ): Promise<Readable> {
    const userData = await this.getUserData(userId, tenantId);
    const transformedData = await this.transformData(userData, request);
    
    let processedRows = 0;
    const totalRows = transformedData.length;

    const csvStream = format({
      headers: true,
      delimiter: ',',
      quote: '"',
      escape: '"',
      quoteColumns: true,
      transform: (row: any) => {
        processedRows++;
        if (onProgress && totalRows > 0) {
          onProgress(Math.round((processedRows / totalRows) * 100));
        }
        return row;
      },
    });

    // Create readable stream from transformed data
    const dataStream = Readable.from(transformedData);
    
    return dataStream.pipe(csvStream);
  }

  private async getUserData(userId: string, tenantId: string): Promise<any[]> {
    const user = await userRepository.findByIdWithRelations(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Build comprehensive user data array
    const userData: any[] = [];

    // Core user data
    userData.push({
      category: 'Profile',
      field: 'ID',
      value: user.id,
      created_at: user.createdAt,
      updated_at: user.updatedAt,
    });

    userData.push({
      category: 'Profile',
      field: 'Email',
      value: user.email,
      created_at: user.createdAt,
      updated_at: user.updatedAt,
    });

    userData.push({
      category: 'Profile',
      field: 'Name',
      value: user.name,
      created_at: user.createdAt,
      updated_at: user.updatedAt,
    });

    userData.push({
      category: 'Profile',
      field: 'Role',
      value: user.role,
      created_at: user.createdAt,
      updated_at: user.updatedAt,
    });

    // Profile customizations
    if (user.profile) {
      Object.entries(user.profile).forEach(([key, value]) => {
        userData.push({
          category: 'Profile_Customization',
          field: key,
          value: JSON.stringify(value),
          created_at: user.profile.createdAt,
          updated_at: user.profile.updatedAt,
        });
      });
    }

    // Activity logs
    if (user.activities) {
      user.activities.forEach((activity: any) => {
        userData.push({
          category: 'Activity',
          field: 'Action',
          value: activity.action,
          created_at: activity.createdAt,
          updated_at: activity.updatedAt,
          metadata: JSON.stringify(activity.metadata),
        });
      });
    }

    // Sessions
    if (user.sessions) {
      user.sessions.forEach((session: any) => {
        userData.push({
          category: 'Session',
          field: 'Session_ID',
          value: session.id,
          created_at: session.createdAt,
          updated_at: session.updatedAt,
          metadata: JSON.stringify({
            ip_address: session.ipAddress,
            user_agent: session.userAgent,
            expires_at: session.expiresAt,
          }),
        });
      });
    }

    // Preferences
    if (user.preferences) {
      Object.entries(user.preferences).forEach(([key, value]) => {
        userData.push({
          category: 'Preferences',
          field: key,
          value: JSON.stringify(value),
          created_at: user.preferences.createdAt,
          updated_at: user.preferences.updatedAt,
        });
      });
    }

    // Tenant relationships
    if (user.tenantMemberships) {
      user.tenantMemberships.forEach((membership: any) => {
        userData.push({
          category: 'Tenant_Membership',
          field: 'Tenant_ID',
          value: membership.tenantId,
          created_at: membership.createdAt,
          updated_at: membership.updatedAt,
          metadata: JSON.stringify({
            role: membership.role,
            permissions: membership.permissions,
          }),
        });
      });
    }

    return userData;
  }

  private async transformData(userData: any[], request: ExportRequest): Promise<any[]> {
    let transformedData = userData;

    // Apply field filtering
    if (request.includeFields?.length) {
      transformedData = transformedData.filter(row => 
        request.includeFields!.includes(row.field)
      );
    }

    if (request.excludeFields?.length) {
      transformedData = transformedData.filter(row => 
        !request.excludeFields!.includes(row.field)
      );
    }

    // Apply date range filtering
    if (request.dateRange) {
      transformedData = transformedData.filter(row => {
        const createdAt = new Date(row.created_at);
        const { from, to } = request.dateRange!;
        
        if (from && createdAt < from) return false;
        if (to && createdAt > to) return false;
        
        return true;
      });
    }

    // Apply privacy filtering
    transformedData = await privacyFilter.filterSensitiveData(transformedData);

    return transformedData;
  }
}

export const csvExporter = new CsvExporter();
```

### JSON Export Implementation

```typescript
// src/lib/data-export/services/json-export.ts
import { Readable } from 'stream';
import { userRepository } from '@/lib/database/repositories/user-repository';
import { privacyFilter } from '../transformers/privacy-filter';
import type { ExportRequest } from './export-service';

export class JsonExporter {
  async createExportStream(
    userId: string,
    tenantId: string,
    request: ExportRequest,
    onProgress?: (progress: number) => void
  ): Promise<Readable> {
    const userData = await this.getUserDataStructured(userId, tenantId);
    const transformedData = await this.transformData(userData, request);
    
    if (onProgress) {
      onProgress(100);
    }

    const jsonString = JSON.stringify(transformedData, null, 2);
    return Readable.from([jsonString]);
  }

  private async getUserDataStructured(userId: string, tenantId: string): Promise<any> {
    const user = await userRepository.findByIdWithRelations(userId);
    if (!user) {
      throw new Error('User not found');
    }

    return {
      export_metadata: {
        export_date: new Date().toISOString(),
        user_id: userId,
        tenant_id: tenantId,
        export_type: 'complete_user_data',
        gdpr_compliant: true,
      },
      user_profile: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        status: user.status,
        created_at: user.createdAt,
        updated_at: user.updatedAt,
        last_login_at: user.lastLoginAt,
        metadata: user.metadata,
      },
      profile_customizations: user.profile ? {
        ...user.profile,
        created_at: user.profile.createdAt,
        updated_at: user.profile.updatedAt,
      } : null,
      preferences: user.preferences ? {
        ...user.preferences,
        created_at: user.preferences.createdAt,
        updated_at: user.preferences.updatedAt,
      } : null,
      activity_history: user.activities?.map((activity: any) => ({
        id: activity.id,
        action: activity.action,
        description: activity.description,
        metadata: activity.metadata,
        ip_address: activity.ipAddress,
        user_agent: activity.userAgent,
        created_at: activity.createdAt,
      })) || [],
      sessions: user.sessions?.map((session: any) => ({
        id: session.id,
        ip_address: session.ipAddress,
        user_agent: session.userAgent,
        created_at: session.createdAt,
        expires_at: session.expiresAt,
        last_activity_at: session.lastActivityAt,
      })) || [],
      tenant_memberships: user.tenantMemberships?.map((membership: any) => ({
        tenant_id: membership.tenantId,
        tenant_name: membership.tenant?.name,
        role: membership.role,
        permissions: membership.permissions,
        joined_at: membership.createdAt,
        updated_at: membership.updatedAt,
      })) || [],
      audit_logs: user.auditLogs?.map((log: any) => ({
        id: log.id,
        action: log.action,
        resource_type: log.resourceType,
        resource_id: log.resourceId,
        changes: log.changes,
        ip_address: log.ipAddress,
        user_agent: log.userAgent,
        created_at: log.createdAt,
      })) || [],
    };
  }

  private async transformData(userData: any, request: ExportRequest): Promise<any> {
    let transformedData = { ...userData };

    // Apply field filtering
    if (request.includeFields?.length) {
      transformedData = this.filterFields(transformedData, request.includeFields, true);
    }

    if (request.excludeFields?.length) {
      transformedData = this.filterFields(transformedData, request.excludeFields, false);
    }

    // Apply date range filtering
    if (request.dateRange) {
      transformedData = this.filterByDateRange(transformedData, request.dateRange);
    }

    // Apply privacy filtering
    transformedData = await privacyFilter.filterSensitiveData(transformedData);

    return transformedData;
  }

  private filterFields(data: any, fields: string[], include: boolean): any {
    // Implementation for field filtering in nested JSON structure
    // This would recursively filter based on field paths
    return data;
  }

  private filterByDateRange(data: any, dateRange: { from?: Date; to?: Date }): any {
    // Implementation for date range filtering
    // This would filter arrays based on date fields
    return data;
  }
}

export const jsonExporter = new JsonExporter();
```

### Data Deletion Service

```typescript
// src/lib/data-deletion/services/deletion-service.ts
import { z } from 'zod';
import { createId } from '@paralleldrive/cuid2';
import { softDeletion } from './soft-deletion';
import { hardDeletion } from './hard-deletion';
import { cascadeDeletion } from './cascade-deletion';
import { deletionVerifier } from '../verification/deletion-verifier';
import { auditLogger } from '@/lib/audit/audit-logger';
import { emailNotifications } from '@/lib/notifications/email-notifications';

export const DeletionRequestSchema = z.object({
  type: z.enum(['soft', 'hard']),
  reason: z.string().optional(),
  confirmationCode: z.string().optional(),
  scheduledFor: z.date().optional(),
  includeBackups: z.boolean().default(true),
  generateCertificate: z.boolean().default(true),
  notificationEmail: z.string().email().optional(),
});

export const DeletionStatusSchema = z.object({
  id: z.string(),
  userId: z.string(),
  tenantId: z.string(),
  type: z.enum(['soft', 'hard']),
  status: z.enum(['pending', 'confirmed', 'processing', 'completed', 'failed', 'cancelled']),
  progress: z.number().min(0).max(100),
  reason: z.string().optional(),
  scheduledFor: z.date().optional(),
  completedAt: z.date().optional(),
  certificateUrl: z.string().optional(),
  verificationHash: z.string().optional(),
  createdAt: z.date(),
  error: z.string().optional(),
});

export type DeletionRequest = z.infer<typeof DeletionRequestSchema>;
export type DeletionStatus = z.infer<typeof DeletionStatusSchema>;

export class DeletionService {
  async requestDeletion(
    userId: string,
    tenantId: string,
    request: DeletionRequest
  ): Promise<DeletionStatus> {
    const deletionId = createId();
    const scheduledFor = request.scheduledFor || new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    const deletionStatus: DeletionStatus = {
      id: deletionId,
      userId,
      tenantId,
      type: request.type,
      status: 'pending',
      progress: 0,
      reason: request.reason,
      scheduledFor,
      createdAt: new Date(),
    };

    // Store deletion request
    await this.saveDeletionStatus(deletionStatus);

    // Log audit event
    await auditLogger.log({
      action: 'data_deletion_requested',
      userId,
      tenantId,
      metadata: {
        deletionId,
        type: request.type,
        reason: request.reason,
        scheduledFor,
      },
    });

    // Send confirmation email
    if (request.notificationEmail) {
      await emailNotifications.sendDeletionConfirmationEmail(
        request.notificationEmail,
        {
          deletionId,
          type: request.type,
          scheduledFor,
          confirmationUrl: this.generateConfirmationUrl(deletionId),
        }
      );
    }

    return deletionStatus;
  }

  async confirmDeletion(
    deletionId: string,
    confirmationCode: string
  ): Promise<DeletionStatus> {
    const deletionStatus = await this.getDeletionStatus(deletionId);
    if (!deletionStatus) {
      throw new Error('Deletion request not found');
    }

    if (deletionStatus.status !== 'pending') {
      throw new Error('Deletion request cannot be confirmed');
    }

    // Verify confirmation code
    if (!this.verifyConfirmationCode(deletionId, confirmationCode)) {
      throw new Error('Invalid confirmation code');
    }

    // Update status to confirmed
    await this.updateDeletionStatus(deletionId, {
      status: 'confirmed',
    });

    // Schedule deletion job
    await this.scheduleDeletionJob(deletionId);

    // Log confirmation
    await auditLogger.log({
      action: 'data_deletion_confirmed',
      userId: deletionStatus.userId,
      tenantId: deletionStatus.tenantId,
      metadata: {
        deletionId,
        type: deletionStatus.type,
      },
    });

    return await this.getDeletionStatus(deletionId);
  }

  async processDeletion(deletionId: string): Promise<void> {
    try {
      const deletionStatus = await this.getDeletionStatus(deletionId);
      if (!deletionStatus) {
        throw new Error('Deletion request not found');
      }

      // Update status to processing
      await this.updateDeletionStatus(deletionId, {
        status: 'processing',
        progress: 0,
      });

      // Execute deletion based on type
      let deletionResult;
      if (deletionStatus.type === 'soft') {
        deletionResult = await softDeletion.execute(
          deletionStatus.userId,
          deletionStatus.tenantId,
          (progress) => this.updateProgress(deletionId, progress)
        );
      } else {
        deletionResult = await hardDeletion.execute(
          deletionStatus.userId,
          deletionStatus.tenantId,
          (progress) => this.updateProgress(deletionId, progress)
        );
      }

      // Verify deletion
      const verificationResult = await deletionVerifier.verify(
        deletionStatus.userId,
        deletionStatus.tenantId,
        deletionStatus.type
      );

      // Generate certificate if requested
      let certificateUrl;
      if (deletionResult.generateCertificate) {
        certificateUrl = await this.generateDeletionCertificate(
          deletionId,
          deletionResult,
          verificationResult
        );
      }

      // Update status to completed
      await this.updateDeletionStatus(deletionId, {
        status: 'completed',
        progress: 100,
        completedAt: new Date(),
        certificateUrl,
        verificationHash: verificationResult.hash,
      });

      // Send completion notification
      await emailNotifications.sendDeletionCompletedEmail(
        deletionStatus.userId,
        {
          deletionId,
          type: deletionStatus.type,
          certificateUrl,
          verificationHash: verificationResult.hash,
        }
      );

      // Log completion
      await auditLogger.log({
        action: 'data_deletion_completed',
        userId: deletionStatus.userId,
        tenantId: deletionStatus.tenantId,
        metadata: {
          deletionId,
          type: deletionStatus.type,
          recordsDeleted: deletionResult.recordsDeleted,
          filesDeleted: deletionResult.filesDeleted,
          verificationHash: verificationResult.hash,
        },
      });

    } catch (error) {
      await this.handleDeletionError(deletionId, error);
    }
  }

  private async updateProgress(deletionId: string, progress: number): Promise<void> {
    await this.updateDeletionStatus(deletionId, { progress });
  }

  private async handleDeletionError(deletionId: string, error: any): Promise<void> {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    await this.updateDeletionStatus(deletionId, {
      status: 'failed',
      error: errorMessage,
    });

    const deletionStatus = await this.getDeletionStatus(deletionId);
    if (deletionStatus) {
      await auditLogger.log({
        action: 'data_deletion_failed',
        userId: deletionStatus.userId,
        tenantId: deletionStatus.tenantId,
        metadata: {
          deletionId,
          error: errorMessage,
        },
      });
    }
  }

  private generateConfirmationUrl(deletionId: string): string {
    return `${process.env.NEXT_PUBLIC_APP_URL}/data-deletion/confirm/${deletionId}`;
  }

  private verifyConfirmationCode(deletionId: string, confirmationCode: string): boolean {
    // Implementation for confirmation code verification
    // This could involve checking against stored codes, time-based validation, etc.
    return true;
  }

  private async generateDeletionCertificate(
    deletionId: string,
    deletionResult: any,
    verificationResult: any
  ): Promise<string> {
    // Implementation for generating compliance certificate
    // This would create a PDF certificate with deletion details
    return `${process.env.NEXT_PUBLIC_APP_URL}/certificates/deletion/${deletionId}`;
  }

  private async saveDeletionStatus(status: DeletionStatus): Promise<void> {
    // Implementation for saving deletion status to database
  }

  private async getDeletionStatus(deletionId: string): Promise<DeletionStatus | null> {
    // Implementation for retrieving deletion status from database
    return null;
  }

  private async updateDeletionStatus(
    deletionId: string,
    updates: Partial<DeletionStatus>
  ): Promise<void> {
    // Implementation for updating deletion status in database
  }

  private async scheduleDeletionJob(deletionId: string): Promise<void> {
    // Implementation for scheduling deletion job
  }
}

export const deletionService = new DeletionService();
```

### API Route Implementation

```typescript
// src/app/api/v1/user-data/export/route.ts
import { NextRequest } from 'next/server';
import { defineRoute } from 'next-openapi-route-handler';
import { z } from 'zod';
import { authMiddleware } from '@/lib/api/middleware/auth';
import { rateLimitMiddleware } from '@/lib/api/middleware/rate-limit';
import { exportService, ExportRequestSchema } from '@/lib/data-export/services/export-service';
import { createResponse, createErrorResponse } from '@/lib/api/utils/responses';

export const POST = defineRoute({
  method: 'POST',
  path: '/api/v1/user-data/export',
  tags: ['User Data'],
  summary: 'Request user data export',
  description: 'Create a new user data export request in compliance with GDPR',
  requestSchema: ExportRequestSchema,
  responseSchema: z.object({
    id: z.string(),
    status: z.enum(['pending', 'processing', 'completed', 'failed']),
    format: z.enum(['csv', 'json', 'pdf']),
    progress: z.number(),
    expiresAt: z.date(),
    createdAt: z.date(),
    downloadUrl: z.string().optional(),
  }),
  middleware: [authMiddleware, rateLimitMiddleware],
  handler: async (req: NextRequest) => {
    try {
      const user = (req as any).user;
      const body = await req.json();
      const request = ExportRequestSchema.parse(body);

      const exportStatus = await exportService.requestExport(
        user.id,
        user.tenantId,
        request
      );

      return createResponse(exportStatus, 201);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return createErrorResponse('Invalid request data', 400, error.errors);
      }
      
      console.error('Error requesting export:', error);
      return createErrorResponse('Failed to request export', 500);
    }
  },
});

export const GET = defineRoute({
  method: 'GET',
  path: '/api/v1/user-data/export',
  tags: ['User Data'],
  summary: 'List user data exports',
  description: 'Get all data export requests for the authenticated user',
  querySchema: z.object({
    status: z.enum(['pending', 'processing', 'completed', 'failed']).optional(),
    format: z.enum(['csv', 'json', 'pdf']).optional(),
    page: z.coerce.number().min(1).default(1),
    limit: z.coerce.number().min(1).max(50).default(10),
  }),
  responseSchema: z.object({
    exports: z.array(z.object({
      id: z.string(),
      status: z.enum(['pending', 'processing', 'completed', 'failed']),
      format: z.enum(['csv', 'json', 'pdf']),
      progress: z.number(),
      recordCount: z.number().optional(),
      fileSize: z.number().optional(),
      downloadUrl: z.string().optional(),
      expiresAt: z.date(),
      createdAt: z.date(),
      completedAt: z.date().optional(),
    })),
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
    }),
  }),
  middleware: [authMiddleware, rateLimitMiddleware],
  handler: async (req: NextRequest) => {
    try {
      const user = (req as any).user;
      const searchParams = Object.fromEntries(req.nextUrl.searchParams);
      const query = z.object({
        status: z.enum(['pending', 'processing', 'completed', 'failed']).optional(),
        format: z.enum(['csv', 'json', 'pdf']).optional(),
        page: z.coerce.number().min(1).default(1),
        limit: z.coerce.number().min(1).max(50).default(10),
      }).parse(searchParams);

      const { exports, total } = await exportService.listExports(
        user.id,
        user.tenantId,
        query
      );

      return createResponse({
        exports,
        pagination: {
          page: query.page,
          limit: query.limit,
          total,
          totalPages: Math.ceil(total / query.limit),
        },
      });
    } catch (error) {
      console.error('Error listing exports:', error);
      return createErrorResponse('Failed to list exports', 500);
    }
  },
});
```

### Secure Download Implementation

```typescript
// src/app/api/v1/user-data/export/[requestId]/download/route.ts
import { NextRequest } from 'next/server';
import { defineRoute } from 'next-openapi-route-handler';
import { z } from 'zod';
import { authMiddleware } from '@/lib/api/middleware/auth';
import { exportStorage } from '@/lib/data-export/storage/export-storage';
import { auditLogger } from '@/lib/audit/audit-logger';
import { createErrorResponse } from '@/lib/api/utils/responses';

export const GET = defineRoute({
  method: 'GET',
  path: '/api/v1/user-data/export/{requestId}/download',
  tags: ['User Data'],
  summary: 'Download exported data',
  description: 'Securely download exported user data file',
  pathSchema: z.object({
    requestId: z.string().min(1),
  }),
  querySchema: z.object({
    token: z.string().optional(),
  }),
  middleware: [authMiddleware],
  handler: async (req: NextRequest, { params }: { params: { requestId: string } }) => {
    try {
      const user = (req as any).user;
      const { requestId } = params;
      const searchParams = Object.fromEntries(req.nextUrl.searchParams);
      const { token } = searchParams;

      // Verify export ownership and status
      const exportStatus = await exportStorage.getExportStatus(requestId);
      if (!exportStatus) {
        return createErrorResponse('Export not found', 404);
      }

      if (exportStatus.userId !== user.id) {
        return createErrorResponse('Unauthorized access to export', 403);
      }

      if (exportStatus.status !== 'completed') {
        return createErrorResponse('Export not ready for download', 400);
      }

      if (new Date() > exportStatus.expiresAt) {
        return createErrorResponse('Export has expired', 410);
      }

      // Verify download token if provided
      if (token && !exportStorage.verifyDownloadToken(requestId, token)) {
        return createErrorResponse('Invalid download token', 401);
      }

      // Get file stream
      const fileStream = await exportStorage.getExportFile(requestId);
      if (!fileStream) {
        return createErrorResponse('Export file not found', 404);
      }

      // Log download event
      await auditLogger.log({
        action: 'data_export_downloaded',
        userId: user.id,
        tenantId: user.tenantId,
        metadata: {
          exportId: requestId,
          format: exportStatus.format,
          fileSize: exportStatus.fileSize,
          downloadedAt: new Date(),
        },
      });

      // Set response headers
      const filename = `user-data-${requestId}.${exportStatus.format}`;
      const headers = new Headers();
      headers.set('Content-Type', this.getContentType(exportStatus.format));
      headers.set('Content-Disposition', `attachment; filename="${filename}"`);
      headers.set('Content-Length', exportStatus.fileSize?.toString() || '0');
      headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
      headers.set('Pragma', 'no-cache');
      headers.set('Expires', '0');

      // Return file stream
      return new Response(fileStream, {
        headers,
        status: 200,
      });

    } catch (error) {
      console.error('Error downloading export:', error);
      return createErrorResponse('Failed to download export', 500);
    }
  },

  private getContentType(format: string): string {
    switch (format) {
      case 'csv':
        return 'text/csv';
      case 'json':
        return 'application/json';
      case 'pdf':
        return 'application/pdf';
      default:
        return 'application/octet-stream';
    }
  },
});
```

### Privacy Filter Implementation

```typescript
// src/lib/data-export/transformers/privacy-filter.ts
import { z } from 'zod';

export const PrivacyLevel = z.enum(['public', 'internal', 'confidential', 'restricted']);

export const PrivacyConfig = z.object({
  maskEmails: z.boolean().default(false),
  maskIpAddresses: z.boolean().default(true),
  excludeSessionData: z.boolean().default(false),
  includeDeletedRecords: z.boolean().default(false),
  anonymizeIdentifiers: z.boolean().default(false),
});

export class PrivacyFilter {
  async filterSensitiveData(data: any, config: PrivacyConfig = {}): Promise<any> {
    const defaultConfig = PrivacyConfig.parse(config);
    
    return this.recursiveFilter(data, defaultConfig);
  }

  private recursiveFilter(data: any, config: PrivacyConfig): any {
    if (Array.isArray(data)) {
      return data.map(item => this.recursiveFilter(item, config));
    }

    if (data && typeof data === 'object' && data.constructor === Object) {
      const filtered: any = {};
      
      for (const [key, value] of Object.entries(data)) {
        // Skip sensitive fields based on configuration
        if (this.shouldExcludeField(key, config)) {
          continue;
        }

        // Apply field-specific filtering
        filtered[key] = this.filterField(key, value, config);
      }

      return filtered;
    }

    return data;
  }

  private shouldExcludeField(fieldName: string, config: PrivacyConfig): boolean {
    const excludePatterns = [
      'password',
      'token',
      'secret',
      'key',
      'salt',
      'hash',
    ];

    if (config.excludeSessionData) {
      excludePatterns.push('session', 'cookie');
    }

    return excludePatterns.some(pattern => 
      fieldName.toLowerCase().includes(pattern)
    );
  }

  private filterField(fieldName: string, value: any, config: PrivacyConfig): any {
    if (value === null || value === undefined) {
      return value;
    }

    // Mask email addresses
    if (config.maskEmails && this.isEmail(value)) {
      return this.maskEmail(value);
    }

    // Mask IP addresses
    if (config.maskIpAddresses && this.isIpAddress(value)) {
      return this.maskIpAddress(value);
    }

    // Anonymize identifiers
    if (config.anonymizeIdentifiers && this.isIdentifier(fieldName)) {
      return this.anonymizeIdentifier(value);
    }

    // Recursively filter nested objects
    if (typeof value === 'object') {
      return this.recursiveFilter(value, config);
    }

    return value;
  }

  private isEmail(value: any): boolean {
    if (typeof value !== 'string') return false;
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
  }

  private isIpAddress(value: any): boolean {
    if (typeof value !== 'string') return false;
    return /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(value);
  }

  private isIdentifier(fieldName: string): boolean {
    const identifierPatterns = ['id', 'uuid', 'guid', 'key'];
    return identifierPatterns.some(pattern => 
      fieldName.toLowerCase().includes(pattern)
    );
  }

  private maskEmail(email: string): string {
    const [local, domain] = email.split('@');
    const maskedLocal = local.slice(0, 2) + '*'.repeat(local.length - 2);
    return `${maskedLocal}@${domain}`;
  }

  private maskIpAddress(ip: string): string {
    const parts = ip.split('.');
    return `${parts[0]}.${parts[1]}.xxx.xxx`;
  }

  private anonymizeIdentifier(value: string): string {
    // Replace with anonymized version while maintaining format
    return `anon_${value.slice(-4)}`;
  }
}

export const privacyFilter = new PrivacyFilter();
```

## Testing Strategy

### Unit Tests
```typescript
// src/lib/data-export/services/__tests__/export-service.test.ts
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { exportService } from '../export-service';
import { csvExporter } from '../csv-export';
import { auditLogger } from '@/lib/audit/audit-logger';

vi.mock('../csv-export');
vi.mock('@/lib/audit/audit-logger');

describe('ExportService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('requestExport', () => {
    it('should create export request successfully', async () => {
      const request = {
        format: 'csv' as const,
        includeRelationships: true,
        compressionLevel: 'medium' as const,
      };

      const result = await exportService.requestExport(
        'user-123',
        'tenant-123',
        request
      );

      expect(result).toMatchObject({
        userId: 'user-123',
        tenantId: 'tenant-123',
        format: 'csv',
        status: 'pending',
        progress: 0,
      });

      expect(auditLogger.log).toHaveBeenCalledWith({
        action: 'data_export_requested',
        userId: 'user-123',
        tenantId: 'tenant-123',
        metadata: expect.objectContaining({
          format: 'csv',
        }),
      });
    });

    it('should handle invalid format', async () => {
      const request = {
        format: 'invalid' as any,
        includeRelationships: true,
        compressionLevel: 'medium' as const,
      };

      await expect(
        exportService.requestExport('user-123', 'tenant-123', request)
      ).rejects.toThrow();
    });
  });

  describe('processExport', () => {
    it('should process CSV export successfully', async () => {
      const mockStream = { pipe: vi.fn() };
      vi.mocked(csvExporter.createExportStream).mockResolvedValue(mockStream as any);

      const request = {
        format: 'csv' as const,
        includeRelationships: true,
        compressionLevel: 'medium' as const,
      };

      await exportService.processExport('export-123', request);

      expect(csvExporter.createExportStream).toHaveBeenCalled();
    });

    it('should handle export errors', async () => {
      vi.mocked(csvExporter.createExportStream).mockRejectedValue(
        new Error('Export failed')
      );

      const request = {
        format: 'csv' as const,
        includeRelationships: true,
        compressionLevel: 'medium' as const,
      };

      await exportService.processExport('export-123', request);

      expect(auditLogger.log).toHaveBeenCalledWith({
        action: 'data_export_failed',
        userId: expect.any(String),
        tenantId: expect.any(String),
        metadata: expect.objectContaining({
          error: 'Export failed',
        }),
      });
    });
  });
});
```

### Integration Tests
```typescript
// src/lib/data-export/services/__tests__/csv-export.integration.test.ts
import { describe, it, expect, beforeEach } from 'vitest';
import { csvExporter } from '../csv-export';
import { userRepository } from '@/lib/database/repositories/user-repository';
import { testDatabase } from '@/lib/test/database';

describe('CsvExporter Integration', () => {
  beforeEach(async () => {
    await testDatabase.seed();
  });

  it('should export user data to CSV format', async () => {
    const user = await userRepository.create({
      email: '<EMAIL>',
      name: 'Test User',
      tenantId: 'tenant-123',
    });

    const request = {
      format: 'csv' as const,
      includeRelationships: true,
      compressionLevel: 'medium' as const,
    };

    const stream = await csvExporter.createExportStream(
      user.id,
      'tenant-123',
      request
    );

    const chunks: Buffer[] = [];
    for await (const chunk of stream) {
      chunks.push(chunk);
    }

    const csvContent = Buffer.concat(chunks).toString();
    
    expect(csvContent).toContain('category,field,value');
    expect(csvContent).toContain('Profile,Email,<EMAIL>');
    expect(csvContent).toContain('Profile,Name,Test User');
  });

  it('should handle large datasets efficiently', async () => {
    // Create large dataset
    const users = [];
    for (let i = 0; i < 1000; i++) {
      users.push({
        email: `user${i}@example.com`,
        name: `User ${i}`,
        tenantId: 'tenant-123',
      });
    }

    await userRepository.bulkCreate(users);

    const request = {
      format: 'csv' as const,
      includeRelationships: true,
      compressionLevel: 'medium' as const,
    };

    const startTime = Date.now();
    const stream = await csvExporter.createExportStream(
      users[0].id,
      'tenant-123',
      request
    );

    let rowCount = 0;
    for await (const chunk of stream) {
      rowCount += chunk.toString().split('\n').length - 1;
    }

    const endTime = Date.now();
    const duration = endTime - startTime;

    expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
    expect(rowCount).toBeGreaterThan(0);
  });
});
```

## Security Considerations

### Data Protection
- **Encryption**: All exported data encrypted at rest and in transit
- **Access Control**: Role-based access with ownership validation
- **Audit Trail**: Complete logging of all export and deletion operations
- **Secure Storage**: Temporary files with automatic cleanup and secure deletion

### Privacy Compliance
- **Data Minimization**: Export only necessary data based on user selection
- **Anonymization**: Optional anonymization of sensitive identifiers
- **Consent Management**: Clear consent mechanisms for data operations
- **Right to Rectification**: Data correction before export

### GDPR Compliance
- **Article 20**: Right to data portability with structured, machine-readable format
- **Article 17**: Right to erasure with complete data deletion
- **Article 5**: Data minimization and purpose limitation
- **Article 32**: Security of processing with appropriate measures

## Performance Optimization

### Streaming Architecture
- **Memory Efficiency**: Streaming-based processing for large datasets
- **Background Processing**: Asynchronous job processing for heavy operations
- **Resource Management**: Automatic cleanup of temporary files and resources
- **Scalability**: Horizontal scaling support for high-volume operations

### Caching Strategy
- **Export Status**: Cache export progress and status updates
- **User Data**: Cache frequently accessed user data for exports
- **File Management**: Efficient file storage and retrieval mechanisms
- **CDN Integration**: Global content delivery for large exports

## Monitoring & Compliance

### Audit Logging
- **Complete Trail**: Every operation logged with full context
- **Compliance Reports**: Automated generation of compliance reports
- **Data Lineage**: Track data flow through export and deletion processes
- **Regulatory Reporting**: Support for regulatory audit requirements

### Performance Monitoring
- **Export Metrics**: Track export completion times and success rates
- **Deletion Metrics**: Monitor deletion success and verification rates
- **User Activity**: Track user data requests and compliance metrics
- **System Health**: Monitor resource usage and system performance

## Success Metrics

### Performance Metrics
- **Export Completion Time**: < 5 minutes for standard exports
- **Deletion Verification**: 100% verification success rate
- **System Uptime**: 99.9% availability for data operations
- **User Satisfaction**: > 95% satisfaction with export quality

### Compliance Metrics
- **GDPR Compliance**: 100% compliance with GDPR requirements
- **Audit Success**: 100% audit trail completeness
- **Data Accuracy**: 99.99% data accuracy in exports
- **Security Incidents**: Zero security incidents related to data operations

This comprehensive User Data Export & Deletion implementation provides complete GDPR compliance with secure, efficient, and user-friendly data portability and deletion capabilities for the NEXUS SaaS Starter.
