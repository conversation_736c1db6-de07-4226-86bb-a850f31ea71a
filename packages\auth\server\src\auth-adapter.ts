import { NextRequest, NextResponse } from "next/server";
import { auth } from "./auth-config";

// Better Auth API handler for Next.js App Router
export async function authHandler(request: NextRequest) {
  return auth.handler(request);
}

// Export auth methods for use in API routes
export const {
  signIn,
  signUp,
  signOut,
  getSession,
  listSessions,
} = auth;

// Middleware helper for protecting routes
export async function getServerSession(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    });
    return session;
  } catch (error) {
    console.error("Failed to get server session:", error);
    return null;
  }
}

// Helper to get user from session
export async function getServerUser(request: NextRequest) {
  const session = await getServerSession(request);
  return session?.user || null;
}

// Helper to check if user is authenticated
export async function isAuthenticated(request: NextRequest): Promise<boolean> {
  const session = await getServerSession(request);
  return !!session?.user;
}

// Helper to check user role
export async function hasRole(request: NextRequest, role: string): Promise<boolean> {
  const user = await getServerUser(request);
  return user?.role === role;
}

// Helper to check if user is admin
export async function isAdmin(request: NextRequest): Promise<boolean> {
  const user = await getServerUser(request);
  return user?.role === "OWNER" || user?.role === "ADMIN";
}
