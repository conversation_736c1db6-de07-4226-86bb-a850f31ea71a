# NEXUS Active Context Memory System
# Maintains project context and decision history with enforced memory creation

context_memory:
  project_context:
    initialized: true
    framework_version: "2.0.0"
    last_updated: "2025-07-17"
    
  # ACTIVE MEMORY STORAGE (Updated via nexus-remember commands)
  active_memory:
    decisions_memory:
      # Format: timestamp: decision - reasoning - outcome
      # Example: "2025-07-17_14:30: Used Zustand for state - lightweight 2KB bundle - better TS integration"
      stored_decisions: []
      
    pattern_memory:
      # Format: pattern_name: description - when_to_use - examples  
      # Example: "component_structure: shadcn in /ui, custom in /custom - consistency - Button.tsx in /ui"
      stored_patterns: []
      
    failure_memory:
      # Format: failure_type: what_failed - cause - solution - prevention
      # Example: "api_error: /api/users 500 - missing await - added await - always await async DB ops"
      stored_failures: 
        - "unnecessary_documentation_creation: Created VALIDATION_LIBRARY_UPDATE.md without being asked - assumed documentation needed for migration - delete unnecessary docs and store in memory - NEVER create summary documents unless explicitly requested"
      
    file_memory:
      # Format: file_path: purpose - key_patterns - modification_history
      # Example: "/components/ui/button.tsx: reusable button - shadcn pattern - modified 2025-07-17 for variants"
      stored_files: []
      
    context_checkpoints:
      # Format: session_id: current_task - progress - next_steps
      # Updated every 10-15 exchanges automatically
      stored_checkpoints: []
      
    success_memory:
      # Format: success_type: what_worked - why_it_worked - when_to_use_again - user_guidance_provided
      # Example: "api_fix: added await to DB query - async operation needed proper handling - always for DB ops - user pointed out missing await"
      stored_successes: []
      
    breakthrough_memory:
      # Format: breakthrough_type: problem_faced - solution_found - how_discovered - pattern_to_remember
      # Example: "error_loop_break: infinite 500 errors - middleware order issue - user suggested checking middleware stack - middleware sequence matters"
      stored_breakthroughs: []
  
  # MEMORY COMMANDS USAGE TRACKING
  memory_usage:
    nexus_remember_count: 0
    nexus_recall_count: 0
    last_memory_creation: null
    last_context_checkpoint: null
