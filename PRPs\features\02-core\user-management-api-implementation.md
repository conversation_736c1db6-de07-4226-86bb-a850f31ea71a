# User Management API Implementation

## Overview
Implement a comprehensive REST API for user management operations in the NEXUS SaaS Starter, providing full CRUD operations, role management, and tenant-aware user administration with OpenAPI documentation.

## Context Research Summary

### Next.js API Route Handler Patterns
- **OpenAPI Route Handler**: Using `next-openapi-route-handler` for type-safe API routes with automatic OpenAPI spec generation
- **Zod Validation**: Schema-based validation for request/response types with automatic OpenAPI schema generation
- **Error Handling**: Structured error responses with consistent HTTP status codes and error messages
- **Type Safety**: Full TypeScript support with automatic type inference from Zod schemas

### Enterprise API Design Patterns
- **Pagination**: Consistent pagination with `limit`, `offset`, and total count metadata
- **Filtering**: Advanced filtering capabilities with multiple field support
- **Sorting**: Multi-field sorting with ascending/descending options
- **Bulk Operations**: Batch operations for efficient bulk user management
- **Versioning**: API versioning strategy for backward compatibility

### Authentication & Authorization
- **JWT-based Authentication**: Secure API access with JWT tokens
- **Role-based Access Control**: Hierarchical permission system
- **Tenant Isolation**: Multi-tenant data separation and access control
- **Rate Limiting**: API rate limiting to prevent abuse

## Implementation Plan

### Phase 1: Core API Infrastructure
1. **API Route Structure Setup**
   - Create base API route handlers with OpenAPI integration
   - Implement consistent error handling middleware
   - Set up request/response validation with Zod schemas
   - Configure automatic OpenAPI documentation generation

2. **Authentication Middleware**
   - Implement JWT token validation
   - Add role-based access control checks
   - Create tenant context resolution
   - Set up rate limiting middleware

### Phase 2: User CRUD Operations
1. **User Creation API**
   - POST `/api/v1/users` - Create new user
   - Bulk user creation endpoint
   - User registration with email verification
   - Admin user creation with role assignment

2. **User Retrieval API**
   - GET `/api/v1/users` - List users with pagination and filtering
   - GET `/api/v1/users/:id` - Get user by ID
   - GET `/api/v1/users/search` - Advanced user search
   - GET `/api/v1/users/export` - Export users data

3. **User Update API**
   - PUT `/api/v1/users/:id` - Update user (full replacement)
   - PATCH `/api/v1/users/:id` - Partial user update
   - PATCH `/api/v1/users/bulk` - Bulk user updates
   - PATCH `/api/v1/users/:id/status` - Update user status

4. **User Deletion API**
   - DELETE `/api/v1/users/:id` - Soft delete user
   - DELETE `/api/v1/users/:id/hard` - Hard delete user (admin only)
   - DELETE `/api/v1/users/bulk` - Bulk user deletion

### Phase 3: Advanced User Management
1. **Role Management API**
   - GET `/api/v1/users/:id/roles` - Get user roles
   - POST `/api/v1/users/:id/roles` - Assign roles to user
   - DELETE `/api/v1/users/:id/roles/:roleId` - Remove role from user
   - GET `/api/v1/roles` - List available roles

2. **User Activity API**
   - GET `/api/v1/users/:id/activity` - Get user activity logs
   - GET `/api/v1/users/:id/sessions` - Get active user sessions
   - DELETE `/api/v1/users/:id/sessions` - Revoke all user sessions
   - GET `/api/v1/users/:id/audit` - Get user audit trail

3. **User Preferences API**
   - GET `/api/v1/users/:id/preferences` - Get user preferences
   - PUT `/api/v1/users/:id/preferences` - Update user preferences
   - DELETE `/api/v1/users/:id/preferences` - Reset user preferences

### Phase 4: Integration & Security
1. **Webhook Integration**
   - User creation webhooks
   - User update webhooks
   - User deletion webhooks
   - Role change webhooks

2. **Security Features**
   - Input sanitization and validation
   - SQL injection prevention
   - XSS protection
   - CSRF protection
   - API rate limiting

## Technical Implementation

### File Structure
```
src/
├── app/
│   ├── api/
│   │   └── v1/
│   │       ├── users/
│   │       │   ├── route.ts                    # Main users endpoint
│   │       │   ├── [id]/
│   │       │   │   ├── route.ts                # User by ID operations
│   │       │   │   ├── roles/
│   │       │   │   │   └── route.ts            # User roles management
│   │       │   │   ├── activity/
│   │       │   │   │   └── route.ts            # User activity logs
│   │       │   │   ├── sessions/
│   │       │   │   │   └── route.ts            # User sessions
│   │       │   │   └── preferences/
│   │       │   │       └── route.ts            # User preferences
│   │       │   ├── search/
│   │       │   │   └── route.ts                # User search
│   │       │   ├── export/
│   │       │   │   └── route.ts                # User data export
│   │       │   └── bulk/
│   │       │       └── route.ts                # Bulk operations
│   │       └── roles/
│   │           └── route.ts                    # Roles management
├── lib/
│   ├── api/
│   │   ├── users/
│   │   │   ├── schemas.ts                      # Zod schemas
│   │   │   ├── handlers.ts                     # Business logic
│   │   │   ├── validation.ts                   # Custom validators
│   │   │   └── types.ts                        # TypeScript types
│   │   ├── middleware/
│   │   │   ├── auth.ts                         # Authentication middleware
│   │   │   ├── validation.ts                   # Request validation
│   │   │   ├── rate-limit.ts                   # Rate limiting
│   │   │   └── error-handler.ts                # Error handling
│   │   └── utils/
│   │       ├── pagination.ts                   # Pagination helpers
│   │       ├── filtering.ts                    # Filtering utilities
│   │       └── sorting.ts                      # Sorting utilities
│   └── database/
│       └── repositories/
│           └── user-repository.ts              # User data access
```

### Core API Route Implementation

```typescript
// src/app/api/v1/users/route.ts
import { NextRequest } from 'next/server';
import { defineRoute } from 'next-openapi-route-handler';
import { z } from 'zod';
import { authMiddleware } from '@/lib/api/middleware/auth';
import { validateRequest } from '@/lib/api/middleware/validation';
import { rateLimitMiddleware } from '@/lib/api/middleware/rate-limit';
import { 
  createUserHandler, 
  listUsersHandler 
} from '@/lib/api/users/handlers';
import { 
  CreateUserSchema, 
  ListUsersQuerySchema, 
  UserResponseSchema 
} from '@/lib/api/users/schemas';

export const GET = defineRoute({
  method: 'GET',
  path: '/api/v1/users',
  tags: ['Users'],
  summary: 'List users with pagination and filtering',
  querySchema: ListUsersQuerySchema,
  responseSchema: z.object({
    users: z.array(UserResponseSchema),
    pagination: z.object({
      total: z.number(),
      page: z.number(),
      limit: z.number(),
      totalPages: z.number(),
    }),
  }),
  middleware: [authMiddleware, rateLimitMiddleware],
  handler: listUsersHandler,
});

export const POST = defineRoute({
  method: 'POST',
  path: '/api/v1/users',
  tags: ['Users'],
  summary: 'Create a new user',
  requestSchema: CreateUserSchema,
  responseSchema: UserResponseSchema,
  middleware: [authMiddleware, validateRequest],
  handler: createUserHandler,
});
```

### Zod Schema Definitions

```typescript
// src/lib/api/users/schemas.ts
import { z } from 'zod';

export const UserBaseSchema = z.object({
  email: z.string().email('Invalid email format'),
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  role: z.enum(['admin', 'user', 'moderator']).optional(),
  status: z.enum(['active', 'inactive', 'suspended']).optional(),
  metadata: z.record(z.any()).optional(),
});

export const CreateUserSchema = UserBaseSchema.extend({
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string(),
  tenantId: z.string().uuid('Invalid tenant ID'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const UpdateUserSchema = UserBaseSchema.partial().extend({
  id: z.string().uuid('Invalid user ID'),
});

export const UserResponseSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  name: z.string(),
  role: z.enum(['admin', 'user', 'moderator']),
  status: z.enum(['active', 'inactive', 'suspended']),
  tenantId: z.string().uuid(),
  createdAt: z.date(),
  updatedAt: z.date(),
  lastLoginAt: z.date().nullable(),
  metadata: z.record(z.any()).optional(),
});

export const ListUsersQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  search: z.string().optional(),
  role: z.enum(['admin', 'user', 'moderator']).optional(),
  status: z.enum(['active', 'inactive', 'suspended']).optional(),
  sortBy: z.enum(['name', 'email', 'createdAt', 'lastLoginAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  tenantId: z.string().uuid().optional(),
});

export const BulkUserOperationSchema = z.object({
  userIds: z.array(z.string().uuid()).min(1).max(100),
  operation: z.enum(['delete', 'activate', 'deactivate', 'suspend']),
  reason: z.string().optional(),
});
```

### Business Logic Handlers

```typescript
// src/lib/api/users/handlers.ts
import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse } from '@/lib/api/utils/responses';
import { userRepository } from '@/lib/database/repositories/user-repository';
import { auditLogger } from '@/lib/audit/audit-logger';
import { sendUserCreatedEmail } from '@/lib/email/user-notifications';
import { 
  CreateUserSchema, 
  ListUsersQuerySchema, 
  UpdateUserSchema 
} from './schemas';

export async function createUserHandler(req: NextRequest) {
  try {
    const body = await req.json();
    const validatedData = CreateUserSchema.parse(body);
    
    // Check if user already exists
    const existingUser = await userRepository.findByEmail(validatedData.email);
    if (existingUser) {
      return createErrorResponse('User already exists', 409);
    }
    
    // Create user with encrypted password
    const user = await userRepository.create({
      ...validatedData,
      password: await hashPassword(validatedData.password),
    });
    
    // Log audit event
    await auditLogger.log({
      action: 'user_created',
      userId: user.id,
      tenantId: user.tenantId,
      metadata: { email: user.email },
    });
    
    // Send welcome email
    await sendUserCreatedEmail(user);
    
    return createResponse(user, 201);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return createErrorResponse('Validation error', 400, error.errors);
    }
    
    console.error('Error creating user:', error);
    return createErrorResponse('Internal server error', 500);
  }
}

export async function listUsersHandler(req: NextRequest) {
  try {
    const searchParams = Object.fromEntries(req.nextUrl.searchParams);
    const query = ListUsersQuerySchema.parse(searchParams);
    
    const { users, total } = await userRepository.findMany({
      page: query.page,
      limit: query.limit,
      search: query.search,
      filters: {
        role: query.role,
        status: query.status,
        tenantId: query.tenantId,
      },
      sort: {
        field: query.sortBy,
        order: query.sortOrder,
      },
    });
    
    const totalPages = Math.ceil(total / query.limit);
    
    return createResponse({
      users,
      pagination: {
        total,
        page: query.page,
        limit: query.limit,
        totalPages,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return createErrorResponse('Invalid query parameters', 400, error.errors);
    }
    
    console.error('Error listing users:', error);
    return createErrorResponse('Internal server error', 500);
  }
}

export async function updateUserHandler(req: NextRequest) {
  try {
    const body = await req.json();
    const validatedData = UpdateUserSchema.parse(body);
    
    const user = await userRepository.findById(validatedData.id);
    if (!user) {
      return createErrorResponse('User not found', 404);
    }
    
    const updatedUser = await userRepository.update(validatedData.id, validatedData);
    
    await auditLogger.log({
      action: 'user_updated',
      userId: updatedUser.id,
      tenantId: updatedUser.tenantId,
      metadata: { changes: validatedData },
    });
    
    return createResponse(updatedUser);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return createErrorResponse('Validation error', 400, error.errors);
    }
    
    console.error('Error updating user:', error);
    return createErrorResponse('Internal server error', 500);
  }
}
```

### Authentication Middleware

```typescript
// src/lib/api/middleware/auth.ts
import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth/jwt';
import { createErrorResponse } from '@/lib/api/utils/responses';

export async function authMiddleware(req: NextRequest) {
  const authHeader = req.headers.get('authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return createErrorResponse('Authorization header required', 401);
  }
  
  const token = authHeader.substring(7);
  
  try {
    const payload = await verifyJWT(token);
    
    // Add user context to request
    (req as any).user = payload;
    
    return NextResponse.next();
  } catch (error) {
    return createErrorResponse('Invalid or expired token', 401);
  }
}

export async function requireRole(allowedRoles: string[]) {
  return async (req: NextRequest) => {
    const user = (req as any).user;
    
    if (!user || !allowedRoles.includes(user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }
    
    return NextResponse.next();
  };
}
```

### User Repository

```typescript
// src/lib/database/repositories/user-repository.ts
import { PrismaClient } from '@prisma/client';
import { User, CreateUserData, UpdateUserData } from '@/lib/types/user';

export class UserRepository {
  constructor(private prisma: PrismaClient) {}
  
  async create(data: CreateUserData): Promise<User> {
    return this.prisma.user.create({
      data: {
        ...data,
        id: crypto.randomUUID(),
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });
  }
  
  async findById(id: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { id },
      include: {
        roles: true,
        tenant: true,
      },
    });
  }
  
  async findByEmail(email: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { email },
    });
  }
  
  async findMany(options: {
    page: number;
    limit: number;
    search?: string;
    filters?: {
      role?: string;
      status?: string;
      tenantId?: string;
    };
    sort?: {
      field: string;
      order: 'asc' | 'desc';
    };
  }): Promise<{ users: User[]; total: number }> {
    const { page, limit, search, filters, sort } = options;
    const skip = (page - 1) * limit;
    
    const where = {
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
        ],
      }),
      ...(filters?.role && { role: filters.role }),
      ...(filters?.status && { status: filters.status }),
      ...(filters?.tenantId && { tenantId: filters.tenantId }),
    };
    
    const [users, total] = await Promise.all([
      this.prisma.user.findMany({
        where,
        skip,
        take: limit,
        orderBy: sort ? { [sort.field]: sort.order } : { createdAt: 'desc' },
        include: {
          roles: true,
          tenant: true,
        },
      }),
      this.prisma.user.count({ where }),
    ]);
    
    return { users, total };
  }
  
  async update(id: string, data: UpdateUserData): Promise<User> {
    return this.prisma.user.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date(),
      },
      include: {
        roles: true,
        tenant: true,
      },
    });
  }
  
  async delete(id: string): Promise<void> {
    await this.prisma.user.update({
      where: { id },
      data: {
        status: 'inactive',
        deletedAt: new Date(),
      },
    });
  }
  
  async hardDelete(id: string): Promise<void> {
    await this.prisma.user.delete({
      where: { id },
    });
  }
}

export const userRepository = new UserRepository(new PrismaClient());
```

### API Response Utilities

```typescript
// src/lib/api/utils/responses.ts
import { NextResponse } from 'next/server';

export function createResponse(data: any, status: number = 200) {
  return NextResponse.json({
    success: true,
    data,
    timestamp: new Date().toISOString(),
  }, { status });
}

export function createErrorResponse(
  message: string, 
  status: number = 500, 
  errors?: any[]
) {
  return NextResponse.json({
    success: false,
    error: {
      message,
      code: status,
      errors,
    },
    timestamp: new Date().toISOString(),
  }, { status });
}

export function createPaginatedResponse(
  data: any[],
  total: number,
  page: number,
  limit: number
) {
  return NextResponse.json({
    success: true,
    data,
    pagination: {
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    },
    timestamp: new Date().toISOString(),
  });
}
```

### Rate Limiting Middleware

```typescript
// src/lib/api/middleware/rate-limit.ts
import { NextRequest, NextResponse } from 'next/server';
import { createErrorResponse } from '@/lib/api/utils/responses';

const rateLimit = new Map<string, { count: number; resetTime: number }>();

export async function rateLimitMiddleware(req: NextRequest) {
  const clientIP = req.ip || 'unknown';
  const key = `${clientIP}:${req.nextUrl.pathname}`;
  const now = Date.now();
  const windowMs = 60 * 1000; // 1 minute
  const maxRequests = 100; // 100 requests per minute
  
  const current = rateLimit.get(key);
  
  if (!current || now > current.resetTime) {
    rateLimit.set(key, { count: 1, resetTime: now + windowMs });
    return NextResponse.next();
  }
  
  if (current.count >= maxRequests) {
    return createErrorResponse('Rate limit exceeded', 429);
  }
  
  current.count++;
  return NextResponse.next();
}
```

### OpenAPI Documentation Config

```typescript
// src/lib/api/openapi-config.ts
import { OpenAPIConfig } from 'next-openapi-route-handler';

export const openApiConfig: OpenAPIConfig = {
  openapi: '3.0.0',
  info: {
    title: 'NEXUS SaaS Starter - User Management API',
    version: '1.0.0',
    description: 'Comprehensive user management API with authentication, roles, and multi-tenant support',
  },
  servers: [
    {
      url: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000',
      description: 'Development server',
    },
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
      },
    },
  },
  security: [
    {
      bearerAuth: [],
    },
  ],
  tags: [
    {
      name: 'Users',
      description: 'User management operations',
    },
    {
      name: 'Roles',
      description: 'Role management operations',
    },
    {
      name: 'Authentication',
      description: 'Authentication and authorization',
    },
  ],
};
```

## Testing Strategy

### Unit Tests
```typescript
// src/lib/api/users/__tests__/handlers.test.ts
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { NextRequest } from 'next/server';
import { createUserHandler, listUsersHandler } from '../handlers';

describe('User API Handlers', () => {
  beforeEach(() => {
    // Setup test database
  });
  
  afterEach(() => {
    // Cleanup test database
  });
  
  describe('createUserHandler', () => {
    it('should create a new user successfully', async () => {
      const req = new NextRequest('http://localhost/api/v1/users', {
        method: 'POST',
        body: JSON.stringify({
          email: '<EMAIL>',
          name: 'Test User',
          password: 'password123',
          confirmPassword: 'password123',
          tenantId: 'tenant-id',
        }),
      });
      
      const response = await createUserHandler(req);
      const data = await response.json();
      
      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(data.data.email).toBe('<EMAIL>');
    });
    
    it('should return 409 if user already exists', async () => {
      // Test duplicate user creation
    });
    
    it('should return 400 for invalid data', async () => {
      // Test validation errors
    });
  });
  
  describe('listUsersHandler', () => {
    it('should return paginated users list', async () => {
      // Test pagination
    });
    
    it('should filter users by role', async () => {
      // Test role filtering
    });
    
    it('should search users by name and email', async () => {
      // Test search functionality
    });
  });
});
```

### Integration Tests
```typescript
// src/lib/api/users/__tests__/integration.test.ts
import { describe, it, expect } from 'vitest';
import { testClient } from '@/lib/test/client';

describe('User API Integration', () => {
  it('should handle complete user lifecycle', async () => {
    // Create user
    const createResponse = await testClient.post('/api/v1/users', {
      email: '<EMAIL>',
      name: 'Integration Test',
      password: 'password123',
      confirmPassword: 'password123',
      tenantId: 'test-tenant',
    });
    
    expect(createResponse.status).toBe(201);
    const user = createResponse.data.data;
    
    // Get user
    const getResponse = await testClient.get(`/api/v1/users/${user.id}`);
    expect(getResponse.status).toBe(200);
    
    // Update user
    const updateResponse = await testClient.put(`/api/v1/users/${user.id}`, {
      name: 'Updated Name',
    });
    expect(updateResponse.status).toBe(200);
    
    // Delete user
    const deleteResponse = await testClient.delete(`/api/v1/users/${user.id}`);
    expect(deleteResponse.status).toBe(204);
  });
});
```

## Security Considerations

### Input Validation
- All input validated using Zod schemas
- SQL injection prevention through parameterized queries
- XSS protection through input sanitization
- File upload validation and scanning

### Authentication & Authorization
- JWT-based authentication with refresh tokens
- Role-based access control (RBAC)
- Multi-tenant data isolation
- Session management and timeout

### Data Protection
- Password hashing with bcrypt
- Sensitive data encryption at rest
- PII data handling compliance
- Audit logging for all operations

### API Security
- Rate limiting to prevent abuse
- CORS configuration for cross-origin requests
- Request size limits
- API versioning for backward compatibility

## Performance Optimization

### Database Optimization
- Proper indexing on frequently queried fields
- Connection pooling for database connections
- Query optimization and N+1 prevention
- Bulk operations for large datasets

### Caching Strategy
- Redis caching for frequently accessed data
- API response caching
- Database query result caching
- Cache invalidation strategies

### Monitoring & Logging
- API endpoint monitoring
- Error tracking and alerting
- Performance metrics collection
- Audit trail maintenance

## Deployment & DevOps

### Environment Configuration
- Environment-specific configuration
- Secret management with secure storage
- Database migration scripts
- Health check endpoints

### CI/CD Pipeline
- Automated testing on pull requests
- Code quality checks with ESLint/Prettier
- Security scanning with SAST tools
- Automated deployment to staging/production

### Monitoring & Alerting
- API performance monitoring
- Error rate monitoring
- Database performance tracking
- User activity analytics

## Documentation & API Reference

### OpenAPI Specification
- Auto-generated API documentation
- Interactive API explorer
- Request/response examples
- Error code reference

### Developer Documentation
- API integration guide
- Authentication flow documentation
- Rate limiting guidelines
- Best practices and examples

## Success Metrics

### Performance Metrics
- API response time < 200ms (95th percentile)
- Database query time < 50ms average
- Memory usage < 512MB per instance
- CPU usage < 70% under normal load

### Quality Metrics
- Test coverage > 90%
- Zero critical security vulnerabilities
- API uptime > 99.9%
- Error rate < 0.1%

### User Experience
- Comprehensive error messages
- Consistent API responses
- Clear documentation
- Developer-friendly integration

## Future Enhancements

### Advanced Features
- GraphQL API support
- Real-time user updates via WebSockets
- Advanced search with Elasticsearch
- Machine learning-based user analytics

### Scalability Improvements
- API gateway integration
- Microservices architecture
- Horizontal scaling support
- Global CDN integration

### Security Enhancements
- Multi-factor authentication API
- OAuth 2.0 / OpenID Connect support
- Advanced threat detection
- Compliance reporting (GDPR, CCPA)

This comprehensive User Management API implementation provides a robust, scalable, and secure foundation for managing users in the NEXUS SaaS Starter, with full OpenAPI documentation, comprehensive testing, and enterprise-grade security features.
