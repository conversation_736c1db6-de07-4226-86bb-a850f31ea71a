import { NotificationSettings } from "./profile-types";
import { validateNotificationSettings } from "./profile-validation";

export class PreferencesService {
  private userId: string;
  private tenantId: string;

  constructor(userId: string, tenantId: string) {
    this.userId = userId;
    this.tenantId = tenantId;
  }

  // Get notification settings
  async getNotificationSettings(): Promise<NotificationSettings> {
    const response = await fetch(`/api/profile/${this.userId}/notifications`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch notification settings");
    }

    return response.json();
  }

  // Update notification settings
  async updateNotificationSettings(settings: NotificationSettings): Promise<NotificationSettings> {
    const validatedSettings = validateNotificationSettings(settings);

    const response = await fetch(`/api/profile/${this.userId}/notifications`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify(validatedSettings),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to update notification settings");
    }

    return response.json();
  }

  // Get available timezones
  async getTimezones(): Promise<{ value: string; label: string; offset: string }[]> {
    const response = await fetch("/api/timezones");

    if (!response.ok) {
      throw new Error("Failed to fetch timezones");
    }

    return response.json();
  }

  // Get available languages
  getLanguages(): { value: string; label: string; flag: string }[] {
    return [
      { value: "en", label: "English", flag: "🇺🇸" },
      { value: "es", label: "Español", flag: "🇪🇸" },
      { value: "fr", label: "Français", flag: "🇫🇷" },
      { value: "de", label: "Deutsch", flag: "🇩🇪" },
      { value: "ja", label: "日本語", flag: "🇯🇵" },
      { value: "zh", label: "中文", flag: "🇨🇳" },
    ];
  }

  // Test notification delivery
  async testNotification(type: "email" | "push"): Promise<void> {
    const response = await fetch(`/api/profile/${this.userId}/test-notification`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify({ type }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to send test notification");
    }
  }

  // Get default notification settings
  getDefaultNotificationSettings(): NotificationSettings {
    return {
      email: {
        security: true,
        updates: true,
        marketing: false,
        mentions: true,
        comments: true,
      },
      push: {
        security: true,
        updates: false,
        mentions: true,
        comments: false,
      },
      inApp: {
        security: true,
        updates: true,
        mentions: true,
        comments: true,
      },
    };
  }
}

export const createPreferencesService = (userId: string, tenantId: string): PreferencesService => {
  return new PreferencesService(userId, tenantId);
};
