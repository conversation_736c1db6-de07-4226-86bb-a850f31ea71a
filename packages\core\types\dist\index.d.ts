export interface User {
    id: string;
    email: string;
    name: string | null;
    avatar: string | null;
    createdAt: Date;
    updatedAt: Date;
}
export interface Tenant {
    id: string;
    name: string;
    slug: string;
    domain: string | null;
    createdAt: Date;
    updatedAt: Date;
}
export interface Workspace {
    id: string;
    name: string;
    slug: string;
    tenantId: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}
export interface PaginatedResponse<T = any> {
    data: T[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
}
export type Role = "admin" | "user" | "viewer";
export interface Permission {
    id: string;
    name: string;
    resource: string;
    action: string;
}
export interface UserRole {
    userId: string;
    role: Role;
    permissions: Permission[];
    tenantId: string;
    workspaceId?: string;
}
//# sourceMappingURL=index.d.ts.map