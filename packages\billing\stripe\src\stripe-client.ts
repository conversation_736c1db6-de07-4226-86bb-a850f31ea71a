import { loadStripe, Stripe } from "@stripe/stripe-js";

// Client-side Stripe configuration
let stripePromise: Promise<Stripe | null>;

export const getStripe = (): Promise<Stripe | null> => {
  if (!stripePromise) {
    const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
    
    if (!publishableKey) {
      console.error("Missing NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY environment variable");
      return Promise.resolve(null);
    }

    stripePromise = loadStripe(publishableKey);
  }
  
  return stripePromise;
};

// Stripe Elements appearance configuration
export const stripeElementsAppearance: Stripe.Appearance = {
  theme: "stripe",
  variables: {
    colorPrimary: "#0570de",
    colorBackground: "#ffffff",
    colorText: "#30313d",
    colorDanger: "#df1b41",
    fontFamily: "Inter, system-ui, sans-serif",
    spacingUnit: "4px",
    borderRadius: "6px",
  },
  rules: {
    ".Input": {
      border: "1px solid #e6e6e6",
      borderRadius: "6px",
      padding: "12px",
    },
    ".Input:focus": {
      border: "1px solid #0570de",
      boxShadow: "0 0 0 2px rgba(5, 112, 222, 0.1)",
    },
    ".Label": {
      fontWeight: "500",
      marginBottom: "8px",
    },
  },
};

// Payment Element options
export const paymentElementOptions: Stripe.PaymentElementOptions = {
  layout: "tabs",
  paymentMethodOrder: ["card", "apple_pay", "google_pay"],
  fields: {
    billingDetails: {
      name: "auto",
      email: "auto",
      phone: "auto",
      address: {
        country: "auto",
        line1: "auto",
        line2: "auto",
        city: "auto",
        state: "auto",
        postalCode: "auto",
      },
    },
  },
};

// Address Element options
export const addressElementOptions: Stripe.AddressElementOptions = {
  mode: "billing",
  allowedCountries: ["US", "CA", "GB", "AU", "DE", "FR", "ES", "IT", "NL"],
  fields: {
    phone: "always",
  },
  validation: {
    phone: {
      required: "never",
    },
  },
};

// Utility functions for client-side operations
export const formatAmountForDisplay = (amount: number, currency: string): string => {
  const numberFormat = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency.toUpperCase(),
    currencyDisplay: "symbol",
  });
  
  return numberFormat.format(amount / 100);
};

export const formatAmountFromCents = (amount: number): number => {
  return Math.round(amount / 100 * 100) / 100;
};

export const formatAmountToCents = (amount: number): number => {
  return Math.round(amount * 100);
};

// Payment method type helpers
export const getPaymentMethodIcon = (type: string): string => {
  const icons: Record<string, string> = {
    card: "💳",
    apple_pay: "🍎",
    google_pay: "🔍",
    paypal: "💙",
    bank_transfer: "🏦",
    sepa_debit: "🏛️",
  };
  
  return icons[type] || "💳";
};

export const getPaymentMethodLabel = (type: string): string => {
  const labels: Record<string, string> = {
    card: "Credit Card",
    apple_pay: "Apple Pay",
    google_pay: "Google Pay",
    paypal: "PayPal",
    bank_transfer: "Bank Transfer",
    sepa_debit: "SEPA Direct Debit",
  };
  
  return labels[type] || "Payment Method";
};

// Error handling utilities
export const isStripeError = (error: any): error is Stripe.StripeError => {
  return error && typeof error === "object" && "type" in error;
};

export const getStripeErrorMessage = (error: Stripe.StripeError): string => {
  switch (error.code) {
    case "card_declined":
      return "Your card was declined. Please try a different payment method.";
    case "expired_card":
      return "Your card has expired. Please use a different card.";
    case "incorrect_cvc":
      return "Your card's security code is incorrect.";
    case "processing_error":
      return "An error occurred while processing your card. Please try again.";
    case "rate_limit":
      return "Too many requests. Please try again in a moment.";
    default:
      return error.message || "An unexpected error occurred.";
  }
};
