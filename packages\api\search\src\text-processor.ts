import natural from "natural";
import stopword from "stopword";
import compromise from "compromise";
import sentiment from "sentiment";
import keywordExtractor from "keyword-extractor";
import { 
  TextProcessor, 
  KeywordExtractionOptions, 
  SentimentAnalysis, 
  EntityExtraction, 
  SummarizationOptions, 
  LanguageDetection, 
  TokenizationOptions 
} from "./types";

export class NaturalTextProcessor implements TextProcessor {
  private stemmer: any;
  private sentimentAnalyzer: any;

  constructor() {
    this.stemmer = natural.PorterStemmer;
    this.sentimentAnalyzer = new sentiment();
  }

  // Extract keywords from text
  extractKeywords(text: string, options: KeywordExtractionOptions = {}): string[] {
    const {
      maxKeywords = 10,
      minLength = 3,
      removeStopwords = true,
      language = "en",
    } = options;

    try {
      // Use keyword-extractor library
      const keywords = keywordExtractor.extract(text, {
        language,
        remove_digits: true,
        return_changed_case: true,
        remove_duplicates: true,
      });

      // Filter by length
      const filteredKeywords = keywords.filter(keyword => 
        keyword.length >= minLength
      );

      // Remove stopwords if requested
      const finalKeywords = removeStopwords 
        ? stopword.removeStopwords(filteredKeywords, stopword[language as keyof typeof stopword] || stopword.en)
        : filteredKeywords;

      // Return top keywords
      return finalKeywords.slice(0, maxKeywords);
    } catch (error) {
      console.error("Keyword extraction failed:", error);
      return [];
    }
  }

  // Analyze sentiment of text
  analyzeSentiment(text: string): SentimentAnalysis {
    try {
      const result = this.sentimentAnalyzer.analyze(text);
      
      return {
        score: result.score,
        comparative: result.comparative,
        calculation: result.calculation.map((calc: any) => ({
          word: calc,
          score: result.score, // Simplified - would need more detailed analysis
        })),
        tokens: result.tokens,
        words: result.words,
        positive: result.positive,
        negative: result.negative,
      };
    } catch (error) {
      console.error("Sentiment analysis failed:", error);
      return {
        score: 0,
        comparative: 0,
        calculation: [],
        tokens: [],
        words: [],
        positive: [],
        negative: [],
      };
    }
  }

  // Extract entities from text
  extractEntities(text: string): EntityExtraction[] {
    try {
      const doc = compromise(text);
      const entities: EntityExtraction[] = [];

      // Extract people
      const people = doc.people().out("array");
      people.forEach((person: string) => {
        const match = text.indexOf(person);
        if (match !== -1) {
          entities.push({
            text: person,
            type: "person",
            confidence: 0.8,
            startOffset: match,
            endOffset: match + person.length,
          });
        }
      });

      // Extract places
      const places = doc.places().out("array");
      places.forEach((place: string) => {
        const match = text.indexOf(place);
        if (match !== -1) {
          entities.push({
            text: place,
            type: "location",
            confidence: 0.7,
            startOffset: match,
            endOffset: match + place.length,
          });
        }
      });

      // Extract organizations
      const organizations = doc.organizations().out("array");
      organizations.forEach((org: string) => {
        const match = text.indexOf(org);
        if (match !== -1) {
          entities.push({
            text: org,
            type: "organization",
            confidence: 0.7,
            startOffset: match,
            endOffset: match + org.length,
          });
        }
      });

      // Extract dates
      const dates = doc.dates().out("array");
      dates.forEach((date: string) => {
        const match = text.indexOf(date);
        if (match !== -1) {
          entities.push({
            text: date,
            type: "date",
            confidence: 0.9,
            startOffset: match,
            endOffset: match + date.length,
          });
        }
      });

      // Extract money
      const money = doc.money().out("array");
      money.forEach((amount: string) => {
        const match = text.indexOf(amount);
        if (match !== -1) {
          entities.push({
            text: amount,
            type: "money",
            confidence: 0.9,
            startOffset: match,
            endOffset: match + amount.length,
          });
        }
      });

      return entities.sort((a, b) => a.startOffset - b.startOffset);
    } catch (error) {
      console.error("Entity extraction failed:", error);
      return [];
    }
  }

  // Summarize text
  summarize(text: string, options: SummarizationOptions = {}): string {
    const {
      maxSentences = 3,
      maxLength = 200,
      algorithm = "frequency",
    } = options;

    try {
      const sentences = this.splitIntoSentences(text);
      
      if (sentences.length <= maxSentences) {
        return text;
      }

      let selectedSentences: string[] = [];

      switch (algorithm) {
        case "frequency":
          selectedSentences = this.frequencyBasedSummarization(sentences, maxSentences);
          break;
        case "position":
          selectedSentences = this.positionBasedSummarization(sentences, maxSentences);
          break;
        case "textrank":
          selectedSentences = this.textRankSummarization(sentences, maxSentences);
          break;
        default:
          selectedSentences = sentences.slice(0, maxSentences);
      }

      let summary = selectedSentences.join(" ");
      
      // Truncate if too long
      if (summary.length > maxLength) {
        summary = summary.substring(0, maxLength - 3) + "...";
      }

      return summary;
    } catch (error) {
      console.error("Summarization failed:", error);
      return text.substring(0, maxLength);
    }
  }

  // Detect language
  detectLanguage(text: string): LanguageDetection {
    try {
      // Simple language detection based on common words
      // In a real implementation, you'd use a proper language detection library
      const languages = {
        en: ["the", "and", "is", "in", "to", "of", "a", "that", "it", "with"],
        es: ["el", "la", "de", "que", "y", "a", "en", "un", "es", "se"],
        fr: ["le", "de", "et", "à", "un", "il", "être", "et", "en", "avoir"],
        de: ["der", "die", "und", "in", "den", "von", "zu", "das", "mit", "sich"],
      };

      const words = text.toLowerCase().split(/\s+/).slice(0, 50);
      const scores: Record<string, number> = {};

      Object.entries(languages).forEach(([lang, commonWords]) => {
        scores[lang] = words.filter(word => commonWords.includes(word)).length;
      });

      const detectedLang = Object.entries(scores).reduce((a, b) => 
        scores[a[0]] > scores[b[0]] ? a : b
      )[0];

      const confidence = scores[detectedLang] / words.length;

      return {
        language: detectedLang,
        confidence,
        alternatives: Object.entries(scores)
          .filter(([lang]) => lang !== detectedLang)
          .map(([lang, score]) => ({
            language: lang,
            confidence: score / words.length,
          }))
          .sort((a, b) => b.confidence - a.confidence)
          .slice(0, 3),
      };
    } catch (error) {
      console.error("Language detection failed:", error);
      return {
        language: "en",
        confidence: 0.5,
        alternatives: [],
      };
    }
  }

  // Tokenize text
  tokenize(text: string, options: TokenizationOptions = {}): string[] {
    const {
      removeStopwords = false,
      stemming = false,
      lowercase = true,
      removePunctuation = true,
      language = "en",
    } = options;

    try {
      let tokens = natural.WordTokenizer.tokenize(text) || [];

      // Convert to lowercase
      if (lowercase) {
        tokens = tokens.map(token => token.toLowerCase());
      }

      // Remove punctuation
      if (removePunctuation) {
        tokens = tokens.filter(token => /^[a-zA-Z0-9]+$/.test(token));
      }

      // Remove stopwords
      if (removeStopwords) {
        tokens = stopword.removeStopwords(tokens, stopword[language as keyof typeof stopword] || stopword.en);
      }

      // Apply stemming
      if (stemming) {
        tokens = tokens.map(token => this.stemmer.stem(token));
      }

      return tokens;
    } catch (error) {
      console.error("Tokenization failed:", error);
      return [];
    }
  }

  // Helper methods
  private splitIntoSentences(text: string): string[] {
    return text.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
  }

  private frequencyBasedSummarization(sentences: string[], maxSentences: number): string[] {
    // Calculate word frequencies
    const wordFreq: Record<string, number> = {};
    const allWords = sentences.join(" ").toLowerCase().split(/\s+/);
    
    allWords.forEach(word => {
      if (word.length > 3) {
        wordFreq[word] = (wordFreq[word] || 0) + 1;
      }
    });

    // Score sentences based on word frequencies
    const sentenceScores = sentences.map(sentence => {
      const words = sentence.toLowerCase().split(/\s+/);
      const score = words.reduce((sum, word) => sum + (wordFreq[word] || 0), 0);
      return { sentence, score: score / words.length };
    });

    // Return top sentences
    return sentenceScores
      .sort((a, b) => b.score - a.score)
      .slice(0, maxSentences)
      .map(item => item.sentence);
  }

  private positionBasedSummarization(sentences: string[], maxSentences: number): string[] {
    // Prefer sentences from the beginning and end
    const scores = sentences.map((sentence, index) => {
      let score = 0;
      
      // Higher score for beginning sentences
      if (index < sentences.length * 0.3) {
        score += 2;
      }
      
      // Higher score for ending sentences
      if (index > sentences.length * 0.7) {
        score += 1;
      }
      
      return { sentence, score, index };
    });

    return scores
      .sort((a, b) => b.score - a.score || a.index - b.index)
      .slice(0, maxSentences)
      .sort((a, b) => a.index - b.index)
      .map(item => item.sentence);
  }

  private textRankSummarization(sentences: string[], maxSentences: number): string[] {
    // Simplified TextRank implementation
    // In a real implementation, you'd use a proper graph-based algorithm
    
    // Calculate similarity matrix
    const similarity: number[][] = [];
    
    for (let i = 0; i < sentences.length; i++) {
      similarity[i] = [];
      for (let j = 0; j < sentences.length; j++) {
        if (i === j) {
          similarity[i][j] = 0;
        } else {
          similarity[i][j] = this.calculateSentenceSimilarity(sentences[i], sentences[j]);
        }
      }
    }

    // Calculate PageRank scores (simplified)
    const scores = sentences.map((_, index) => {
      const score = similarity[index].reduce((sum, sim) => sum + sim, 0);
      return { sentence: sentences[index], score, index };
    });

    return scores
      .sort((a, b) => b.score - a.score)
      .slice(0, maxSentences)
      .sort((a, b) => a.index - b.index)
      .map(item => item.sentence);
  }

  private calculateSentenceSimilarity(sentence1: string, sentence2: string): number {
    const words1 = new Set(sentence1.toLowerCase().split(/\s+/));
    const words2 = new Set(sentence2.toLowerCase().split(/\s+/));
    
    const intersection = new Set([...words1].filter(word => words2.has(word)));
    const union = new Set([...words1, ...words2]);
    
    return intersection.size / union.size;
  }
}
