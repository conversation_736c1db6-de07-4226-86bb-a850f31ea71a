import nodemailer from "nodemailer";
import { EmailConfig, Notification, NotificationTemplate } from "../types";

export interface EmailProvider {
  send(notification: Notification, template: NotificationTemplate, variables: Record<string, any>): Promise<void>;
  verify(): Promise<boolean>;
}

export class SMTPEmailProvider implements EmailProvider {
  private transporter: nodemailer.Transporter;
  private config: EmailConfig;

  constructor(config: EmailConfig) {
    this.config = config;
    
    if (!config.smtp) {
      throw new Error("SMTP configuration is required");
    }

    this.transporter = nodemailer.createTransporter({
      host: config.smtp.host,
      port: config.smtp.port,
      secure: config.smtp.secure,
      auth: config.smtp.auth,
      pool: true,
      maxConnections: 5,
      maxMessages: 100,
    });
  }

  async send(
    notification: Notification,
    template: NotificationTemplate,
    variables: Record<string, any>
  ): Promise<void> {
    try {
      const subject = this.replaceVariables(template.subject || template.content.text, variables);
      const text = this.replaceVariables(template.content.text, variables);
      const html = template.content.html 
        ? this.replaceVariables(template.content.html, variables)
        : undefined;

      const mailOptions = {
        from: `${this.config.from.name} <${this.config.from.email}>`,
        to: variables.email || variables.userEmail,
        subject,
        text,
        html,
        headers: {
          'X-Notification-ID': notification.id,
          'X-Tenant-ID': notification.tenantId,
          'X-Priority': this.getPriorityHeader(notification.priority),
        },
      };

      await this.transporter.sendMail(mailOptions);
    } catch (error: any) {
      throw new Error(`Failed to send email: ${error.message}`);
    }
  }

  async verify(): Promise<boolean> {
    try {
      await this.transporter.verify();
      return true;
    } catch (error) {
      return false;
    }
  }

  private replaceVariables(content: string, variables: Record<string, any>): string {
    let result = content;
    
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      result = result.replace(regex, String(value));
    }
    
    return result;
  }

  private getPriorityHeader(priority: string): string {
    switch (priority) {
      case "urgent":
        return "1 (Highest)";
      case "high":
        return "2 (High)";
      case "normal":
        return "3 (Normal)";
      case "low":
        return "4 (Low)";
      default:
        return "3 (Normal)";
    }
  }
}

export class SendGridEmailProvider implements EmailProvider {
  private apiKey: string;
  private config: EmailConfig;

  constructor(config: EmailConfig) {
    this.config = config;
    
    if (!config.sendgrid?.apiKey) {
      throw new Error("SendGrid API key is required");
    }

    this.apiKey = config.sendgrid.apiKey;
  }

  async send(
    notification: Notification,
    template: NotificationTemplate,
    variables: Record<string, any>
  ): Promise<void> {
    try {
      const sgMail = require('@sendgrid/mail');
      sgMail.setApiKey(this.apiKey);

      const subject = this.replaceVariables(template.subject || template.content.text, variables);
      const text = this.replaceVariables(template.content.text, variables);
      const html = template.content.html 
        ? this.replaceVariables(template.content.html, variables)
        : undefined;

      const msg = {
        to: variables.email || variables.userEmail,
        from: {
          name: this.config.from.name,
          email: this.config.from.email,
        },
        subject,
        text,
        html,
        customArgs: {
          notificationId: notification.id,
          tenantId: notification.tenantId,
          userId: notification.userId,
        },
        categories: [
          notification.type,
          `priority-${notification.priority}`,
        ],
      };

      await sgMail.send(msg);
    } catch (error: any) {
      throw new Error(`Failed to send email via SendGrid: ${error.message}`);
    }
  }

  async verify(): Promise<boolean> {
    try {
      const sgMail = require('@sendgrid/mail');
      sgMail.setApiKey(this.apiKey);
      
      // Test API key by making a simple request
      await sgMail.send({
        to: this.config.from.email,
        from: this.config.from.email,
        subject: 'Test',
        text: 'Test',
      }, false); // Don't actually send
      
      return true;
    } catch (error) {
      return false;
    }
  }

  private replaceVariables(content: string, variables: Record<string, any>): string {
    let result = content;
    
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      result = result.replace(regex, String(value));
    }
    
    return result;
  }
}

export class MailgunEmailProvider implements EmailProvider {
  private apiKey: string;
  private domain: string;
  private config: EmailConfig;

  constructor(config: EmailConfig) {
    this.config = config;
    
    if (!config.mailgun?.apiKey || !config.mailgun?.domain) {
      throw new Error("Mailgun API key and domain are required");
    }

    this.apiKey = config.mailgun.apiKey;
    this.domain = config.mailgun.domain;
  }

  async send(
    notification: Notification,
    template: NotificationTemplate,
    variables: Record<string, any>
  ): Promise<void> {
    try {
      const mailgun = require('mailgun-js')({
        apiKey: this.apiKey,
        domain: this.domain,
      });

      const subject = this.replaceVariables(template.subject || template.content.text, variables);
      const text = this.replaceVariables(template.content.text, variables);
      const html = template.content.html 
        ? this.replaceVariables(template.content.html, variables)
        : undefined;

      const data = {
        from: `${this.config.from.name} <${this.config.from.email}>`,
        to: variables.email || variables.userEmail,
        subject,
        text,
        html,
        'o:tag': [notification.type, `priority-${notification.priority}`],
        'v:notificationId': notification.id,
        'v:tenantId': notification.tenantId,
        'v:userId': notification.userId,
      };

      await mailgun.messages().send(data);
    } catch (error: any) {
      throw new Error(`Failed to send email via Mailgun: ${error.message}`);
    }
  }

  async verify(): Promise<boolean> {
    try {
      const mailgun = require('mailgun-js')({
        apiKey: this.apiKey,
        domain: this.domain,
      });
      
      // Test by validating the domain
      await mailgun.domains(this.domain).info();
      return true;
    } catch (error) {
      return false;
    }
  }

  private replaceVariables(content: string, variables: Record<string, any>): string {
    let result = content;
    
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      result = result.replace(regex, String(value));
    }
    
    return result;
  }
}

// Factory function to create email provider
export function createEmailProvider(config: EmailConfig): EmailProvider {
  switch (config.provider) {
    case "smtp":
      return new SMTPEmailProvider(config);
    case "sendgrid":
      return new SendGridEmailProvider(config);
    case "mailgun":
      return new MailgunEmailProvider(config);
    case "ses":
      // TODO: Implement AWS SES provider
      throw new Error("AWS SES provider not implemented yet");
    default:
      throw new Error(`Unsupported email provider: ${config.provider}`);
  }
}
