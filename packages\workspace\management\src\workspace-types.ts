// Workspace management types

export interface WorkspaceSettings {
  theme: "light" | "dark" | "auto";
  notifications: {
    email: boolean;
    push: boolean;
    slack: boolean;
  };
  features: {
    analytics: boolean;
    api: boolean;
    integrations: boolean;
    customDomain: boolean;
  };
  limits: {
    members: number;
    projects: number;
    storage: number;
  };
}

export interface WorkspaceBranding {
  logo?: string;
  primaryColor: string;
  secondaryColor: string;
  favicon?: string;
  customCSS?: string;
}

export interface WorkspaceData {
  id: string;
  tenantId: string;
  name: string;
  slug: string;
  description?: string;
  visibility: "public" | "private" | "restricted";
  status: "active" | "archived" | "suspended";
  settings: WorkspaceSettings;
  branding: WorkspaceBranding;
  color?: string;
  icon?: string;
  parentId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkspaceMember {
  id: string;
  workspaceId: string;
  userId: string;
  role: "owner" | "admin" | "member" | "viewer" | "guest";
  permissions: string[];
  joinedAt: Date;
  lastActiveAt: Date;
  invitedBy?: string;
}

export interface WorkspaceInvitation {
  id: string;
  workspaceId: string;
  email: string;
  role: "admin" | "member" | "viewer" | "guest";
  permissions: string[];
  status: "pending" | "accepted" | "declined" | "expired";
  token: string;
  expiresAt: Date;
  invitedBy: string;
  createdAt: Date;
  acceptedAt?: Date;
}

export interface CreateWorkspaceData {
  name: string;
  slug: string;
  description?: string;
  visibility?: "public" | "private" | "restricted";
  settings?: Partial<WorkspaceSettings>;
  branding?: Partial<WorkspaceBranding>;
  color?: string;
  icon?: string;
  parentId?: string;
}

export interface UpdateWorkspaceData {
  name?: string;
  slug?: string;
  description?: string;
  visibility?: "public" | "private" | "restricted";
  settings?: Partial<WorkspaceSettings>;
  branding?: Partial<WorkspaceBranding>;
  color?: string;
  icon?: string;
}

export interface InviteUserData {
  email: string;
  role: "admin" | "member" | "viewer" | "guest";
  permissions?: string[];
  message?: string;
}

export interface WorkspaceActivity {
  id: string;
  workspaceId: string;
  userId: string;
  action: string;
  details: Record<string, any>;
  metadata: Record<string, any>;
  createdAt: Date;
}

export interface WorkspaceStats {
  memberCount: number;
  projectCount: number;
  storageUsed: number;
  apiCallsThisMonth: number;
  lastActivity: Date;
}

export interface WorkspaceTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  settings: WorkspaceSettings;
  branding: WorkspaceBranding;
  features: string[];
  isPublic: boolean;
}
