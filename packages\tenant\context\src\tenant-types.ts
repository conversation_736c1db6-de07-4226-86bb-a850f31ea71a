// Tenant context types

export interface TenantSettings {
  theme: "light" | "dark" | "auto";
  branding: {
    logo?: string;
    primaryColor: string;
    secondaryColor: string;
  };
  features: {
    analytics: boolean;
    api: boolean;
    customDomain: boolean;
    sso: boolean;
  };
  limits: {
    users: number;
    storage: number;
    apiCalls: number;
  };
}

export interface TenantData {
  id: string;
  name: string;
  slug: string;
  domain?: string;
  plan: "STARTER" | "PROFESSIONAL" | "ENTERPRISE";
  status: "ACTIVE" | "INACTIVE" | "SUSPENDED";
  settings: TenantSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface TenantUser {
  id: string;
  tenantId: string;
  userId: string;
  role: "OWNER" | "ADMIN" | "MEMBER" | "VIEWER";
  permissions: string[];
  joinedAt: Date;
  lastActiveAt: Date;
}

export interface TenantContextState {
  tenant: TenantData | null;
  user: TenantUser | null;
  isLoading: boolean;
  error: string | null;
  permissions: string[];
}

export interface TenantContextActions {
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  switchTenant: (tenantId: string) => Promise<void>;
  updateTenant: (updates: Partial<TenantData>) => Promise<void>;
  refreshTenant: () => Promise<void>;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export interface TenantContext extends TenantContextState, TenantContextActions {}

export type TenantAction =
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_TENANT"; payload: TenantData | null }
  | { type: "SET_USER"; payload: TenantUser | null }
  | { type: "SET_ERROR"; payload: string | null }
  | { type: "SET_PERMISSIONS"; payload: string[] }
  | { type: "RESET" };

export interface TenantResolution {
  method: "domain" | "subdomain" | "path" | "header";
  value: string;
  tenantId?: string;
}
