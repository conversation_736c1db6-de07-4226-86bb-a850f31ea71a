{"name": "@nexus/tenant-context", "version": "0.1.0", "description": "Tenant context management for Nexus SaaS", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "@nexus/validation": "workspace:*", "@nexus/database-service": "workspace:*", "@nexus/auth-client": "workspace:*", "react": "19.1.0", "zustand": "5.0.6"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@types/react": "19.1.8", "typescript": "5.8.3"}}