import jwt from "jsonwebtoken";
import { Socket } from "socket.io";
import { User } from "@nexus/types";
import { RealtimeError } from "./types";

export interface JWTPayload {
  sub: string; // user ID
  email: string;
  name: string;
  tenantId: string;
  roles: string[];
  permissions: string[];
  iat: number;
  exp: number;
}

export class AuthService {
  private jwtSecret: string;

  constructor(jwtSecret: string) {
    this.jwtSecret = jwtSecret;
  }

  // Verify JWT token
  verifyToken(token: string): JWTPayload {
    try {
      const payload = jwt.verify(token, this.jwtSecret) as JWTPayload;
      
      // Check if token is expired
      if (payload.exp && Date.now() >= payload.exp * 1000) {
        throw new Error("Token expired");
      }

      return payload;
    } catch (error: any) {
      throw new RealtimeError({
        code: "INVALID_TOKEN",
        message: "Invalid or expired token",
        details: error.message,
      });
    }
  }

  // Extract token from socket handshake
  extractToken(socket: Socket): string | null {
    // Try Authorization header
    const authHeader = socket.handshake.headers.authorization;
    if (authHeader && authHeader.startsWith("Bearer ")) {
      return authHeader.substring(7);
    }

    // Try query parameter
    const tokenQuery = socket.handshake.query.token;
    if (typeof tokenQuery === "string") {
      return tokenQuery;
    }

    // Try auth object
    const authToken = socket.handshake.auth?.token;
    if (typeof authToken === "string") {
      return authToken;
    }

    return null;
  }

  // Authenticate socket connection
  async authenticateSocket(socket: Socket): Promise<User> {
    const token = this.extractToken(socket);
    
    if (!token) {
      throw new RealtimeError({
        code: "NO_TOKEN",
        message: "Authentication token required",
      });
    }

    try {
      const payload = this.verifyToken(token);
      
      // Convert JWT payload to User object
      const user: User = {
        id: payload.sub,
        email: payload.email,
        name: payload.name,
        tenantId: payload.tenantId,
        roles: payload.roles,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return user;
    } catch (error: any) {
      throw new RealtimeError({
        code: "AUTH_FAILED",
        message: "Authentication failed",
        details: error.message,
      });
    }
  }

  // Check if user has permission
  hasPermission(user: User, permission: string): boolean {
    // System admin has all permissions
    if (user.roles.includes("system_admin")) {
      return true;
    }

    // Check specific permission
    // This would typically check against a permission system
    // For now, we'll use basic role-based checks
    
    const rolePermissions: Record<string, string[]> = {
      admin: ["*"],
      manager: [
        "workspace:read",
        "workspace:write",
        "team:read",
        "team:write",
        "project:read",
        "project:write",
        "chat:read",
        "chat:write",
        "presence:read",
        "collaboration:read",
        "collaboration:write",
      ],
      member: [
        "workspace:read",
        "team:read",
        "project:read",
        "project:write",
        "chat:read",
        "chat:write",
        "presence:read",
        "collaboration:read",
        "collaboration:write",
      ],
      viewer: [
        "workspace:read",
        "team:read",
        "project:read",
        "chat:read",
        "presence:read",
        "collaboration:read",
      ],
    };

    for (const role of user.roles) {
      const permissions = rolePermissions[role] || [];
      if (permissions.includes("*") || permissions.includes(permission)) {
        return true;
      }
    }

    return false;
  }

  // Check if user can access resource
  canAccessResource(
    user: User,
    resourceType: string,
    resourceId: string,
    action: string = "read"
  ): boolean {
    // This would typically check against database/cache
    // For now, we'll use basic tenant-based access control
    
    const permission = `${resourceType}:${action}`;
    return this.hasPermission(user, permission);
  }

  // Generate room access token
  generateRoomToken(user: User, roomId: string, permissions: string[]): string {
    const payload = {
      sub: user.id,
      roomId,
      permissions,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour
    };

    return jwt.sign(payload, this.jwtSecret);
  }

  // Verify room access token
  verifyRoomToken(token: string): { userId: string; roomId: string; permissions: string[] } {
    try {
      const payload = jwt.verify(token, this.jwtSecret) as any;
      
      return {
        userId: payload.sub,
        roomId: payload.roomId,
        permissions: payload.permissions || [],
      };
    } catch (error: any) {
      throw new RealtimeError({
        code: "INVALID_ROOM_TOKEN",
        message: "Invalid room access token",
        details: error.message,
      });
    }
  }
}

// Rate limiting for authentication
export class AuthRateLimiter {
  private attempts: Map<string, { count: number; resetTime: number }> = new Map();
  private maxAttempts: number;
  private windowMs: number;

  constructor(maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000) {
    this.maxAttempts = maxAttempts;
    this.windowMs = windowMs;
  }

  // Check if IP is rate limited
  isRateLimited(ip: string): boolean {
    const now = Date.now();
    const attempt = this.attempts.get(ip);

    if (!attempt) {
      return false;
    }

    // Reset if window expired
    if (now > attempt.resetTime) {
      this.attempts.delete(ip);
      return false;
    }

    return attempt.count >= this.maxAttempts;
  }

  // Record failed attempt
  recordFailedAttempt(ip: string): void {
    const now = Date.now();
    const attempt = this.attempts.get(ip);

    if (!attempt || now > attempt.resetTime) {
      this.attempts.set(ip, {
        count: 1,
        resetTime: now + this.windowMs,
      });
    } else {
      attempt.count++;
    }
  }

  // Clear attempts for IP
  clearAttempts(ip: string): void {
    this.attempts.delete(ip);
  }

  // Cleanup expired entries
  cleanup(): void {
    const now = Date.now();
    for (const [ip, attempt] of this.attempts.entries()) {
      if (now > attempt.resetTime) {
        this.attempts.delete(ip);
      }
    }
  }
}
