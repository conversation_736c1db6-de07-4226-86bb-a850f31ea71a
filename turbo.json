{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"], "env": ["NODE_ENV", "NEXTAUTH_SECRET"]}, "dev": {"cache": false, "persistent": true}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.tsx", "src/**/*.ts", "test/**/*.ts", "test/**/*.tsx"]}, "lint": {"dependsOn": ["^build"], "outputs": []}, "type-check": {"dependsOn": ["^build"], "outputs": []}, "clean": {"cache": false}}, "remoteCache": {"enabled": true}}