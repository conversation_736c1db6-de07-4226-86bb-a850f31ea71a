import { z } from "zod";
import { VALIDATION_RULES } from "@nexus/constants";

// User validation schemas
export const userSchema = z.object({
  id: z.string().cuid2(),
  email: z
    .string()
    .email()
    .min(VALIDATION_RULES.email.minLength)
    .max(VALIDATION_RULES.email.maxLength),
  name: z
    .string()
    .min(VALIDATION_RULES.name.minLength)
    .max(VALIDATION_RULES.name.maxLength)
    .regex(VALIDATION_RULES.name.pattern)
    .nullable(),
  avatar: z.string().url().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const createUserSchema = userSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const updateUserSchema = createUserSchema.partial();

// Authentication validation schemas
export const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(1),
  remember: z.boolean().optional(),
});

export const registerSchema = z.object({
  email: z.string().email(),
  password: z
    .string()
    .min(VALIDATION_RULES.password.minLength)
    .max(VALIDATION_RULES.password.maxLength),
  name: z
    .string()
    .min(VALIDATION_RULES.name.minLength)
    .max(VALIDATION_RULES.name.maxLength),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Tenant validation schemas
export const tenantSchema = z.object({
  id: z.string().cuid2(),
  name: z
    .string()
    .min(VALIDATION_RULES.name.minLength)
    .max(VALIDATION_RULES.name.maxLength),
  slug: z
    .string()
    .min(VALIDATION_RULES.slug.minLength)
    .max(VALIDATION_RULES.slug.maxLength)
    .regex(VALIDATION_RULES.slug.pattern),
  domain: z.string().url().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const createTenantSchema = tenantSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Workspace validation schemas
export const workspaceSchema = z.object({
  id: z.string().cuid2(),
  name: z
    .string()
    .min(VALIDATION_RULES.name.minLength)
    .max(VALIDATION_RULES.name.maxLength),
  slug: z
    .string()
    .min(VALIDATION_RULES.slug.minLength)
    .max(VALIDATION_RULES.slug.maxLength)
    .regex(VALIDATION_RULES.slug.pattern),
  tenantId: z.string().cuid2(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const createWorkspaceSchema = workspaceSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// API response validation schemas
export const apiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  message: z.string().optional(),
});

export const paginatedResponseSchema = z.object({
  data: z.array(z.any()),
  pagination: z.object({
    page: z.number().int().positive(),
    limit: z.number().int().positive(),
    total: z.number().int().nonnegative(),
    totalPages: z.number().int().nonnegative(),
  }),
});

// Common validation utilities
export const validateEmail = (email: string): boolean => {
  return VALIDATION_RULES.email.pattern.test(email);
};

export const validatePassword = (password: string): boolean => {
  const rules = VALIDATION_RULES.password;
  
  if (password.length < rules.minLength || password.length > rules.maxLength) {
    return false;
  }
  
  if (rules.requireUppercase && !/[A-Z]/.test(password)) {
    return false;
  }
  
  if (rules.requireLowercase && !/[a-z]/.test(password)) {
    return false;
  }
  
  if (rules.requireNumbers && !/\d/.test(password)) {
    return false;
  }
  
  if (rules.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    return false;
  }
  
  return true;
};

export const validateSlug = (slug: string): boolean => {
  return VALIDATION_RULES.slug.pattern.test(slug);
};

// Type exports
export type User = z.infer<typeof userSchema>;
export type CreateUser = z.infer<typeof createUserSchema>;
export type UpdateUser = z.infer<typeof updateUserSchema>;
export type Login = z.infer<typeof loginSchema>;
export type Register = z.infer<typeof registerSchema>;
export type Tenant = z.infer<typeof tenantSchema>;
export type CreateTenant = z.infer<typeof createTenantSchema>;
export type Workspace = z.infer<typeof workspaceSchema>;
export type CreateWorkspace = z.infer<typeof createWorkspaceSchema>;
export type ApiResponse<T = any> = z.infer<typeof apiResponseSchema> & { data?: T };
export type PaginatedResponse<T = any> = z.infer<typeof paginatedResponseSchema> & { data: T[] };
