import { User } from "@nexus/types";

// Notification types
export interface Notification {
  id: string;
  userId: string;
  tenantId: string;
  type: NotificationType;
  channel: NotificationChannel[];
  title: string;
  message: string;
  data?: Record<string, any>;
  actionUrl?: string;
  imageUrl?: string;
  priority: NotificationPriority;
  status: NotificationStatus;
  scheduledAt?: Date;
  sentAt?: Date;
  readAt?: Date;
  clickedAt?: Date;
  metadata?: {
    templateId?: string;
    campaignId?: string;
    source?: string;
    tags?: string[];
  };
  createdAt: Date;
  updatedAt: Date;
}

export type NotificationType = 
  | "welcome"
  | "mention"
  | "assignment"
  | "comment"
  | "invitation"
  | "reminder"
  | "system"
  | "security"
  | "billing"
  | "marketing"
  | "announcement";

export type NotificationChannel = 
  | "in_app"
  | "email"
  | "push"
  | "sms"
  | "webhook"
  | "slack"
  | "teams";

export type NotificationPriority = 
  | "low"
  | "normal"
  | "high"
  | "urgent";

export type NotificationStatus = 
  | "pending"
  | "scheduled"
  | "sent"
  | "delivered"
  | "read"
  | "clicked"
  | "failed"
  | "cancelled";

// Template types
export interface NotificationTemplate {
  id: string;
  name: string;
  type: NotificationType;
  channels: NotificationChannel[];
  subject?: string;
  content: {
    text: string;
    html?: string;
    push?: {
      title: string;
      body: string;
      icon?: string;
      badge?: string;
      image?: string;
      actions?: Array<{
        action: string;
        title: string;
        icon?: string;
      }>;
    };
    sms?: string;
  };
  variables: string[];
  isActive: boolean;
  tenantId?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Preference types
export interface NotificationPreference {
  id: string;
  userId: string;
  tenantId: string;
  type: NotificationType;
  channels: NotificationChannel[];
  enabled: boolean;
  quietHours?: {
    enabled: boolean;
    start: string; // HH:mm format
    end: string;   // HH:mm format
    timezone: string;
  };
  frequency?: "immediate" | "hourly" | "daily" | "weekly";
  createdAt: Date;
  updatedAt: Date;
}

// Device types for push notifications
export interface NotificationDevice {
  id: string;
  userId: string;
  tenantId: string;
  type: "web" | "ios" | "android";
  token: string;
  endpoint?: string;
  keys?: {
    p256dh: string;
    auth: string;
  };
  userAgent?: string;
  isActive: boolean;
  lastUsed: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Subscription types
export interface NotificationSubscription {
  id: string;
  userId: string;
  tenantId: string;
  resourceType: "workspace" | "team" | "project" | "user";
  resourceId: string;
  types: NotificationType[];
  channels: NotificationChannel[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Campaign types
export interface NotificationCampaign {
  id: string;
  name: string;
  description?: string;
  tenantId?: string;
  templateId: string;
  audience: {
    type: "all" | "segment" | "users";
    userIds?: string[];
    segmentId?: string;
    filters?: Record<string, any>;
  };
  schedule: {
    type: "immediate" | "scheduled" | "recurring";
    scheduledAt?: Date;
    timezone?: string;
    recurring?: {
      frequency: "daily" | "weekly" | "monthly";
      interval: number;
      endDate?: Date;
    };
  };
  status: "draft" | "scheduled" | "running" | "completed" | "cancelled";
  stats: {
    totalRecipients: number;
    sent: number;
    delivered: number;
    read: number;
    clicked: number;
    failed: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

// Provider configurations
export interface EmailConfig {
  provider: "smtp" | "sendgrid" | "mailgun" | "ses";
  smtp?: {
    host: string;
    port: number;
    secure: boolean;
    auth: {
      user: string;
      pass: string;
    };
  };
  sendgrid?: {
    apiKey: string;
  };
  mailgun?: {
    apiKey: string;
    domain: string;
  };
  ses?: {
    region: string;
    accessKeyId: string;
    secretAccessKey: string;
  };
  from: {
    name: string;
    email: string;
  };
}

export interface SMSConfig {
  provider: "twilio" | "aws_sns";
  twilio?: {
    accountSid: string;
    authToken: string;
    from: string;
  };
  aws_sns?: {
    region: string;
    accessKeyId: string;
    secretAccessKey: string;
  };
}

export interface PushConfig {
  web?: {
    vapidPublicKey: string;
    vapidPrivateKey: string;
    subject: string;
  };
  firebase?: {
    projectId: string;
    privateKey: string;
    clientEmail: string;
  };
  apns?: {
    keyId: string;
    teamId: string;
    privateKey: string;
    production: boolean;
  };
}

export interface WebhookConfig {
  url: string;
  secret?: string;
  headers?: Record<string, string>;
  retries: number;
  timeout: number;
}

// Job types for queue processing
export interface NotificationJob {
  id: string;
  notificationId: string;
  userId: string;
  channel: NotificationChannel;
  data: any;
  attempts: number;
  maxAttempts: number;
  priority: number;
  delay?: number;
  createdAt: Date;
}

// Analytics types
export interface NotificationAnalytics {
  id: string;
  notificationId?: string;
  campaignId?: string;
  userId: string;
  tenantId: string;
  type: NotificationType;
  channel: NotificationChannel;
  event: "sent" | "delivered" | "read" | "clicked" | "failed";
  metadata?: Record<string, any>;
  timestamp: Date;
}

// Rate limiting
export interface RateLimit {
  userId: string;
  channel: NotificationChannel;
  count: number;
  windowStart: Date;
  windowEnd: Date;
}

// Service configuration
export interface NotificationConfig {
  port: number;
  redis: {
    host: string;
    port: number;
    password?: string;
    db?: number;
  };
  database: {
    url: string;
  };
  email: EmailConfig;
  sms: SMSConfig;
  push: PushConfig;
  webhook: WebhookConfig;
  rateLimit: {
    email: {
      perHour: number;
      perDay: number;
    };
    sms: {
      perHour: number;
      perDay: number;
    };
    push: {
      perHour: number;
      perDay: number;
    };
  };
  queue: {
    concurrency: number;
    retryAttempts: number;
    retryDelay: number;
  };
  templates: {
    directory: string;
    cache: boolean;
  };
}
