TITLE: Initialize Next.js Project with API Example
DESCRIPTION: This command uses `create-next-app` to scaffold a new Next.js project. The `--api` flag automatically includes an example `route.ts` file in the `app/` folder, demonstrating how to create an API endpoint for backend functionality.
SOURCE: https://nextjs.org/docs/app/guides/backend-for-frontend

LANGUAGE: Terminal
CODE:
```
npx create-next-app@latest --api
```

----------------------------------------

TITLE: Next.js App Router Route Handler Example
DESCRIPTION: Illustrates a basic Route Handler in the Next.js `app` directory, replacing traditional API Routes. This example shows a `GET` handler using Web `Request` API, demonstrating the new approach for handling API requests.
SOURCE: https://nextjs.org/docs/pages/guides/migrating/app-router-migration

LANGUAGE: TypeScript
CODE:
```
export async function GET(request: Request) {}
```

----------------------------------------

TITLE: Next.js 14 Command Line Interface (CLI) API Reference
DESCRIPTION: API documentation for Next.js 14 CLI commands, including create-next-app for project initialization and the general next command for development, build, and start tasks.
SOURCE: https://nextjs.org/docs/14/app/building-your-application/optimizing/scripts

LANGUAGE: APIDOC
CODE:
```
create-next-app: Initializes a new Next.js project from a template.
next CLI: Provides commands for development (dev), building (build), and starting (start) Next.js applications.
```

----------------------------------------

TITLE: Creating a Next.js App from an Official Example
DESCRIPTION: Illustrates how to bootstrap a Next.js application using an official example from the Next.js repository by specifying the `--example` flag along with the example name and project name.
SOURCE: https://nextjs.org/docs/app/api-reference/cli/create-next-app

LANGUAGE: bash
CODE:
```
npx create-next-app@latest --example [example-name] [your-project-name]
```

----------------------------------------

TITLE: Asset Import with URL Constructor Example
DESCRIPTION: Example showing how to import assets using the `URL` constructor and `import.meta.url` to get the resolved path.
SOURCE: https://nextjs.org/docs/14/app/api-reference/next-config-js/urlImports

LANGUAGE: JavaScript
CODE:
```
const logo = new URL('https://example.com/assets/file.txt', import.meta.url)

console.log(logo.pathname)

// prints "/_next/static/media/file.a9727b5d.txt"
```

----------------------------------------

TITLE: Create Next.js App from Official Example
DESCRIPTION: Command to initialize a new Next.js application using a specific official example from the Next.js repository, specifying the example name and project name.
SOURCE: https://nextjs.org/docs/pages/api-reference/cli/create-next-app

LANGUAGE: Terminal
CODE:
```
npx create-next-app@latest --example [example-name] [your-project-name]
```

----------------------------------------

TITLE: Next.js API Reference Overview
DESCRIPTION: This entry provides a structured index of the Next.js API documentation, detailing available directives, built-in components, file-system conventions used for routing and metadata, and a wide array of utility functions. It serves as a navigational guide to the Next.js API surface.
SOURCE: https://nextjs.org/docs/app/getting-started/metadata-and-og-images

LANGUAGE: APIDOC
CODE:
```
Directives:\n  - use server\nComponents:\n  - Font\n  - Form Component\n  - Image Component\n  - Link Component\n  - Script Component\nFile-system conventions:\n  - default.js\n  - Dynamic Segments\n  - error.js\n  - forbidden.js\n  - instrumentation.js\n  - instrumentation-client.js\n  - Intercepting Routes\n  - layout.js\n  - loading.js\n  - mdx-components.js\n  - middleware.js\n  - not-found.js\n  - page.js\n  - Parallel Routes\n  - public\n  - route.js\n  - Route Groups\n  - Route Segment Config\n  - src\n  - template.js\n  - unauthorized.js\n  - Metadata Files:\n    - favicon, icon, and apple-icon\n    - manifest.json\n    - opengraph-image and twitter-image\n    - robots.txt\n    - sitemap.xml\nFunctions:\n  - after\n  - cacheLife\n  - cacheTag\n  - connection\n  - cookies\n  - draftMode\n  - fetch\n  - forbidden\n  - generateImageMetadata\n  - generateMetadata\n  - generateSitemaps\n  - generateStaticParams\n  - generateViewport\n  - headers\n  - ImageResponse\n  - NextRequest\n  - NextResponse\n  - notFound\n  - permanentRedirect\n  - redirect\n  - revalidatePath\n  - revalidateTag\n  - unauthorized\n  - unstable_cache\n  - unstable_noStore\n  - unstable_rethrow\n  - useLinkStatus\n  - useParams\n  - usePathname\n  - useReportWebVitals\n  - useRouter\n  - useSearchParams
```

----------------------------------------

TITLE: Initialize Next.js Project from Public GitHub Example
DESCRIPTION: Use this command to create a new Next.js application based on an existing public GitHub repository. The `--example` option takes the URL of the GitHub repository, and `[your-project-name]` specifies the directory where the new project will be created.
SOURCE: https://nextjs.org/docs/pages/api-reference/cli/create-next-app

LANGUAGE: Shell
CODE:
```
npx create-next-app@latest --example "https://github.com/.../" [your-project-name]
```

----------------------------------------

TITLE: Cache a GET Route Handler with force-static in Next.js
DESCRIPTION: This example illustrates how to enable caching for a GET Route Handler by setting `export const dynamic = 'force-static'`. It fetches data from an external API and returns it as JSON, demonstrating how to make a static data fetch within a cached handler.
SOURCE: https://nextjs.org/docs/app/getting-started/route-handlers-and-middleware

LANGUAGE: TypeScript
CODE:
```
export const dynamic = 'force-static'

export async function GET() {
  const res = await fetch('https://data.mongodb-api.com/...', {
    headers: {
      'Content-Type': 'application/json',
      'API-Key': process.env.DATA_API_KEY
    }
  })
  const data = await res.json()

  return Response.json({ data })
}
```

LANGUAGE: JavaScript
CODE:
```
export const dynamic = 'force-static'

export async function GET() {
  const res = await fetch('https://data.mongodb-api.com/...', {
    headers: {
      'Content-Type': 'application/json',
      'API-Key': process.env.DATA_API_KEY
    }
  })
  const data = await res.json()

  return Response.json({ data })
}
```

----------------------------------------

TITLE: Basic Next.js API Route Handler Example
DESCRIPTION: This example demonstrates a fundamental Next.js API route handler that returns a JSON response. It illustrates how to import and use `NextApiRequest` and `NextApiResponse` types for strong typing and how to send a 200 OK status with a custom message.
SOURCE: https://nextjs.org/docs/14/pages/building-your-application/routing/api-routes

LANGUAGE: TypeScript
CODE:
```
import type { NextApiRequest, NextApiResponse } from 'next'

type ResponseData = {
  message: string
}

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<ResponseData>
) {
  res.status(200).json({ message: 'Hello from Next.js!' })
}
```

----------------------------------------

TITLE: Create Next.js App from Public GitHub Example
DESCRIPTION: This command allows users to create a Next.js application from any public GitHub repository that contains a Next.js example. The `--example` flag is used with the full URL of the GitHub repository.
SOURCE: https://nextjs.org/docs/14/pages/api-reference/cli/create-next-app

LANGUAGE: Shell
CODE:
```
npx create-next-app@latest --example [your-project-name] "https://github.com/.../"
```

----------------------------------------

TITLE: Next.js Docs File Structure for API Reference
DESCRIPTION: Example of file-system routing in Next.js documentation, showing an alphabetically sorted structure for API reference pages within the '03-functions' directory, making it easier to locate specific functions.
SOURCE: https://nextjs.org/docs/14/community/contribution-guide

LANGUAGE: Markdown
CODE:
```
03-functions
├── cookies.mdx
├── draft-mode.mdx
├── fetch.mdx
└── ...
```

----------------------------------------

TITLE: Next.js App API: useSelectedLayoutSegment()
DESCRIPTION: API hook to get the currently selected layout segment in Next.js 14 applications.
SOURCE: https://nextjs.org/docs/14/app/api-reference/file-conventions/route-segment-config

LANGUAGE: APIDOC
CODE:
```
useSelectedLayoutSegment(): string | null
```

----------------------------------------

TITLE: Next.js API Reference: Directives
DESCRIPTION: Documentation for Next.js directives, including 'use server' for marking server-side code.
SOURCE: https://nextjs.org/docs/pages/getting-started/project-structure

LANGUAGE: APIDOC
CODE:
```
use server
```

----------------------------------------

TITLE: Next.js App API: usePathname()
DESCRIPTION: API hook to get the current URL's pathname in Next.js 14 applications.
SOURCE: https://nextjs.org/docs/14/app/api-reference/file-conventions/route-segment-config

LANGUAGE: APIDOC
CODE:
```
usePathname(): string
```

----------------------------------------

TITLE: Next.js App API: useSelectedLayoutSegments()
DESCRIPTION: API hook to get all currently selected layout segments in Next.js 14 applications.
SOURCE: https://nextjs.org/docs/14/app/api-reference/file-conventions/route-segment-config

LANGUAGE: APIDOC
CODE:
```
useSelectedLayoutSegments(): string[]
```

----------------------------------------

TITLE: Next.js 14 Command Line Interface (CLI) API Reference
DESCRIPTION: Documentation for Next.js 14 command-line tools used for project creation and development server management.
SOURCE: https://nextjs.org/docs/14/pages/building-your-application/data-fetching/get-static-paths

LANGUAGE: APIDOC
CODE:
```
- create-next-app
- next CLI
```

----------------------------------------

TITLE: Basic GET Route Handler in Next.js
DESCRIPTION: Demonstrates a simple asynchronous GET request handler that returns a JSON response for a given route. This example shows the minimal setup for a Route Handler.
SOURCE: https://nextjs.org/docs/app/api-reference/file-conventions/route

LANGUAGE: TypeScript
CODE:
```
export async function GET() {
  return Response.json({ message: 'Hello World' })
}
```

----------------------------------------

TITLE: Next.js API Reference Overview
DESCRIPTION: A structured overview of the Next.js API, including directives, built-in components, file-system conventions for routing and metadata, and various utility functions.
SOURCE: https://nextjs.org/docs/app/guides/self-hosting

LANGUAGE: APIDOC
CODE:
```
API Reference:
  Directives:
    - use server
  Components:
    - Font
    - Form Component
    - Image Component
    - Link Component
    - Script Component
  File-system conventions:
    - default.js
    - Dynamic Segments
    - error.js
    - forbidden.js
    - instrumentation.js
    - instrumentation-client.js
    - Intercepting Routes
    - layout.js
    - loading.js
    - mdx-components.js
    - middleware.js
    - not-found.js
    - page.js
    - Parallel Routes
    - public
    - route.js
    - Route Groups
    - Route Segment Config
    - src
    - template.js
    - unauthorized.js
    - Metadata Files:
      - favicon, icon, and apple-icon
      - manifest.json
      - opengraph-image and twitter-image
      - robots.txt
      - sitemap.xml
  Functions:
    - after
    - cacheLife
    - cacheTag
    - connection
    - cookies
    - draftMode
    - fetch
    - forbidden
    - generateImageMetadata
    - generateMetadata
    - generateSitemaps
    - generateStaticParams
    - generateViewport
    - headers
    - ImageResponse
    - NextRequest
    - NextResponse
    - notFound
    - permanentRedirect
    - redirect
    - revalidatePath
    - revalidateTag
    - unauthorized
    - unstable_cache
    - unstable_noStore
    - unstable_rethrow
    - useLinkStatus
    - useParams
    - usePathname
    - useReportWebVitals
    - useRouter
    - useSearchParams
```

----------------------------------------

TITLE: Next.js 14 Application Building and Routing Concepts
DESCRIPTION: Covers fundamental concepts for building Next.js applications, with a focus on routing patterns, including pages, layouts, dynamic routes, navigation, and redirects.
SOURCE: https://nextjs.org/docs/14/getting-started

LANGUAGE: APIDOC
CODE:
```
Pages and Layouts
```

LANGUAGE: APIDOC
CODE:
```
Dynamic Routes
```

LANGUAGE: APIDOC
CODE:
```
Linking and Navigating
```

LANGUAGE: APIDOC
CODE:
```
Redirecting
```

----------------------------------------

TITLE: Quickstart: Create Next.js App with Vitest Example
DESCRIPTION: Use the `create-next-app` command with the `with-vitest` example to quickly scaffold a new Next.js project pre-configured for Vitest, allowing for rapid setup.
SOURCE: https://nextjs.org/docs/pages/guides/testing/vitest

LANGUAGE: Terminal
CODE:
```
npx create-next-app@latest --example with-vitest with-vitest-app
```

----------------------------------------

TITLE: Example Next.js Preview API Route URL
DESCRIPTION: Provides an example URL structure for accessing the Next.js preview API route. This URL includes `secret` for authentication and `slug` to specify the content path for preview.
SOURCE: https://nextjs.org/docs/pages/guides/preview-mode

LANGUAGE: APIDOC
CODE:
```
https://<your-site>/api/preview?secret=<token>&slug=<path>
```

----------------------------------------

TITLE: Next.js `fetch` API Introduction Version
DESCRIPTION: Documents the version in which the `fetch` API was introduced in Next.js.
SOURCE: https://nextjs.org/docs/app/api-reference/functions/fetch

LANGUAGE: APIDOC
CODE:
```
Version History:
  v13.0.0: `fetch` introduced.
```

----------------------------------------

TITLE: Run Next.js Development Server
DESCRIPTION: This command starts the Next.js development server, which will guide the user through installing necessary packages like Partytown for the web worker strategy.
SOURCE: https://nextjs.org/docs/14/app/building-your-application/optimizing/scripts

LANGUAGE: bash
CODE:
```
npm run dev
```

----------------------------------------

TITLE: Quickstart: Create Next.js app with Jest example
DESCRIPTION: Use `create-next-app` with the `with-jest` example to quickly scaffold a Next.js project that includes a pre-configured Jest setup.
SOURCE: https://nextjs.org/docs/pages/guides/testing/jest

LANGUAGE: Terminal
CODE:
```
npx create-next-app@latest --example with-jest with-jest-app
```

----------------------------------------

TITLE: Create Next.js App from Official Example
DESCRIPTION: Command to initialize a new Next.js application using a specific official example from the Next.js GitHub repository, specifying the project name and example name.
SOURCE: https://nextjs.org/docs/14/app/api-reference/cli/create-next-app

LANGUAGE: bash
CODE:
```
npx create-next-app@latest --example [your-project-name] [example-name]
```

----------------------------------------

TITLE: Next.js API Reference Index Overview
DESCRIPTION: This structured overview lists the main categories and individual API elements within the Next.js documentation, providing a quick reference to available directives, components, file-system conventions, and functions.
SOURCE: https://nextjs.org/docs/pages/guides/testing/vitest

LANGUAGE: APIDOC
CODE:
```
Next.js API Reference:
  Directives:
    - use server
  Components:
    - Font
    - Form Component
    - Image Component
    - Link Component
    - Script Component
  File-system conventions:
    - default.js
    - Dynamic Segments
    - error.js
    - forbidden.js
    - instrumentation.js
    - instrumentation-client.js
    - Intercepting Routes
    - layout.js
    - loading.js
    - mdx-components.js
    - middleware.js
    - not-found.js
    - page.js
    - Parallel Routes
    - public
    - route.js
    - Route Groups
    - Route Segment Config
    - src
    - template.js
    - unauthorized.js
    - Metadata Files:
      - favicon, icon, and apple-icon
      - manifest.json
      - opengraph-image and twitter-image
      - robots.txt
      - sitemap.xml
  Functions:
    - after
    - cacheLife
    - cacheTag
    - connection
    - cookies
    - draftMode
    - fetch
    - forbidden
    - generateImageMetadata
    - generateMetadata
    - generateSitemaps
    - generateStaticParams
    - generateViewport
    - headers
    - ImageResponse
    - NextRequest
    - NextResponse
    - notFound
    - permanentRedirect
    - redirect
    - revalidatePath
    - revalidateTag
    - unauthorized
    - unstable_cache
    - unstable_noStore
    - unstable_rethrow
    - useLinkStatus
    - useParams
    - usePathname
    - useReportWebVitals
    - useRouter
    - useSearchParams
```

----------------------------------------

TITLE: Quickstart: Initialize Next.js App with Playwright Example
DESCRIPTION: Use `create-next-app` with the `with-playwright` example to quickly scaffold a Next.js project pre-configured for Playwright E2E testing.
SOURCE: https://nextjs.org/docs/14/pages/building-your-application/testing/playwright

LANGUAGE: Shell
CODE:
```
npx create-next-app@latest --example with-playwright with-playwright-app
```

----------------------------------------

TITLE: create-next-app CLI Options Reference
DESCRIPTION: A comprehensive list of command-line options available for `create-next-app`, detailing their purpose and usage for customizing new Next.js projects.
SOURCE: https://nextjs.org/docs/pages/api-reference/cli/create-next-app

LANGUAGE: APIDOC
CODE:
```
Options:
  -h, --help: Show all available options
  -v, --version: Output the version number
  --no-*: Negate default options. E.g. --no-eslint
  --ts, --typescript: Initialize as a TypeScript project (default)
  --js, --javascript: Initialize as a JavaScript project
  --tailwind: Initialize with Tailwind CSS config (default)
  --eslint: Initialize with ESLint config
  --app: Initialize as an App Router project
  --api: Initialize a project with only route handlers
  --src-dir: Initialize inside a `src/` directory
  --turbopack: Enable Turbopack by default for development
  --import-alias <alias-to-configure>: Specify import alias to use (default "@/*")
  --empty: Initialize an empty project
  --use-npm: Explicitly tell the CLI to bootstrap the application using npm
  --use-pnpm: Explicitly tell the CLI to bootstrap the application using pnpm
  --use-yarn: Explicitly tell the CLI to bootstrap the application using Yarn
  --use-bun: Explicitly tell the CLI to bootstrap the application using Bun
  -e, --example [name] [github-url]: An example to bootstrap the app with
  --example-path <path-to-example>: Specify the path to the example separately
  --reset-preferences: Explicitly tell the CLI to reset any stored preferences
  --skip-install: Explicitly tell the CLI to skip installing packages
  --disable-git: Explicitly tell the CLI to disable git initialization
  --yes: Use previous preferences or defaults for all options
```

----------------------------------------

TITLE: Next.js App Directory: Basic Route Handler GET Method
DESCRIPTION: Illustrates the basic structure of a Route Handler `GET` method in the Next.js `app` directory. Route Handlers replace API Routes and allow custom request handling using Web `Request` and `Response` APIs.
SOURCE: https://nextjs.org/docs/14/app/building-your-application/upgrading/app-router-migration

LANGUAGE: TypeScript
CODE:
```
export async function GET(request: Request) {}
```

----------------------------------------

TITLE: Next.js 14 CLI Commands API Reference
DESCRIPTION: API documentation for command-line interface (CLI) tools provided by Next.js, including `create-next-app` for project initialization and the general `next` CLI.
SOURCE: https://nextjs.org/docs/14/getting-started/project-structure

LANGUAGE: APIDOC
CODE:
```
create-next-app
next CLI
```

----------------------------------------

TITLE: Next.js 14 Application Building: Routing Concepts
DESCRIPTION: Documentation on routing principles in Next.js 14, covering the structure of pages and layouts, dynamic routing, client-side linking and navigation, and server-side redirects.
SOURCE: https://nextjs.org/docs/14/app/building-your-application/configuring/eslint

LANGUAGE: APIDOC
CODE:
```
Pages and Layouts
```

LANGUAGE: APIDOC
CODE:
```
Dynamic Routes
```

LANGUAGE: APIDOC
CODE:
```
Linking and Navigating
```

LANGUAGE: APIDOC
CODE:
```
Redirecting
```

----------------------------------------

TITLE: Example: Revalidate Tag in Next.js Route Handler
DESCRIPTION: Illustrates the usage of `revalidateTag` within a Next.js Route Handler. This example shows how to revalidate a cache tag dynamically based on a query parameter received in a GET request.
SOURCE: https://nextjs.org/docs/app/api-reference/functions/revalidateTag

LANGUAGE: TypeScript
CODE:
```
import type { NextRequest } from 'next/server'
import { revalidateTag } from 'next/cache'

export async function GET(request: NextRequest) {
  const tag = request.nextUrl.searchParams.get('tag')
  revalidateTag(tag)
  return Response.json({ revalidated: true, now: Date.now() })
}
```

LANGUAGE: JavaScript
CODE:
```
import { revalidateTag } from 'next/cache'

export async function GET(request) {
  const tag = request.nextUrl.searchParams.get('tag')
  revalidateTag(tag)
  return Response.json({ revalidated: true, now: Date.now() })
}
```

----------------------------------------

TITLE: Next.js API Reference: Components
DESCRIPTION: Documentation for Next.js built-in components that provide enhanced functionality for common web elements.
SOURCE: https://nextjs.org/docs/pages/getting-started/project-structure

LANGUAGE: APIDOC
CODE:
```
Font
Form Component
Image Component
Link Component
Script Component
```

----------------------------------------

TITLE: Manage Cookies in Next.js Server Actions
DESCRIPTION: Provides examples of how to get, set, and delete cookies within a Next.js Server Action using the `cookies` API from `next/headers`. This allows for server-side manipulation of HTTP cookies.
SOURCE: https://nextjs.org/docs/14/app/building-your-application/data-fetching/server-actions-and-mutations

LANGUAGE: TypeScript
CODE:
```
'use server'

import { cookies } from 'next/headers'

export async function exampleAction() {
  // Get cookie
  const value = cookies().get('name')?.value

  // Set cookie
  cookies().set('name', 'Delba')

  // Delete cookie
  cookies().delete('name')
}
```

----------------------------------------

TITLE: Next.js Functions API Reference
DESCRIPTION: Documentation for various Next.js functions, including those for data fetching, caching, routing, metadata generation, and server-side utilities, enabling dynamic and optimized web applications.
SOURCE: https://nextjs.org/docs/pages/guides/custom-server

LANGUAGE: APIDOC
CODE:
```
Functions:
  - after
  - cacheLife
  - cacheTag
  - connection
  - cookies
  - draftMode
  - fetch
  - forbidden
  - generateImageMetadata
  - generateMetadata
  - generateSitemaps
  - generateStaticParams
  - generateViewport
  - headers
  - ImageResponse
  - NextRequest
  - NextResponse
  - notFound
  - permanentRedirect
  - redirect
  - revalidatePath
  - revalidateTag
  - unauthorized
  - unstable_cache
  - unstable_noStore
  - unstable_rethrow
  - useLinkStatus
  - useParams
  - usePathname
  - useReportWebVitals
  - useRouter
  - useSearchParams
```

----------------------------------------

TITLE: useSearchParams API: Returns
DESCRIPTION: Documentation for the return value of the `useSearchParams` hook. It returns a read-only `URLSearchParams` interface, detailing key methods like `get()`, `has()`, `getAll()`, `keys()`, `values()`, `entries()`, `forEach()`, and `toString()` with examples and explanations.
SOURCE: https://nextjs.org/docs/14/app/api-reference/functions/use-search-params

LANGUAGE: APIDOC
CODE:
```
useSearchParams() returns: Readonly<URLSearchParams>

Methods:
  - get(name: string): string | null
    Description: Returns the first value associated with the search parameter.
    Examples:
      - URL: /dashboard?a=1, Result: '1'
      - URL: /dashboard?a=, Result: ''
      - URL: /dashboard?b=3, Result: null
      - URL: /dashboard?a=1&a=2, Result: '1' (use getAll() for all values)

  - has(name: string): boolean
    Description: Returns a boolean value indicating if the given parameter exists.
    Examples:
      - URL: /dashboard?a=1, Result: true
      - URL: /dashboard?b=3, Result: false

  - getAll(name: string): string[]
  - keys(): Iterator<string>
  - values(): Iterator<string>
  - entries(): Iterator<[string, string]>
  - forEach(callback: (value: string, name: string, parent: URLSearchParams) => void, thisArg?: any): void
  - toString(): string
```

----------------------------------------

TITLE: Next.js 14 CLI Commands
DESCRIPTION: Provides an overview of command-line interface (CLI) tools for Next.js, including project creation and general CLI usage.
SOURCE: https://nextjs.org/docs/14/getting-started

LANGUAGE: APIDOC
CODE:
```
create-next-app
```

LANGUAGE: APIDOC
CODE:
```
next CLI
```

----------------------------------------

TITLE: Static Web Manifest File Example for Next.js
DESCRIPTION: A basic `manifest.json` file placed in the `app` directory, providing essential metadata for a web application such as its name, short name, description, and starting URL.
SOURCE: https://nextjs.org/docs/app/api-reference/file-conventions/metadata/manifest

LANGUAGE: JSON
CODE:
```
{
  "name": "My Next.js Application",
  "short_name": "Next.js App",
  "description": "An application built with Next.js",
  "start_url": "/"
  // ...
}
```

----------------------------------------

TITLE: Cache GET requests in Next.js Route Handlers
DESCRIPTION: Demonstrates how Next.js Route Handlers automatically cache `GET` requests using the `Response` object. This example fetches data from an external API and returns it as JSON, showcasing default caching behavior.
SOURCE: https://nextjs.org/docs/14/app/building-your-application/routing/route-handlers

LANGUAGE: TypeScript
CODE:
```
export async function GET() {
  const res = await fetch('https://data.mongodb-api.com/...', {
    headers: {
      'Content-Type': 'application/json',
      'API-Key': process.env.DATA_API_KEY
    }
  })
  const data = await res.json()

  return Response.json({ data })
}
```

LANGUAGE: JavaScript
CODE:
```
export async function GET() {
  const res = await fetch('https://data.mongodb-api.com/...', {
    headers: {
      'Content-Type': 'application/json',
      'API-Key': process.env.DATA_API_KEY
    }
  })
  const data = await res.json()

  return Response.json({ data })
}
```

----------------------------------------

TITLE: Next.js API Reference: Functions
DESCRIPTION: Documentation for Next.js utility functions that provide various capabilities for data fetching, caching, routing, and metadata generation.
SOURCE: https://nextjs.org/docs/pages/getting-started/project-structure

LANGUAGE: APIDOC
CODE:
```
after
cacheLife
cacheTag
connection
cookies
draftMode
fetch
forbidden
generateImageMetadata
generateMetadata
generateSitemaps
generateStaticParams
generateViewport
headers
ImageResponse
NextRequest
NextResponse
notFound
permanentRedirect
redirect
revalidatePath
revalidateTag
unauthorized
unstable_cache
unstable_noStore
unstable_rethrow
useLinkStatus
useParams
usePathname
useReportWebVitals
useRouter
useSearchParams
```

----------------------------------------

TITLE: Next.js next start Command-Line Options
DESCRIPTION: API documentation for the `next start` command, detailing the available flags for configuring how the production application is run, including port and hostname settings.
SOURCE: https://nextjs.org/docs/14/app/api-reference/cli/next

LANGUAGE: APIDOC
CODE:
```
-h or --help: Show all available options.
[directory]: A directory on which to start the application. If no directory is provided, the current directory will be used.
-p or --port <port>: Specify a port number on which to start the application. (default: 3000, env: PORT)
-H or --hostname <hostname>: Specify a hostname on which to start the application (default: 0.0.0.0).
--keepAliveTimeout <keepAliveTimeout>: Specify the maximum amount of milliseconds to wait before closing the inactive connections.
```

----------------------------------------

TITLE: Access and Manipulate Cookies in Next.js Middleware
DESCRIPTION: This comprehensive middleware example showcases how to interact with cookies using the `RequestCookies` and `ResponseCookies` APIs. It demonstrates getting, checking for existence, deleting, and setting cookies on both incoming requests and outgoing responses.
SOURCE: https://nextjs.org/docs/app/api-reference/file-conventions/middleware

LANGUAGE: TypeScript
CODE:
```
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Assume a "Cookie:nextjs=fast" header to be present on the incoming request
  // Getting cookies from the request using the `RequestCookies` API
  let cookie = request.cookies.get('nextjs')
  console.log(cookie) // => { name: 'nextjs', value: 'fast', Path: '/' }
  const allCookies = request.cookies.getAll()
  console.log(allCookies) // => [{ name: 'nextjs', value: 'fast' }]

  request.cookies.has('nextjs') // => true
  request.cookies.delete('nextjs')
  request.cookies.has('nextjs') // => false

  // Setting cookies on the response using the `ResponseCookies` API
  const response = NextResponse.next()
  response.cookies.set('vercel', 'fast')
  response.cookies.set({
    name: 'vercel',
    value: 'fast',
    path: '/',
  })
  cookie = response.cookies.get('vercel')
  console.log(cookie) // => { name: 'vercel', value: 'fast', Path: '/' }
  // The outgoing response will have a `Set-Cookie:vercel=fast;path=/` header.

  return response
}
```

----------------------------------------

TITLE: Static Image Import from URL Example
DESCRIPTION: Example showing how to import and use a static image directly from a URL with `next/image`.
SOURCE: https://nextjs.org/docs/14/app/api-reference/next-config-js/urlImports

LANGUAGE: JavaScript
CODE:
```
import Image from 'next/image'
import logo from 'https://example.com/assets/logo.png'

export default () => (
  <div>
    <Image src={logo} placeholder="blur" />
  </div>
)
```

----------------------------------------

TITLE: Next.js API Route Handler: Read, Set, and Delete Cookies with next/headers
DESCRIPTION: Demonstrates how to access, modify, and remove cookies within a Next.js API Route Handler using the `cookies` function from `next/headers`. It shows examples of getting a cookie's value, setting a new cookie, and deleting an existing cookie.
SOURCE: https://nextjs.org/docs/app/api-reference/file-conventions/route

LANGUAGE: TypeScript
CODE:
```
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  const cookieStore = await cookies()

  const a = cookieStore.get('a')
  const b = cookieStore.set('b', '1')
  const c = cookieStore.delete('c')
}
```

----------------------------------------

TITLE: Next.js Preview API Route URL Example
DESCRIPTION: Provides an example URL structure for accessing the Next.js preview API route, including necessary secret and slug parameters for activation.
SOURCE: https://nextjs.org/docs/14/pages/building-your-application/configuring/preview-mode

LANGUAGE: Shell
CODE:
```
https://<your-site>/api/preview?secret=<token>&slug=<path>
```

----------------------------------------

TITLE: Fetch and display data with getServerSideProps
DESCRIPTION: This example demonstrates how to use `getServerSideProps` to fetch data from an external API (GitHub) on each request and pass it as props to a Next.js page component. The page then displays the fetched data, making it suitable for frequently updated content.
SOURCE: https://nextjs.org/docs/14/pages/api-reference/functions/get-server-side-props

LANGUAGE: TypeScript
CODE:
```
import type { InferGetServerSidePropsType, GetServerSideProps } from 'next'

type Repo = {
  name: string
  stargazers_count: number
}

export const getServerSideProps = (async () => {
  // Fetch data from external API
  const res = await fetch('https://api.github.com/repos/vercel/next.js')
  const repo: Repo = await res.json()
  // Pass data to the page via props
  return { props: { repo } }
}) satisfies GetServerSideProps<{ repo: Repo }>

export default function Page({
  repo,
}: InferGetServerSidePropsType<typeof getServerSideProps>) {
  return (
    <main>
      <p>{repo.stargazers_count}</p>
    </main>
  )
}
```

----------------------------------------

TITLE: create-next-app CLI Options Reference
DESCRIPTION: Detailed reference of all available command-line options for `create-next-app`, including their descriptions and purpose for customizing project initialization.
SOURCE: https://nextjs.org/docs/app/api-reference/cli/create-next-app

LANGUAGE: APIDOC
CODE:
```
create-next-app CLI Options:
  -h, --help: Show all available options
  -v, --version: Output the version number
  --no-*: Negate default options. E.g. --no-eslint
  --ts, --typescript: Initialize as a TypeScript project (default)
  --js, --javascript: Initialize as a JavaScript project
  --tailwind: Initialize with Tailwind CSS config (default)
  --eslint: Initialize with ESLint config
  --app: Initialize as an App Router project
  --api: Initialize a project with only route handlers
  --src-dir: Initialize inside a `src/` directory
  --turbopack: Enable Turbopack by default for development
  --import-alias <alias-to-configure>: Specify import alias to use (default "@/*")
  --empty: Initialize an empty project
  --use-npm: Explicitly tell the CLI to bootstrap the application using npm
  --use-pnpm: Explicitly tell the CLI to bootstrap the application using pnpm
  --use-yarn: Explicitly tell the CLI to bootstrap the application using Yarn
  --use-bun: Explicitly tell the CLI to bootstrap the application using Bun
  -e, --example [name] [github-url]: An example to bootstrap the app with
  --example-path <path-to-example>: Specify the path to the example separately
  --reset-preferences: Explicitly tell the CLI to reset any stored preferences
  --skip-install: Explicitly tell the CLI to skip installing packages
  --disable-git: Explicitly tell the CLI to disable git initialization
  --yes: Use previous preferences or defaults for all options
```

----------------------------------------

TITLE: Example: Importing Generic Assets with URL Constructor
DESCRIPTION: Demonstrates importing a generic asset (like a text file) using the `URL` constructor with `import.meta.url` to get its processed path in the Next.js build.
SOURCE: https://nextjs.org/docs/app/api-reference/config/next-config-js/urlImports

LANGUAGE: JavaScript
CODE:
```
const logo = new URL('https://example.com/assets/file.txt', import.meta.url)

console.log(logo.pathname)

// prints "/_next/static/media/file.a9727b5d.txt"
```

----------------------------------------

TITLE: Next.js API Reference Links
DESCRIPTION: This section provides a list of links to various Next.js API references, covering topics like data security, the extended `fetch` function, `loading.js` file convention, logging configuration, and the `taint` function.
SOURCE: https://nextjs.org/docs/app/getting-started/fetching-data

LANGUAGE: APIDOC
CODE:
```
Data Security: Learn the built-in data security features in Next.js and learn best practices for protecting your application's data.
fetch: API reference for the extended fetch function.
loading.js: API reference for the loading.js file.
logging: Configure how data fetches are logged to the console when running Next.js in development mode.
taint: Enable tainting Objects and Values.
```

----------------------------------------

TITLE: Next.js Page Component with Deprecated url Prop
DESCRIPTION: An example of a Next.js page component that directly accesses the `url` property from `this.props`, which is now deprecated and should be replaced with `withRouter`.
SOURCE: https://nextjs.org/docs/14/pages/building-your-application/upgrading/codemods

LANGUAGE: javascript
CODE:
```
import React from 'react'
export default class extends React.Component {
  render() {
    const { pathname } = this.props.url
    return <div>Current pathname: {pathname}</div>
  }
}
```

----------------------------------------

TITLE: Quickstart: Create Next.js App with Vitest Example
DESCRIPTION: Use `create-next-app` with the `with-vitest` example to quickly scaffold a new Next.js project pre-configured for Vitest.
SOURCE: https://nextjs.org/docs/14/app/building-your-application/testing/vitest

LANGUAGE: Terminal
CODE:
```
npx create-next-app@latest --example with-vitest with-vitest-app
```

----------------------------------------

TITLE: React Fetch Request Memoization Example
DESCRIPTION: This code demonstrates how React's extended `fetch` API automatically memoizes requests. The `getItem` function, when called multiple times with the same URL, will only execute the network request once, with subsequent calls returning cached results within the same render pass.
SOURCE: https://nextjs.org/docs/14/app/building-your-application/caching

LANGUAGE: TypeScript
CODE:
```
async function getItem() {
  // The `fetch` function is automatically memoized and the result
  // is cached
  const res = await fetch('https://.../item/1')
  return res.json()
}

// This function is called twice, but only executed the first time
const item = await getItem() // cache MISS

// The second call could be anywhere in your route
const item = await getItem() // cache HIT
```

LANGUAGE: JavaScript
CODE:
```
async function getItem() {
  // The `fetch` function is automatically memoized and the result
  // is cached
  const res = await fetch('https://.../item/1')
  return res.json()
}

// This function is called twice, but only executed the first time
const item = await getItem() // cache MISS

// The second call could be anywhere in your route
const item = await getItem() // cache HIT
```

----------------------------------------

TITLE: Next.js 14 API Reference Overview
DESCRIPTION: A structured overview of the Next.js 14 API, detailing available components, server-side and client-side functions, and configurable options within the `next.config.js` file.
SOURCE: https://nextjs.org/docs/14/app/building-your-application/routing/redirecting

LANGUAGE: APIDOC
CODE:
```
API Reference:
  Components:
    - Font
    - <Head>
    - <Image>
    - <Image> (Legacy)
    - <Link>
    - <Script>
  Functions:
    - getInitialProps
    - getServerSideProps
    - getStaticPaths
    - getStaticProps
    - NextRequest
    - NextResponse
    - useAmp
    - useReportWebVitals
    - useRouter
    - userAgent
  next.config.js Options:
    - assetPrefix
    - basePath
    - compress
    - crossOrigin
    - devIndicators
    - distDir
    - env
    - eslint
    - exportPathMap
    - generateBuildId
    - generateEtags
    - headers
    - httpAgentOptions
    - images
    - instrumentationHook
    - onDemandEntries
    - optimizePackageImports
    - output
    - pageExtensions
    - poweredByHeader
    - productionBrowserSourceMaps
    - reactStrictMode
    - redirects
    - rewrites
    - Runtime Config
    - trailingSlash
```

----------------------------------------

TITLE: Next.js 14 Command Line Interface (CLI) API
DESCRIPTION: API documentation for command-line interface tools used with Next.js 14, including commands for project creation and general Next.js operations.
SOURCE: https://nextjs.org/docs/14/app/building-your-application/configuring/src-directory

LANGUAGE: APIDOC
CODE:
```
create-next-app: Creates a new Next.js application.
next CLI: General Next.js CLI commands (e.g., dev, build, start).
```

----------------------------------------

TITLE: Run Next.js ESLint Setup from Terminal
DESCRIPTION: Execute this command in your terminal to start the ESLint configuration process for your Next.js project. It will guide you through selecting a configuration preset (Strict, Base, or Cancel).
SOURCE: https://nextjs.org/docs/app/getting-started/installation

LANGUAGE: shell
CODE:
```
npm run lint
```

----------------------------------------

TITLE: Next.js App Directory: Basic GET Route Handler
DESCRIPTION: Illustrates a basic GET request handler using Web Request and Response APIs in the `app` directory, replacing traditional API Routes.
SOURCE: https://nextjs.org/docs/14/pages/building-your-application/upgrading/app-router-migration

LANGUAGE: TypeScript
CODE:
```
export async function GET(request: Request) {}
```

----------------------------------------

TITLE: Next.js 14 Edge Runtime Reference
DESCRIPTION: References documentation related to the Next.js Edge Runtime, designed for high-performance, low-latency execution at the edge.
SOURCE: https://nextjs.org/docs/14/getting-started

LANGUAGE: APIDOC
CODE:
```
Edge Runtime
```

----------------------------------------

TITLE: API Reference: robots.txt
DESCRIPTION: API Reference for robots.txt file.
SOURCE: https://nextjs.org/docs/app/getting-started/metadata-and-og-images

LANGUAGE: APIDOC
CODE:
```
robots.txt
```

----------------------------------------

TITLE: Next.js Debugger Listening Output
DESCRIPTION: Example terminal output when the Next.js development server starts with the `--inspect` flag, showing the debugger's WebSocket address and server status.
SOURCE: https://nextjs.org/docs/pages/guides/debugging

LANGUAGE: text
CODE:
```
Debugger listening on ws://127.0.0.1:9229/0cf90313-350d-4466-a748-cd60f4e47c95
For help, see: https://nodejs.org/en/docs/inspector
ready - started server on 0.0.0.0:3000, url: http://localhost:3000
```

----------------------------------------

TITLE: Next.js 14 Command Line Interface (CLI) Commands API
DESCRIPTION: Documentation for command-line tools used to initialize and manage Next.js projects, including create-next-app and the next CLI.
SOURCE: https://nextjs.org/docs/14/pages/api-reference/next-config-js/images

LANGUAGE: APIDOC
CODE:
```
CLI Commands:
- create-next-app
- next CLI
```

----------------------------------------

TITLE: Quickstart: Create Next.js App with Vitest Example
DESCRIPTION: Use `create-next-app` with the `with-vitest` example to quickly initialize a Next.js project configured for Vitest.
SOURCE: https://nextjs.org/docs/app/guides/testing/vitest

LANGUAGE: Terminal
CODE:
```
npx create-next-app@latest --example with-vitest with-vitest-app
```

----------------------------------------

TITLE: Manage Cookies in Next.js Middleware
DESCRIPTION: This TypeScript middleware example illustrates how to access, read, check for existence, delete, and set cookies using the `RequestCookies` and `ResponseCookies` APIs provided by Next.js. It demonstrates methods like `get`, `getAll`, `has`, `delete` for incoming requests and `set` for outgoing responses, providing comprehensive cookie management capabilities.
SOURCE: https://nextjs.org/docs/pages/api-reference/file-conventions/middleware

LANGUAGE: TypeScript
CODE:
```
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Assume a "Cookie:nextjs=fast" header to be present on the incoming request
  // Getting cookies from the request using the `RequestCookies` API
  let cookie = request.cookies.get('nextjs')
  console.log(cookie) // => { name: 'nextjs', value: 'fast', Path: '/' }
  const allCookies = request.cookies.getAll()
  console.log(allCookies) // => [{ name: 'nextjs', value: 'fast' }]

  request.cookies.has('nextjs') // => true
  request.cookies.delete('nextjs')
  request.cookies.has('nextjs') // => false

  // Setting cookies on the response using the `ResponseCookies` API
  const response = NextResponse.next()
  response.cookies.set('vercel', 'fast')
  response.cookies.set({
    name: 'vercel',
    value: 'fast',
    path: '/',
  })
  cookie = response.cookies.get('vercel')
  console.log(cookie) // => { name: 'vercel', value: 'fast', Path: '/' }
  // The outgoing response will have a `Set-Cookie:vercel=fast;path=/` header.

  return response
}
```

----------------------------------------

TITLE: Next.js Server and Client Functions API Reference
DESCRIPTION: API documentation for various Next.js functions, including utilities for data fetching, caching, routing, and other core functionalities available in both server and client environments.
SOURCE: https://nextjs.org/docs/app/guides/instrumentation

LANGUAGE: APIDOC
CODE:
```
Functions:
  - after
  - cacheLife
  - cacheTag
  - connection
  - cookies
  - draftMode
  - fetch
  - forbidden
  - generateImageMetadata
  - generateMetadata
  - generateSitemaps
  - generateStaticParams
  - generateViewport
  - headers
  - ImageResponse
  - NextRequest
  - NextResponse
  - notFound
  - permanentRedirect
  - redirect
  - revalidatePath
  - revalidateTag
  - unauthorized
  - unstable_cache
  - unstable_noStore
  - unstable_rethrow
  - useLinkStatus
  - useParams
  - usePathname
  - useReportWebVitals
  - useRouter
  - useSearchParams
```

----------------------------------------

TITLE: Next.js Route Handler API Reference
DESCRIPTION: Detailed API reference for Next.js Route Handlers, including supported HTTP methods, and the `request` and `context` parameters with their properties, types, and usage examples for dynamic routes.
SOURCE: https://nextjs.org/docs/app/api-reference/file-conventions/route

LANGUAGE: APIDOC
CODE:
```
Route Handlers:
  Supported HTTP Methods: GET, POST, PUT, PATCH, DELETE, HEAD, OPTIONS
  Parameters:
    request (optional):
      Type: NextRequest (extension of Web Request API)
      Description: Provides further control over incoming request, including easy access to cookies and an extended, parsed, URL object nextUrl.
    context (optional):
      Properties:
        params:
          Type: Promise<object>
          Description: Resolves to an object containing the dynamic route parameters for the current route.
          Examples:
            app/dashboard/[team]/route.js:
              URL: /dashboard/1
              params: Promise<{ team: '1' }>
            app/shop/[tag]/[item]/route.js:
              URL: /shop/1/2
              params: Promise<{ tag: '1', item: '2' }>
            app/blog/[...slug]/route.js:
              URL: /blog/1/2
              params: Promise<{ slug: ['1', '2'] }>
```

----------------------------------------

TITLE: Next.js Page Component `params` Prop API Reference
DESCRIPTION: API documentation for the `params` prop available to Next.js page components. It details the type, description, and provides examples of how `params` resolves based on different dynamic route configurations and URLs, along with important usage notes regarding its Promise nature and version compatibility.
SOURCE: https://nextjs.org/docs/app/api-reference/file-conventions/page

LANGUAGE: APIDOC
CODE:
```
Props:
  params (optional):
    Type: Promise<object>
    Description: A promise that resolves to an object containing the dynamic route parameters from the root segment down to that page.
    Examples:
      - Route: `app/shop/[slug]/page.js`
        URL: `/shop/1`
        params: `Promise<{ slug: '1' }>`
      - Route: `app/shop/[category]/[item]/page.js`
        URL: `/shop/1/2`
        params: `Promise<{ category: '1', item: '2' }>`
      - Route: `app/shop/[...slug]/page.js`
        URL: `/shop/1/2`
        params: `Promise<{ slug: ['1', '2'] }>`
    Notes:
      - Since the `params` prop is a promise, you must use `async/await` or React's `use` function to access the values.
      - In version 14 and earlier, `params` was a synchronous prop. To help with backwards compatibility, you can still access it synchronously in Next.js 15, but this behavior will be deprecated in the future.
```

----------------------------------------

TITLE: Next.js API Functions
DESCRIPTION: Lists core API functions available in Next.js, such as hooks and utilities.
SOURCE: https://nextjs.org/docs/app/guides/debugging

LANGUAGE: APIDOC
CODE:
```
Functions:
  - useSelectedLayoutSegment
  - useSelectedLayoutSegments
  - userAgent
```

----------------------------------------

TITLE: Next.js API Directives
DESCRIPTION: Overview of directives available in Next.js, such as 'use server' for marking server-side modules or functions.
SOURCE: https://nextjs.org/docs/app/api-reference/file-conventions/middleware

LANGUAGE: APIDOC
CODE:
```
Directives:
  - use server
```

----------------------------------------

TITLE: Next.js Rewriting to External URLs
DESCRIPTION: Shows how to configure Next.js rewrites to direct traffic to external URLs, useful for incremental adoption. Includes examples for rewriting a base path and paths with slugs.
SOURCE: https://nextjs.org/docs/14/pages/api-reference/next-config-js/rewrites

LANGUAGE: javascript
CODE:
```
module.exports = {
  async rewrites() {
    return [
      {
        source: '/blog',
        destination: 'https://example.com/blog',
      },
      {
        source: '/blog/:slug',
        destination: 'https://example.com/blog/:slug', // Matched parameters can be used in the destination
      },
    ]
  },
}
```

----------------------------------------

TITLE: Example: Alphabetical File Structure for API Reference
DESCRIPTION: Illustrates the file-system routing structure for API reference documentation where pages are sorted alphabetically.
SOURCE: https://nextjs.org/docs/community/contribution-guide

LANGUAGE: Text
CODE:
```
04-functions
├── after.mdx
├── cacheLife.mdx
├── cacheTag.mdx
└── ...
```