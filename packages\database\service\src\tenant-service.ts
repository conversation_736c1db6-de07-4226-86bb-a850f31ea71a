import { Tenant, TenantStatus, SubscriptionPlan } from "@nexus/database-schema";
import { prisma } from "./client";
import { CreateTenant } from "@nexus/validation";

export class TenantService {
  // Create a new tenant
  async create(data: CreateTenant): Promise<Tenant> {
    return prisma.tenant.create({
      data: {
        ...data,
        status: TenantStatus.ACTIVE,
        plan: SubscriptionPlan.STARTER,
      },
    });
  }

  // Find tenant by ID
  async findById(id: string): Promise<Tenant | null> {
    return prisma.tenant.findUnique({
      where: { id },
      include: {
        users: true,
        workspaces: true,
      },
    });
  }

  // Find tenant by slug
  async findBySlug(slug: string): Promise<Tenant | null> {
    return prisma.tenant.findUnique({
      where: { slug },
      include: {
        users: true,
        workspaces: true,
      },
    });
  }

  // Find tenant by domain
  async findByDomain(domain: string): Promise<Tenant | null> {
    return prisma.tenant.findUnique({
      where: { domain },
      include: {
        users: true,
        workspaces: true,
      },
    });
  }

  // Update tenant
  async update(id: string, data: Partial<CreateTenant>): Promise<Tenant> {
    return prisma.tenant.update({
      where: { id },
      data,
    });
  }

  // Update tenant status
  async updateStatus(id: string, status: TenantStatus): Promise<Tenant> {
    return prisma.tenant.update({
      where: { id },
      data: { status },
    });
  }

  // Update tenant plan
  async updatePlan(id: string, plan: SubscriptionPlan): Promise<Tenant> {
    return prisma.tenant.update({
      where: { id },
      data: { plan },
    });
  }

  // Delete tenant (soft delete by setting status to INACTIVE)
  async delete(id: string): Promise<Tenant> {
    return prisma.tenant.update({
      where: { id },
      data: { status: TenantStatus.INACTIVE },
    });
  }

  // Hard delete tenant (use with caution)
  async hardDelete(id: string): Promise<Tenant> {
    return prisma.tenant.delete({
      where: { id },
    });
  }

  // List all tenants with pagination
  async list(page: number = 1, limit: number = 10) {
    const skip = (page - 1) * limit;
    
    const [tenants, total] = await Promise.all([
      prisma.tenant.findMany({
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
        include: {
          _count: {
            select: {
              users: true,
              workspaces: true,
            },
          },
        },
      }),
      prisma.tenant.count(),
    ]);

    return {
      data: tenants,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  // Check if slug is available
  async isSlugAvailable(slug: string): Promise<boolean> {
    const tenant = await prisma.tenant.findUnique({
      where: { slug },
    });
    return !tenant;
  }

  // Check if domain is available
  async isDomainAvailable(domain: string): Promise<boolean> {
    const tenant = await prisma.tenant.findUnique({
      where: { domain },
    });
    return !tenant;
  }
}

export const tenantService = new TenantService();
