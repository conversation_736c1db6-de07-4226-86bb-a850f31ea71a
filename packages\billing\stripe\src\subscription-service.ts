import { CreateSubscriptionRequest, UpdateSubscriptionRequest, Subscription, Product, Price } from "./stripe-types";

export class SubscriptionService {
  private tenantId: string;

  constructor(tenantId: string) {
    this.tenantId = tenantId;
  }

  // Get all products and prices
  async getProducts(): Promise<Product[]> {
    const response = await fetch("/api/stripe/products", {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to get products");
    }

    return response.json();
  }

  // Get current subscription
  async getCurrentSubscription(): Promise<Subscription | null> {
    const response = await fetch("/api/stripe/subscriptions/current", {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (response.status === 404) {
      return null;
    }

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to get current subscription");
    }

    return response.json();
  }

  // Create subscription
  async createSubscription(data: CreateSubscriptionRequest): Promise<Subscription> {
    const response = await fetch("/api/stripe/subscriptions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to create subscription");
    }

    return response.json();
  }

  // Update subscription
  async updateSubscription(data: UpdateSubscriptionRequest): Promise<Subscription> {
    const response = await fetch(`/api/stripe/subscriptions/${data.subscriptionId}`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to update subscription");
    }

    return response.json();
  }

  // Cancel subscription
  async cancelSubscription(subscriptionId: string, cancelAtPeriodEnd: boolean = true): Promise<Subscription> {
    const response = await fetch(`/api/stripe/subscriptions/${subscriptionId}/cancel`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify({ cancelAtPeriodEnd }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to cancel subscription");
    }

    return response.json();
  }

  // Resume subscription
  async resumeSubscription(subscriptionId: string): Promise<Subscription> {
    const response = await fetch(`/api/stripe/subscriptions/${subscriptionId}/resume`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to resume subscription");
    }

    return response.json();
  }

  // Get subscription history
  async getSubscriptionHistory(): Promise<Subscription[]> {
    const response = await fetch("/api/stripe/subscriptions/history", {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to get subscription history");
    }

    return response.json();
  }

  // Preview subscription change
  async previewSubscriptionChange(subscriptionId: string, newPriceId: string): Promise<{
    amountDue: number;
    currency: string;
    prorationDate: Date;
  }> {
    const response = await fetch(`/api/stripe/subscriptions/${subscriptionId}/preview`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify({ priceId: newPriceId }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to preview subscription change");
    }

    return response.json();
  }

  // Get usage records (for usage-based billing)
  async getUsageRecords(subscriptionItemId: string): Promise<any[]> {
    const response = await fetch(`/api/stripe/usage/${subscriptionItemId}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to get usage records");
    }

    return response.json();
  }

  // Report usage (for usage-based billing)
  async reportUsage(subscriptionItemId: string, quantity: number): Promise<void> {
    const response = await fetch(`/api/stripe/usage/${subscriptionItemId}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify({ quantity }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to report usage");
    }
  }
}

export const createSubscriptionService = (tenantId: string): SubscriptionService => {
  return new SubscriptionService(tenantId);
};
