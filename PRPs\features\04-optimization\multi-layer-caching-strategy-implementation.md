# Multi-Layer Caching Strategy Implementation

## Feature Overview

Implement a comprehensive multi-layer caching strategy for the NEXUS SaaS Starter that combines Redis, CDN, and application-level caching to achieve sub-200ms response times globally while maintaining data consistency across multi-tenant architecture.

## Context & Research

### Current Implementation Analysis
Based on codebase analysis, existing caching patterns include:
- **Redis Integration**: Already implemented in analytics, theme caching, and rate limiting
- **Tenant-Aware Caching**: Cache keys include tenant context (`analytics:${tenantId}:${dateRange}`)
- **Connection Pooling**: Database optimization with Prisma connection pooling
- **CDN Assets**: Static asset delivery through Vercel/CloudFront

### Technology Stack Verification (Context7)

**Redis Caching Patterns**:
- Client-side caching with Redis for reduced latency
- Cache hit rate optimization: `keyspace_hits / (keyspace_hits + keyspace_misses) * 100`
- LFU/LRU eviction policies for memory management
- Connection pooling for high-performance access

**Next.js Caching Mechanisms**:
- Data Cache: `fetch()` with `cache: 'force-cache'` and `next: { revalidate: 3600 }`
- Request Memoization: React `cache()` function for deduplication
- Full Route Cache: Static and dynamic route caching
- Router Cache: Client-side navigation caching

**Performance Optimization Patterns**:
- Multi-layer cache hierarchy: Memory → Redis → Database
- Cache invalidation with tags: `revalidateTag()` for granular control
- Stale-while-revalidate patterns for better UX

## Implementation Blueprint

### Data Models and Structure

```typescript
// Enhanced caching configuration
interface CacheConfig {
  layers: {
    memory: {
      maxSize: number;
      ttl: number;
    };
    redis: {
      url: string;
      keyPrefix: string;
      defaultTtl: number;
      maxRetries: number;
    };
    cdn: {
      provider: 'cloudfront' | 'vercel';
      regions: string[];
      cachePolicies: Record<string, CachePolicyConfig>;
    };
  };
  strategies: {
    tenant: TenantCacheStrategy;
    api: ApiCacheStrategy;
    static: StaticCacheStrategy;
    database: DatabaseCacheStrategy;
  };
}

interface TenantCacheStrategy {
  isolation: 'strict' | 'shared';
  keyPattern: string;
  invalidationRules: InvalidationRule[];
}
```

### Task Breakdown

**Phase 1: Redis Infrastructure Enhancement (2-3 hours)**

1. **Enhanced Redis Configuration**
   - File: `lib/cache/redis.ts`
   - Implement connection pooling with cluster support
   - Add cache hit/miss metrics collection
   - Configure eviction policies (LFU for analytics, LRU for sessions)
   - Pattern: Use existing Redis patterns from analytics caching

2. **Multi-Tenant Cache Isolation**
   - File: `lib/cache/tenant-cache.ts`
   - Extend existing tenant-aware caching (`analytics:${tenantId}:${key}`)
   - Implement cache namespace isolation
   - Add tenant-specific cache policies and TTL
   - Pattern: Follow existing tenant context patterns

**Phase 2: Application-Level Caching (3-4 hours)**

3. **Next.js Data Cache Optimization**
   - File: `lib/cache/data-cache.ts`
   - Implement React `cache()` for database queries
   - Configure `fetch()` with appropriate cache strategies
   - Add cache tags for granular invalidation
   - Pattern: Extend existing Prisma query patterns

4. **In-Memory Cache Layer**
   - File: `lib/cache/memory-cache.ts`
   - Implement LRU cache for frequently accessed data
   - Add cache warming strategies
   - Configure cache size limits and eviction
   - Pattern: Complement existing Redis caching

**Phase 3: CDN Integration (2-3 hours)**

5. **Static Asset Optimization**
   - File: `lib/cache/cdn-cache.ts`
   - Configure CloudFront/Vercel CDN policies
   - Implement cache headers for different asset types
   - Add cache invalidation for dynamic assets
   - Pattern: Extend existing asset delivery patterns

6. **API Response Caching**
   - File: `app/api/cache/route.ts`
   - Implement HTTP cache headers for API responses
   - Add conditional requests (ETag, Last-Modified)
   - Configure cache-control policies
   - Pattern: Follow existing API rate limiting patterns

### Integration Points

**Database Integration**:
- Extend existing Prisma connection pooling
- Add query result caching with automatic invalidation
- Implement read replica caching strategies
- Maintain existing multi-tenant isolation

**Authentication Integration**:
- Cache user sessions and permissions in Redis
- Implement tenant context caching
- Add authentication result caching
- Maintain security boundaries

**API Integration**:
- Add response caching middleware
- Implement cache-aware rate limiting
- Configure tenant-specific cache policies
- Extend existing API monitoring

## Validation Gates

### Level 1: Syntax & Style
```bash
npm run lint                    # ESLint checks
npx tsc --noEmit               # TypeScript validation
npm run test:unit              # Unit tests for cache layers
```

### Level 2: Integration Tests
```bash
npm run dev                    # Start development server

# Test Redis caching
curl -H "x-tenant-id: test-tenant" \
     -H "Authorization: Bearer $TOKEN" \
     http://localhost:3000/api/analytics/dashboard

# Verify cache hit (should be faster)
curl -H "x-tenant-id: test-tenant" \
     -H "Authorization: Bearer $TOKEN" \
     http://localhost:3000/api/analytics/dashboard

# Test cache invalidation
curl -X POST -H "x-tenant-id: test-tenant" \
     -H "Authorization: Bearer $TOKEN" \
     http://localhost:3000/api/cache/invalidate/analytics
```

### Level 3: Performance Tests
```bash
# Load testing with cache warming
npx autocannon -c 100 -d 30 \
  -H "x-tenant-id=test-tenant" \
  -H "Authorization=Bearer $TOKEN" \
  http://localhost:3000/api/analytics/dashboard

# Cache hit rate monitoring
redis-cli info stats | grep keyspace
```

### Level 4: Multi-Tenant Validation
```bash
# Test tenant isolation
npm run test:e2e:cache-isolation

# Verify cache key patterns
redis-cli keys "tenant:*" | head -20

# Test cache invalidation across tenants
npm run test:cache:invalidation
```

## Quality Standards Checklist

- [ ] Redis connection pooling with <50ms connection time
- [ ] Cache hit rate >80% for frequently accessed data
- [ ] Tenant isolation with zero data leakage
- [ ] Sub-200ms API response times with cache hits
- [ ] Automatic cache invalidation on data updates
- [ ] CDN cache hit rate >90% for static assets
- [ ] Memory usage optimization with proper eviction
- [ ] Cache warming strategies for critical data
- [ ] Monitoring and alerting for cache performance
- [ ] Graceful degradation when cache is unavailable

## Security Considerations

- **Tenant Isolation**: Strict cache key namespacing
- **Data Encryption**: Encrypt sensitive cached data
- **Access Control**: Cache access tied to authentication
- **Audit Logging**: Log cache access patterns
- **TTL Management**: Appropriate expiration for sensitive data

## Performance Targets

- **Cache Hit Rate**: >80% for application data, >90% for static assets
- **Response Time**: <200ms for cached responses, <500ms for cache misses
- **Memory Usage**: <2GB Redis memory per 1000 active tenants
- **CDN Performance**: <100ms global asset delivery
- **Database Load**: 50% reduction in database queries

---

**Implementation Priority**: HIGH - Critical for production performance
**Estimated Effort**: 8-12 hours
**Dependencies**: Redis infrastructure, CDN configuration
**Success Metrics**: Response time reduction, cache hit rates, database load reduction

## Detailed Implementation

### 1. Enhanced Redis Configuration

```typescript
// lib/cache/redis.ts
import { Redis, Cluster } from 'ioredis';
import { logger } from '@/lib/logger';

interface RedisConfig {
  url: string;
  keyPrefix: string;
  maxRetriesPerRequest: number;
  retryDelayOnFailover: number;
  enableReadyCheck: boolean;
  lazyConnect: boolean;
}

class EnhancedRedisClient {
  private client: Redis | Cluster;
  private metrics: CacheMetrics;

  constructor(config: RedisConfig) {
    this.client = new Redis({
      ...config,
      retryDelayOnFailover: 100,
      enableOfflineQueue: false,
      maxRetriesPerRequest: 3,
    });

    this.metrics = new CacheMetrics();
    this.setupEventHandlers();
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const start = Date.now();
      const result = await this.client.get(key);

      this.metrics.recordHit(key, Date.now() - start);

      return result ? JSON.parse(result) : null;
    } catch (error) {
      this.metrics.recordMiss(key);
      logger.error('Redis get error:', { key, error });
      return null;
    }
  }

  async setex<T>(key: string, ttl: number, value: T): Promise<void> {
    try {
      await this.client.setex(key, ttl, JSON.stringify(value));
      this.metrics.recordSet(key);
    } catch (error) {
      logger.error('Redis setex error:', { key, ttl, error });
    }
  }

  async invalidatePattern(pattern: string): Promise<number> {
    try {
      const keys = await this.client.keys(pattern);
      if (keys.length > 0) {
        return await this.client.del(...keys);
      }
      return 0;
    } catch (error) {
      logger.error('Redis invalidation error:', { pattern, error });
      return 0;
    }
  }

  getMetrics(): CacheMetrics {
    return this.metrics;
  }

  private setupEventHandlers(): void {
    this.client.on('connect', () => {
      logger.info('Redis connected');
    });

    this.client.on('error', (error) => {
      logger.error('Redis connection error:', error);
    });
  }
}

class CacheMetrics {
  private hits = 0;
  private misses = 0;
  private sets = 0;
  private totalLatency = 0;

  recordHit(key: string, latency: number): void {
    this.hits++;
    this.totalLatency += latency;
  }

  recordMiss(key: string): void {
    this.misses++;
  }

  recordSet(key: string): void {
    this.sets++;
  }

  getHitRate(): number {
    const total = this.hits + this.misses;
    return total > 0 ? this.hits / total : 0;
  }

  getAverageLatency(): number {
    return this.hits > 0 ? this.totalLatency / this.hits : 0;
  }

  getStats() {
    return {
      hits: this.hits,
      misses: this.misses,
      sets: this.sets,
      hitRate: this.getHitRate(),
      averageLatency: this.getAverageLatency(),
    };
  }
}

export const redis = new EnhancedRedisClient({
  url: process.env.REDIS_URL!,
  keyPrefix: 'nexus:',
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  enableReadyCheck: true,
  lazyConnect: true,
});
```

### 2. Multi-Tenant Cache Isolation

```typescript
// lib/cache/tenant-cache.ts
import { redis } from './redis';
import { getTenantContext } from '@/lib/auth/tenant-context';

interface TenantCacheOptions {
  ttl?: number;
  tags?: string[];
  namespace?: string;
}

class TenantCache {
  private readonly DEFAULT_TTL = 3600; // 1 hour
  private readonly KEY_SEPARATOR = ':';

  private buildKey(tenantId: string, key: string, namespace?: string): string {
    const parts = ['tenant', tenantId];
    if (namespace) parts.push(namespace);
    parts.push(key);
    return parts.join(this.KEY_SEPARATOR);
  }

  async get<T>(
    key: string,
    options: TenantCacheOptions = {}
  ): Promise<T | null> {
    const tenantId = await getTenantContext();
    if (!tenantId) return null;

    const cacheKey = this.buildKey(tenantId, key, options.namespace);
    return redis.get<T>(cacheKey);
  }

  async set<T>(
    key: string,
    value: T,
    options: TenantCacheOptions = {}
  ): Promise<void> {
    const tenantId = await getTenantContext();
    if (!tenantId) return;

    const cacheKey = this.buildKey(tenantId, key, options.namespace);
    const ttl = options.ttl || this.DEFAULT_TTL;

    await redis.setex(cacheKey, ttl, value);

    // Store cache tags for invalidation
    if (options.tags) {
      await this.storeCacheTags(tenantId, cacheKey, options.tags);
    }
  }

  async invalidate(key: string, namespace?: string): Promise<void> {
    const tenantId = await getTenantContext();
    if (!tenantId) return;

    const cacheKey = this.buildKey(tenantId, key, namespace);
    await redis.invalidatePattern(cacheKey);
  }

  async invalidateByTag(tag: string): Promise<number> {
    const tenantId = await getTenantContext();
    if (!tenantId) return 0;

    const tagKey = this.buildKey(tenantId, `tag:${tag}`, 'meta');
    const keys = await redis.get<string[]>(tagKey);

    if (!keys || keys.length === 0) return 0;

    // Invalidate all keys associated with this tag
    let invalidated = 0;
    for (const key of keys) {
      const result = await redis.invalidatePattern(key);
      invalidated += result;
    }

    // Clean up the tag reference
    await redis.invalidatePattern(tagKey);

    return invalidated;
  }

  async invalidateNamespace(namespace: string): Promise<number> {
    const tenantId = await getTenantContext();
    if (!tenantId) return 0;

    const pattern = this.buildKey(tenantId, '*', namespace);
    return redis.invalidatePattern(pattern);
  }

  private async storeCacheTags(
    tenantId: string,
    cacheKey: string,
    tags: string[]
  ): Promise<void> {
    for (const tag of tags) {
      const tagKey = this.buildKey(tenantId, `tag:${tag}`, 'meta');
      const existingKeys = await redis.get<string[]>(tagKey) || [];

      if (!existingKeys.includes(cacheKey)) {
        existingKeys.push(cacheKey);
        await redis.setex(tagKey, this.DEFAULT_TTL * 24, existingKeys); // 24 hour TTL for tags
      }
    }
  }
}

export const tenantCache = new TenantCache();
```

### 3. Next.js Data Cache Optimization

```typescript
// lib/cache/data-cache.ts
import { cache } from 'react';
import { unstable_cache } from 'next/cache';
import { tenantCache } from './tenant-cache';
import { prisma } from '@/lib/database/prisma';

// Memoize database queries within request lifecycle
export const getUser = cache(async (id: string) => {
  const cached = await tenantCache.get(`user:${id}`, {
    namespace: 'users',
    ttl: 1800, // 30 minutes
    tags: ['user', `user:${id}`]
  });

  if (cached) return cached;

  const user = await prisma.user.findUnique({
    where: { id },
    include: {
      workspaces: {
        include: {
          workspace: true
        }
      }
    }
  });

  if (user) {
    await tenantCache.set(`user:${id}`, user, {
      namespace: 'users',
      ttl: 1800,
      tags: ['user', `user:${id}`]
    });
  }

  return user;
});

// Cache expensive analytics queries
export const getAnalyticsData = unstable_cache(
  async (tenantId: string, dateRange: { from: string; to: string }) => {
    const cacheKey = `analytics:${dateRange.from}:${dateRange.to}`;

    const cached = await tenantCache.get(cacheKey, {
      namespace: 'analytics',
      ttl: 3600 // 1 hour
    });

    if (cached) return cached;

    // Expensive analytics query
    const data = await prisma.$queryRaw`
      SELECT
        DATE_TRUNC('day', created_at) as date,
        COUNT(*) as count,
        SUM(amount) as revenue
      FROM transactions
      WHERE tenant_id = ${tenantId}
        AND created_at >= ${dateRange.from}::timestamp
        AND created_at <= ${dateRange.to}::timestamp
      GROUP BY DATE_TRUNC('day', created_at)
      ORDER BY date;
    `;

    await tenantCache.set(cacheKey, data, {
      namespace: 'analytics',
      ttl: 3600,
      tags: ['analytics', 'transactions']
    });

    return data;
  },
  ['analytics-data'],
  {
    revalidate: 3600, // 1 hour
    tags: ['analytics']
  }
);

// Cached fetch with automatic revalidation
export async function fetchWithCache<T>(
  url: string,
  options: RequestInit & {
    cacheKey?: string;
    cacheTtl?: number;
    tags?: string[];
  } = {}
): Promise<T> {
  const { cacheKey, cacheTtl = 3600, tags, ...fetchOptions } = options;

  if (cacheKey) {
    const cached = await tenantCache.get<T>(cacheKey, {
      namespace: 'api',
      ttl: cacheTtl,
      tags
    });

    if (cached) return cached;
  }

  const response = await fetch(url, {
    ...fetchOptions,
    next: {
      revalidate: cacheTtl,
      tags: tags || []
    }
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();

  if (cacheKey) {
    await tenantCache.set(cacheKey, data, {
      namespace: 'api',
      ttl: cacheTtl,
      tags
    });
  }

  return data;
}
```

### 4. In-Memory Cache Layer

```typescript
// lib/cache/memory-cache.ts
interface CacheEntry<T> {
  value: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
}

class LRUCache<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private maxSize: number;
  private defaultTtl: number;

  constructor(maxSize = 1000, defaultTtl = 300) {
    this.maxSize = maxSize;
    this.defaultTtl = defaultTtl;
  }

  get(key: string): T | null {
    const entry = this.cache.get(key);

    if (!entry) return null;

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl * 1000) {
      this.cache.delete(key);
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();

    // Move to end (most recently used)
    this.cache.delete(key);
    this.cache.set(key, entry);

    return entry.value;
  }

  set(key: string, value: T, ttl?: number): void {
    // Remove oldest entries if at capacity
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    const entry: CacheEntry<T> = {
      value,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTtl,
      accessCount: 1,
      lastAccessed: Date.now()
    };

    this.cache.set(key, entry);
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  getStats() {
    const entries = Array.from(this.cache.values());
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      averageAccessCount: entries.reduce((sum, e) => sum + e.accessCount, 0) / entries.length,
      oldestEntry: Math.min(...entries.map(e => e.timestamp)),
      newestEntry: Math.max(...entries.map(e => e.timestamp))
    };
  }
}

// Global memory cache instances
export const userCache = new LRUCache<any>(500, 300); // 5 minutes
export const sessionCache = new LRUCache<any>(1000, 1800); // 30 minutes
export const configCache = new LRUCache<any>(100, 3600); // 1 hour

// Memory cache wrapper with fallback to Redis
export class HybridCache<T> {
  constructor(
    private memoryCache: LRUCache<T>,
    private cacheKey: string
  ) {}

  async get(key: string): Promise<T | null> {
    // Try memory cache first
    const memoryResult = this.memoryCache.get(key);
    if (memoryResult) return memoryResult;

    // Fallback to Redis
    const redisResult = await tenantCache.get<T>(`${this.cacheKey}:${key}`);
    if (redisResult) {
      // Populate memory cache
      this.memoryCache.set(key, redisResult);
      return redisResult;
    }

    return null;
  }

  async set(key: string, value: T, ttl?: number): Promise<void> {
    // Set in both caches
    this.memoryCache.set(key, value, ttl);
    await tenantCache.set(`${this.cacheKey}:${key}`, value, {
      ttl: ttl || 3600
    });
  }

  async delete(key: string): Promise<void> {
    this.memoryCache.delete(key);
    await tenantCache.invalidate(`${this.cacheKey}:${key}`);
  }
}
```

### 5. CDN Integration & Static Asset Optimization

```typescript
// lib/cache/cdn-cache.ts
import { NextRequest, NextResponse } from 'next/server';

interface CDNConfig {
  provider: 'cloudfront' | 'vercel';
  regions: string[];
  defaultTtl: number;
  maxAge: number;
}

class CDNCacheManager {
  private config: CDNConfig;

  constructor(config: CDNConfig) {
    this.config = config;
  }

  // Generate cache headers for different asset types
  getCacheHeaders(assetType: 'static' | 'dynamic' | 'api' | 'image'): Record<string, string> {
    const headers: Record<string, string> = {};

    switch (assetType) {
      case 'static':
        headers['Cache-Control'] = 'public, max-age=31536000, immutable'; // 1 year
        headers['CDN-Cache-Control'] = 'public, max-age=31536000';
        break;

      case 'image':
        headers['Cache-Control'] = 'public, max-age=86400, stale-while-revalidate=604800'; // 1 day, 1 week stale
        headers['CDN-Cache-Control'] = 'public, max-age=86400';
        break;

      case 'api':
        headers['Cache-Control'] = 'public, max-age=300, stale-while-revalidate=60'; // 5 minutes, 1 minute stale
        headers['CDN-Cache-Control'] = 'public, max-age=300';
        break;

      case 'dynamic':
        headers['Cache-Control'] = 'public, max-age=60, stale-while-revalidate=300'; // 1 minute, 5 minutes stale
        headers['CDN-Cache-Control'] = 'public, max-age=60';
        break;
    }

    return headers;
  }

  // Add ETag support for conditional requests
  generateETag(content: string | Buffer): string {
    const crypto = require('crypto');
    return crypto.createHash('md5').update(content).digest('hex');
  }

  // Middleware for adding cache headers
  addCacheHeaders(request: NextRequest, response: NextResponse, assetType: 'static' | 'dynamic' | 'api' | 'image'): NextResponse {
    const headers = this.getCacheHeaders(assetType);

    Object.entries(headers).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    // Add Vary header for tenant-specific content
    if (request.headers.get('x-tenant-id')) {
      response.headers.set('Vary', 'x-tenant-id, Authorization');
    }

    return response;
  }

  // Invalidate CDN cache for specific paths
  async invalidateCDN(paths: string[]): Promise<void> {
    if (this.config.provider === 'cloudfront') {
      await this.invalidateCloudFront(paths);
    } else if (this.config.provider === 'vercel') {
      await this.invalidateVercel(paths);
    }
  }

  private async invalidateCloudFront(paths: string[]): Promise<void> {
    // CloudFront invalidation implementation
    const AWS = require('aws-sdk');
    const cloudfront = new AWS.CloudFront();

    const params = {
      DistributionId: process.env.CLOUDFRONT_DISTRIBUTION_ID,
      InvalidationBatch: {
        CallerReference: Date.now().toString(),
        Paths: {
          Quantity: paths.length,
          Items: paths
        }
      }
    };

    try {
      await cloudfront.createInvalidation(params).promise();
    } catch (error) {
      console.error('CloudFront invalidation failed:', error);
    }
  }

  private async invalidateVercel(paths: string[]): Promise<void> {
    // Vercel Edge Cache purging
    const response = await fetch('https://api.vercel.com/v1/edge-config/purge', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.VERCEL_TOKEN}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ paths })
    });

    if (!response.ok) {
      console.error('Vercel cache purge failed:', await response.text());
    }
  }
}

export const cdnCache = new CDNCacheManager({
  provider: (process.env.CDN_PROVIDER as 'cloudfront' | 'vercel') || 'vercel',
  regions: ['us-east-1', 'eu-west-1', 'ap-southeast-1'],
  defaultTtl: 3600,
  maxAge: 86400
});
```

### 6. Cache Monitoring & Analytics

```typescript
// lib/cache/monitoring.ts
import { redis } from './redis';
import { userCache, sessionCache, configCache } from './memory-cache';

interface CacheMetrics {
  redis: {
    hitRate: number;
    averageLatency: number;
    memoryUsage: number;
    keyCount: number;
  };
  memory: {
    userCache: any;
    sessionCache: any;
    configCache: any;
  };
  performance: {
    averageResponseTime: number;
    cacheEffectiveness: number;
  };
}

class CacheMonitor {
  private metricsHistory: CacheMetrics[] = [];
  private readonly MAX_HISTORY = 1000;

  async collectMetrics(): Promise<CacheMetrics> {
    const redisMetrics = redis.getMetrics().getStats();
    const redisInfo = await this.getRedisInfo();

    const metrics: CacheMetrics = {
      redis: {
        hitRate: redisMetrics.hitRate,
        averageLatency: redisMetrics.averageLatency,
        memoryUsage: redisInfo.used_memory,
        keyCount: redisInfo.db0_keys || 0
      },
      memory: {
        userCache: userCache.getStats(),
        sessionCache: sessionCache.getStats(),
        configCache: configCache.getStats()
      },
      performance: {
        averageResponseTime: await this.calculateAverageResponseTime(),
        cacheEffectiveness: this.calculateCacheEffectiveness(redisMetrics.hitRate)
      }
    };

    this.metricsHistory.push(metrics);
    if (this.metricsHistory.length > this.MAX_HISTORY) {
      this.metricsHistory.shift();
    }

    return metrics;
  }

  private async getRedisInfo(): Promise<any> {
    try {
      const info = await redis.client.info('memory');
      const keyspace = await redis.client.info('keyspace');

      const parsed: any = {};
      info.split('\r\n').forEach(line => {
        const [key, value] = line.split(':');
        if (key && value) {
          parsed[key] = isNaN(Number(value)) ? value : Number(value);
        }
      });

      keyspace.split('\r\n').forEach(line => {
        const match = line.match(/db(\d+):keys=(\d+)/);
        if (match) {
          parsed[`db${match[1]}_keys`] = Number(match[2]);
        }
      });

      return parsed;
    } catch (error) {
      console.error('Failed to get Redis info:', error);
      return {};
    }
  }

  private async calculateAverageResponseTime(): Promise<number> {
    // This would integrate with your existing monitoring system
    // For now, return a placeholder
    return 150; // ms
  }

  private calculateCacheEffectiveness(hitRate: number): number {
    // Cache effectiveness score based on hit rate and performance impact
    return Math.min(100, hitRate * 100 + (hitRate > 0.8 ? 20 : 0));
  }

  getMetricsHistory(): CacheMetrics[] {
    return this.metricsHistory;
  }

  async generateReport(): Promise<string> {
    const current = await this.collectMetrics();

    return `
Cache Performance Report
========================
Redis Hit Rate: ${(current.redis.hitRate * 100).toFixed(2)}%
Average Latency: ${current.redis.averageLatency.toFixed(2)}ms
Memory Usage: ${(current.redis.memoryUsage / 1024 / 1024).toFixed(2)}MB
Key Count: ${current.redis.keyCount}

Memory Cache Stats:
- User Cache: ${current.memory.userCache.size}/${current.memory.userCache.maxSize} entries
- Session Cache: ${current.memory.sessionCache.size}/${current.memory.sessionCache.maxSize} entries
- Config Cache: ${current.memory.configCache.size}/${current.memory.configCache.maxSize} entries

Performance:
- Average Response Time: ${current.performance.averageResponseTime}ms
- Cache Effectiveness: ${current.performance.cacheEffectiveness.toFixed(1)}/100
    `;
  }
}

export const cacheMonitor = new CacheMonitor();

// API endpoint for cache metrics
// app/api/admin/cache/metrics/route.ts
export async function GET() {
  try {
    const metrics = await cacheMonitor.collectMetrics();
    return Response.json(metrics);
  } catch (error) {
    return Response.json({ error: 'Failed to collect metrics' }, { status: 500 });
  }
}
```

*Built with ❤️ by Nexus-Master Agent*
*Where 125 Senior Developers Meet AI Excellence*
