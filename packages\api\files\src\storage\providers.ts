import AWS from "aws-sdk";
import { Storage as GCSStorage } from "@google-cloud/storage";
import { BlobServiceClient } from "@azure/storage-blob";
import fs from "fs";
import path from "path";
import { StorageProvider, UploadOptions, UploadResult, FileConfig } from "../types";

// Local file system storage provider
export class LocalStorageProvider implements StorageProvider {
  private uploadPath: string;
  private publicPath: string;

  constructor(config: FileConfig["storage"]["local"]) {
    if (!config) {
      throw new Error("Local storage configuration is required");
    }

    this.uploadPath = config.uploadPath;
    this.publicPath = config.publicPath;

    // Ensure directories exist
    if (!fs.existsSync(this.uploadPath)) {
      fs.mkdirSync(this.uploadPath, { recursive: true });
    }
  }

  async upload(file: Express.Multer.File, options: UploadOptions): Promise<UploadResult> {
    const fullPath = path.join(this.uploadPath, options.path);
    const directory = path.dirname(fullPath);

    // Ensure directory exists
    if (!fs.existsSync(directory)) {
      fs.mkdirSync(directory, { recursive: true });
    }

    // Write file
    await fs.promises.writeFile(fullPath, file.buffer);

    const stats = await fs.promises.stat(fullPath);
    const url = path.join(this.publicPath, options.path).replace(/\\/g, "/");

    return {
      path: options.path,
      url,
      size: stats.size,
    };
  }

  async download(filePath: string): Promise<Buffer> {
    const fullPath = path.join(this.uploadPath, filePath);
    return fs.promises.readFile(fullPath);
  }

  async delete(filePath: string): Promise<void> {
    const fullPath = path.join(this.uploadPath, filePath);
    
    if (await this.exists(filePath)) {
      await fs.promises.unlink(fullPath);
    }
  }

  async copy(sourcePath: string, destinationPath: string): Promise<void> {
    const sourceFullPath = path.join(this.uploadPath, sourcePath);
    const destFullPath = path.join(this.uploadPath, destinationPath);
    const destDirectory = path.dirname(destFullPath);

    // Ensure destination directory exists
    if (!fs.existsSync(destDirectory)) {
      fs.mkdirSync(destDirectory, { recursive: true });
    }

    await fs.promises.copyFile(sourceFullPath, destFullPath);
  }

  async move(sourcePath: string, destinationPath: string): Promise<void> {
    await this.copy(sourcePath, destinationPath);
    await this.delete(sourcePath);
  }

  async getSignedUrl(filePath: string, operation: "read" | "write", expiresIn?: number): Promise<string> {
    // For local storage, return the public URL
    return path.join(this.publicPath, filePath).replace(/\\/g, "/");
  }

  async exists(filePath: string): Promise<boolean> {
    const fullPath = path.join(this.uploadPath, filePath);
    
    try {
      await fs.promises.access(fullPath);
      return true;
    } catch {
      return false;
    }
  }

  async getMetadata(filePath: string): Promise<any> {
    const fullPath = path.join(this.uploadPath, filePath);
    const stats = await fs.promises.stat(fullPath);

    return {
      size: stats.size,
      lastModified: stats.mtime,
      created: stats.birthtime,
    };
  }
}

// Amazon S3 storage provider
export class S3StorageProvider implements StorageProvider {
  private s3: AWS.S3;
  private bucket: string;

  constructor(config: FileConfig["storage"]["s3"]) {
    if (!config) {
      throw new Error("S3 storage configuration is required");
    }

    this.bucket = config.bucket;

    this.s3 = new AWS.S3({
      region: config.region,
      accessKeyId: config.accessKeyId,
      secretAccessKey: config.secretAccessKey,
      endpoint: config.endpoint,
      s3ForcePathStyle: config.forcePathStyle,
    });
  }

  async upload(file: Express.Multer.File, options: UploadOptions): Promise<UploadResult> {
    const params: AWS.S3.PutObjectRequest = {
      Bucket: this.bucket,
      Key: options.path,
      Body: file.buffer,
      ContentType: options.contentType,
      Metadata: options.metadata,
      ACL: options.acl || "private",
      StorageClass: options.storageClass || "STANDARD",
    };

    if (options.encryption) {
      params.ServerSideEncryption = "AES256";
    }

    const result = await this.s3.upload(params).promise();

    return {
      path: options.path,
      url: result.Location,
      size: file.size,
      etag: result.ETag,
      versionId: result.VersionId,
    };
  }

  async download(filePath: string): Promise<Buffer> {
    const params: AWS.S3.GetObjectRequest = {
      Bucket: this.bucket,
      Key: filePath,
    };

    const result = await this.s3.getObject(params).promise();
    return result.Body as Buffer;
  }

  async delete(filePath: string): Promise<void> {
    const params: AWS.S3.DeleteObjectRequest = {
      Bucket: this.bucket,
      Key: filePath,
    };

    await this.s3.deleteObject(params).promise();
  }

  async copy(sourcePath: string, destinationPath: string): Promise<void> {
    const params: AWS.S3.CopyObjectRequest = {
      Bucket: this.bucket,
      CopySource: `${this.bucket}/${sourcePath}`,
      Key: destinationPath,
    };

    await this.s3.copyObject(params).promise();
  }

  async move(sourcePath: string, destinationPath: string): Promise<void> {
    await this.copy(sourcePath, destinationPath);
    await this.delete(sourcePath);
  }

  async getSignedUrl(filePath: string, operation: "read" | "write", expiresIn: number = 3600): Promise<string> {
    const s3Operation = operation === "read" ? "getObject" : "putObject";
    
    const params = {
      Bucket: this.bucket,
      Key: filePath,
      Expires: expiresIn,
    };

    return this.s3.getSignedUrl(s3Operation, params);
  }

  async exists(filePath: string): Promise<boolean> {
    try {
      const params: AWS.S3.HeadObjectRequest = {
        Bucket: this.bucket,
        Key: filePath,
      };

      await this.s3.headObject(params).promise();
      return true;
    } catch (error: any) {
      if (error.code === "NotFound") {
        return false;
      }
      throw error;
    }
  }

  async getMetadata(filePath: string): Promise<any> {
    const params: AWS.S3.HeadObjectRequest = {
      Bucket: this.bucket,
      Key: filePath,
    };

    const result = await this.s3.headObject(params).promise();

    return {
      size: result.ContentLength,
      lastModified: result.LastModified,
      etag: result.ETag,
      contentType: result.ContentType,
      metadata: result.Metadata,
      storageClass: result.StorageClass,
      versionId: result.VersionId,
    };
  }
}

// Google Cloud Storage provider
export class GCSStorageProvider implements StorageProvider {
  private storage: GCSStorage;
  private bucket: any;

  constructor(config: FileConfig["storage"]["gcs"]) {
    if (!config) {
      throw new Error("GCS storage configuration is required");
    }

    this.storage = new GCSStorage({
      projectId: config.projectId,
      keyFilename: config.keyFilename,
    });

    this.bucket = this.storage.bucket(config.bucket);
  }

  async upload(file: Express.Multer.File, options: UploadOptions): Promise<UploadResult> {
    const gcsFile = this.bucket.file(options.path);

    const stream = gcsFile.createWriteStream({
      metadata: {
        contentType: options.contentType,
        metadata: options.metadata,
      },
      public: options.acl === "public-read",
    });

    return new Promise((resolve, reject) => {
      stream.on("error", reject);
      stream.on("finish", async () => {
        const [metadata] = await gcsFile.getMetadata();
        
        resolve({
          path: options.path,
          url: `gs://${this.bucket.name}/${options.path}`,
          size: parseInt(metadata.size),
          etag: metadata.etag,
        });
      });

      stream.end(file.buffer);
    });
  }

  async download(filePath: string): Promise<Buffer> {
    const file = this.bucket.file(filePath);
    const [buffer] = await file.download();
    return buffer;
  }

  async delete(filePath: string): Promise<void> {
    const file = this.bucket.file(filePath);
    await file.delete();
  }

  async copy(sourcePath: string, destinationPath: string): Promise<void> {
    const sourceFile = this.bucket.file(sourcePath);
    const destFile = this.bucket.file(destinationPath);
    
    await sourceFile.copy(destFile);
  }

  async move(sourcePath: string, destinationPath: string): Promise<void> {
    await this.copy(sourcePath, destinationPath);
    await this.delete(sourcePath);
  }

  async getSignedUrl(filePath: string, operation: "read" | "write", expiresIn: number = 3600): Promise<string> {
    const file = this.bucket.file(filePath);
    const action = operation === "read" ? "read" : "write";
    
    const [url] = await file.getSignedUrl({
      action,
      expires: Date.now() + expiresIn * 1000,
    });

    return url;
  }

  async exists(filePath: string): Promise<boolean> {
    const file = this.bucket.file(filePath);
    const [exists] = await file.exists();
    return exists;
  }

  async getMetadata(filePath: string): Promise<any> {
    const file = this.bucket.file(filePath);
    const [metadata] = await file.getMetadata();

    return {
      size: parseInt(metadata.size),
      lastModified: new Date(metadata.updated),
      created: new Date(metadata.timeCreated),
      etag: metadata.etag,
      contentType: metadata.contentType,
      metadata: metadata.metadata,
      storageClass: metadata.storageClass,
    };
  }
}

// Factory function to create storage provider
export function createStorageProvider(config: FileConfig["storage"]): StorageProvider {
  switch (config.provider) {
    case "local":
      return new LocalStorageProvider(config.local);
    case "s3":
      return new S3StorageProvider(config.s3);
    case "gcs":
      return new GCSStorageProvider(config.gcs);
    case "azure":
      // TODO: Implement Azure Blob Storage provider
      throw new Error("Azure Blob Storage provider not implemented yet");
    default:
      throw new Error(`Unsupported storage provider: ${config.provider}`);
  }
}
