import { EventEmitter } from "events";
import * as Y from "yjs";
import { User } from "@nexus/types";
import { 
  CollaborativeDocument, 
  DocumentUpdate, 
  CursorPosition, 
  SelectionRange,
  SocketContext 
} from "./types";

export class CollaborationManager extends EventEmitter {
  private documents: Map<string, CollaborativeDocument> = new Map();
  private yDocuments: Map<string, Y.Doc> = new Map();
  private maxDocumentSize: number;
  private maxParticipants: number;
  private operationTimeout: number;
  private snapshotInterval: number;

  constructor(config: {
    maxDocumentSize?: number;
    maxParticipants?: number;
    operationTimeout?: number;
    snapshotInterval?: number;
  } = {}) {
    super();
    this.maxDocumentSize = config.maxDocumentSize || 10 * 1024 * 1024; // 10MB
    this.maxParticipants = config.maxParticipants || 50;
    this.operationTimeout = config.operationTimeout || 5000; // 5 seconds
    this.snapshotInterval = config.snapshotInterval || 60000; // 1 minute

    // Start snapshot interval
    setInterval(() => {
      this.createSnapshots();
    }, this.snapshotInterval);
  }

  // Create or get document
  getDocument(documentId: string, type: string = "text"): CollaborativeDocument {
    let document = this.documents.get(documentId);
    
    if (!document) {
      document = {
        id: documentId,
        type: type as any,
        content: "",
        version: 0,
        participants: new Map(),
        operations: [],
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      this.documents.set(documentId, document);

      // Create Yjs document
      const yDoc = new Y.Doc();
      this.yDocuments.set(documentId, yDoc);

      // Listen for Yjs updates
      yDoc.on("update", (update: Uint8Array) => {
        this.handleYjsUpdate(documentId, update);
      });
    }

    return document;
  }

  // Join document collaboration
  async joinDocument(
    documentId: string,
    context: SocketContext
  ): Promise<CollaborativeDocument> {
    const document = this.getDocument(documentId);
    const { user } = context;

    // Check participant limit
    if (document.participants.size >= this.maxParticipants) {
      throw new Error("Document has reached maximum participants");
    }

    // Add participant
    document.participants.set(user.id, {
      userId: user.id,
      user: {
        id: user.id,
        name: user.name,
        avatar: user.avatar,
      },
      joinedAt: new Date(),
      lastActivity: new Date(),
    });

    document.updatedAt = new Date();

    // Emit join event
    this.emit("document:join", {
      documentId,
      userId: user.id,
      user,
      participantCount: document.participants.size,
    });

    return document;
  }

  // Leave document collaboration
  async leaveDocument(documentId: string, userId: string): Promise<void> {
    const document = this.documents.get(documentId);
    if (!document) return;

    // Remove participant
    const participant = document.participants.get(userId);
    if (participant) {
      document.participants.delete(userId);
      document.updatedAt = new Date();

      // Emit leave event
      this.emit("document:leave", {
        documentId,
        userId,
        user: participant.user,
        participantCount: document.participants.size,
      });

      // Clean up empty documents
      if (document.participants.size === 0) {
        this.cleanupDocument(documentId);
      }
    }
  }

  // Apply document operation
  async applyOperation(
    documentId: string,
    operation: DocumentUpdate,
    context: SocketContext
  ): Promise<void> {
    const document = this.documents.get(documentId);
    if (!document) {
      throw new Error("Document not found");
    }

    const participant = document.participants.get(context.user.id);
    if (!participant) {
      throw new Error("User not participating in document");
    }

    // Validate operation
    this.validateOperation(document, operation);

    // Apply operation to Yjs document
    const yDoc = this.yDocuments.get(documentId);
    if (yDoc) {
      this.applyOperationToYjs(yDoc, operation);
    }

    // Update document
    document.operations.push(operation);
    document.version++;
    document.updatedAt = new Date();
    participant.lastActivity = new Date();

    // Emit operation event
    this.emit("document:operation", {
      documentId,
      operation,
      version: document.version,
    });
  }

  // Update cursor position
  updateCursor(
    documentId: string,
    cursor: CursorPosition,
    context: SocketContext
  ): void {
    const document = this.documents.get(documentId);
    if (!document) return;

    const participant = document.participants.get(context.user.id);
    if (!participant) return;

    participant.cursor = cursor;
    participant.lastActivity = new Date();

    // Emit cursor update
    this.emit("document:cursor", {
      documentId,
      cursor,
    });
  }

  // Update selection range
  updateSelection(
    documentId: string,
    selection: SelectionRange,
    context: SocketContext
  ): void {
    const document = this.documents.get(documentId);
    if (!document) return;

    const participant = document.participants.get(context.user.id);
    if (!participant) return;

    participant.selection = selection;
    participant.lastActivity = new Date();

    // Emit selection update
    this.emit("document:selection", {
      documentId,
      selection,
    });
  }

  // Get document participants
  getParticipants(documentId: string): Array<{
    userId: string;
    user: Pick<User, "id" | "name" | "avatar">;
    cursor?: CursorPosition;
    selection?: SelectionRange;
    joinedAt: Date;
    lastActivity: Date;
  }> {
    const document = this.documents.get(documentId);
    if (!document) return [];

    return Array.from(document.participants.values());
  }

  // Get document state
  getDocumentState(documentId: string): {
    content: string;
    version: number;
    participants: number;
    lastUpdate: Date;
  } | null {
    const document = this.documents.get(documentId);
    if (!document) return null;

    return {
      content: document.content,
      version: document.version,
      participants: document.participants.size,
      lastUpdate: document.updatedAt,
    };
  }

  // Validate operation
  private validateOperation(document: CollaborativeDocument, operation: DocumentUpdate): void {
    // Check document size
    if (document.content.length > this.maxDocumentSize) {
      throw new Error("Document size limit exceeded");
    }

    // Validate operation position
    if (operation.operation.position < 0 || operation.operation.position > document.content.length) {
      throw new Error("Invalid operation position");
    }

    // Validate operation content
    if (operation.operation.type === "insert" && !operation.operation.content) {
      throw new Error("Insert operation requires content");
    }

    if (operation.operation.type === "delete" && !operation.operation.length) {
      throw new Error("Delete operation requires length");
    }
  }

  // Apply operation to Yjs document
  private applyOperationToYjs(yDoc: Y.Doc, operation: DocumentUpdate): void {
    const yText = yDoc.getText("content");
    
    switch (operation.operation.type) {
      case "insert":
        if (operation.operation.content) {
          yText.insert(operation.operation.position, operation.operation.content);
        }
        break;
      
      case "delete":
        if (operation.operation.length) {
          yText.delete(operation.operation.position, operation.operation.length);
        }
        break;
      
      case "format":
        if (operation.operation.length && operation.operation.attributes) {
          yText.format(
            operation.operation.position,
            operation.operation.length,
            operation.operation.attributes
          );
        }
        break;
    }
  }

  // Handle Yjs update
  private handleYjsUpdate(documentId: string, update: Uint8Array): void {
    const document = this.documents.get(documentId);
    const yDoc = this.yDocuments.get(documentId);
    
    if (document && yDoc) {
      // Update document content from Yjs
      const yText = yDoc.getText("content");
      document.content = yText.toString();
      document.updatedAt = new Date();

      // Emit content update
      this.emit("document:content", {
        documentId,
        content: document.content,
        version: document.version,
        update,
      });
    }
  }

  // Create snapshots
  private createSnapshots(): void {
    for (const [documentId, document] of this.documents.entries()) {
      if (document.participants.size > 0) {
        this.emit("document:snapshot", {
          documentId,
          content: document.content,
          version: document.version,
          timestamp: new Date(),
        });
      }
    }
  }

  // Cleanup document
  private cleanupDocument(documentId: string): void {
    const document = this.documents.get(documentId);
    if (document && document.participants.size === 0) {
      // Create final snapshot
      this.emit("document:snapshot", {
        documentId,
        content: document.content,
        version: document.version,
        timestamp: new Date(),
        final: true,
      });

      // Remove from memory
      this.documents.delete(documentId);
      
      const yDoc = this.yDocuments.get(documentId);
      if (yDoc) {
        yDoc.destroy();
        this.yDocuments.delete(documentId);
      }

      this.emit("document:cleanup", { documentId });
    }
  }

  // Get collaboration statistics
  getStats(): {
    activeDocuments: number;
    totalParticipants: number;
    totalOperations: number;
    averageParticipants: number;
  } {
    let totalParticipants = 0;
    let totalOperations = 0;

    for (const document of this.documents.values()) {
      totalParticipants += document.participants.size;
      totalOperations += document.operations.length;
    }

    return {
      activeDocuments: this.documents.size,
      totalParticipants,
      totalOperations,
      averageParticipants: this.documents.size > 0 ? totalParticipants / this.documents.size : 0,
    };
  }

  // Destroy collaboration manager
  destroy(): void {
    // Destroy all Yjs documents
    for (const yDoc of this.yDocuments.values()) {
      yDoc.destroy();
    }

    this.documents.clear();
    this.yDocuments.clear();
    this.removeAllListeners();
  }
}
