// Database schema exports and utilities
/// <reference types="node" />

export * from "@prisma/client";

// Re-export Prisma types for convenience
export type {
    Session, SubscriptionPlan, Tenant, TenantStatus, User, UserRole,
    UserStatus, Workspace
} from "@prisma/client";

// Database configuration
export const DATABASE_CONFIG = {
  maxConnections: parseInt(process.env.DATABASE_MAX_CONNECTIONS || "100"),
  connectionTimeout: parseInt(process.env.DATABASE_CONNECTION_TIMEOUT || "30000"),
  idleTimeout: parseInt(process.env.DATABASE_IDLE_TIMEOUT || "600000"),
  logLevel: process.env.DATABASE_LOG_LEVEL || "info",
} as const;

// Multi-tenant utilities
export const TENANT_CONTEXT_KEY = "app.current_tenant_id";

export interface TenantContext {
  tenantId: string;
  userId?: string;
}

// Database connection utilities
export const getDatabaseUrl = (): string => {
  const url = process.env.DATABASE_URL;
  if (!url) {
    throw new Error("DATABASE_URL environment variable is required");
  }
  return url;
};

// Row-level security utilities
export const setTenantContext = (tenantId: string): string => {
  return `SET LOCAL ${TENANT_CONTEXT_KEY} = '${tenantId}'`;
};

export const getTenantContext = (): string => {
  return `current_setting('${TENANT_CONTEXT_KEY}')::text`;
};

// Migration utilities
export const MIGRATION_SCRIPTS = {
  enableRLS: `
    -- Enable Row Level Security on tenant-isolated tables
    ALTER TABLE users ENABLE ROW LEVEL SECURITY;
    ALTER TABLE workspaces ENABLE ROW LEVEL SECURITY;
    ALTER TABLE sessions ENABLE ROW LEVEL SECURITY;
  `,
  createRLSPolicies: `
    -- Create RLS policies for tenant isolation
    CREATE POLICY tenant_isolation_users ON users
      FOR ALL TO authenticated
      USING (tenant_id = current_setting('app.current_tenant_id')::text);

    CREATE POLICY tenant_isolation_workspaces ON workspaces
      FOR ALL TO authenticated
      USING (tenant_id = current_setting('app.current_tenant_id')::text);

    CREATE POLICY tenant_isolation_sessions ON sessions
      FOR ALL TO authenticated
      USING (tenant_id = current_setting('app.current_tenant_id')::text);
  `,
  createIndexes: `
    -- Create performance indexes
    CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
    CREATE INDEX IF NOT EXISTS idx_workspaces_tenant_id ON workspaces(tenant_id);
    CREATE INDEX IF NOT EXISTS idx_sessions_tenant_id ON sessions(tenant_id);
    CREATE INDEX IF NOT EXISTS idx_tenants_slug ON tenants(slug);
    CREATE INDEX IF NOT EXISTS idx_tenants_domain ON tenants(domain);
    CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions(token);
    CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);
  `,
} as const;
