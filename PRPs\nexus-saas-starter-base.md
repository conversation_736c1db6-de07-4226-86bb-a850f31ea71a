# NEXUS SaaS Starter - Base Implementation PRP

**PRP Name**: NEXUS SaaS Starter - Multi-Tenant Enterprise Boilerplate  
**Version**: 1.0  
**Date**: July 18, 2025  
**Type**: Base Implementation PRP  
**Framework**: Next.js 15.4+ / React 19 / TypeScript 5.8+  

---

## Purpose

Template optimized for AI agents to implement features within the NEXUS SaaS Starter with sufficient context and self-validation capabilities to achieve working code through iterative refinement.

## Core Principles

1. **Context is King**: Include ALL necessary documentation, examples, and caveats
2. **Validation Loops**: Provide executable tests/lints the AI can run and fix
3. **Information Dense**: Use keywords and patterns from the codebase
4. **Progressive Success**: Start simple, validate, then enhance
5. **Production-Ready First**: Every solution must be deployment-ready, not proof-of-concept
6. **Multi-Tenant Architecture**: Every component designed for enterprise multi-tenancy

---

## Goal

Build a comprehensive, enterprise-grade multi-tenant SaaS boilerplate that enables developers to launch production-ready SaaS applications with authentication, billing, compliance, and analytics built-in.

## Why

- **Time to Market**: Reduce SaaS development time from 6-12 months to 2-4 weeks
- **Enterprise Readiness**: 100% compliance with SOC 2, GDPR, HIPAA requirements
- **Developer Experience**: Minimize setup time, maximize productivity
- **Performance**: Sub-200ms API response times at 10,000+ concurrent users
- **Security**: Zero critical security vulnerabilities
- **Scalability**: Handle enterprise-scale workloads from day one

## What

A complete multi-tenant SaaS foundation with:
- Multi-tenant authentication and authorization
- Subscription billing and payment management
- Workspace and team management
- Compliance and audit logging
- Analytics and reporting
- Enterprise security features
- Modern developer experience

### Success Criteria

- [ ] Authentication system with multi-tenant support
- [ ] Subscription billing integration with Stripe
- [ ] Workspace and team management functionality
- [ ] Database schema with proper multi-tenant isolation
- [ ] API endpoints with proper authentication and authorization
- [ ] Frontend components with modern UI/UX
- [ ] Comprehensive test coverage (>90%)
- [ ] Security audit passing with zero critical vulnerabilities
- [ ] Performance benchmarks meeting <200ms response times
- [ ] Complete documentation and deployment guides

---

## All Needed Context

### Documentation & References

```yaml
# MUST READ - Include these in your context window
- url: https://nextjs.org/docs/app
  why: Next.js 15.4+ App Router patterns and Server Components
  critical: Server Components architecture and data fetching patterns

- url: https://react.dev/reference/react
  why: React 19 features and best practices
  critical: New React 19 features and concurrent features

- url: https://www.typescriptlang.org/docs/handbook/typescript-in-5-minutes.html
  why: TypeScript 5.8+ features and strict mode configuration
  critical: Type safety and strict mode requirements

- url: https://tailwindcss.com/docs/installation
  why: Tailwind CSS 4.1.11+ configuration and utility classes
  critical: Modern CSS patterns and responsive design

- url: https://better-auth.com/docs/introduction
  why: Modern authentication patterns and multi-tenant support
  critical: Authentication flows and security best practices

- url: https://zod.dev/
  why: Production-proven validation with extensive ecosystem support
  critical: Input validation and schema definition

- url: https://stripe.com/docs/billing/subscriptions
  why: Subscription billing implementation patterns
  critical: Webhook handling and subscription lifecycle

- url: https://www.prisma.io/docs/getting-started
  why: Database ORM with type safety and multi-tenant patterns
  critical: Multi-tenant database design and migrations

- file: PROJECT_DOCUMENTATION/01-PRODUCT_REQUIREMENTS_DOCUMENT.md
  why: Complete product requirements and business context
  critical: Success criteria and feature specifications

- file: PROJECT_DOCUMENTATION/02-TECHNICAL_ARCHITECTURE_DOCUMENT.md
  why: Technical architecture and system design
  critical: Multi-tenant architecture patterns and technology stack

- file: PROJECT_DOCUMENTATION/03-AGILE_PROJECT_PLAN.md
  why: Development methodology and sprint planning
  critical: Implementation phases and milestone tracking

- file: package.json
  why: Current dependencies and build configuration
  critical: Technology stack versions and compatibility

- file: next.config.ts
  why: Next.js configuration and optimization settings
  critical: Build optimization and deployment configuration

- file: tsconfig.json
  why: TypeScript configuration and strict mode settings
  critical: Type checking rules and compiler options
```

### Current Codebase Tree

```bash
saas-starter-cursor-temp/
├── .cursor/
│   └── rules/
│       └── nexus-master.mdc
├── .github/
│   └── chatmodes/
│       └── nexus-master.chatmode.md
├── .nexus-core/
│   ├── enforcement-rules/
│   ├── templates-essential/
│   ├── commands.yaml
│   ├── config.yaml
│   ├── context-memory.yaml
│   ├── nexus-capabilities.yaml
│   ├── nexus-master.yaml
│   └── validation.yaml
├── .ref/
├── .vscode/
├── PROJECT_DOCUMENTATION/
│   ├── 01-PRODUCT_REQUIREMENTS_DOCUMENT.md
│   ├── 02-TECHNICAL_ARCHITECTURE_DOCUMENT.md
│   └── 03-AGILE_PROJECT_PLAN.md
├── public/
│   ├── file.svg
│   ├── globe.svg
│   ├── next.svg
│   ├── vercel.svg
│   └── window.svg
├── src/
│   ├── app/
│   │   ├── favicon.ico
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── components/
│   │   └── ui/ (complete shadcn/ui components)
│   ├── hooks/
│   │   └── use-mobile.ts
│   └── lib/
│       └── utils.ts
├── components.json
├── eslint.config.mjs
├── next.config.ts
├── package.json
├── pnpm-lock.yaml
├── pnpm-workspace.yaml
├── postcss.config.mjs
├── README.md
└── tsconfig.json
```

### Desired Codebase Tree with Multi-Tenant Architecture

```bash
saas-starter-cursor-temp/
├── src/
│   ├── app/
│   │   ├── (auth)/
│   │   │   ├── login/
│   │   │   ├── register/
│   │   │   ├── forgot-password/
│   │   │   └── verify-email/
│   │   ├── (dashboard)/
│   │   │   ├── [workspaceId]/
│   │   │   │   ├── dashboard/
│   │   │   │   ├── settings/
│   │   │   │   ├── billing/
│   │   │   │   ├── team/
│   │   │   │   └── analytics/
│   │   │   └── workspace-selector/
│   │   ├── api/
│   │   │   ├── auth/
│   │   │   ├── workspaces/
│   │   │   ├── billing/
│   │   │   ├── users/
│   │   │   └── webhooks/
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── components/
│   │   ├── ui/ (existing shadcn/ui components)
│   │   ├── auth/
│   │   ├── billing/
│   │   ├── dashboard/
│   │   ├── workspace/
│   │   └── shared/
│   ├── lib/
│   │   ├── auth/
│   │   ├── billing/
│   │   ├── database/
│   │   ├── validation/
│   │   ├── constants/
│   │   └── utils/
│   ├── hooks/
│   │   ├── use-auth.ts
│   │   ├── use-workspace.ts
│   │   ├── use-billing.ts
│   │   └── use-mobile.ts
│   ├── types/
│   │   ├── auth.ts
│   │   ├── workspace.ts
│   │   ├── billing.ts
│   │   └── database.ts
│   └── middleware.ts
├── prisma/
│   ├── schema.prisma
│   ├── migrations/
│   └── seed.ts
├── tests/
│   ├── __tests__/
│   ├── e2e/
│   └── setup.ts
└── docs/
    ├── API.md
    ├── DEPLOYMENT.md
    └── DEVELOPMENT.md
```

### Known Gotchas of our codebase & Library Quirks

```typescript
// CRITICAL: Next.js 15.4+ App Router gotchas
// Server Components Restrictions: Cannot use browser APIs, event handlers, hooks
// Must be async functions and use import { cookies } from 'next/headers'
// Client/Server Boundary: 'use client' directive affects entire component tree
// Dynamic Imports: Use next/dynamic for client-only components (hydration issues)
// Middleware: Runs on every request - keep lightweight, avoid heavy computations
// Route Handlers: Cannot use cookies() or headers() in Server Components
// Hydration Issues: Server/client rendering mismatches cause hydration errors
// Image Optimization: Configure domains in next.config.js for external images
// Font Optimization: Use next/font for automatic font optimization
// Streaming: Suspense boundaries required for progressive loading
// Metadata: Use generateMetadata for dynamic metadata, not static objects

// CRITICAL: React 19 gotchas
// useActionState: Replaces useFormState for form actions with pending states
// useFormStatus: Must be used within form components to get submission status
// Server Components: Cannot use hooks, event handlers, or browser APIs
// Concurrent Rendering: State updates may be batched differently
// Suspense: All async components must be wrapped in Suspense boundaries
// Form Actions: Server actions must be imported and used properly in forms
// Hydration: React 19 has stricter hydration requirements
// useOptimistic: New hook for optimistic updates requires careful state management
// New JSX Runtime: Automatic JSX runtime may cause issues with older build tools

// CRITICAL: TypeScript 5.8+ strict mode gotchas
// Strict Mode: Enabled by default, catches more potential errors
// Reserved Word Errors: Cannot use 'type', 'interface', 'enum' as property names
// Module Resolution: NodeNext resolution may break existing imports
// Stricter Null Checks: More aggressive undefined checking
// Type Inference: Better inference may break existing type assertions
// Import/Export: Stricter checking of import/export statements
// Generic Constraints: More restrictive generic type constraints
// Decorator Support: Experimental decorators may not work in all contexts

// CRITICAL: better-auth gotchas
// Environment Variables: Secret keys must be set in production (BETTER_AUTH_SECRET)
// Social Providers: Each provider requires specific client ID and secret
// Session Management: Sessions stored in cookies by default, configure for production
// CSRF Protection: Trusted origins must be configured to prevent CSRF attacks
// Multi-tenant: Account linking requires careful provider trust configuration
// Rate Limiting: Enabled by default in production, configure custom rules
// Database Adapters: Each adapter has specific configuration requirements
// Cookie Configuration: Secure cookies must be enabled in production
// Callback URLs: Must match exactly with OAuth provider settings
// Plugin Dependencies: Some plugins require specific adapter configurations

// CRITICAL: Zod v4 best practices
// Schema Definition: Use method chaining for validation .string().email().min(5)
// Error Handling: Use .safeParse() for error handling without exceptions
// Type Inference: Use z.infer<typeof Schema> for TypeScript types
// Parsing Methods: Use schema.parse(value) for direct parsing
// Object Schemas: Use .strict() for unknown key rejection
// Performance: Use .parseAsync() for async validation pipelines
// Custom Validation: Use .refine() for custom validation rules
// Transformations: Use .transform() for data transformation
// Branded Types: Use .brand() for nominal typing
// Ecosystem: Native integration with Prisma, React Hook Form, tRPC

// CRITICAL: Multi-tenant architecture requirements
// Every database query must include workspace/tenant context
// All API endpoints must validate workspace access
// Database schema must support tenant isolation
// Authentication must include workspace context

// CRITICAL: Stripe integration
// Webhook endpoints must be idempotent
// Subscription lifecycle must be properly handled
// Multi-tenant billing requires proper customer segmentation

// CRITICAL: Database design
// Prisma schema must support multi-tenant patterns
// Row-level security for PostgreSQL multi-tenancy
// Proper indexing for performance at scale
```

---

## Implementation Blueprint

### Data Models and Structure

Create the core data models ensuring type safety, multi-tenant isolation, and consistency.

```typescript
// Database Schema (Prisma)
// Multi-tenant architecture with workspace isolation
// User -> Workspace -> Resources pattern
// Proper indexes for performance
// Audit logging for compliance

// Authentication Types
// User authentication with workspace context
// Role-based access control (RBAC)
// Session management with proper security

// Billing Types
// Subscription management with Stripe integration
// Usage tracking and metering
// Invoice and payment history

// Workspace Types
// Team management and collaboration
// Resource organization and permissions
// Settings and configuration
```

### List of Tasks to be Completed (Priority Order)

```yaml
Task 1: Database Schema & Multi-Tenant Foundation
MODIFY prisma/schema.prisma:
  - CREATE User model with authentication fields
  - CREATE Workspace model with multi-tenant structure
  - CREATE WorkspaceMember model for team management
  - CREATE Subscription model for billing
  - ESTABLISH proper relationships and indexes
  - CONFIGURE row-level security patterns

Task 2: Authentication System Implementation  
CREATE src/lib/auth/:
  - IMPLEMENT better-auth configuration
  - CREATE authentication middleware
  - SETUP session management
  - CONFIGURE multi-tenant authentication flows

CREATE src/app/(auth)/:
  - BUILD login/register pages
  - IMPLEMENT forgot password flow
  - CREATE email verification system
  - SETUP OAuth provider integration

Task 3: Multi-Tenant Workspace Management
CREATE src/lib/workspace/:
  - IMPLEMENT workspace creation/management
  - CREATE team member invitation system
  - SETUP role-based access control
  - BUILD workspace switching logic

CREATE src/app/(dashboard)/[workspaceId]/:
  - BUILD workspace dashboard
  - IMPLEMENT team management interface
  - CREATE workspace settings pages
  - SETUP workspace-specific routing

Task 4: Billing & Subscription System
CREATE src/lib/billing/:
  - IMPLEMENT Stripe integration
  - CREATE subscription management
  - SETUP webhook handling
  - BUILD usage tracking system

CREATE src/app/(dashboard)/[workspaceId]/billing/:
  - BUILD billing dashboard
  - IMPLEMENT subscription management UI
  - CREATE payment method management
  - SETUP invoice viewing and download

Task 5: API Endpoints & Backend Logic
CREATE src/app/api/:
  - IMPLEMENT authentication endpoints
  - CREATE workspace management APIs
  - BUILD billing and subscription APIs
  - SETUP webhook endpoints for Stripe

Task 6: Frontend Components & User Interface
CREATE src/components/:
  - BUILD authentication components
  - CREATE workspace management UI
  - IMPLEMENT billing components
  - SETUP dashboard layouts and navigation

Task 7: Middleware & Security
CREATE src/middleware.ts:
  - IMPLEMENT authentication middleware
  - CREATE workspace access validation
  - SETUP CSRF protection
  - CONFIGURE security headers

Task 8: Testing & Quality Assurance
CREATE tests/:
  - IMPLEMENT unit tests for all components
  - CREATE integration tests for API endpoints
  - BUILD end-to-end tests for user flows
  - SETUP performance testing

Task 9: Documentation & Deployment
CREATE docs/:
  - WRITE API documentation
  - CREATE deployment guides
  - BUILD developer documentation
  - SETUP monitoring and logging
```

### Per Task Pseudocode

```typescript
// Task 1: Database Schema
// Pseudocode with CRITICAL multi-tenant details
model User {
  id              String   @id @default(cuid())
  email           String   @unique
  name            String?
  avatar          String?
  emailVerified   DateTime?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // Multi-tenant relationships
  workspaceMembers WorkspaceMember[]
  
  @@map("users")
}

model Workspace {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  avatar      String?
  plan        String   @default("free")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Multi-tenant relationships
  members      WorkspaceMember[]
  subscription Subscription?
  
  @@map("workspaces")
}

// Task 2: Authentication Implementation
// PATTERN: Use better-auth with workspace context
export const authConfig = {
  database: {
    provider: 'prisma',
    client: prisma
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    jwt: async ({ token, user, account }) => {
      // CRITICAL: Include workspace context in JWT
      if (user) {
        token.workspaces = await getUserWorkspaces(user.id)
      }
      return token
    },
    session: async ({ session, token }) => {
      // CRITICAL: Pass workspace context to session
      session.user.workspaces = token.workspaces
      return session
    }
  }
}

// Task 3: Workspace Management
// PATTERN: Multi-tenant workspace switching
export async function switchWorkspace(workspaceId: string) {
  // CRITICAL: Validate user has access to workspace
  const hasAccess = await validateWorkspaceAccess(workspaceId)
  if (!hasAccess) throw new Error('Unauthorized')
  
  // PATTERN: Update session with new workspace context
  const session = await getSession()
  session.activeWorkspace = workspaceId
  await updateSession(session)
}
```

### Integration Points

```yaml
DATABASE:
  - provider: "PostgreSQL with Prisma ORM"
  - migrations: "prisma migrate dev for development"
  - client: "import { prisma } from '@/lib/database'"
  - pattern: "Multi-tenant queries with workspace context"

AUTHENTICATION:
  - provider: "better-auth with multi-tenant support"
  - session: "JWT tokens with workspace context"
  - middleware: "Authentication validation on protected routes"
  - pattern: "Workspace-aware authentication flows"

BILLING:
  - provider: "Stripe for subscription management"
  - webhooks: "Idempotent webhook handling"
  - client: "import { stripe } from '@/lib/billing'"
  - pattern: "Multi-tenant billing with workspace isolation"

FRONTEND:
  - framework: "Next.js 15.4+ App Router"
  - components: "shadcn/ui component library"
  - styling: "Tailwind CSS 4.0+ utility classes"
  - pattern: "Server Components with client interactivity"

CONFIG:
  - environment: ".env.local for development"
  - pattern: "NEXT_PUBLIC_* for client-side variables"
  - secrets: "Private keys for server-side only"
```

---

## Validation Loop

### Level 1: Syntax & Style

```bash
# Run these FIRST - fix any errors before proceeding
npm run lint                    # ESLint checks
npx tsc --noEmit               # TypeScript type checking
npm run format                 # Prettier formatting (if configured)

# Expected: No errors. If errors, READ the error and fix.
```

### Level 2: Unit Tests

```typescript
// CREATE __tests__/auth.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { signIn, signOut } from '@/lib/auth'
import { LoginForm } from '@/components/auth/login-form'

describe('Authentication', () => {
  test('login form validates input correctly', async () => {
    render(<LoginForm />)
    
    const emailInput = screen.getByLabelText(/email/i)
    const passwordInput = screen.getByLabelText(/password/i)
    const submitButton = screen.getByRole('button', { name: /sign in/i })
    
    // Test validation
    fireEvent.click(submitButton)
    await waitFor(() => {
      expect(screen.getByText(/email is required/i)).toBeInTheDocument()
    })
    
    // Test successful login
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'password123' } })
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(signIn).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      })
    })
  })
})

// CREATE __tests__/workspace.test.tsx
import { createWorkspace, switchWorkspace } from '@/lib/workspace'

describe('Workspace Management', () => {
  test('creates workspace with proper validation', async () => {
    const workspace = await createWorkspace({
      name: 'Test Workspace',
      slug: 'test-workspace'
    })
    
    expect(workspace.id).toBeDefined()
    expect(workspace.name).toBe('Test Workspace')
    expect(workspace.slug).toBe('test-workspace')
  })
  
  test('validates workspace access before switching', async () => {
    const unauthorizedWorkspaceId = 'unauthorized-workspace'
    
    await expect(
      switchWorkspace(unauthorizedWorkspaceId)
    ).rejects.toThrow('Unauthorized')
  })
})
```

```bash
# Run and iterate until passing:
npm test
# If failing: Read error, understand root cause, fix code, re-run
```

### Level 3: Integration Test

```bash
# Start the development server
npm run dev

# Test authentication flow
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'

# Expected: {"success": true, "user": {...}}

# Test workspace creation
curl -X POST http://localhost:3000/api/workspaces \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"name": "Test Workspace", "slug": "test-workspace"}'

# Expected: {"success": true, "workspace": {...}}

# Test billing endpoint
curl -X POST http://localhost:3000/api/billing/create-checkout-session \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"priceId": "price_1234", "workspaceId": "workspace_123"}'

# Expected: {"success": true, "url": "https://checkout.stripe.com/..."}
```

### Level 4: End-to-End & Creative Validation

```bash
# Production build check
npm run build

# Expected: Successful build with no errors
# Common issues:
# - "Module not found" → Check import paths
# - "Hydration mismatch" → Ensure server/client render same content
# - Type errors → Run tsc to identify

# Test production build
npm run start

# Creative validation methods:
# - E2E testing with Playwright
# - Performance testing with Lighthouse
# - Security testing with OWASP tools
# - Accessibility testing with axe
# - Load testing with k6
# - Database migration testing
# - Stripe webhook testing with ngrok

# Multi-tenant specific validation
# - Test workspace isolation
# - Verify billing segregation
# - Check authentication boundaries
# - Validate database row-level security
```

---

## Final Validation Checklist

- [ ] All tests pass: `npm test`
- [ ] No linting errors: `npm run lint`
- [ ] No type errors: `npx tsc --noEmit`
- [ ] Database migrations run successfully
- [ ] Authentication flows work correctly
- [ ] Workspace creation and switching functional
- [ ] Billing integration properly configured
- [ ] API endpoints return expected responses
- [ ] Multi-tenant isolation verified
- [ ] Security headers configured
- [ ] Performance meets <200ms requirement
- [ ] Documentation updated and accurate

---

## Anti-Patterns to Avoid

- ❌ Don't create new patterns when existing ones work
- ❌ Don't skip validation because "it should work"
- ❌ Don't ignore failing tests - fix them immediately
- ❌ Don't use 'use client' unnecessarily - embrace Server Components
- ❌ Don't hardcode values that should be configuration
- ❌ Don't catch all exceptions - be specific about error handling
- ❌ Don't ignore multi-tenant context in database queries
- ❌ Don't skip workspace access validation
- ❌ Don't implement authentication without proper security measures
- ❌ Don't handle webhooks without idempotency
- ❌ Don't deploy without proper monitoring and logging
- ❌ Don't ignore performance implications of database queries
- ❌ Don't skip compliance and audit requirements
- ❌ Don't implement features without proper testing coverage

---

**PRP Quality Score: 9/10**

This PRP provides comprehensive context for implementing a production-ready, multi-tenant SaaS application with enterprise-grade security, performance, and scalability. The detailed implementation blueprint, validation loops, and anti-patterns should enable successful one-pass implementation with minimal debugging cycles.
