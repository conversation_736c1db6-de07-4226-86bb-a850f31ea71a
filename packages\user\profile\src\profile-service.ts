import { UserProfile, ProfileUpdateData, PreferencesUpdateData, PrivacyUpdateData, ProfileStats } from "./profile-types";
import { validateProfileUpdate, validatePreferencesUpdate, validatePrivacyUpdate } from "./profile-validation";

export class ProfileService {
  private userId: string;
  private tenantId: string;

  constructor(userId: string, tenantId: string) {
    this.userId = userId;
    this.tenantId = tenantId;
  }

  // Get user profile
  async getProfile(): Promise<UserProfile | null> {
    try {
      const response = await fetch(`/api/profile/${this.userId}`, {
        headers: {
          "x-tenant-id": this.tenantId,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch profile");
      }

      return response.json();
    } catch (error) {
      console.error("Error fetching profile:", error);
      return null;
    }
  }

  // Update profile information
  async updateProfile(data: ProfileUpdateData): Promise<UserProfile> {
    const validatedData = validateProfileUpdate(data);

    const response = await fetch(`/api/profile/${this.userId}`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify(validatedData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to update profile");
    }

    return response.json();
  }

  // Update preferences
  async updatePreferences(data: PreferencesUpdateData): Promise<UserProfile> {
    const validatedData = validatePreferencesUpdate(data);

    const response = await fetch(`/api/profile/${this.userId}/preferences`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify(validatedData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to update preferences");
    }

    return response.json();
  }

  // Update privacy settings
  async updatePrivacy(data: PrivacyUpdateData): Promise<UserProfile> {
    const validatedData = validatePrivacyUpdate(data);

    const response = await fetch(`/api/profile/${this.userId}/privacy`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify(validatedData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to update privacy settings");
    }

    return response.json();
  }

  // Get profile statistics
  async getProfileStats(): Promise<ProfileStats> {
    const response = await fetch(`/api/profile/${this.userId}/stats`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch profile stats");
    }

    return response.json();
  }

  // Check username availability
  async checkUsernameAvailability(username: string): Promise<boolean> {
    const response = await fetch(`/api/profile/username/check?username=${encodeURIComponent(username)}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to check username availability");
    }

    const result = await response.json();
    return result.available;
  }

  // Delete profile (GDPR compliance)
  async deleteProfile(): Promise<void> {
    const response = await fetch(`/api/profile/${this.userId}`, {
      method: "DELETE",
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to delete profile");
    }
  }

  // Export profile data (GDPR compliance)
  async exportProfileData(): Promise<Blob> {
    const response = await fetch(`/api/profile/${this.userId}/export`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to export profile data");
    }

    return response.blob();
  }

  // Calculate profile completion percentage
  calculateProfileCompletion(profile: UserProfile): number {
    const fields = [
      profile.firstName,
      profile.lastName,
      profile.bio,
      profile.location,
      profile.company,
      profile.jobTitle,
      profile.image,
    ];

    const completedFields = fields.filter(field => field && field.trim() !== "").length;
    return Math.round((completedFields / fields.length) * 100);
  }
}

export const createProfileService = (userId: string, tenantId: string): ProfileService => {
  return new ProfileService(userId, tenantId);
};
