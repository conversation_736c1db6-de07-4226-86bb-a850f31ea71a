import passport from "passport";
import { Strategy as GoogleStrategy } from "passport-google-oauth20";
import { Strategy as GitHubStrategy } from "passport-github2";
import { Strategy as MicrosoftStrategy } from "passport-microsoft";
import { Strategy as SlackStrategy } from "passport-slack-oauth2";
import { OAuthProvider, OAuthUser, IntegrationsConfig } from "../types";

export class OAuthManager {
  private config: IntegrationsConfig["oauth"];
  private providers: Map<string, OAuthProvider> = new Map();

  constructor(config: IntegrationsConfig["oauth"]) {
    this.config = config;
    this.initializeProviders();
    this.setupPassport();
  }

  // Initialize OAuth providers
  private initializeProviders(): void {
    Object.entries(this.config.providers).forEach(([name, provider]) => {
      this.providers.set(name, provider);
    });
  }

  // Setup Passport strategies
  private setupPassport(): void {
    // Google OAuth
    if (this.providers.has("google")) {
      const googleProvider = this.providers.get("google")!;
      passport.use(
        new GoogleStrategy(
          {
            clientID: googleProvider.clientId,
            clientSecret: googleProvider.clientSecret,
            callbackURL: `${this.config.callbackUrl}/google`,
            scope: googleProvider.scopes,
          },
          async (accessToken, refreshToken, profile, done) => {
            try {
              const user = this.mapGoogleProfile(profile, accessToken, refreshToken);
              done(null, user);
            } catch (error) {
              done(error, null);
            }
          }
        )
      );
    }

    // GitHub OAuth
    if (this.providers.has("github")) {
      const githubProvider = this.providers.get("github")!;
      passport.use(
        new GitHubStrategy(
          {
            clientID: githubProvider.clientId,
            clientSecret: githubProvider.clientSecret,
            callbackURL: `${this.config.callbackUrl}/github`,
            scope: githubProvider.scopes,
          },
          async (accessToken, refreshToken, profile, done) => {
            try {
              const user = this.mapGitHubProfile(profile, accessToken, refreshToken);
              done(null, user);
            } catch (error) {
              done(error, null);
            }
          }
        )
      );
    }

    // Microsoft OAuth
    if (this.providers.has("microsoft")) {
      const microsoftProvider = this.providers.get("microsoft")!;
      passport.use(
        new MicrosoftStrategy(
          {
            clientID: microsoftProvider.clientId,
            clientSecret: microsoftProvider.clientSecret,
            callbackURL: `${this.config.callbackUrl}/microsoft`,
            scope: microsoftProvider.scopes,
          },
          async (accessToken, refreshToken, profile, done) => {
            try {
              const user = this.mapMicrosoftProfile(profile, accessToken, refreshToken);
              done(null, user);
            } catch (error) {
              done(error, null);
            }
          }
        )
      );
    }

    // Slack OAuth
    if (this.providers.has("slack")) {
      const slackProvider = this.providers.get("slack")!;
      passport.use(
        new SlackStrategy(
          {
            clientID: slackProvider.clientId,
            clientSecret: slackProvider.clientSecret,
            callbackURL: `${this.config.callbackUrl}/slack`,
            scope: slackProvider.scopes,
          },
          async (accessToken, refreshToken, profile, done) => {
            try {
              const user = this.mapSlackProfile(profile, accessToken, refreshToken);
              done(null, user);
            } catch (error) {
              done(error, null);
            }
          }
        )
      );
    }

    // Passport serialization
    passport.serializeUser((user: any, done) => {
      done(null, user);
    });

    passport.deserializeUser((user: any, done) => {
      done(null, user);
    });
  }

  // Get authorization URL
  getAuthorizationUrl(provider: string, state?: string): string {
    const providerConfig = this.providers.get(provider);
    if (!providerConfig) {
      throw new Error(`Provider ${provider} not configured`);
    }

    const params = new URLSearchParams({
      client_id: providerConfig.clientId,
      redirect_uri: providerConfig.redirectUri,
      scope: providerConfig.scopes.join(" "),
      response_type: "code",
      access_type: "offline",
      prompt: "consent",
    });

    if (state) {
      params.append("state", state);
    }

    return `${providerConfig.authorizationUrl}?${params.toString()}`;
  }

  // Exchange code for token
  async exchangeCodeForToken(
    provider: string,
    code: string,
    state?: string
  ): Promise<{
    accessToken: string;
    refreshToken?: string;
    expiresIn?: number;
    tokenType: string;
    scope?: string;
  }> {
    const providerConfig = this.providers.get(provider);
    if (!providerConfig) {
      throw new Error(`Provider ${provider} not configured`);
    }

    const params = new URLSearchParams({
      client_id: providerConfig.clientId,
      client_secret: providerConfig.clientSecret,
      code,
      grant_type: "authorization_code",
      redirect_uri: providerConfig.redirectUri,
    });

    const response = await fetch(providerConfig.tokenUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Accept: "application/json",
      },
      body: params.toString(),
    });

    if (!response.ok) {
      throw new Error(`Token exchange failed: ${response.statusText}`);
    }

    const data = await response.json();

    return {
      accessToken: data.access_token,
      refreshToken: data.refresh_token,
      expiresIn: data.expires_in,
      tokenType: data.token_type || "Bearer",
      scope: data.scope,
    };
  }

  // Refresh access token
  async refreshAccessToken(
    provider: string,
    refreshToken: string
  ): Promise<{
    accessToken: string;
    refreshToken?: string;
    expiresIn?: number;
    tokenType: string;
  }> {
    const providerConfig = this.providers.get(provider);
    if (!providerConfig) {
      throw new Error(`Provider ${provider} not configured`);
    }

    const params = new URLSearchParams({
      client_id: providerConfig.clientId,
      client_secret: providerConfig.clientSecret,
      refresh_token: refreshToken,
      grant_type: "refresh_token",
    });

    const response = await fetch(providerConfig.tokenUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Accept: "application/json",
      },
      body: params.toString(),
    });

    if (!response.ok) {
      throw new Error(`Token refresh failed: ${response.statusText}`);
    }

    const data = await response.json();

    return {
      accessToken: data.access_token,
      refreshToken: data.refresh_token || refreshToken,
      expiresIn: data.expires_in,
      tokenType: data.token_type || "Bearer",
    };
  }

  // Get user info
  async getUserInfo(provider: string, accessToken: string): Promise<OAuthUser> {
    const providerConfig = this.providers.get(provider);
    if (!providerConfig) {
      throw new Error(`Provider ${provider} not configured`);
    }

    const response = await fetch(providerConfig.userInfoUrl, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to get user info: ${response.statusText}`);
    }

    const data = await response.json();

    switch (provider) {
      case "google":
        return this.mapGoogleUserInfo(data, accessToken);
      case "github":
        return this.mapGitHubUserInfo(data, accessToken);
      case "microsoft":
        return this.mapMicrosoftUserInfo(data, accessToken);
      case "slack":
        return this.mapSlackUserInfo(data, accessToken);
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  // Profile mapping methods
  private mapGoogleProfile(profile: any, accessToken: string, refreshToken?: string): OAuthUser {
    return {
      id: profile.id,
      email: profile.emails?.[0]?.value || "",
      name: profile.displayName || "",
      avatar: profile.photos?.[0]?.value,
      provider: "google",
      providerData: {
        profile,
        accessToken,
        refreshToken,
      },
    };
  }

  private mapGitHubProfile(profile: any, accessToken: string, refreshToken?: string): OAuthUser {
    return {
      id: profile.id,
      email: profile.emails?.[0]?.value || "",
      name: profile.displayName || profile.username || "",
      avatar: profile.photos?.[0]?.value,
      provider: "github",
      providerData: {
        profile,
        accessToken,
        refreshToken,
        username: profile.username,
      },
    };
  }

  private mapMicrosoftProfile(profile: any, accessToken: string, refreshToken?: string): OAuthUser {
    return {
      id: profile.id,
      email: profile.emails?.[0]?.value || "",
      name: profile.displayName || "",
      avatar: profile.photos?.[0]?.value,
      provider: "microsoft",
      providerData: {
        profile,
        accessToken,
        refreshToken,
      },
    };
  }

  private mapSlackProfile(profile: any, accessToken: string, refreshToken?: string): OAuthUser {
    return {
      id: profile.id,
      email: profile.user?.email || "",
      name: profile.user?.name || "",
      avatar: profile.user?.image_192,
      provider: "slack",
      providerData: {
        profile,
        accessToken,
        refreshToken,
        team: profile.team,
      },
    };
  }

  // User info mapping methods
  private mapGoogleUserInfo(data: any, accessToken: string): OAuthUser {
    return {
      id: data.sub || data.id,
      email: data.email || "",
      name: data.name || "",
      avatar: data.picture,
      provider: "google",
      providerData: {
        ...data,
        accessToken,
      },
    };
  }

  private mapGitHubUserInfo(data: any, accessToken: string): OAuthUser {
    return {
      id: data.id.toString(),
      email: data.email || "",
      name: data.name || data.login || "",
      avatar: data.avatar_url,
      provider: "github",
      providerData: {
        ...data,
        accessToken,
      },
    };
  }

  private mapMicrosoftUserInfo(data: any, accessToken: string): OAuthUser {
    return {
      id: data.id,
      email: data.mail || data.userPrincipalName || "",
      name: data.displayName || "",
      avatar: data.photo,
      provider: "microsoft",
      providerData: {
        ...data,
        accessToken,
      },
    };
  }

  private mapSlackUserInfo(data: any, accessToken: string): OAuthUser {
    return {
      id: data.user?.id || data.id,
      email: data.user?.email || "",
      name: data.user?.name || "",
      avatar: data.user?.image_192,
      provider: "slack",
      providerData: {
        ...data,
        accessToken,
      },
    };
  }

  // Validate token
  async validateToken(provider: string, accessToken: string): Promise<boolean> {
    try {
      await this.getUserInfo(provider, accessToken);
      return true;
    } catch (error) {
      return false;
    }
  }

  // Revoke token
  async revokeToken(provider: string, token: string): Promise<void> {
    const revokeUrls: Record<string, string> = {
      google: "https://oauth2.googleapis.com/revoke",
      github: "https://api.github.com/applications/{client_id}/grant",
      microsoft: "https://graph.microsoft.com/v1.0/me/revokeSignInSessions",
    };

    const revokeUrl = revokeUrls[provider];
    if (!revokeUrl) {
      throw new Error(`Token revocation not supported for provider: ${provider}`);
    }

    const response = await fetch(revokeUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({ token }).toString(),
    });

    if (!response.ok) {
      throw new Error(`Token revocation failed: ${response.statusText}`);
    }
  }

  // Get available providers
  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  // Get provider configuration
  getProviderConfig(provider: string): OAuthProvider | null {
    return this.providers.get(provider) || null;
  }
}
