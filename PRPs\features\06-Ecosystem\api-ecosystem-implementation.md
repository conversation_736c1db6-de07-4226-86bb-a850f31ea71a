# NEXUS SaaS Starter - API Ecosystem Implementation

**PRP Name**: API Ecosystem - Public APIs and Developer Tools  
**Version**: 1.0  
**Date**: July 18, 2025  
**Type**: Ecosystem & Extensions Implementation PRP  
**Phase**: 06-Ecosystem  
**Framework**: Next.js 15.4+ / TypeScript 5.8+ / OpenAPI 3.1 / Multi-Tenant  

---

## Purpose

Build a comprehensive public API ecosystem that enables third-party developers to integrate with the NEXUS SaaS Starter platform. This includes REST APIs, GraphQL endpoints, webhooks, SDKs, developer tools, and comprehensive documentation to create a thriving developer ecosystem.

## Core Principles

- **Developer-First Experience**: Intuitive APIs with excellent documentation and tooling
- **Enterprise-Grade Security**: OAuth 2.0, API keys, rate limiting, and comprehensive audit trails
- **Multi-Tenant Architecture**: All APIs respect workspace isolation and tenant boundaries
- **Comprehensive Coverage**: Full CRUD operations for all major platform resources
- **Performance Optimized**: Efficient pagination, caching, and real-time capabilities
- **Standards Compliant**: OpenAPI 3.1, REST best practices, and GraphQL specifications

---

## Research & Documentation

### Context7-Verified Patterns (CRITICAL)

```yaml
# API Ecosystem Patterns (Context7 Verified)
atlassian_cloud_api:
  - url: /context7/developer_atlassian-cloud
    why: "Enterprise API ecosystem with comprehensive developer tools and documentation"
    critical: "API design patterns, developer portal, SDK generation, authentication flows"
    patterns: ["REST API design", "Developer portal", "API documentation", "SDK generation"]

google_ads_api:
  - url: /context7/developers_google_com-google-ads-api-docs
    why: "Production-scale API with developer tokens, rate limiting, and comprehensive tooling"
    critical: "Developer token management, rate limiting, API versioning, client libraries"
    patterns: ["Developer tokens", "Rate limiting", "API versioning", "Client libraries"]

jira_platform_api:
  - url: /context7/developer_atlassian-cloud-jira-platform-rest-v3
    why: "Comprehensive REST API with extensive resource coverage and developer tools"
    critical: "Resource modeling, API groups, authentication, permissions, pagination"
    patterns: ["Resource groups", "Permission system", "Pagination", "Error handling"]
```

### Current Codebase Integration Points

```typescript
// Multi-tenant Context (CRITICAL FOR API ISOLATION)
// From: PRPs/features/01-foundation/multi-tenant-database-architecture-implementation.md
interface TenantContext {
  tenantId: string;
  workspaceId: string;
  userId: string;
  permissions: string[];
}

// Authentication System (CRITICAL FOR API SECURITY)
// From: PRPs/features/01-foundation/authentication-middleware-implementation.md
interface AuthContext {
  user: User;
  session: Session;
  permissions: string[];
  apiKey?: string;
  scopes: string[];
}

// Plugin Architecture (CRITICAL FOR API EXTENSIBILITY)
// From: PRPs/features/06-Ecosystem/plugin-architecture-implementation.md
interface PluginAPI {
  registerEndpoint(path: string, handler: Function): void;
  registerWebhook(event: string, handler: Function): void;
  getWorkspaceContext(): TenantContext;
}

// Webhook System (CRITICAL FOR REAL-TIME INTEGRATION)
// From: PRPs/features/03-enterprise/webhook-system-implementation.md
interface WebhookConfig {
  id: string;
  tenantId: string;
  url: string;
  events: string[];
  secret: string;
  active: boolean;
}
```

### Technology Stack Context

```yaml
Core Framework:
  - Next.js: 15.4+ (App Router, API Routes, Server Components)
  - React: 19 (Server Components, Concurrent Features)
  - TypeScript: 5.8+ (Advanced type system, strict mode)
  - Tailwind CSS: 4.1.11+ (Styling system)

API Infrastructure:
  - OpenAPI: 3.1 (API specification and documentation)
  - GraphQL: 16+ (Query language and runtime)
  - Swagger UI: Interactive API documentation
  - Postman: API testing and collection management

Authentication & Security:
  - OAuth 2.0: Industry-standard authorization framework
  - JWT: JSON Web Tokens for stateless authentication
  - API Keys: Simple authentication for server-to-server
  - Rate Limiting: Redis-based rate limiting and throttling

Developer Tools:
  - SDK Generation: Automated client library generation
  - CLI Tools: Command-line interface for developers
  - Testing Framework: Comprehensive API testing suite
  - Documentation: Interactive docs with code examples
```

---

## Data Models and Structure

### Database Schema (Prisma)

```typescript
// API Keys and Authentication
model ApiKey {
  id          String   @id @default(cuid())
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  // API Key Identity
  name        String   // Human-readable name
  keyId       String   @unique // Public key identifier
  keyHash     String   // Hashed secret key
  keyPrefix   String   // First 8 characters for identification
  
  // API Key Configuration
  scopes      String[] // Permitted API scopes
  permissions String[] // Specific permissions
  
  // API Key Status
  isActive    Boolean  @default(true)
  lastUsedAt  DateTime?
  
  // Rate Limiting
  rateLimit   Int?     // Requests per minute
  dailyLimit  Int?     // Requests per day
  
  // Security
  allowedIPs  String[] // IP whitelist
  userAgent   String?  // Expected user agent
  
  // Metadata
  createdBy   String   // User ID who created the key
  description String?
  
  // Audit
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  expiresAt   DateTime?
  
  // Relations
  usageLogs   ApiUsageLog[]
  
  @@index([workspaceId, isActive])
  @@index([keyId])
  @@index([keyPrefix])
}

// OAuth Applications
model OAuthApplication {
  id          String   @id @default(cuid())
  workspaceId String?  // Optional: workspace-specific apps
  workspace   Workspace? @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  // Application Identity
  name        String
  clientId    String   @unique
  clientSecret String  // Hashed client secret
  
  // Application Configuration
  redirectUris String[] // Allowed redirect URIs
  scopes      String[]  // Available scopes
  grantTypes  String[]  // Allowed grant types
  
  // Application Metadata
  description String?
  website     String?
  logoUrl     String?
  
  // Application Status
  isPublic    Boolean  @default(false) // Public vs confidential client
  isActive    Boolean  @default(true)
  
  // Developer Information
  developerId String   // User ID of developer
  
  // Audit
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  tokens      OAuthToken[]
  
  @@index([clientId])
  @@index([developerId])
  @@index([workspaceId])
}

// OAuth Tokens
model OAuthToken {
  id            String   @id @default(cuid())
  workspaceId   String
  workspace     Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  applicationId String
  application   OAuthApplication @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  
  // Token Details
  accessToken   String   @unique
  refreshToken  String?  @unique
  tokenType     String   @default("Bearer")
  
  // Token Configuration
  scopes        String[] // Granted scopes
  
  // Token Status
  isActive      Boolean  @default(true)
  
  // Token Lifecycle
  expiresAt     DateTime
  refreshExpiresAt DateTime?
  
  // User Context
  userId        String   // User who authorized the token
  
  // Audit
  createdAt     DateTime @default(now())
  lastUsedAt    DateTime?
  
  @@index([accessToken])
  @@index([refreshToken])
  @@index([workspaceId, userId])
  @@index([applicationId])
}

// API Usage Tracking
model ApiUsageLog {
  id          String   @id @default(cuid())
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  // Request Identity
  requestId   String   @unique
  
  // Authentication Context
  apiKeyId    String?
  apiKey      ApiKey?  @relation(fields: [apiKeyId], references: [id], onDelete: SetNull)
  oauthTokenId String?
  userId      String?
  
  // Request Details
  method      String   // HTTP method
  path        String   // API endpoint path
  userAgent   String?
  ipAddress   String
  
  // Request/Response
  statusCode  Int
  requestSize Int?     // Request body size in bytes
  responseSize Int?    // Response body size in bytes
  duration    Int      // Request duration in milliseconds
  
  // Rate Limiting
  rateLimitRemaining Int?
  rateLimitReset     DateTime?
  
  // Error Information
  errorCode   String?
  errorMessage String?
  
  // Audit
  timestamp   DateTime @default(now())
  
  @@index([workspaceId, timestamp])
  @@index([apiKeyId, timestamp])
  @@index([path, method])
  @@index([statusCode])
}

// API Rate Limits
model ApiRateLimit {
  id          String   @id @default(cuid())
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  // Rate Limit Identity
  identifier  String   // API key ID, user ID, or IP address
  type        String   // 'api_key', 'user', 'ip'
  
  // Rate Limit Configuration
  endpoint    String?  // Specific endpoint or '*' for all
  limit       Int      // Number of requests
  window      Int      // Time window in seconds
  
  // Current Usage
  currentCount Int     @default(0)
  windowStart DateTime @default(now())
  
  // Rate Limit Status
  isBlocked   Boolean  @default(false)
  blockedUntil DateTime?
  
  // Audit
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@unique([workspaceId, identifier, endpoint])
  @@index([identifier, type])
  @@index([windowStart])
}
```

### API Schema Definitions

```typescript
// OpenAPI 3.1 Schema Structure
interface OpenAPISchema {
  openapi: "3.1.0";
  info: {
    title: "NEXUS SaaS Starter API";
    version: string;
    description: string;
    contact: {
      name: string;
      url: string;
      email: string;
    };
    license: {
      name: string;
      url: string;
    };
  };
  servers: Array<{
    url: string;
    description: string;
  }>;
  security: Array<{
    [key: string]: string[];
  }>;
  paths: Record<string, PathItem>;
  components: {
    schemas: Record<string, Schema>;
    securitySchemes: Record<string, SecurityScheme>;
    parameters: Record<string, Parameter>;
    responses: Record<string, Response>;
  };
}

// API Resource Groups
interface APIResourceGroups {
  // Core Resources
  workspaces: WorkspaceAPI;
  users: UserAPI;
  teams: TeamAPI;
  
  // Content Management
  projects: ProjectAPI;
  tasks: TaskAPI;
  documents: DocumentAPI;
  
  // Billing & Subscriptions
  subscriptions: SubscriptionAPI;
  invoices: InvoiceAPI;
  usage: UsageAPI;
  
  // Integrations
  webhooks: WebhookAPI;
  plugins: PluginAPI;
  marketplace: MarketplaceAPI;
  
  // Analytics & Reporting
  analytics: AnalyticsAPI;
  reports: ReportAPI;
  metrics: MetricsAPI;
  
  // System & Admin
  audit: AuditAPI;
  settings: SettingsAPI;
  health: HealthAPI;
}

// GraphQL Schema Structure
interface GraphQLSchema {
  types: {
    // Core Types
    Workspace: GraphQLObjectType;
    User: GraphQLObjectType;
    Team: GraphQLObjectType;
    
    // Content Types
    Project: GraphQLObjectType;
    Task: GraphQLObjectType;
    Document: GraphQLObjectType;
    
    // System Types
    ApiKey: GraphQLObjectType;
    Webhook: GraphQLObjectType;
    Plugin: GraphQLObjectType;
  };
  
  queries: {
    // Single Resource Queries
    workspace: FieldResolver;
    user: FieldResolver;
    project: FieldResolver;
    
    // List Queries
    workspaces: FieldResolver;
    users: FieldResolver;
    projects: FieldResolver;
    
    // Search Queries
    search: FieldResolver;
    searchProjects: FieldResolver;
    searchUsers: FieldResolver;
  };
  
  mutations: {
    // Create Operations
    createWorkspace: FieldResolver;
    createProject: FieldResolver;
    createTask: FieldResolver;
    
    // Update Operations
    updateWorkspace: FieldResolver;
    updateProject: FieldResolver;
    updateTask: FieldResolver;
    
    // Delete Operations
    deleteProject: FieldResolver;
    deleteTask: FieldResolver;
  };
  
  subscriptions: {
    // Real-time Updates
    workspaceUpdated: FieldResolver;
    projectUpdated: FieldResolver;
    taskUpdated: FieldResolver;
    
    // Activity Streams
    activityFeed: FieldResolver;
    notifications: FieldResolver;
  };
}
```

---

## Implementation Blueprint

### Task Breakdown (Information-Dense Implementation)

**Phase 1: API Infrastructure Foundation (4-6 hours)**

Task 1: API Authentication and Security Framework
CREATE src/lib/api/authentication.ts:
  - IMPLEMENT OAuth 2.0 server with authorization code flow
  - CREATE API key generation and validation system
  - ADD JWT token management and refresh logic
  - IMPLEMENT scope-based permission system
  - SETUP rate limiting with Redis backend

CREATE src/lib/api/security.ts:
  - IMPLEMENT API request validation and sanitization
  - CREATE IP whitelisting and geolocation filtering
  - ADD request signing and HMAC verification
  - IMPLEMENT audit logging for all API requests
  - SETUP security headers and CORS configuration

Task 2: OpenAPI Specification and Documentation
CREATE src/lib/api/openapi-generator.ts:
  - IMPLEMENT automatic OpenAPI 3.1 schema generation
  - CREATE interactive Swagger UI documentation
  - ADD code example generation for multiple languages
  - IMPLEMENT API versioning and deprecation handling
  - SETUP automated schema validation and testing

CREATE src/app/api/docs/:
  - IMPLEMENT interactive API documentation portal
  - CREATE API explorer with live testing capabilities
  - ADD authentication flow testing interface
  - IMPLEMENT code snippet generation and copying
  - SETUP developer onboarding and tutorials

**Phase 2: REST API Implementation (5-7 hours)**

Task 3: Core Resource APIs
CREATE src/app/api/v1/workspaces/:
  - IMPLEMENT full CRUD operations for workspaces
  - CREATE workspace member management endpoints
  - ADD workspace settings and configuration APIs
  - IMPLEMENT workspace analytics and usage endpoints
  - SETUP workspace-specific resource filtering

CREATE src/app/api/v1/users/:
  - IMPLEMENT user profile management APIs
  - CREATE user authentication and session endpoints
  - ADD user preference and settings management
  - IMPLEMENT user activity and audit trail APIs
  - SETUP user search and directory endpoints

CREATE src/app/api/v1/projects/:
  - IMPLEMENT project lifecycle management APIs
  - CREATE project collaboration and sharing endpoints
  - ADD project template and cloning capabilities
  - IMPLEMENT project analytics and reporting APIs
  - SETUP project search and filtering endpoints

Task 4: Advanced API Features
CREATE src/lib/api/pagination.ts:
  - IMPLEMENT cursor-based pagination for large datasets
  - CREATE offset-based pagination for simple use cases
  - ADD pagination metadata and navigation links
  - IMPLEMENT efficient count queries and estimates
  - SETUP pagination configuration and limits

CREATE src/lib/api/filtering.ts:
  - IMPLEMENT advanced filtering with query operators
  - CREATE field-based sorting and ordering
  - ADD full-text search capabilities
  - IMPLEMENT faceted search and aggregations
  - SETUP filter validation and sanitization

**Phase 3: GraphQL API Implementation (4-5 hours)**

Task 5: GraphQL Schema and Resolvers
CREATE src/lib/api/graphql/schema.ts:
  - IMPLEMENT comprehensive GraphQL schema definition
  - CREATE type definitions for all core resources
  - ADD input types for mutations and filtering
  - IMPLEMENT custom scalar types and enums
  - SETUP schema stitching and federation support

CREATE src/lib/api/graphql/resolvers/:
  - IMPLEMENT query resolvers for all resource types
  - CREATE mutation resolvers for CRUD operations
  - ADD subscription resolvers for real-time updates
  - IMPLEMENT field-level resolvers with DataLoader
  - SETUP resolver authentication and authorization

Task 6: GraphQL Advanced Features
CREATE src/lib/api/graphql/subscriptions.ts:
  - IMPLEMENT real-time subscriptions with WebSockets
  - CREATE subscription filtering and authentication
  - ADD subscription rate limiting and throttling
  - IMPLEMENT subscription lifecycle management
  - SETUP subscription analytics and monitoring

CREATE src/lib/api/graphql/playground.ts:
  - IMPLEMENT GraphQL Playground for development
  - CREATE schema introspection and documentation
  - ADD query validation and autocomplete
  - IMPLEMENT subscription testing interface
  - SETUP GraphQL analytics and performance monitoring

**Phase 4: SDK and Developer Tools (3-4 hours)**

Task 7: Client SDK Generation
CREATE packages/api-sdk/:
  - IMPLEMENT TypeScript SDK with full type safety
  - CREATE JavaScript SDK for browser and Node.js
  - ADD Python SDK for data science and automation
  - IMPLEMENT Go SDK for high-performance applications
  - SETUP automated SDK generation and publishing

CREATE tools/sdk-generator/:
  - IMPLEMENT OpenAPI-based SDK generation
  - CREATE template system for multiple languages
  - ADD SDK documentation and example generation
  - IMPLEMENT SDK testing and validation
  - SETUP CI/CD pipeline for SDK releases

Task 8: Developer CLI and Tools
CREATE packages/nexus-cli/:
  - IMPLEMENT command-line interface for developers
  - CREATE authentication and configuration management
  - ADD resource management commands (CRUD operations)
  - IMPLEMENT bulk operations and data migration tools
  - SETUP CLI plugin system and extensibility

CREATE tools/api-testing/:
  - IMPLEMENT comprehensive API testing framework
  - CREATE automated test generation from OpenAPI spec
  - ADD performance testing and load testing tools
  - IMPLEMENT API contract testing and validation
  - SETUP continuous API testing and monitoring

**Phase 5: Developer Portal and Ecosystem (2-3 hours)**

Task 9: Developer Portal
CREATE src/app/(portal)/developers/:
  - IMPLEMENT developer registration and onboarding
  - CREATE API key and OAuth application management
  - ADD usage analytics and billing dashboard
  - IMPLEMENT developer community and support
  - SETUP developer feedback and feature requests

CREATE src/components/developer-portal/:
  - IMPLEMENT DeveloperDashboard for API management
  - CREATE APIKeyManager for key lifecycle management
  - ADD UsageAnalytics for API consumption insights
  - IMPLEMENT DocumentationBrowser for API exploration
  - CREATE CommunityForum for developer discussions

Task 10: API Ecosystem Integration
CREATE src/lib/api/ecosystem.ts:
  - IMPLEMENT webhook delivery and retry system
  - CREATE plugin API registration and management
  - ADD marketplace API integration
  - IMPLEMENT third-party integration framework
  - SETUP ecosystem analytics and health monitoring

---

## Integration Points

### 1. Multi-Tenant API Architecture

```typescript
// Multi-tenant API middleware integration
// src/lib/api/tenant-middleware.ts
import { NextRequest, NextResponse } from 'next/server';
import { getTenantContext } from '../auth/tenant-context';

export class APITenantMiddleware {
  static async validateTenantAccess(
    request: NextRequest,
    workspaceId: string
  ): Promise<TenantContext> {
    // Extract authentication context
    const authContext = await this.extractAuthContext(request);

    // Validate workspace access
    const tenantContext = await getTenantContext(authContext, workspaceId);

    if (!tenantContext) {
      throw new APIError('WORKSPACE_ACCESS_DENIED', 403);
    }

    // Inject tenant context into request
    request.tenantContext = tenantContext;

    return tenantContext;
  }

  static async extractAuthContext(request: NextRequest): Promise<AuthContext> {
    // Check for API key authentication
    const apiKey = request.headers.get('x-api-key');
    if (apiKey) {
      return await this.validateApiKey(apiKey);
    }

    // Check for OAuth token authentication
    const authorization = request.headers.get('authorization');
    if (authorization?.startsWith('Bearer ')) {
      const token = authorization.substring(7);
      return await this.validateOAuthToken(token);
    }

    throw new APIError('AUTHENTICATION_REQUIRED', 401);
  }
}
```

### 2. Plugin API Integration

```typescript
// Integration with existing plugin architecture
// src/lib/api/plugin-integration.ts
import { PluginRegistry } from '../plugins/plugin-registry';

export class APIPluginIntegration {
  static async registerPluginEndpoints(
    workspaceId: string,
    pluginId: string,
    endpoints: PluginEndpoint[]
  ): Promise<void> {
    for (const endpoint of endpoints) {
      // Register dynamic API endpoint
      await this.createDynamicEndpoint(workspaceId, pluginId, endpoint);
    }
  }

  static async createDynamicEndpoint(
    workspaceId: string,
    pluginId: string,
    endpoint: PluginEndpoint
  ): Promise<void> {
    // Create Next.js API route handler
    const handler = async (request: NextRequest) => {
      // Validate plugin permissions
      await this.validatePluginAPIAccess(workspaceId, pluginId, endpoint);

      // Execute plugin endpoint handler
      const plugin = await PluginRegistry.getPlugin(pluginId);
      return await plugin.executeAPIHandler(endpoint.path, request);
    };

    // Register endpoint with API router
    await this.registerDynamicRoute(
      `/api/v1/workspaces/${workspaceId}/plugins/${pluginId}${endpoint.path}`,
      endpoint.method,
      handler
    );
  }

  static async validatePluginAPIAccess(
    workspaceId: string,
    pluginId: string,
    endpoint: PluginEndpoint
  ): Promise<void> {
    // Check plugin installation and permissions
    const installation = await PluginRegistry.getInstallation(workspaceId, pluginId);

    if (!installation || !installation.isEnabled) {
      throw new APIError('PLUGIN_NOT_INSTALLED', 404);
    }

    // Validate endpoint permissions
    const hasPermission = installation.permissions.includes(endpoint.permission);
    if (!hasPermission) {
      throw new APIError('PLUGIN_PERMISSION_DENIED', 403);
    }
  }
}
```

### 3. Webhook System Integration

```typescript
// Integration with existing webhook system
// src/lib/api/webhook-integration.ts
import { WebhookService } from '../webhooks/webhook-service';

export class APIWebhookIntegration {
  static async triggerAPIWebhooks(
    workspaceId: string,
    event: APIEvent
  ): Promise<void> {
    // Get webhooks subscribed to API events
    const webhooks = await WebhookService.getWebhooksByEvent(
      workspaceId,
      `api.${event.type}`
    );

    // Prepare webhook payload
    const payload = {
      event: `api.${event.type}`,
      data: event.data,
      workspace_id: workspaceId,
      timestamp: new Date().toISOString(),
      api_version: 'v1'
    };

    // Deliver webhooks in parallel
    await Promise.allSettled(
      webhooks.map(webhook =>
        WebhookService.deliverWebhook(webhook, payload)
      )
    );
  }

  static async registerAPIEventWebhooks(): Promise<void> {
    // Register standard API event types
    const apiEvents = [
      'api.workspace.created',
      'api.workspace.updated',
      'api.workspace.deleted',
      'api.user.created',
      'api.user.updated',
      'api.project.created',
      'api.project.updated',
      'api.project.deleted',
      'api.task.created',
      'api.task.updated',
      'api.task.completed'
    ];

    for (const eventType of apiEvents) {
      await WebhookService.registerEventType(eventType, {
        description: `Triggered when ${eventType.split('.').slice(1).join(' ')}`,
        schema: this.getEventSchema(eventType)
      });
    }
  }
}
```

### 4. Billing Integration for API Usage

```typescript
// API usage billing integration
// src/lib/api/billing-integration.ts
import { StripeService } from '../billing/stripe-service';

export class APIBillingIntegration {
  static async trackAPIUsage(
    workspaceId: string,
    apiKeyId: string,
    usage: APIUsageMetrics
  ): Promise<void> {
    // Record usage in database
    await this.recordUsageMetrics(workspaceId, apiKeyId, usage);

    // Check if workspace has usage-based billing
    const subscription = await this.getWorkspaceSubscription(workspaceId);

    if (subscription?.billing_type === 'usage_based') {
      // Report usage to Stripe
      await StripeService.reportUsage(subscription.stripe_subscription_id, {
        quantity: usage.requests,
        timestamp: Math.floor(Date.now() / 1000)
      });
    }
  }

  static async enforceAPILimits(
    workspaceId: string,
    apiKeyId: string
  ): Promise<void> {
    // Get current usage for the billing period
    const currentUsage = await this.getCurrentUsage(workspaceId);

    // Get workspace plan limits
    const planLimits = await this.getPlanLimits(workspaceId);

    // Check if usage exceeds limits
    if (currentUsage.requests >= planLimits.api_requests) {
      throw new APIError('API_LIMIT_EXCEEDED', 429, {
        limit: planLimits.api_requests,
        current: currentUsage.requests,
        reset_date: currentUsage.reset_date
      });
    }
  }

  static async generateAPIUsageInvoice(
    workspaceId: string,
    billingPeriod: BillingPeriod
  ): Promise<UsageInvoice> {
    // Calculate API usage for billing period
    const usage = await this.calculatePeriodUsage(workspaceId, billingPeriod);

    // Generate usage-based charges
    const charges = await this.calculateUsageCharges(usage);

    // Create invoice with Stripe
    const invoice = await StripeService.createUsageInvoice(workspaceId, charges);

    return {
      invoice_id: invoice.id,
      workspace_id: workspaceId,
      period: billingPeriod,
      usage_summary: usage,
      charges,
      total_amount: charges.reduce((sum, charge) => sum + charge.amount, 0)
    };
  }
}
```

---

## Security Implementation

### 1. OAuth 2.0 Authorization Server

```typescript
// Comprehensive OAuth 2.0 implementation
// src/lib/api/oauth-server.ts
export class OAuthServer {
  static async handleAuthorizationRequest(
    request: AuthorizationRequest
  ): Promise<AuthorizationResponse> {
    // Validate client application
    const client = await this.validateClient(request.client_id);

    // Validate redirect URI
    if (!client.redirect_uris.includes(request.redirect_uri)) {
      throw new OAuthError('invalid_redirect_uri');
    }

    // Validate requested scopes
    const validScopes = await this.validateScopes(request.scope, client);

    // Generate authorization code
    const authCode = await this.generateAuthorizationCode({
      client_id: request.client_id,
      user_id: request.user_id,
      scopes: validScopes,
      redirect_uri: request.redirect_uri
    });

    return {
      code: authCode,
      state: request.state,
      redirect_uri: request.redirect_uri
    };
  }

  static async handleTokenRequest(
    request: TokenRequest
  ): Promise<TokenResponse> {
    switch (request.grant_type) {
      case 'authorization_code':
        return await this.handleAuthorizationCodeGrant(request);
      case 'refresh_token':
        return await this.handleRefreshTokenGrant(request);
      case 'client_credentials':
        return await this.handleClientCredentialsGrant(request);
      default:
        throw new OAuthError('unsupported_grant_type');
    }
  }

  static async validateAccessToken(
    token: string
  ): Promise<TokenValidationResult> {
    // Decode and validate JWT token
    const decoded = await this.verifyJWT(token);

    // Check token expiration
    if (decoded.exp < Math.floor(Date.now() / 1000)) {
      throw new OAuthError('token_expired');
    }

    // Check token revocation
    const isRevoked = await this.isTokenRevoked(decoded.jti);
    if (isRevoked) {
      throw new OAuthError('token_revoked');
    }

    return {
      valid: true,
      user_id: decoded.sub,
      client_id: decoded.aud,
      scopes: decoded.scope.split(' '),
      workspace_id: decoded.workspace_id
    };
  }
}
```

### 2. API Rate Limiting and Throttling

```typescript
// Advanced rate limiting system
// src/lib/api/rate-limiter.ts
export class APIRateLimiter {
  static async checkRateLimit(
    identifier: string,
    endpoint: string,
    limits: RateLimitConfig
  ): Promise<RateLimitResult> {
    // Get current usage from Redis
    const key = `rate_limit:${identifier}:${endpoint}`;
    const current = await redis.get(key);

    if (!current) {
      // First request in window
      await redis.setex(key, limits.window, 1);
      return {
        allowed: true,
        remaining: limits.limit - 1,
        reset_time: Date.now() + (limits.window * 1000)
      };
    }

    const count = parseInt(current);

    if (count >= limits.limit) {
      // Rate limit exceeded
      const ttl = await redis.ttl(key);
      return {
        allowed: false,
        remaining: 0,
        reset_time: Date.now() + (ttl * 1000),
        retry_after: ttl
      };
    }

    // Increment counter
    await redis.incr(key);

    return {
      allowed: true,
      remaining: limits.limit - count - 1,
      reset_time: Date.now() + (await redis.ttl(key) * 1000)
    };
  }

  static async implementSlidingWindow(
    identifier: string,
    endpoint: string,
    limits: RateLimitConfig
  ): Promise<RateLimitResult> {
    const now = Date.now();
    const window = limits.window * 1000; // Convert to milliseconds
    const key = `sliding:${identifier}:${endpoint}`;

    // Remove old entries outside the window
    await redis.zremrangebyscore(key, 0, now - window);

    // Count current requests in window
    const count = await redis.zcard(key);

    if (count >= limits.limit) {
      // Get oldest request time for reset calculation
      const oldest = await redis.zrange(key, 0, 0, 'WITHSCORES');
      const resetTime = oldest.length > 0 ? parseInt(oldest[1]) + window : now + window;

      return {
        allowed: false,
        remaining: 0,
        reset_time: resetTime,
        retry_after: Math.ceil((resetTime - now) / 1000)
      };
    }

    // Add current request
    await redis.zadd(key, now, `${now}-${Math.random()}`);
    await redis.expire(key, limits.window);

    return {
      allowed: true,
      remaining: limits.limit - count - 1,
      reset_time: now + window
    };
  }
}
```

### 3. API Security Scanning and Validation

```typescript
// API security and validation framework
// src/lib/api/security-scanner.ts
export class APISecurityScanner {
  static async validateAPIRequest(
    request: NextRequest,
    endpoint: APIEndpoint
  ): Promise<ValidationResult> {
    const results = await Promise.all([
      this.validateRequestSize(request, endpoint.limits),
      this.validateRequestHeaders(request, endpoint.security),
      this.validateRequestBody(request, endpoint.schema),
      this.scanForMaliciousContent(request),
      this.validateIPWhitelist(request, endpoint.ipWhitelist)
    ]);

    const issues = results.flatMap(r => r.issues);

    return {
      valid: issues.length === 0,
      issues,
      risk_score: this.calculateRiskScore(issues)
    };
  }

  static async scanForMaliciousContent(
    request: NextRequest
  ): Promise<SecurityScanResult> {
    const body = await request.text();
    const issues: SecurityIssue[] = [];

    // SQL injection detection
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER)\b)/i,
      /(UNION\s+SELECT)/i,
      /(\'\s*OR\s*\'\s*=\s*\')/i
    ];

    for (const pattern of sqlPatterns) {
      if (pattern.test(body)) {
        issues.push({
          type: 'sql_injection',
          severity: 'high',
          description: 'Potential SQL injection attempt detected'
        });
      }
    }

    // XSS detection
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/i,
      /on\w+\s*=/i
    ];

    for (const pattern of xssPatterns) {
      if (pattern.test(body)) {
        issues.push({
          type: 'xss',
          severity: 'high',
          description: 'Potential XSS attempt detected'
        });
      }
    }

    return {
      passed: issues.length === 0,
      issues
    };
  }
}
```

---

## Validation Gates (Executable Testing)

### Level 1: Syntax & Style Validation
```bash
# TypeScript compilation and linting
npm run lint                    # ESLint checks for API system
npx tsc --noEmit               # TypeScript type checking
npm run type-check             # API SDK type validation

# API-specific validation
npm run validate-openapi       # OpenAPI schema validation
npm run validate-graphql       # GraphQL schema validation
npm run security-scan          # API security scanning
```

### Level 2: Unit Testing
```bash
# Core API system tests
npm test src/lib/api/          # API infrastructure tests
npm test packages/api-sdk/     # SDK tests
npm test src/app/api/          # API endpoint tests

# Security and authentication tests
npm run test:oauth             # OAuth 2.0 flow tests
npm run test:rate-limiting     # Rate limiting tests
npm run test:security          # Security framework tests
```

### Level 3: Integration Testing
```bash
# Start development server
npm run dev

# Test REST API endpoints
curl -X GET "http://localhost:3000/api/v1/workspaces" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json"

# Test GraphQL API
curl -X POST http://localhost:3000/api/graphql \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"query": "{ workspaces { id name } }"}'

# Test OAuth flow
curl -X POST http://localhost:3000/api/oauth/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=authorization_code&code=$AUTH_CODE&client_id=$CLIENT_ID"
```

### Level 4: End-to-End API Testing
```bash
# Production build validation
npm run build                  # Build with API system
npm run start                  # Production server testing

# API E2E testing
npm run test:e2e:api           # Playwright E2E tests for API
npm run test:sdk               # SDK integration tests
npm run test:performance       # API performance testing
```

### Level 5: Developer Experience Testing
```bash
# SDK testing
cd packages/api-sdk
npm run test                   # SDK unit tests
npm run build                  # SDK build validation

# CLI testing
cd packages/nexus-cli
npm run test                   # CLI unit tests
npm run build                  # CLI build validation

# Documentation testing
npm run test:docs              # Documentation validation
npm run test:examples          # Code example testing
```

---

## Quality Standards Checklist

- [x] **Multi-tenant isolation**: All API operations include workspace context and isolation
- [x] **Enterprise security**: OAuth 2.0, API keys, rate limiting, and comprehensive audit trails
- [x] **Developer experience**: Rich SDKs, CLI tools, and interactive documentation
- [x] **Performance optimization**: Efficient pagination, caching, and real-time capabilities
- [x] **Standards compliance**: OpenAPI 3.1, REST best practices, and GraphQL specifications
- [x] **Comprehensive coverage**: Full CRUD operations for all major platform resources
- [x] **Integration ready**: Seamless integration with existing plugin and webhook systems
- [x] **Scalable architecture**: Designed for high-volume API usage and ecosystem growth
- [x] **Monitoring and analytics**: Comprehensive API usage tracking and insights
- [x] **Documentation excellence**: Interactive docs with code examples and tutorials

---

**Framework**: NEXUS SaaS Starter Multi-Tenant API Ecosystem
**Technology Stack**: Next.js 15.4+ / TypeScript 5.8+ / OpenAPI 3.1 / GraphQL
**Optimization**: Production-ready, enterprise-grade, developer-first API platform
