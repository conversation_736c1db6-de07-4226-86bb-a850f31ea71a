import { useState, useCallback, useEffect } from "react";
import { ErrorHand<PERSON> } from "../utils";
import { ApiError } from "../types";

interface ErrorState {
  errors: ApiError[];
  hasErrors: boolean;
}

export function useError() {
  const [state, setState] = useState<ErrorState>({
    errors: [],
    hasErrors: false,
  });

  const addError = useCallback((error: ApiError) => {
    setState(prev => ({
      errors: [...prev.errors, { ...error, id: Date.now().toString() }],
      hasErrors: true,
    }));
  }, []);

  const removeError = useCallback((errorId: string) => {
    setState(prev => {
      const filteredErrors = prev.errors.filter(e => (e as any).id !== errorId);
      return {
        errors: filteredErrors,
        hasErrors: filteredErrors.length > 0,
      };
    });
  }, []);

  const clearErrors = useCallback(() => {
    setState({
      errors: [],
      hasErrors: false,
    });
  }, []);

  const clearErrorsByCode = useCallback((code: string) => {
    setState(prev => {
      const filteredErrors = prev.errors.filter(e => e.code !== code);
      return {
        errors: filteredErrors,
        hasErrors: filteredErrors.length > 0,
      };
    });
  }, []);

  // Listen to global errors
  useEffect(() => {
    const errorHandler = ErrorHandler.getInstance();
    errorHandler.addListener(addError);

    return () => {
      errorHandler.removeListener(addError);
    };
  }, [addError]);

  return {
    errors: state.errors,
    hasErrors: state.hasErrors,
    addError,
    removeError,
    clearErrors,
    clearErrorsByCode,
    latestError: state.errors[state.errors.length - 1] || null,
    errorCount: state.errors.length,
  };
}

// Hook for handling specific error types
export function useErrorHandler() {
  const { addError, clearErrorsByCode } = useError();

  const handleNetworkError = useCallback(() => {
    addError({
      message: "Network connection failed. Please check your internet connection.",
      code: "NETWORK_ERROR",
    });
  }, [addError]);

  const handleAuthError = useCallback(() => {
    addError({
      message: "Authentication failed. Please log in again.",
      code: "AUTH_ERROR",
      status: 401,
    });
  }, [addError]);

  const handleValidationError = useCallback((details: any) => {
    addError({
      message: "Please check your input and try again.",
      code: "VALIDATION_ERROR",
      status: 400,
      details,
    });
  }, [addError]);

  const handleServerError = useCallback(() => {
    addError({
      message: "Server error occurred. Please try again later.",
      code: "SERVER_ERROR",
      status: 500,
    });
  }, [addError]);

  const handleRateLimitError = useCallback((retryAfter?: number) => {
    addError({
      message: `Rate limit exceeded. Please try again ${retryAfter ? `in ${retryAfter} seconds` : "later"}.`,
      code: "RATE_LIMITED",
      status: 429,
      details: { retryAfter },
    });
  }, [addError]);

  return {
    handleNetworkError,
    handleAuthError,
    handleValidationError,
    handleServerError,
    handleRateLimitError,
    clearErrorsByCode,
  };
}

// Hook for error boundaries
export function useErrorBoundary() {
  const [error, setError] = useState<Error | null>(null);

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  const captureError = useCallback((error: Error) => {
    setError(error);
    console.error("Error boundary captured:", error);
  }, []);

  return {
    error,
    hasError: !!error,
    resetError,
    captureError,
  };
}

// Hook for retry logic
export function useRetry() {
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);

  const retry = useCallback(async (
    fn: () => Promise<any>,
    maxRetries: number = 3,
    delay: number = 1000
  ) => {
    setIsRetrying(true);
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = await fn();
        setRetryCount(0);
        setIsRetrying(false);
        return result;
      } catch (error) {
        setRetryCount(attempt);
        
        if (attempt === maxRetries) {
          setIsRetrying(false);
          throw error;
        }
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }
  }, []);

  const resetRetry = useCallback(() => {
    setRetryCount(0);
    setIsRetrying(false);
  }, []);

  return {
    retry,
    retryCount,
    isRetrying,
    resetRetry,
  };
}
