import { useAuth } from "./auth-context";
import { AuthUser } from "./auth-types";
export { useAuth };
export declare function useUser(): AuthUser | null;
export declare function useIsAuthenticated(): boolean;
export declare function useIsLoading(): boolean;
export declare function useHasRole(role: string): boolean;
export declare function useHasPermission(permission: string): boolean;
export declare function useIsOwner(): boolean;
export declare function useIsAdmin(): boolean;
export declare function useCanRead(): boolean;
export declare function useCanWrite(): boolean;
export declare function useCanDelete(): boolean;
export declare function useTenantId(): string | null;
export declare function useLogin(): (credentials: import("./auth-types").LoginCredentials) => Promise<void>;
export declare function useRegister(): (credentials: import("./auth-types").RegisterCredentials) => Promise<void>;
export declare function useLogout(): () => Promise<void>;
//# sourceMappingURL=auth-hooks.d.ts.map