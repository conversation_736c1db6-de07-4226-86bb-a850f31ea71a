@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Downloads\saas-starter-cursor-temp\node_modules\.pnpm\@pothos+plugin-prisma@4.10.0_@pothos+core@4.7.2_@prisma+client@6.12.0_graphql@16.11.0_typescript@5.8.3\node_modules\@pothos\plugin-prisma\bin\node_modules;C:\Users\<USER>\Downloads\saas-starter-cursor-temp\node_modules\.pnpm\@pothos+plugin-prisma@4.10.0_@pothos+core@4.7.2_@prisma+client@6.12.0_graphql@16.11.0_typescript@5.8.3\node_modules\@pothos\plugin-prisma\node_modules;C:\Users\<USER>\Downloads\saas-starter-cursor-temp\node_modules\.pnpm\@pothos+plugin-prisma@4.10.0_@pothos+core@4.7.2_@prisma+client@6.12.0_graphql@16.11.0_typescript@5.8.3\node_modules\@pothos\node_modules;C:\Users\<USER>\Downloads\saas-starter-cursor-temp\node_modules\.pnpm\@pothos+plugin-prisma@4.10.0_@pothos+core@4.7.2_@prisma+client@6.12.0_graphql@16.11.0_typescript@5.8.3\node_modules;C:\Users\<USER>\Downloads\saas-starter-cursor-temp\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Downloads\saas-starter-cursor-temp\node_modules\.pnpm\@pothos+plugin-prisma@4.10.0_@pothos+core@4.7.2_@prisma+client@6.12.0_graphql@16.11.0_typescript@5.8.3\node_modules\@pothos\plugin-prisma\bin\node_modules;C:\Users\<USER>\Downloads\saas-starter-cursor-temp\node_modules\.pnpm\@pothos+plugin-prisma@4.10.0_@pothos+core@4.7.2_@prisma+client@6.12.0_graphql@16.11.0_typescript@5.8.3\node_modules\@pothos\plugin-prisma\node_modules;C:\Users\<USER>\Downloads\saas-starter-cursor-temp\node_modules\.pnpm\@pothos+plugin-prisma@4.10.0_@pothos+core@4.7.2_@prisma+client@6.12.0_graphql@16.11.0_typescript@5.8.3\node_modules\@pothos\node_modules;C:\Users\<USER>\Downloads\saas-starter-cursor-temp\node_modules\.pnpm\@pothos+plugin-prisma@4.10.0_@pothos+core@4.7.2_@prisma+client@6.12.0_graphql@16.11.0_typescript@5.8.3\node_modules;C:\Users\<USER>\Downloads\saas-starter-cursor-temp\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\node_modules\.pnpm\@pothos+plugin-prisma@4.10.0_@pothos+core@4.7.2_@prisma+client@6.12.0_graphql@16.11.0_typescript@5.8.3\node_modules\@pothos\plugin-prisma\bin\generator.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\node_modules\.pnpm\@pothos+plugin-prisma@4.10.0_@pothos+core@4.7.2_@prisma+client@6.12.0_graphql@16.11.0_typescript@5.8.3\node_modules\@pothos\plugin-prisma\bin\generator.js" %*
)
