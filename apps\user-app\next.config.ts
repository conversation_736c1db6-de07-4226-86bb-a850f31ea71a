import type { NextConfig } from "next";

// Security headers matching Next.js Header interface exactly
interface SecurityHeader { 
  key: string; 
  value: string; 
}

function securityHeaders(): SecurityHeader[] {
  const isDevelopment = (process.env.NODE_ENV as string) === 'development';
  
  // Development-friendly CSP that allows Next.js dev features
  const developmentCSP = `default-src 'self';
    script-src 'self' 'unsafe-eval' 'unsafe-inline';
    style-src 'self' 'unsafe-inline';
    img-src 'self' data: blob:;
    media-src 'self';
    connect-src 'self' ws: wss:;
    font-src 'self' data:;`.replace(/\s{2,}/g, ' ').trim();
    
  // Production CSP - stricter security
  // const productionCSP = `default-src 'self';
  //   script-src 'self';
  //   style-src 'self';
  //   img-src 'self' data:; 
  //   media-src 'none';
  //   connect-src 'self';
  //   font-src 'self';`.replace(/\s{2,}/g, ' ').trim();
    
  // const ContentSecurityPolicy = isDevelopment ? developmentCSP : productionCSP;
  const ContentSecurityPolicy = isDevelopment ? developmentCSP: developmentCSP;

  return [
    { key: 'Content-Security-Policy', value: ContentSecurityPolicy },
    { key: 'X-Frame-Options', value: 'SAMEORIGIN' },
    { key: 'X-Content-Type-Options', value: 'nosniff' },
    { key: 'Referrer-Policy', value: 'strict-origin-when-cross-origin' },
    { key: 'Permissions-Policy', value: 'camera=(), microphone=(), geolocation=()' },
    // HSTS – one year, include sub-domains, preload
    { key: 'Strict-Transport-Security', value: 'max-age=31536000; includeSubDomains; preload' },
  ];
}

const nextConfig: NextConfig = {
  reactStrictMode: true,
  poweredByHeader: false,
  // Enhanced image optimisation
  images: {
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 60 * 60 * 24 * 7, // 7 days
  },
  productionBrowserSourceMaps: false,
  async headers() {
    return [
      {
        source: '/:path*',
        headers: securityHeaders(),
      },
    ];
  },
  // Basic error handling – direct unmatched routes to custom 404 when deployed
  async redirects() {
    return [
      {
        source: '/404',
        destination: '/not-found',
        permanent: false,
      },
    ];
  },
} satisfies NextConfig;

export default nextConfig;
