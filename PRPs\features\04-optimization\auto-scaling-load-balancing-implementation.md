# Auto-Scaling & Load Balancing Implementation

## Feature Overview

Implement comprehensive auto-scaling and load balancing for the NEXUS SaaS Starter to handle dynamic traffic patterns, ensure high availability, and optimize resource utilization across multiple deployment environments (Docker, Kubernetes, serverless).

## Context & Research

### Current Implementation Analysis
Based on codebase analysis, existing scaling patterns include:
- **Vercel Serverless**: Automatic scaling for Next.js API routes
- **Database Connection Pooling**: Basic connection management with Prisma
- **Redis Clustering**: Distributed caching with failover capabilities
- **Multi-Tenant Architecture**: Tenant isolation with shared infrastructure

### Technology Stack Verification (Context7)

**Kubernetes Auto-Scaling Patterns**:
- Horizontal Pod Autoscaler (HPA) with CPU, memory, and custom metrics
- Vertical Pod Autoscaler (VPA) for resource optimization
- Cluster Autoscaler for node-level scaling
- Multi-metric scaling with external metrics from Prometheus

**Load Balancing Strategies**:
- Container-native load balancing with Network Endpoint Groups (NEGs)
- Traffic distribution with readiness probes and health checks
- Session affinity and sticky sessions for stateful applications
- Geographic load balancing for global deployments

**Scaling Policies**:
- Stabilization windows to prevent thrashing
- Scale-up/scale-down policies with different rates
- Resource-based and custom metric-based scaling
- Predictive scaling based on historical patterns

## Implementation Blueprint

### Data Models and Structure

```typescript
// Enhanced auto-scaling configuration
interface AutoScalingConfig {
  deployment: {
    environment: 'docker' | 'kubernetes' | 'serverless';
    strategy: 'horizontal' | 'vertical' | 'hybrid';
  };
  scaling: {
    horizontal: HorizontalScalingConfig;
    vertical: VerticalScalingConfig;
    cluster: ClusterScalingConfig;
  };
  loadBalancing: {
    strategy: 'round-robin' | 'least-connections' | 'ip-hash' | 'weighted';
    healthChecks: HealthCheckConfig;
    sessionAffinity: boolean;
  };
  metrics: {
    cpu: MetricConfig;
    memory: MetricConfig;
    custom: CustomMetricConfig[];
  };
}

interface HorizontalScalingConfig {
  minReplicas: number;
  maxReplicas: number;
  targetCPUUtilization: number;
  targetMemoryUtilization: number;
  scaleUpPolicy: ScalingPolicy;
  scaleDownPolicy: ScalingPolicy;
  stabilizationWindow: number;
}

interface ScalingPolicy {
  type: 'Percent' | 'Pods';
  value: number;
  periodSeconds: number;
}
```

### Task Breakdown

**Phase 1: Kubernetes Auto-Scaling (3-4 hours)**

1. **Horizontal Pod Autoscaler (HPA) Setup**
   - File: `k8s/autoscaling/hpa.yaml`
   - Configure CPU, memory, and custom metric scaling
   - Implement multi-tenant scaling policies
   - Add stabilization and scaling behavior
   - Pattern: Follow GKE HPA best practices

2. **Vertical Pod Autoscaler (VPA) Configuration**
   - File: `k8s/autoscaling/vpa.yaml`
   - Implement resource recommendation and auto-adjustment
   - Configure update policies and resource limits
   - Add VPA for different workload types
   - Pattern: Optimize resource allocation

**Phase 2: Load Balancing & Traffic Management (3-4 hours)**

3. **Ingress and Load Balancer Configuration**
   - File: `k8s/networking/ingress.yaml`
   - Configure container-native load balancing
   - Implement health checks and readiness probes
   - Add session affinity and traffic distribution
   - Pattern: GKE ingress with NEG support

4. **Service Mesh Integration**
   - File: `k8s/networking/service-mesh.yaml`
   - Implement Istio or similar service mesh
   - Add traffic splitting and canary deployments
   - Configure circuit breakers and retry policies
   - Pattern: Advanced traffic management

**Phase 3: Monitoring & Optimization (2-3 hours)**

5. **Custom Metrics and Monitoring**
   - File: `k8s/monitoring/custom-metrics.yaml`
   - Implement Prometheus metrics collection
   - Add custom application metrics for scaling
   - Configure alerting and notification
   - Pattern: Comprehensive observability

6. **Predictive Scaling**
   - File: `lib/scaling/predictive-scaling.ts`
   - Implement machine learning-based scaling predictions
   - Add historical pattern analysis
   - Configure proactive scaling policies
   - Pattern: AI-driven resource optimization

### Integration Points

**Multi-Tenant Integration**:
- Tenant-specific scaling policies and resource limits
- Isolated scaling metrics and monitoring
- Tenant-aware load balancing and traffic routing
- Resource quotas and fair sharing

**Database Integration**:
- Connection pool scaling with application scaling
- Database read replica routing for scaled workloads
- Connection limit management across scaled instances
- Database performance monitoring integration

**Caching Integration**:
- Redis cluster scaling with application scaling
- Cache warming strategies for new instances
- Distributed cache consistency across scaled pods
- Cache performance metrics integration

## Validation Gates

### Level 1: Syntax & Style
```bash
npm run lint                    # ESLint checks
npx tsc --noEmit               # TypeScript validation
kubectl apply --dry-run=client -f k8s/  # Kubernetes manifest validation
```

### Level 2: Scaling Tests
```bash
# Deploy to Kubernetes cluster
kubectl apply -f k8s/

# Test HPA scaling
kubectl run -i --tty load-generator --rm --image=busybox --restart=Never -- /bin/sh
# Inside the pod: while true; do wget -q -O- http://nexus-app/api/health; done

# Monitor scaling
kubectl get hpa -w
kubectl get pods -w
```

### Level 3: Load Testing
```bash
# Load testing with scaling
npx autocannon -c 100 -d 300 \
  -H "x-tenant-id=test-tenant" \
  -H "Authorization=Bearer $TOKEN" \
  https://nexus-app.example.com/api/analytics/dashboard

# Monitor resource utilization
kubectl top pods
kubectl top nodes
```

### Level 4: Production Validation
```bash
# Scaling performance analysis
kubectl describe hpa nexus-app-hpa

# Load balancer health checks
kubectl get ingress nexus-app-ingress -o yaml

# Service mesh traffic analysis
istioctl proxy-status
istioctl analyze
```

## Quality Standards Checklist

- [ ] HPA scales based on CPU, memory, and custom metrics
- [ ] VPA optimizes resource requests and limits
- [ ] Load balancer distributes traffic evenly across pods
- [ ] Health checks prevent traffic to unhealthy pods
- [ ] Scaling policies prevent thrashing and oscillation
- [ ] Multi-tenant resource isolation and fair sharing
- [ ] Custom metrics reflect application performance
- [ ] Predictive scaling reduces response time to load spikes
- [ ] Circuit breakers prevent cascade failures
- [ ] Monitoring and alerting for scaling events

## Security Considerations

- **Resource Limits**: Prevent resource exhaustion attacks
- **Network Policies**: Isolate tenant traffic and prevent lateral movement
- **RBAC**: Proper permissions for scaling operations
- **Pod Security**: Security contexts and admission controllers
- **Traffic Encryption**: TLS termination and end-to-end encryption

## Performance Targets

- **Scaling Response Time**: <60 seconds for horizontal scaling
- **Resource Utilization**: 60-80% CPU/memory utilization target
- **Load Distribution**: <5% variance in traffic distribution
- **Health Check Latency**: <100ms for readiness/liveness probes
- **Availability**: 99.9% uptime during scaling events

---

**Implementation Priority**: HIGH - Critical for production scalability
**Estimated Effort**: 10-15 hours
**Dependencies**: Kubernetes cluster, monitoring infrastructure
**Success Metrics**: Scaling responsiveness, resource efficiency, availability

## Detailed Implementation

### 1. Horizontal Pod Autoscaler (HPA) Configuration

```yaml
# k8s/autoscaling/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: nexus-app-hpa
  namespace: nexus-saas
  labels:
    app: nexus-saas
    component: autoscaling
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: nexus-app
  minReplicas: 3
  maxReplicas: 50

  # Scaling behavior configuration
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60  # Wait 60s before scaling up again
      policies:
      - type: Percent
        value: 100  # Scale up by 100% (double)
        periodSeconds: 60
      - type: Pods
        value: 4    # Or add 4 pods
        periodSeconds: 60
      selectPolicy: Max  # Use the policy that adds more pods
    scaleDown:
      stabilizationWindowSeconds: 300  # Wait 5 minutes before scaling down
      policies:
      - type: Percent
        value: 50   # Scale down by 50%
        periodSeconds: 60
      - type: Pods
        value: 2    # Or remove 2 pods
        periodSeconds: 60
      selectPolicy: Min  # Use the policy that removes fewer pods

  # Multi-metric scaling configuration
  metrics:
  # CPU utilization
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70

  # Memory utilization
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80

  # Custom metric: HTTP requests per second
  - type: Pods
    pods:
      metric:
        name: http_requests_per_second
      target:
        type: AverageValue
        averageValue: "100"

  # External metric: Database connection pool utilization
  - type: External
    external:
      metric:
        name: database_connection_pool_utilization
        selector:
          matchLabels:
            tenant: "all"
      target:
        type: AverageValue
        averageValue: "70"

---
# Tenant-specific HPA for enterprise customers
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: nexus-app-enterprise-hpa
  namespace: nexus-saas
  labels:
    app: nexus-saas
    tier: enterprise
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: nexus-app-enterprise
  minReplicas: 5
  maxReplicas: 100

  behavior:
    scaleUp:
      stabilizationWindowSeconds: 30  # Faster scaling for enterprise
      policies:
      - type: Percent
        value: 200  # Scale up by 200%
        periodSeconds: 30
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 600  # More conservative scale down
      policies:
      - type: Percent
        value: 25   # Scale down by 25%
        periodSeconds: 60
      selectPolicy: Min

  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 60  # Lower threshold for enterprise
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 70
  - type: Pods
    pods:
      metric:
        name: tenant_specific_requests_per_second
      target:
        type: AverageValue
        averageValue: "200"  # Higher capacity for enterprise
```

### 2. Vertical Pod Autoscaler (VPA) Configuration

```yaml
# k8s/autoscaling/vpa.yaml
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: nexus-app-vpa
  namespace: nexus-saas
  labels:
    app: nexus-saas
    component: vpa
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: nexus-app

  updatePolicy:
    updateMode: "Auto"  # Automatically apply recommendations

  resourcePolicy:
    containerPolicies:
    - containerName: nexus-app
      minAllowed:
        cpu: 100m
        memory: 128Mi
      maxAllowed:
        cpu: 2000m
        memory: 4Gi
      controlledResources: ["cpu", "memory"]
      controlledValues: RequestsAndLimits

    - containerName: redis-sidecar
      minAllowed:
        cpu: 50m
        memory: 64Mi
      maxAllowed:
        cpu: 500m
        memory: 1Gi
      controlledResources: ["cpu", "memory"]

---
# VPA for database connections
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: nexus-db-proxy-vpa
  namespace: nexus-saas
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: nexus-db-proxy

  updatePolicy:
    updateMode: "Initial"  # Only set resources at pod creation

  resourcePolicy:
    containerPolicies:
    - containerName: db-proxy
      minAllowed:
        cpu: 200m
        memory: 256Mi
      maxAllowed:
        cpu: 1000m
        memory: 2Gi
      controlledResources: ["cpu", "memory"]
```

### 3. Load Balancer and Ingress Configuration

```yaml
# k8s/networking/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nexus-app-ingress
  namespace: nexus-saas
  annotations:
    # Enable container-native load balancing
    cloud.google.com/neg: '{"ingress": true}'

    # Load balancing configuration
    cloud.google.com/load-balancer-type: "External"
    kubernetes.io/ingress.class: "gce"

    # Health check configuration
    cloud.google.com/health-check-path: "/api/health"
    cloud.google.com/health-check-port: "3000"
    cloud.google.com/health-check-interval: "10"
    cloud.google.com/health-check-timeout: "5"
    cloud.google.com/health-check-unhealthy-threshold: "3"
    cloud.google.com/health-check-healthy-threshold: "2"

    # Session affinity
    cloud.google.com/session-affinity: "CLIENT_IP"
    cloud.google.com/session-affinity-timeout: "3600"

    # SSL and security
    kubernetes.io/ingress.allow-http: "false"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"

    # Rate limiting
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"

spec:
  tls:
  - hosts:
    - nexus-saas.com
    - "*.nexus-saas.com"
    secretName: nexus-saas-tls

  rules:
  - host: nexus-saas.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: nexus-app-service
            port:
              number: 80

  - host: api.nexus-saas.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: nexus-api-service
            port:
              number: 80

---
# Service configuration with NEG support
apiVersion: v1
kind: Service
metadata:
  name: nexus-app-service
  namespace: nexus-saas
  annotations:
    # Enable Network Endpoint Groups for container-native load balancing
    cloud.google.com/neg: '{"ingress": true}'

    # Service-level load balancing configuration
    cloud.google.com/load-balancer-type: "Internal"

spec:
  type: ClusterIP
  selector:
    app: nexus-saas
    component: app
  ports:
  - name: http
    port: 80
    targetPort: 3000
    protocol: TCP
    appProtocol: HTTP
  - name: metrics
    port: 9090
    targetPort: 9090
    protocol: TCP

---
# Backend configuration for advanced load balancing
apiVersion: cloud.google.com/v1
kind: BackendConfig
metadata:
  name: nexus-app-backend-config
  namespace: nexus-saas
spec:
  # Health check configuration
  healthCheck:
    checkIntervalSec: 10
    timeoutSec: 5
    healthyThreshold: 2
    unhealthyThreshold: 3
    type: HTTP
    requestPath: /api/health
    port: 3000

  # Session affinity
  sessionAffinity:
    affinityType: "CLIENT_IP"
    affinityCookieTtlSec: 3600

  # Connection draining
  connectionDraining:
    drainingTimeoutSec: 300

  # Custom request headers
  customRequestHeaders:
    headers:
    - "X-Load-Balancer: GCP-GLB"
    - "X-Forwarded-Proto: https"

  # Timeout configuration
  timeoutSec: 30

  # CDN configuration
  cdn:
    enabled: true
    cachePolicy:
      includeHost: true
      includeProtocol: true
      includeQueryString: false
```

### 4. Custom Metrics and Monitoring

```yaml
# k8s/monitoring/custom-metrics.yaml
apiVersion: v1
kind: ServiceMonitor
metadata:
  name: nexus-app-metrics
  namespace: nexus-saas
  labels:
    app: nexus-saas
    component: monitoring
spec:
  selector:
    matchLabels:
      app: nexus-saas
      component: app
  endpoints:
  - port: metrics
    interval: 15s
    path: /metrics

---
# Custom metrics for HPA
apiVersion: v1
kind: ConfigMap
metadata:
  name: custom-metrics-config
  namespace: nexus-saas
data:
  config.yaml: |
    rules:
    - seriesQuery: 'http_requests_total{namespace!="",pod!=""}'
      resources:
        overrides:
          namespace: {resource: "namespace"}
          pod: {resource: "pod"}
      name:
        matches: "^http_requests_total"
        as: "http_requests_per_second"
      metricsQuery: 'rate(http_requests_total{<<.LabelMatchers>>}[2m])'

    - seriesQuery: 'database_connections_active{namespace!="",pod!=""}'
      resources:
        overrides:
          namespace: {resource: "namespace"}
          pod: {resource: "pod"}
      name:
        matches: "^database_connections_active"
        as: "database_connection_pool_utilization"
      metricsQuery: '(database_connections_active{<<.LabelMatchers>>} / database_connections_max{<<.LabelMatchers>>}) * 100'

---
# Prometheus rules for scaling metrics
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: nexus-scaling-rules
  namespace: nexus-saas
spec:
  groups:
  - name: nexus.scaling
    rules:
    - alert: HighCPUUtilization
      expr: rate(container_cpu_usage_seconds_total{pod=~"nexus-app-.*"}[5m]) * 100 > 80
      for: 2m
      labels:
        severity: warning
        component: autoscaling
      annotations:
        summary: "High CPU utilization detected"
        description: "CPU utilization is above 80% for {{ $labels.pod }}"

    - alert: HighMemoryUtilization
      expr: (container_memory_working_set_bytes{pod=~"nexus-app-.*"} / container_spec_memory_limit_bytes{pod=~"nexus-app-.*"}) * 100 > 85
      for: 2m
      labels:
        severity: warning
        component: autoscaling
      annotations:
        summary: "High memory utilization detected"
        description: "Memory utilization is above 85% for {{ $labels.pod }}"

    - alert: HPAScalingEvent
      expr: increase(kube_hpa_status_current_replicas{hpa=~"nexus-app.*"}[5m]) > 0
      for: 0m
      labels:
        severity: info
        component: autoscaling
      annotations:
        summary: "HPA scaling event occurred"
        description: "HPA {{ $labels.hpa }} scaled to {{ $value }} replicas"
```

### 5. Predictive Scaling Implementation

```typescript
// lib/scaling/predictive-scaling.ts
import { k8sApi } from '@/lib/kubernetes/client';
import { logger } from '@/lib/logger';
import { prisma } from '@/lib/database/prisma';

interface ScalingPrediction {
  timestamp: Date;
  predictedLoad: number;
  recommendedReplicas: number;
  confidence: number;
  factors: string[];
}

interface HistoricalMetric {
  timestamp: Date;
  cpuUtilization: number;
  memoryUtilization: number;
  requestRate: number;
  replicas: number;
  tenantCount: number;
}

class PredictiveScaler {
  private readonly PREDICTION_WINDOW = 30 * 60 * 1000; // 30 minutes
  private readonly HISTORY_DAYS = 30;
  private readonly MIN_CONFIDENCE = 0.7;

  async generateScalingPrediction(
    deploymentName: string,
    namespace: string = 'nexus-saas'
  ): Promise<ScalingPrediction | null> {
    try {
      // Collect historical data
      const historicalData = await this.collectHistoricalData(deploymentName);

      if (historicalData.length < 100) {
        logger.warn('Insufficient historical data for prediction', {
          deploymentName,
          dataPoints: historicalData.length
        });
        return null;
      }

      // Analyze patterns
      const patterns = this.analyzePatterns(historicalData);

      // Generate prediction
      const prediction = this.predictLoad(patterns, historicalData);

      // Calculate recommended replicas
      const recommendedReplicas = this.calculateRecommendedReplicas(
        prediction.predictedLoad,
        historicalData
      );

      const scalingPrediction: ScalingPrediction = {
        timestamp: new Date(),
        predictedLoad: prediction.predictedLoad,
        recommendedReplicas,
        confidence: prediction.confidence,
        factors: prediction.factors
      };

      // Apply prediction if confidence is high enough
      if (prediction.confidence >= this.MIN_CONFIDENCE) {
        await this.applyPredictiveScaling(
          deploymentName,
          namespace,
          scalingPrediction
        );
      }

      return scalingPrediction;
    } catch (error) {
      logger.error('Failed to generate scaling prediction', {
        deploymentName,
        error: error.message
      });
      return null;
    }
  }

  private async collectHistoricalData(deploymentName: string): Promise<HistoricalMetric[]> {
    const endTime = new Date();
    const startTime = new Date(endTime.getTime() - (this.HISTORY_DAYS * 24 * 60 * 60 * 1000));

    // This would typically query Prometheus or your metrics store
    // For now, we'll simulate with database queries
    const metrics = await prisma.scalingMetric.findMany({
      where: {
        deploymentName,
        timestamp: {
          gte: startTime,
          lte: endTime
        }
      },
      orderBy: { timestamp: 'asc' }
    });

    return metrics.map(metric => ({
      timestamp: metric.timestamp,
      cpuUtilization: metric.cpuUtilization,
      memoryUtilization: metric.memoryUtilization,
      requestRate: metric.requestRate,
      replicas: metric.replicas,
      tenantCount: metric.tenantCount
    }));
  }

  private analyzePatterns(data: HistoricalMetric[]): {
    hourlyPatterns: Map<number, number>;
    dailyPatterns: Map<number, number>;
    weeklyPatterns: Map<number, number>;
    trends: { slope: number; correlation: number };
  } {
    const hourlyPatterns = new Map<number, number>();
    const dailyPatterns = new Map<number, number>();
    const weeklyPatterns = new Map<number, number>();

    // Analyze hourly patterns
    for (const metric of data) {
      const hour = metric.timestamp.getHours();
      const currentAvg = hourlyPatterns.get(hour) || 0;
      const count = Array.from(hourlyPatterns.values()).length || 1;
      hourlyPatterns.set(hour, (currentAvg * count + metric.requestRate) / (count + 1));
    }

    // Analyze daily patterns
    for (const metric of data) {
      const day = metric.timestamp.getDay();
      const currentAvg = dailyPatterns.get(day) || 0;
      const count = Array.from(dailyPatterns.values()).length || 1;
      dailyPatterns.set(day, (currentAvg * count + metric.requestRate) / (count + 1));
    }

    // Analyze weekly patterns
    for (const metric of data) {
      const week = Math.floor(metric.timestamp.getTime() / (7 * 24 * 60 * 60 * 1000));
      const currentAvg = weeklyPatterns.get(week) || 0;
      const count = Array.from(weeklyPatterns.values()).length || 1;
      weeklyPatterns.set(week, (currentAvg * count + metric.requestRate) / (count + 1));
    }

    // Calculate trends (simplified linear regression)
    const trends = this.calculateTrends(data);

    return {
      hourlyPatterns,
      dailyPatterns,
      weeklyPatterns,
      trends
    };
  }

  private calculateTrends(data: HistoricalMetric[]): { slope: number; correlation: number } {
    if (data.length < 2) return { slope: 0, correlation: 0 };

    const n = data.length;
    const sumX = data.reduce((sum, _, i) => sum + i, 0);
    const sumY = data.reduce((sum, metric) => sum + metric.requestRate, 0);
    const sumXY = data.reduce((sum, metric, i) => sum + i * metric.requestRate, 0);
    const sumXX = data.reduce((sum, _, i) => sum + i * i, 0);
    const sumYY = data.reduce((sum, metric) => sum + metric.requestRate * metric.requestRate, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const correlation = (n * sumXY - sumX * sumY) /
      Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));

    return { slope, correlation };
  }

  private predictLoad(
    patterns: any,
    historicalData: HistoricalMetric[]
  ): { predictedLoad: number; confidence: number; factors: string[] } {
    const now = new Date();
    const futureTime = new Date(now.getTime() + this.PREDICTION_WINDOW);

    const currentHour = now.getHours();
    const futureHour = futureTime.getHours();
    const currentDay = now.getDay();
    const futureDay = futureTime.getDay();

    // Get pattern-based predictions
    const hourlyPrediction = patterns.hourlyPatterns.get(futureHour) || 0;
    const dailyPrediction = patterns.dailyPatterns.get(futureDay) || 0;

    // Get trend-based prediction
    const trendPrediction = patterns.trends.slope * (this.PREDICTION_WINDOW / (60 * 60 * 1000));

    // Combine predictions with weights
    const weights = {
      hourly: 0.4,
      daily: 0.3,
      trend: 0.3
    };

    const predictedLoad =
      hourlyPrediction * weights.hourly +
      dailyPrediction * weights.daily +
      trendPrediction * weights.trend;

    // Calculate confidence based on pattern consistency
    const confidence = Math.min(
      Math.abs(patterns.trends.correlation),
      patterns.hourlyPatterns.size > 20 ? 0.9 : 0.6
    );

    const factors = [
      `Hourly pattern (${futureHour}:00): ${hourlyPrediction.toFixed(2)}`,
      `Daily pattern (${['Sun','Mon','Tue','Wed','Thu','Fri','Sat'][futureDay]}): ${dailyPrediction.toFixed(2)}`,
      `Trend: ${trendPrediction > 0 ? '+' : ''}${trendPrediction.toFixed(2)}`
    ];

    return {
      predictedLoad: Math.max(0, predictedLoad),
      confidence,
      factors
    };
  }

  private calculateRecommendedReplicas(
    predictedLoad: number,
    historicalData: HistoricalMetric[]
  ): number {
    // Find historical data points with similar load
    const similarLoadPoints = historicalData.filter(
      metric => Math.abs(metric.requestRate - predictedLoad) < predictedLoad * 0.2
    );

    if (similarLoadPoints.length === 0) {
      // Fallback to simple calculation
      return Math.max(1, Math.ceil(predictedLoad / 100)); // Assume 100 RPS per replica
    }

    // Average replicas for similar load
    const avgReplicas = similarLoadPoints.reduce((sum, metric) => sum + metric.replicas, 0) /
                       similarLoadPoints.length;

    return Math.max(1, Math.ceil(avgReplicas));
  }

  private async applyPredictiveScaling(
    deploymentName: string,
    namespace: string,
    prediction: ScalingPrediction
  ): Promise<void> {
    try {
      // Get current HPA
      const hpa = await k8sApi.autoscaling.readNamespacedHorizontalPodAutoscaler(
        `${deploymentName}-hpa`,
        namespace
      );

      const currentReplicas = hpa.body.status?.currentReplicas || 1;
      const targetReplicas = prediction.recommendedReplicas;

      // Only scale if the change is significant
      const changeThreshold = Math.max(2, currentReplicas * 0.2);

      if (Math.abs(targetReplicas - currentReplicas) >= changeThreshold) {
        // Create a temporary annotation to influence HPA
        await k8sApi.apps.patchNamespacedDeployment(
          deploymentName,
          namespace,
          {
            metadata: {
              annotations: {
                'nexus.ai/predictive-scaling': JSON.stringify({
                  prediction: prediction.predictedLoad,
                  confidence: prediction.confidence,
                  recommendedReplicas: targetReplicas,
                  timestamp: prediction.timestamp.toISOString()
                })
              }
            }
          }
        );

        logger.info('Applied predictive scaling', {
          deploymentName,
          currentReplicas,
          targetReplicas,
          predictedLoad: prediction.predictedLoad,
          confidence: prediction.confidence
        });
      }
    } catch (error) {
      logger.error('Failed to apply predictive scaling', {
        deploymentName,
        error: error.message
      });
    }
  }

  async startPredictiveScaling(): Promise<void> {
    // Run predictions every 5 minutes
    setInterval(async () => {
      const deployments = ['nexus-app', 'nexus-api', 'nexus-worker'];

      for (const deployment of deployments) {
        await this.generateScalingPrediction(deployment);
      }
    }, 5 * 60 * 1000);

    logger.info('Predictive scaling started');
  }
}

export const predictiveScaler = new PredictiveScaler();
```

*Built with ❤️ by Nexus-Master Agent*
*Where 125 Senior Developers Meet AI Excellence*
