import { Client } from "@elastic/elasticsearch";
import { 
  SearchConfig, 
  SearchDocument, 
  SearchQuery, 
  SearchResult, 
  IndexConfig, 
  IndexStats,
  SearchFilters 
} from "./types";

export class ElasticsearchService {
  private client: Client;
  private config: SearchConfig;

  constructor(config: SearchConfig) {
    this.config = config;
    
    this.client = new Client({
      nodes: config.elasticsearch.nodes,
      auth: config.elasticsearch.auth,
      tls: config.elasticsearch.ssl,
      requestTimeout: config.elasticsearch.requestTimeout,
      pingTimeout: config.elasticsearch.pingTimeout,
      maxRetries: config.elasticsearch.maxRetries,
    });
  }

  // Initialize indices
  async initializeIndices(): Promise<void> {
    const indices = [
      {
        name: this.config.indices.documents,
        config: this.getDocumentsIndexConfig(),
      },
      {
        name: this.config.indices.analytics,
        config: this.getAnalyticsIndexConfig(),
      },
      {
        name: this.config.indices.suggestions,
        config: this.getSuggestionsIndexConfig(),
      },
    ];

    for (const index of indices) {
      await this.createIndexIfNotExists(index.name, index.config);
    }
  }

  // Create index if it doesn't exist
  private async createIndexIfNotExists(name: string, config: IndexConfig): Promise<void> {
    try {
      const exists = await this.client.indices.exists({ index: name });
      
      if (!exists) {
        await this.client.indices.create({
          index: name,
          body: {
            settings: config.settings,
            mappings: config.mappings,
          },
        });
        
        console.log(`Created index: ${name}`);
      }
    } catch (error) {
      console.error(`Failed to create index ${name}:`, error);
      throw error;
    }
  }

  // Index document
  async indexDocument(document: SearchDocument): Promise<void> {
    try {
      await this.client.index({
        index: this.config.indices.documents,
        id: document.id,
        body: {
          ...document,
          indexedAt: new Date(),
        },
        refresh: "wait_for",
      });
    } catch (error) {
      console.error(`Failed to index document ${document.id}:`, error);
      throw error;
    }
  }

  // Bulk index documents
  async bulkIndexDocuments(documents: SearchDocument[]): Promise<void> {
    if (documents.length === 0) return;

    try {
      const body = documents.flatMap(doc => [
        { index: { _index: this.config.indices.documents, _id: doc.id } },
        { ...doc, indexedAt: new Date() },
      ]);

      const response = await this.client.bulk({
        body,
        refresh: "wait_for",
      });

      if (response.errors) {
        const errors = response.items
          .filter((item: any) => item.index?.error)
          .map((item: any) => item.index.error);
        
        console.error("Bulk indexing errors:", errors);
        throw new Error(`Bulk indexing failed: ${errors.length} errors`);
      }
    } catch (error) {
      console.error("Failed to bulk index documents:", error);
      throw error;
    }
  }

  // Search documents
  async searchDocuments(query: SearchQuery): Promise<SearchResult> {
    try {
      const searchBody = this.buildSearchQuery(query);
      
      const response = await this.client.search({
        index: this.config.indices.documents,
        body: searchBody,
        timeout: this.config.search.timeout,
      });

      return this.parseSearchResponse(response);
    } catch (error) {
      console.error("Search failed:", error);
      throw error;
    }
  }

  // Build Elasticsearch query
  private buildSearchQuery(query: SearchQuery): any {
    const searchBody: any = {
      query: this.buildQuery(query.query, query.filters),
      size: Math.min(
        query.pagination?.size || this.config.search.defaultSize,
        this.config.search.maxSize
      ),
      from: ((query.pagination?.page || 1) - 1) * (query.pagination?.size || this.config.search.defaultSize),
    };

    // Add sorting
    if (query.sort && query.sort.length > 0) {
      searchBody.sort = query.sort.map(sort => ({
        [sort.field]: {
          order: sort.order,
          mode: sort.mode,
        },
      }));
    } else {
      // Default sort by relevance and date
      searchBody.sort = [
        "_score",
        { "createdAt": { order: "desc" } },
      ];
    }

    // Add highlighting
    if (query.highlight?.enabled) {
      searchBody.highlight = {
        fields: query.highlight.fields.reduce((acc, field) => {
          acc[field] = {
            fragment_size: query.highlight?.fragmentSize || this.config.search.highlightFragmentSize,
            number_of_fragments: query.highlight?.numberOfFragments || this.config.search.highlightNumberOfFragments,
          };
          return acc;
        }, {} as any),
        pre_tags: ["<mark>"],
        post_tags: ["</mark>"],
      };
    }

    // Add aggregations
    if (query.aggregations && query.aggregations.length > 0) {
      searchBody.aggs = query.aggregations.reduce((acc, agg) => {
        acc[agg.name] = this.buildAggregation(agg);
        return acc;
      }, {} as any);
    }

    // Add suggestions
    if (query.suggestions?.enabled) {
      searchBody.suggest = {
        text: query.query,
        simple_phrase: {
          phrase: {
            field: query.suggestions.field,
            size: this.config.search.suggestionSize,
            gram_size: 3,
            direct_generator: [{
              field: query.suggestions.field,
              suggest_mode: "missing",
            }],
          },
        },
      };
    }

    return searchBody;
  }

  // Build main query
  private buildQuery(queryString: string, filters?: SearchFilters): any {
    const mustClauses: any[] = [];
    const filterClauses: any[] = [];

    // Main text query
    if (queryString && queryString.trim()) {
      mustClauses.push({
        multi_match: {
          query: queryString,
          fields: [
            "title^3",
            "content^2",
            "summary^2",
            "tags^1.5",
            "categories^1.5",
            "author.name",
          ],
          type: "best_fields",
          fuzziness: "AUTO",
          operator: "or",
        },
      });
    } else {
      mustClauses.push({ match_all: {} });
    }

    // Apply filters
    if (filters) {
      if (filters.types && filters.types.length > 0) {
        filterClauses.push({ terms: { type: filters.types } });
      }

      if (filters.tenantId) {
        filterClauses.push({ term: { tenantId: filters.tenantId } });
      }

      if (filters.workspaceId) {
        filterClauses.push({ term: { workspaceId: filters.workspaceId } });
      }

      if (filters.projectId) {
        filterClauses.push({ term: { projectId: filters.projectId } });
      }

      if (filters.authorId) {
        filterClauses.push({ term: { "author.id": filters.authorId } });
      }

      if (filters.tags && filters.tags.length > 0) {
        filterClauses.push({ terms: { tags: filters.tags } });
      }

      if (filters.categories && filters.categories.length > 0) {
        filterClauses.push({ terms: { categories: filters.categories } });
      }

      if (filters.status && filters.status.length > 0) {
        filterClauses.push({ terms: { status: filters.status } });
      }

      if (filters.language && filters.language.length > 0) {
        filterClauses.push({ terms: { language: filters.language } });
      }

      if (filters.dateRange) {
        const dateFilter: any = { range: {} };
        dateFilter.range[filters.dateRange.field] = {};
        
        if (filters.dateRange.start) {
          dateFilter.range[filters.dateRange.field].gte = filters.dateRange.start;
        }
        
        if (filters.dateRange.end) {
          dateFilter.range[filters.dateRange.field].lte = filters.dateRange.end;
        }
        
        filterClauses.push(dateFilter);
      }

      // Permission filtering
      if (filters.permissions) {
        const permissionClauses: any[] = [];
        
        if (filters.permissions.includePublic) {
          permissionClauses.push({ term: { "permissions.public": true } });
        }
        
        permissionClauses.push({ term: { "permissions.users": filters.permissions.userId } });
        
        filterClauses.push({ bool: { should: permissionClauses } });
      }
    }

    return {
      bool: {
        must: mustClauses,
        filter: filterClauses,
      },
    };
  }

  // Build aggregation
  private buildAggregation(agg: any): any {
    switch (agg.type) {
      case "terms":
        return {
          terms: {
            field: agg.field,
            size: agg.size || 10,
          },
        };
      
      case "date_histogram":
        return {
          date_histogram: {
            field: agg.field,
            calendar_interval: agg.interval || "day",
          },
        };
      
      case "range":
        return {
          range: {
            field: agg.field,
            ranges: agg.ranges || [],
          },
        };
      
      case "stats":
        return {
          stats: {
            field: agg.field,
          },
        };
      
      default:
        throw new Error(`Unsupported aggregation type: ${agg.type}`);
    }
  }

  // Parse search response
  private parseSearchResponse(response: any): SearchResult {
    return {
      documents: response.hits.hits.map((hit: any) => ({
        id: hit._id,
        type: hit._source.type,
        score: hit._score,
        source: hit._source,
        highlight: hit.highlight,
        sort: hit.sort,
      })),
      total: {
        value: response.hits.total.value,
        relation: response.hits.total.relation,
      },
      maxScore: response.hits.max_score || 0,
      aggregations: response.aggregations || {},
      suggestions: this.parseSuggestions(response.suggest),
      took: response.took,
    };
  }

  // Parse suggestions
  private parseSuggestions(suggest: any): any[] {
    if (!suggest || !suggest.simple_phrase) return [];
    
    return suggest.simple_phrase.map((suggestion: any) => ({
      text: suggestion.text,
      offset: suggestion.offset,
      length: suggestion.length,
      options: suggestion.options.map((option: any) => ({
        text: option.text,
        score: option.score,
        freq: option.freq,
      })),
    }));
  }

  // Delete document
  async deleteDocument(id: string): Promise<void> {
    try {
      await this.client.delete({
        index: this.config.indices.documents,
        id,
        refresh: "wait_for",
      });
    } catch (error: any) {
      if (error.meta?.statusCode !== 404) {
        console.error(`Failed to delete document ${id}:`, error);
        throw error;
      }
    }
  }

  // Update document
  async updateDocument(id: string, updates: Partial<SearchDocument>): Promise<void> {
    try {
      await this.client.update({
        index: this.config.indices.documents,
        id,
        body: {
          doc: {
            ...updates,
            indexedAt: new Date(),
          },
        },
        refresh: "wait_for",
      });
    } catch (error) {
      console.error(`Failed to update document ${id}:`, error);
      throw error;
    }
  }

  // Get index statistics
  async getIndexStats(): Promise<IndexStats[]> {
    try {
      const response = await this.client.indices.stats({
        index: Object.values(this.config.indices),
      });

      return Object.entries(response.indices).map(([name, stats]: [string, any]) => ({
        name,
        health: stats.health || "unknown",
        status: stats.status || "unknown",
        uuid: stats.uuid || "",
        primaryShards: stats.primaries?.docs?.count || 0,
        replicaShards: stats.total?.docs?.count || 0,
        documentsCount: stats.primaries?.docs?.count || 0,
        documentsDeleted: stats.primaries?.docs?.deleted || 0,
        storeSize: stats.total?.store?.size_in_bytes || "0b",
        primaryStoreSize: stats.primaries?.store?.size_in_bytes || "0b",
      }));
    } catch (error) {
      console.error("Failed to get index stats:", error);
      throw error;
    }
  }

  // Index configurations
  private getDocumentsIndexConfig(): IndexConfig {
    return {
      name: this.config.indices.documents,
      settings: {
        numberOfShards: this.config.indexing.numberOfShards,
        numberOfReplicas: this.config.indexing.numberOfReplicas,
        refreshInterval: this.config.indexing.refreshInterval,
        maxResultWindow: 10000,
        analysis: {
          analyzers: {
            content_analyzer: {
              type: "custom",
              tokenizer: "standard",
              filter: ["lowercase", "stop", "snowball"],
            },
            search_analyzer: {
              type: "custom",
              tokenizer: "standard",
              filter: ["lowercase", "stop"],
            },
          },
          tokenizers: {},
          filters: {},
        },
      },
      mappings: {
        properties: {
          id: { type: "keyword" },
          type: { type: "keyword" },
          tenantId: { type: "keyword" },
          workspaceId: { type: "keyword" },
          projectId: { type: "keyword" },
          title: {
            type: "text",
            analyzer: "content_analyzer",
            searchAnalyzer: "search_analyzer",
            fields: {
              keyword: { type: "keyword" },
            },
          },
          content: {
            type: "text",
            analyzer: "content_analyzer",
            searchAnalyzer: "search_analyzer",
          },
          summary: {
            type: "text",
            analyzer: "content_analyzer",
          },
          author: {
            type: "object",
            properties: {
              id: { type: "keyword" },
              name: { type: "text" },
              email: { type: "keyword" },
            },
          },
          tags: { type: "keyword" },
          categories: { type: "keyword" },
          metadata: { type: "object" },
          permissions: {
            type: "object",
            properties: {
              public: { type: "boolean" },
              users: { type: "keyword" },
              teams: { type: "keyword" },
              workspaces: { type: "keyword" },
            },
          },
          status: { type: "keyword" },
          language: { type: "keyword" },
          createdAt: { type: "date" },
          updatedAt: { type: "date" },
          indexedAt: { type: "date" },
        },
      },
    };
  }

  private getAnalyticsIndexConfig(): IndexConfig {
    return {
      name: this.config.indices.analytics,
      settings: {
        numberOfShards: 1,
        numberOfReplicas: 0,
        refreshInterval: "5s",
        maxResultWindow: 10000,
        analysis: {
          analyzers: {},
          tokenizers: {},
          filters: {},
        },
      },
      mappings: {
        properties: {
          id: { type: "keyword" },
          type: { type: "keyword" },
          tenantId: { type: "keyword" },
          workspaceId: { type: "keyword" },
          projectId: { type: "keyword" },
          userId: { type: "keyword" },
          sessionId: { type: "keyword" },
          properties: { type: "object" },
          context: {
            type: "object",
            properties: {
              userAgent: { type: "text" },
              ip: { type: "ip" },
              referrer: { type: "keyword" },
              page: { type: "keyword" },
              timestamp: { type: "date" },
            },
          },
          createdAt: { type: "date" },
        },
      },
    };
  }

  private getSuggestionsIndexConfig(): IndexConfig {
    return {
      name: this.config.indices.suggestions,
      settings: {
        numberOfShards: 1,
        numberOfReplicas: 0,
        refreshInterval: "30s",
        maxResultWindow: 1000,
        analysis: {
          analyzers: {
            suggest_analyzer: {
              type: "custom",
              tokenizer: "standard",
              filter: ["lowercase"],
            },
          },
          tokenizers: {},
          filters: {},
        },
      },
      mappings: {
        properties: {
          text: {
            type: "text",
            analyzer: "suggest_analyzer",
          },
          weight: { type: "integer" },
          context: { type: "keyword" },
        },
      },
    };
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.client.ping();
      return response.statusCode === 200;
    } catch (error) {
      return false;
    }
  }

  // Close connection
  async close(): Promise<void> {
    await this.client.close();
  }
}
