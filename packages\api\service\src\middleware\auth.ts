import { FastifyInstance, FastifyRequest, FastifyReply } from "fastify";
import fp from "fastify-plugin";
import { AuthenticatedRequest, AuthenticationError, AuthorizationError } from "../types";
import { config } from "../config";

// JWT payload interface
interface JWTPayload {
  userId: string;
  email: string;
  tenantId: string;
  roles: string[];
  iat: number;
  exp: number;
}

// Authentication plugin
export const authPlugin = fp(async (fastify: FastifyInstance) => {
  // Register JWT plugin
  await fastify.register(require("@fastify/jwt"), {
    secret: config.JWT_SECRET,
    sign: {
      expiresIn: config.JWT_EXPIRES_IN,
    },
    verify: {
      extractToken: (request) => {
        const authorization = request.headers.authorization;
        if (authorization && authorization.startsWith("Bearer ")) {
          return authorization.slice(7);
        }
        return null;
      },
    },
  });

  // Add authentication decorator
  fastify.decorate("authenticate", async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const payload = await request.jwtVerify<JWTPayload>();
      
      // Add user to request
      (request as AuthenticatedRequest).user = {
        id: payload.userId,
        email: payload.email,
        tenantId: payload.tenantId,
        roles: payload.roles,
      };

      // Update request context
      (request as AuthenticatedRequest).context = {
        ...(request as any).context,
        userId: payload.userId,
        tenantId: payload.tenantId,
        roles: payload.roles,
      };
    } catch (error) {
      throw new AuthenticationError("Invalid or expired token");
    }
  });

  // Add optional authentication decorator
  fastify.decorate("optionalAuthenticate", async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const payload = await request.jwtVerify<JWTPayload>();
      
      // Add user to request
      (request as AuthenticatedRequest).user = {
        id: payload.userId,
        email: payload.email,
        tenantId: payload.tenantId,
        roles: payload.roles,
      };

      // Update request context
      (request as AuthenticatedRequest).context = {
        ...(request as any).context,
        userId: payload.userId,
        tenantId: payload.tenantId,
        roles: payload.roles,
      };
    } catch (error) {
      // Ignore authentication errors for optional auth
      (request as AuthenticatedRequest).user = undefined;
    }
  });
});

// Permission checking middleware
export const requirePermissions = (permissions: string[]) => {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    const authRequest = request as AuthenticatedRequest;
    
    if (!authRequest.user) {
      throw new AuthenticationError("Authentication required");
    }

    // TODO: Implement actual permission checking with RBAC
    // For now, just check if user has admin role for any permission
    const hasPermission = authRequest.user.roles.includes("admin") || 
                         authRequest.user.roles.includes("owner") ||
                         authRequest.user.roles.includes("system_admin");

    if (!hasPermission) {
      throw new AuthorizationError(`Missing required permissions: ${permissions.join(", ")}`);
    }
  };
};

// Role checking middleware
export const requireRoles = (roles: string[]) => {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    const authRequest = request as AuthenticatedRequest;
    
    if (!authRequest.user) {
      throw new AuthenticationError("Authentication required");
    }

    const hasRole = roles.some(role => authRequest.user!.roles.includes(role));
    
    if (!hasRole) {
      throw new AuthorizationError(`Missing required roles: ${roles.join(", ")}`);
    }
  };
};

// Tenant isolation middleware
export const requireTenant = (request: FastifyRequest, reply: FastifyReply) => {
  const authRequest = request as AuthenticatedRequest;
  
  if (!authRequest.user) {
    throw new AuthenticationError("Authentication required");
  }

  if (!authRequest.user.tenantId) {
    throw new AuthorizationError("Tenant context required");
  }

  // Add tenant ID to context
  authRequest.context.tenantId = authRequest.user.tenantId;
};

// Admin only middleware
export const requireAdmin = requireRoles(["admin", "owner", "system_admin"]);

// Owner only middleware
export const requireOwner = requireRoles(["owner", "system_admin"]);

// System admin only middleware
export const requireSystemAdmin = requireRoles(["system_admin"]);

// Generate JWT token
export const generateToken = (fastify: FastifyInstance, payload: Omit<JWTPayload, "iat" | "exp">) => {
  return fastify.jwt.sign(payload);
};

// Generate refresh token
export const generateRefreshToken = (fastify: FastifyInstance, payload: Omit<JWTPayload, "iat" | "exp">) => {
  return fastify.jwt.sign(payload, { expiresIn: config.JWT_REFRESH_EXPIRES_IN });
};

// Verify token
export const verifyToken = async (fastify: FastifyInstance, token: string): Promise<JWTPayload> => {
  try {
    return fastify.jwt.verify(token) as JWTPayload;
  } catch (error) {
    throw new AuthenticationError("Invalid token");
  }
};
