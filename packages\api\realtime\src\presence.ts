import { EventEmitter } from "events";
import { User } from "@nexus/types";
import { UserPresence, SocketContext } from "./types";

export class PresenceManager extends EventEmitter {
  private presenceMap: Map<string, UserPresence> = new Map();
  private userSockets: Map<string, Set<string>> = new Map(); // userId -> socketIds
  private socketUsers: Map<string, string> = new Map(); // socketId -> userId
  private updateInterval: number;
  private offlineTimeout: number;
  private cleanupInterval: NodeJS.Timeout;

  constructor(
    updateInterval: number = 30000, // 30 seconds
    offlineTimeout: number = 300000 // 5 minutes
  ) {
    super();
    this.updateInterval = updateInterval;
    this.offlineTimeout = offlineTimeout;

    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanupOfflineUsers();
    }, 60000); // Cleanup every minute
  }

  // Add user presence
  addUser(socketId: string, context: SocketContext): void {
    const { user } = context;
    const userId = user.id;

    // Track socket-user mapping
    this.socketUsers.set(socketId, userId);
    
    if (!this.userSockets.has(userId)) {
      this.userSockets.set(userId, new Set());
    }
    this.userSockets.get(userId)!.add(socketId);

    // Create or update presence
    const existingPresence = this.presenceMap.get(userId);
    const presence: UserPresence = {
      userId,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        avatar: user.avatar,
      },
      status: existingPresence?.status || "online",
      lastSeen: new Date(),
      currentLocation: {
        workspaceId: context.workspaceId,
        teamId: context.teamId,
      },
      metadata: {
        device: this.extractDevice(context.socket.handshake.headers["user-agent"]),
        browser: this.extractBrowser(context.socket.handshake.headers["user-agent"]),
        ip: context.socket.handshake.address,
      },
    };

    this.presenceMap.set(userId, presence);

    // Emit presence update
    this.emit("presence:update", presence);
    this.emit("user:online", { userId, presence });
  }

  // Remove user presence
  removeUser(socketId: string): void {
    const userId = this.socketUsers.get(socketId);
    if (!userId) return;

    // Remove socket mapping
    this.socketUsers.delete(socketId);
    const userSockets = this.userSockets.get(userId);
    if (userSockets) {
      userSockets.delete(socketId);
      
      // If no more sockets for this user, mark as offline
      if (userSockets.size === 0) {
        this.userSockets.delete(userId);
        this.setUserOffline(userId);
      }
    }
  }

  // Update user status
  updateUserStatus(userId: string, status: UserPresence["status"]): void {
    const presence = this.presenceMap.get(userId);
    if (!presence) return;

    presence.status = status;
    presence.lastSeen = new Date();

    this.emit("presence:update", presence);
  }

  // Update user location
  updateUserLocation(
    userId: string,
    location: UserPresence["currentLocation"]
  ): void {
    const presence = this.presenceMap.get(userId);
    if (!presence) return;

    presence.currentLocation = { ...presence.currentLocation, ...location };
    presence.lastSeen = new Date();

    this.emit("presence:update", presence);
  }

  // Set user offline
  private setUserOffline(userId: string): void {
    const presence = this.presenceMap.get(userId);
    if (!presence) return;

    presence.status = "offline";
    presence.lastSeen = new Date();

    this.emit("presence:update", presence);
    this.emit("user:offline", { userId, presence });
  }

  // Get user presence
  getUserPresence(userId: string): UserPresence | null {
    return this.presenceMap.get(userId) || null;
  }

  // Get all presence data
  getAllPresence(): UserPresence[] {
    return Array.from(this.presenceMap.values());
  }

  // Get online users
  getOnlineUsers(): UserPresence[] {
    return this.getAllPresence().filter(p => p.status !== "offline");
  }

  // Get users in workspace
  getUsersInWorkspace(workspaceId: string): UserPresence[] {
    return this.getAllPresence().filter(
      p => p.currentLocation?.workspaceId === workspaceId
    );
  }

  // Get users in team
  getUsersInTeam(teamId: string): UserPresence[] {
    return this.getAllPresence().filter(
      p => p.currentLocation?.teamId === teamId
    );
  }

  // Get users in project
  getUsersInProject(projectId: string): UserPresence[] {
    return this.getAllPresence().filter(
      p => p.currentLocation?.projectId === projectId
    );
  }

  // Get users in document
  getUsersInDocument(documentId: string): UserPresence[] {
    return this.getAllPresence().filter(
      p => p.currentLocation?.documentId === documentId
    );
  }

  // Check if user is online
  isUserOnline(userId: string): boolean {
    const presence = this.getUserPresence(userId);
    return presence ? presence.status !== "offline" : false;
  }

  // Get user socket IDs
  getUserSocketIds(userId: string): string[] {
    const sockets = this.userSockets.get(userId);
    return sockets ? Array.from(sockets) : [];
  }

  // Cleanup offline users
  private cleanupOfflineUsers(): void {
    const now = Date.now();
    
    for (const [userId, presence] of this.presenceMap.entries()) {
      const timeSinceLastSeen = now - presence.lastSeen.getTime();
      
      // Mark as offline if no activity for offlineTimeout
      if (timeSinceLastSeen > this.offlineTimeout && presence.status !== "offline") {
        this.setUserOffline(userId);
      }
      
      // Remove from memory if offline for too long (24 hours)
      if (timeSinceLastSeen > 24 * 60 * 60 * 1000 && presence.status === "offline") {
        this.presenceMap.delete(userId);
        this.emit("presence:removed", { userId });
      }
    }
  }

  // Extract device from user agent
  private extractDevice(userAgent?: string): string {
    if (!userAgent) return "unknown";
    
    if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
      return "mobile";
    }
    if (/Tablet|iPad/.test(userAgent)) {
      return "tablet";
    }
    return "desktop";
  }

  // Extract browser from user agent
  private extractBrowser(userAgent?: string): string {
    if (!userAgent) return "unknown";
    
    if (userAgent.includes("Chrome")) return "chrome";
    if (userAgent.includes("Firefox")) return "firefox";
    if (userAgent.includes("Safari")) return "safari";
    if (userAgent.includes("Edge")) return "edge";
    return "unknown";
  }

  // Get presence statistics
  getStats(): {
    total: number;
    online: number;
    away: number;
    busy: number;
    offline: number;
    byWorkspace: Record<string, number>;
  } {
    const presence = this.getAllPresence();
    const stats = {
      total: presence.length,
      online: 0,
      away: 0,
      busy: 0,
      offline: 0,
      byWorkspace: {} as Record<string, number>,
    };

    for (const p of presence) {
      stats[p.status]++;
      
      if (p.currentLocation?.workspaceId) {
        const workspaceId = p.currentLocation.workspaceId;
        stats.byWorkspace[workspaceId] = (stats.byWorkspace[workspaceId] || 0) + 1;
      }
    }

    return stats;
  }

  // Destroy presence manager
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.presenceMap.clear();
    this.userSockets.clear();
    this.socketUsers.clear();
    this.removeAllListeners();
  }
}
