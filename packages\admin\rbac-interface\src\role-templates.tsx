"use client";

import React, { useState, useEffect } from "react";
import { useRBAC, useIsAdmin } from "@nexus/rbac";
import { RoleTemplate, RoleLevel } from "@nexus/rbac";

// Role templates component
export function RoleTemplates() {
  const { createRole, isLoading } = useRBAC();
  const isAdmin = useIsAdmin();
  const [templates, setTemplates] = useState<RoleTemplate[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  // Mock templates (in real app, these would come from API)
  useEffect(() => {
    setTemplates([
      {
        id: "1",
        name: "Content Manager",
        slug: "content_manager",
        description: "Can create, edit, and publish content across workspaces",
        level: "workspace",
        permissions: [
          { resource: "document", actions: ["create", "read", "update", "delete", "publish"], scope: "workspace" },
          { resource: "file", actions: ["create", "read", "update", "delete", "upload"], scope: "workspace" },
          { resource: "project", actions: ["read", "update"], scope: "workspace" },
        ],
        isPublic: true,
        category: "content",
        tags: ["content", "publishing", "workspace"],
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "2",
        name: "Team Lead",
        slug: "team_lead",
        description: "Manages team members and team-level resources",
        level: "team",
        permissions: [
          { resource: "team", actions: ["read", "update", "manage"], scope: "team" },
          { resource: "member", actions: ["create", "read", "update", "invite", "remove"], scope: "team" },
          { resource: "project", actions: ["create", "read", "update", "delete"], scope: "team" },
        ],
        isPublic: true,
        category: "management",
        tags: ["team", "leadership", "management"],
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "3",
        name: "Billing Administrator",
        slug: "billing_admin",
        description: "Manages billing, subscriptions, and financial data",
        level: "organization",
        permissions: [
          { resource: "subscription", actions: ["read", "create", "update", "cancel"], scope: "organization" },
          { resource: "invoice", actions: ["read", "create", "update", "delete"], scope: "organization" },
          { resource: "analytics", actions: ["read", "export"], scope: "organization" },
        ],
        isPublic: true,
        category: "billing",
        tags: ["billing", "finance", "subscription"],
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "4",
        name: "Developer",
        slug: "developer",
        description: "Access to development tools and integrations",
        level: "workspace",
        permissions: [
          { resource: "api", actions: ["read", "create", "update", "delete"], scope: "workspace" },
          { resource: "integration", actions: ["read", "create", "update", "delete"], scope: "workspace" },
          { resource: "analytics", actions: ["read"], scope: "workspace" },
        ],
        isPublic: true,
        category: "development",
        tags: ["development", "api", "integration"],
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "5",
        name: "Analyst",
        slug: "analyst",
        description: "Read-only access to analytics and reporting",
        level: "workspace",
        permissions: [
          { resource: "analytics", actions: ["read", "export"], scope: "workspace" },
          { resource: "document", actions: ["read"], scope: "workspace" },
          { resource: "project", actions: ["read"], scope: "workspace" },
        ],
        isPublic: true,
        category: "analytics",
        tags: ["analytics", "reporting", "read-only"],
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ]);
  }, []);

  const categories = ["all", "content", "management", "billing", "development", "analytics"];

  const filteredTemplates = selectedCategory === "all" 
    ? templates 
    : templates.filter(t => t.category === selectedCategory);

  if (!isAdmin) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Access denied. Admin privileges required.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Role Templates</h1>
        <p className="text-gray-600">Pre-configured role templates to quickly set up common access patterns</p>
      </div>

      {/* Category Filter */}
      <div className="flex space-x-2">
        {categories.map((category) => (
          <button
            key={category}
            onClick={() => setSelectedCategory(category)}
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              selectedCategory === category
                ? "bg-blue-600 text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            {category.charAt(0).toUpperCase() + category.slice(1)}
          </button>
        ))}
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template) => (
          <RoleTemplateCard
            key={template.id}
            template={template}
            onUse={async (template, customizations) => {
              try {
                await createRole({
                  name: customizations.name,
                  slug: customizations.slug,
                  description: template.description,
                  level: template.level,
                  permissions: template.permissions.flatMap(p => 
                    p.actions.map(action => ({
                      id: `${p.resource}-${action}`,
                      resource: p.resource,
                      action,
                      scope: p.scope,
                      createdAt: new Date(),
                      updatedAt: new Date(),
                    }))
                  ),
                  isSystem: false,
                  isActive: true,
                  metadata: { templateId: template.id },
                });
                alert("Role created successfully!");
              } catch (error) {
                console.error("Failed to create role from template:", error);
                alert("Failed to create role");
              }
            }}
          />
        ))}
      </div>
    </div>
  );
}

// Role template card component
function RoleTemplateCard({
  template,
  onUse,
}: {
  template: RoleTemplate;
  onUse: (template: RoleTemplate, customizations: { name: string; slug: string }) => void;
}) {
  const [showUseModal, setShowUseModal] = useState(false);

  return (
    <>
      <div className="bg-white border rounded-lg p-6 hover:shadow-md transition-shadow">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold">{template.name}</h3>
            <span className={`inline-block px-2 py-1 text-xs rounded mt-1 ${
              template.level === "system" ? "bg-red-100 text-red-800" :
              template.level === "organization" ? "bg-purple-100 text-purple-800" :
              template.level === "workspace" ? "bg-blue-100 text-blue-800" :
              "bg-gray-100 text-gray-800"
            }`}>
              {template.level}
            </span>
          </div>
          <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
            {template.category}
          </span>
        </div>

        <p className="text-gray-600 text-sm mb-4">{template.description}</p>

        <div className="mb-4">
          <h4 className="text-sm font-medium mb-2">Permissions:</h4>
          <div className="space-y-1">
            {template.permissions.map((perm, index) => (
              <div key={index} className="text-xs bg-gray-50 px-2 py-1 rounded">
                <span className="font-medium">{perm.resource}:</span> {perm.actions.join(", ")}
              </div>
            ))}
          </div>
        </div>

        <div className="flex flex-wrap gap-1 mb-4">
          {template.tags.map((tag) => (
            <span key={tag} className="px-2 py-1 text-xs bg-blue-50 text-blue-700 rounded">
              {tag}
            </span>
          ))}
        </div>

        <button
          onClick={() => setShowUseModal(true)}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 text-sm"
        >
          Use Template
        </button>
      </div>

      {/* Use Template Modal */}
      {showUseModal && (
        <UseTemplateModal
          template={template}
          onClose={() => setShowUseModal(false)}
          onConfirm={(customizations) => {
            onUse(template, customizations);
            setShowUseModal(false);
          }}
        />
      )}
    </>
  );
}

// Use template modal
function UseTemplateModal({
  template,
  onClose,
  onConfirm,
}: {
  template: RoleTemplate;
  onClose: () => void;
  onConfirm: (customizations: { name: string; slug: string }) => void;
}) {
  const [name, setName] = useState(template.name);
  const [slug, setSlug] = useState(template.slug);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onConfirm({ name, slug });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Create Role from Template</h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Role Name</label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2 border rounded-md"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Role Slug</label>
            <input
              type="text"
              value={slug}
              onChange={(e) => setSlug(e.target.value)}
              className="w-full px-3 py-2 border rounded-md"
              required
            />
          </div>
          
          <div className="bg-gray-50 p-3 rounded">
            <h4 className="text-sm font-medium mb-2">Template: {template.name}</h4>
            <p className="text-xs text-gray-600">{template.description}</p>
          </div>
          
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
            >
              Create Role
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
