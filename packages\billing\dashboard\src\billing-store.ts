import { create } from "zustand";
import { Invoice, PaymentRecord, BillingSettings, BillingStats, UsageMetrics } from "./billing-types";

interface BillingStore {
  // State
  invoices: Invoice[];
  payments: PaymentRecord[];
  settings: BillingSettings | null;
  stats: BillingStats | null;
  usage: UsageMetrics | null;
  isLoading: boolean;
  error: string | null;
  
  // Pagination
  invoicePagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  paymentPagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  
  // Actions
  setInvoices: (invoices: Invoice[], pagination?: any) => void;
  addInvoice: (invoice: Invoice) => void;
  updateInvoice: (invoiceId: string, updates: Partial<Invoice>) => void;
  removeInvoice: (invoiceId: string) => void;
  
  setPayments: (payments: PaymentRecord[], pagination?: any) => void;
  addPayment: (payment: PaymentRecord) => void;
  updatePayment: (paymentId: string, updates: Partial<PaymentRecord>) => void;
  
  setSettings: (settings: BillingSettings | null) => void;
  setStats: (stats: BillingStats | null) => void;
  setUsage: (usage: UsageMetrics | null) => void;
  
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
  
  // Computed
  getTotalRevenue: () => number;
  getPendingAmount: () => number;
  getOverdueInvoices: () => Invoice[];
  getRecentPayments: () => PaymentRecord[];
}

export const useBillingStore = create<BillingStore>((set, get) => ({
  // Initial state
  invoices: [],
  payments: [],
  settings: null,
  stats: null,
  usage: null,
  isLoading: false,
  error: null,
  
  invoicePagination: {
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  },
  paymentPagination: {
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  },
  
  // Actions
  setInvoices: (invoices, pagination) => set({
    invoices,
    invoicePagination: pagination || get().invoicePagination,
  }),
  
  addInvoice: (invoice) => set((state) => ({
    invoices: [invoice, ...state.invoices],
  })),
  
  updateInvoice: (invoiceId, updates) => set((state) => ({
    invoices: state.invoices.map((invoice) =>
      invoice.id === invoiceId ? { ...invoice, ...updates } : invoice
    ),
  })),
  
  removeInvoice: (invoiceId) => set((state) => ({
    invoices: state.invoices.filter((invoice) => invoice.id !== invoiceId),
  })),
  
  setPayments: (payments, pagination) => set({
    payments,
    paymentPagination: pagination || get().paymentPagination,
  }),
  
  addPayment: (payment) => set((state) => ({
    payments: [payment, ...state.payments],
  })),
  
  updatePayment: (paymentId, updates) => set((state) => ({
    payments: state.payments.map((payment) =>
      payment.id === paymentId ? { ...payment, ...updates } : payment
    ),
  })),
  
  setSettings: (settings) => set({ settings }),
  setStats: (stats) => set({ stats }),
  setUsage: (usage) => set({ usage }),
  
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),
  
  reset: () => set({
    invoices: [],
    payments: [],
    settings: null,
    stats: null,
    usage: null,
    isLoading: false,
    error: null,
    invoicePagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
    paymentPagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
  }),
  
  // Computed
  getTotalRevenue: () => {
    const { invoices } = get();
    return invoices
      .filter((invoice) => invoice.status === "paid")
      .reduce((total, invoice) => total + invoice.total, 0);
  },
  
  getPendingAmount: () => {
    const { invoices } = get();
    return invoices
      .filter((invoice) => invoice.status === "open")
      .reduce((total, invoice) => total + invoice.amountDue, 0);
  },
  
  getOverdueInvoices: () => {
    const { invoices } = get();
    const now = new Date();
    return invoices.filter((invoice) => 
      invoice.status === "open" && 
      invoice.dueDate && 
      new Date(invoice.dueDate) < now
    );
  },
  
  getRecentPayments: () => {
    const { payments } = get();
    return payments
      .filter((payment) => payment.status === "succeeded")
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5);
  },
}));

// Selectors
export const selectInvoices = (state: BillingStore) => state.invoices;
export const selectPayments = (state: BillingStore) => state.payments;
export const selectSettings = (state: BillingStore) => state.settings;
export const selectStats = (state: BillingStore) => state.stats;
export const selectUsage = (state: BillingStore) => state.usage;
export const selectIsLoading = (state: BillingStore) => state.isLoading;
export const selectError = (state: BillingStore) => state.error;
export const selectTotalRevenue = (state: BillingStore) => state.getTotalRevenue();
export const selectPendingAmount = (state: BillingStore) => state.getPendingAmount();
export const selectOverdueInvoices = (state: BillingStore) => state.getOverdueInvoices();
export const selectRecentPayments = (state: BillingStore) => state.getRecentPayments();
