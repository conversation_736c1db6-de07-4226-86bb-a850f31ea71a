import { FastifyInstance, FastifyRequest, FastifyReply } from "fastify";
import fp from "fastify-plugin";

// Request logger plugin
export const requestLogger = fp(async (fastify: FastifyInstance) => {
  // Add request context
  fastify.addHook("onRequest", async (request: FastifyRequest, reply: FastifyReply) => {
    // Add request context
    (request as any).context = {
      requestId: request.id,
      userAgent: request.headers["user-agent"],
      ip: request.ip,
    };

    // Log request start
    request.log.info({
      requestId: request.id,
      method: request.method,
      url: request.url,
      userAgent: request.headers["user-agent"],
      ip: request.ip,
      headers: {
        authorization: request.headers.authorization ? "[REDACTED]" : undefined,
        "content-type": request.headers["content-type"],
        "content-length": request.headers["content-length"],
      },
    }, "Request started");
  });

  // Log response
  fastify.addHook("onResponse", async (request: FastifyRequest, reply: FastifyReply) => {
    const responseTime = reply.getResponseTime();
    
    request.log.info({
      requestId: request.id,
      method: request.method,
      url: request.url,
      statusCode: reply.statusCode,
      responseTime: `${responseTime}ms`,
      contentLength: reply.getHeader("content-length"),
    }, "Request completed");
  });

  // Log errors
  fastify.addHook("onError", async (request: FastifyRequest, reply: FastifyReply, error: Error) => {
    request.log.error({
      requestId: request.id,
      method: request.method,
      url: request.url,
      error: {
        message: error.message,
        stack: error.stack,
      },
    }, "Request error");
  });
});
