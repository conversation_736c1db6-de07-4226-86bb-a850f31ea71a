import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from "axios";
import axiosRetry from "axios-retry";
import { getConfig, endpoints, headers } from "./config";
import { Token<PERSON>anager, AuthError, AUTH_EVENTS, AuthEventEmitter } from "./auth";
import { ApiError, ApiResponse, AuthTokens } from "./types";

// Create REST API client
export class RestApiClient {
  private client: AxiosInstance;
  private tokenManager: TokenManager;
  private authEventEmitter: AuthEventEmitter;
  private config = getConfig();

  constructor() {
    this.tokenManager = TokenManager.getInstance();
    this.authEventEmitter = AuthEventEmitter.getInstance();
    this.client = this.createAxiosInstance();
    this.setupInterceptors();
    this.setupRetry();
  }

  // Create axios instance with base configuration
  private createAxiosInstance(): AxiosInstance {
    return axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: headers.common,
    });
  }

  // Setup request/response interceptors
  private setupInterceptors(): void {
    // Request interceptor - add auth token
    this.client.interceptors.request.use(
      async (config) => {
        // Add auth token if available
        const token = this.tokenManager.getAccessToken();
        if (token) {
          config.headers = {
            ...config.headers,
            ...headers.auth(token),
          };
        }

        // Add tenant/workspace/team context from headers
        const tenantId = config.headers?.["X-Tenant-ID"];
        const workspaceId = config.headers?.["X-Workspace-ID"];
        const teamId = config.headers?.["X-Team-ID"];

        if (tenantId) config.headers = { ...config.headers, ...headers.tenant(tenantId) };
        if (workspaceId) config.headers = { ...config.headers, ...headers.workspace(workspaceId) };
        if (teamId) config.headers = { ...config.headers, ...headers.team(teamId) };

        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor - handle auth errors and token refresh
    this.client.interceptors.response.use(
      (response) => response,
      async (error: AxiosError) => {
        const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

        // Handle 401 errors with token refresh
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            // Try to refresh token
            if (this.tokenManager.getRefreshToken()) {
              await this.refreshToken();
              
              // Retry original request with new token
              const token = this.tokenManager.getAccessToken();
              if (token && originalRequest.headers) {
                originalRequest.headers = {
                  ...originalRequest.headers,
                  ...headers.auth(token),
                };
              }
              
              return this.client(originalRequest);
            }
          } catch (refreshError) {
            // Refresh failed, logout user
            this.tokenManager.clearTokens();
            this.authEventEmitter.emit(AUTH_EVENTS.TOKEN_EXPIRED);
            throw new AuthError("Session expired", "TOKEN_EXPIRED", 401);
          }
        }

        // Transform axios error to API error
        throw this.transformError(error);
      }
    );
  }

  // Setup retry logic
  private setupRetry(): void {
    axiosRetry(this.client, {
      retries: this.config.retries,
      retryDelay: (retryCount) => {
        return retryCount * this.config.retryDelay;
      },
      retryCondition: (error) => {
        // Retry on network errors and 5xx status codes
        return axiosRetry.isNetworkOrIdempotentRequestError(error) ||
               (error.response?.status ? error.response.status >= 500 : false);
      },
    });
  }

  // Transform axios error to API error
  private transformError(error: AxiosError): ApiError {
    if (error.response) {
      // Server responded with error status
      const data = error.response.data as any;
      return {
        message: data?.message || error.message,
        code: data?.code || "API_ERROR",
        status: error.response.status,
        details: data?.details || data,
      };
    } else if (error.request) {
      // Network error
      return {
        message: "Network error",
        code: "NETWORK_ERROR",
        details: error.message,
      };
    } else {
      // Request setup error
      return {
        message: error.message,
        code: "REQUEST_ERROR",
        details: error,
      };
    }
  }

  // Refresh authentication token
  private async refreshToken(): Promise<AuthTokens> {
    const refreshToken = this.tokenManager.getRefreshToken();
    if (!refreshToken) {
      throw new AuthError("No refresh token available", "NO_REFRESH_TOKEN");
    }

    try {
      const response = await axios.post(
        `${this.config.baseURL}${endpoints.auth.refresh}`,
        { refreshToken },
        { headers: headers.common }
      );

      const tokens: AuthTokens = response.data.data;
      this.tokenManager.setTokens(tokens);
      this.authEventEmitter.emit(AUTH_EVENTS.TOKEN_REFRESH, tokens);
      
      return tokens;
    } catch (error) {
      throw new AuthError("Token refresh failed", "REFRESH_FAILED");
    }
  }

  // Set context headers for requests
  setContext(context: {
    tenantId?: string;
    workspaceId?: string;
    teamId?: string;
  }): void {
    const contextHeaders: Record<string, string> = {};
    
    if (context.tenantId) {
      contextHeaders["X-Tenant-ID"] = context.tenantId;
    }
    if (context.workspaceId) {
      contextHeaders["X-Workspace-ID"] = context.workspaceId;
    }
    if (context.teamId) {
      contextHeaders["X-Team-ID"] = context.teamId;
    }

    this.client.defaults.headers.common = {
      ...this.client.defaults.headers.common,
      ...contextHeaders,
    };
  }

  // Generic request method
  async request<T = any>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await this.client(config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // GET request
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: "GET", url });
  }

  // POST request
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: "POST", url, data });
  }

  // PUT request
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: "PUT", url, data });
  }

  // PATCH request
  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: "PATCH", url, data });
  }

  // DELETE request
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: "DELETE", url });
  }

  // Upload file with progress tracking
  async uploadFile<T = any>(
    url: string,
    file: File,
    data?: Record<string, any>,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append("file", file);
    
    if (data) {
      Object.entries(data).forEach(([key, value]) => {
        formData.append(key, typeof value === "string" ? value : JSON.stringify(value));
      });
    }

    return this.request<T>({
      method: "POST",
      url,
      data: formData,
      headers: headers.multipart,
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
  }

  // Download file
  async downloadFile(url: string, filename?: string): Promise<void> {
    try {
      const response = await this.client({
        method: "GET",
        url,
        responseType: "blob",
      });

      // Create download link
      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = filename || "download";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      throw this.transformError(error as AxiosError);
    }
  }
}

// Create singleton instance
export const restApiClient = new RestApiClient();
