import { PrismaClient } from "@nexus/database-schema";
export declare const prisma: any;
export declare class TenantPrismaClient {
    private client;
    private tenantId;
    constructor(tenantId: string, client?: PrismaClient);
    withTenantContext<T>(operation: (client: PrismaClient) => Promise<T>): Promise<T>;
    get tenant(): {
        user: {
            findMany: (args?: any) => Promise<unknown>;
            findUnique: (args: any) => Promise<unknown>;
            create: (args: any) => Promise<unknown>;
            update: (args: any) => Promise<unknown>;
            delete: (args: any) => Promise<unknown>;
        };
        workspace: {
            findMany: (args?: any) => Promise<unknown>;
            findUnique: (args: any) => Promise<unknown>;
            create: (args: any) => Promise<unknown>;
            update: (args: any) => Promise<unknown>;
            delete: (args: any) => Promise<unknown>;
        };
        session: {
            findMany: (args?: any) => Promise<unknown>;
            findUnique: (args: any) => Promise<unknown>;
            create: (args: any) => Promise<unknown>;
            update: (args: any) => Promise<unknown>;
            delete: (args: any) => Promise<unknown>;
        };
    };
}
export declare const createTenantClient: (tenantId: string) => TenantPrismaClient;
export declare const checkDatabaseHealth: () => Promise<boolean>;
export declare const disconnectDatabase: () => Promise<void>;
//# sourceMappingURL=client.d.ts.map