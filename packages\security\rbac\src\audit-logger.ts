import { AuditLog, ResourceType, ActionType } from "./rbac-types";

export class AuditLogger {
  private tenantId: string;

  constructor(tenantId: string) {
    this.tenantId = tenantId;
  }

  // Log permission check
  async logPermissionCheck(data: {
    userId: string;
    resource: ResourceType;
    action: ActionType;
    resourceId?: string;
    result: "success" | "failure" | "denied";
    details: Record<string, any>;
    ipAddress?: string;
    userAgent?: string;
    workspaceId?: string;
  }): Promise<void> {
    const auditLog: Omit<AuditLog, "id" | "createdAt"> = {
      userId: data.userId,
      action: "permission_check",
      resource: data.resource,
      resourceId: data.resourceId,
      details: {
        action: data.action,
        result: data.result,
        ...data.details,
      },
      result: data.result,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      tenantId: this.tenantId,
      workspaceId: data.workspaceId,
    };

    await this.saveAuditLog(auditLog);
  }

  // Log role assignment
  async logRoleAssign(data: {
    userId: string;
    targetUserId: string;
    roleId: string;
    workspaceId?: string;
    teamId?: string;
    ipAddress?: string;
    userAgent?: string;
  }): Promise<void> {
    const auditLog: Omit<AuditLog, "id" | "createdAt"> = {
      userId: data.userId,
      action: "role_assign",
      resource: "user",
      resourceId: data.targetUserId,
      details: {
        roleId: data.roleId,
        workspaceId: data.workspaceId,
        teamId: data.teamId,
      },
      result: "success",
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      tenantId: this.tenantId,
      workspaceId: data.workspaceId,
    };

    await this.saveAuditLog(auditLog);
  }

  // Log role revocation
  async logRoleRevoke(data: {
    userId: string;
    targetUserId: string;
    roleId: string;
    workspaceId?: string;
    teamId?: string;
    ipAddress?: string;
    userAgent?: string;
  }): Promise<void> {
    const auditLog: Omit<AuditLog, "id" | "createdAt"> = {
      userId: data.userId,
      action: "role_revoke",
      resource: "user",
      resourceId: data.targetUserId,
      details: {
        roleId: data.roleId,
        workspaceId: data.workspaceId,
        teamId: data.teamId,
      },
      result: "success",
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      tenantId: this.tenantId,
      workspaceId: data.workspaceId,
    };

    await this.saveAuditLog(auditLog);
  }

  // Log permission grant
  async logPermissionGrant(data: {
    userId: string;
    targetUserId: string;
    resource: ResourceType;
    action: ActionType;
    resourceId?: string;
    ipAddress?: string;
    userAgent?: string;
    workspaceId?: string;
  }): Promise<void> {
    const auditLog: Omit<AuditLog, "id" | "createdAt"> = {
      userId: data.userId,
      action: "permission_grant",
      resource: data.resource,
      resourceId: data.resourceId,
      details: {
        targetUserId: data.targetUserId,
        action: data.action,
      },
      result: "success",
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      tenantId: this.tenantId,
      workspaceId: data.workspaceId,
    };

    await this.saveAuditLog(auditLog);
  }

  // Log permission denial
  async logPermissionDeny(data: {
    userId: string;
    resource: ResourceType;
    action: ActionType;
    resourceId?: string;
    reason: string;
    ipAddress?: string;
    userAgent?: string;
    workspaceId?: string;
  }): Promise<void> {
    const auditLog: Omit<AuditLog, "id" | "createdAt"> = {
      userId: data.userId,
      action: "permission_deny",
      resource: data.resource,
      resourceId: data.resourceId,
      details: {
        action: data.action,
        reason: data.reason,
      },
      result: "denied",
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      tenantId: this.tenantId,
      workspaceId: data.workspaceId,
    };

    await this.saveAuditLog(auditLog);
  }

  // Get audit logs
  async getAuditLogs(filters?: {
    userId?: string;
    resource?: ResourceType;
    action?: string;
    startDate?: Date;
    endDate?: Date;
    result?: "success" | "failure" | "denied";
    workspaceId?: string;
  }, page: number = 1, limit: number = 50): Promise<{
    data: AuditLog[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          if (value instanceof Date) {
            params.append(key, value.toISOString());
          } else {
            params.append(key, value.toString());
          }
        }
      });
    }

    const response = await fetch(`/api/rbac/audit-logs?${params}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch audit logs");
    }

    return response.json();
  }

  // Export audit logs
  async exportAuditLogs(filters?: {
    userId?: string;
    resource?: ResourceType;
    action?: string;
    startDate?: Date;
    endDate?: Date;
    result?: "success" | "failure" | "denied";
    workspaceId?: string;
  }, format: "csv" | "xlsx" = "csv"): Promise<Blob> {
    const params = new URLSearchParams({ format });

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          if (value instanceof Date) {
            params.append(key, value.toISOString());
          } else {
            params.append(key, value.toString());
          }
        }
      });
    }

    const response = await fetch(`/api/rbac/audit-logs/export?${params}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to export audit logs");
    }

    return response.blob();
  }

  // Get audit statistics
  async getAuditStats(period?: { start: Date; end: Date }): Promise<{
    totalLogs: number;
    permissionChecks: number;
    roleAssignments: number;
    roleRevocations: number;
    deniedAttempts: number;
    topUsers: Array<{ userId: string; count: number }>;
    topResources: Array<{ resource: string; count: number }>;
    topActions: Array<{ action: string; count: number }>;
  }> {
    const params = new URLSearchParams();
    
    if (period) {
      params.append("startDate", period.start.toISOString());
      params.append("endDate", period.end.toISOString());
    }

    const response = await fetch(`/api/rbac/audit-logs/stats?${params}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch audit statistics");
    }

    return response.json();
  }

  // Save audit log to database
  private async saveAuditLog(auditLog: Omit<AuditLog, "id" | "createdAt">): Promise<void> {
    try {
      const response = await fetch("/api/rbac/audit-logs", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-tenant-id": this.tenantId,
        },
        body: JSON.stringify(auditLog),
      });

      if (!response.ok) {
        console.error("Failed to save audit log:", await response.text());
      }
    } catch (error) {
      console.error("Error saving audit log:", error);
    }
  }
}

export const createAuditLogger = (tenantId: string): AuditLogger => {
  return new AuditLogger(tenantId);
};
