"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useRBAC, useIsAdmin } from "@nexus/rbac";
import { Role, UserRole } from "@nexus/rbac";
import { format } from "date-fns";

// Main user role assignment component
export function UserRoleAssignment() {
  const { roles, userRoles, assignRole, revokeRole, isLoading, error } = useRBAC();
  const isAdmin = useIsAdmin();
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [showAssignForm, setShowAssignForm] = useState(false);
  const [users, setUsers] = useState<Array<{ id: string; name: string; email: string }>>([]);

  // Load users (this would come from a user service)
  useEffect(() => {
    // Mock users for now
    setUsers([
      { id: "user1", name: "<PERSON>", email: "<EMAIL>" },
      { id: "user2", name: "<PERSON>", email: "<EMAIL>" },
      { id: "user3", name: "<PERSON>", email: "<EMAIL>" },
    ]);
  }, []);

  if (!isAdmin) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Access denied. Admin privileges required.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">User Role Assignment</h1>
          <p className="text-gray-600">Assign and manage user roles across your organization</p>
        </div>
        <button
          onClick={() => setShowAssignForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          Assign Role
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* User List */}
        <div>
          <UserList
            users={users}
            selectedUser={selectedUser}
            onSelectUser={setSelectedUser}
          />
        </div>

        {/* User Role Details */}
        <div className="lg:col-span-2">
          {selectedUser ? (
            <UserRoleDetails
              userId={selectedUser}
              user={users.find(u => u.id === selectedUser)}
              userRoles={userRoles.filter(ur => ur.userId === selectedUser)}
              roles={roles}
              onRevokeRole={revokeRole}
            />
          ) : (
            <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <p className="text-gray-500">Select a user to view their roles</p>
            </div>
          )}
        </div>
      </div>

      {/* Assign Role Modal */}
      {showAssignForm && (
        <AssignRoleModal
          users={users}
          roles={roles}
          onClose={() => setShowAssignForm(false)}
          onSuccess={() => setShowAssignForm(false)}
        />
      )}
    </div>
  );
}

// User list component
function UserList({
  users,
  selectedUser,
  onSelectUser,
}: {
  users: Array<{ id: string; name: string; email: string }>;
  selectedUser: string | null;
  onSelectUser: (userId: string) => void;
}) {
  return (
    <div className="bg-white rounded-lg border">
      <div className="px-6 py-4 border-b">
        <h2 className="text-lg font-semibold">Users ({users.length})</h2>
      </div>
      
      <div className="divide-y max-h-96 overflow-y-auto">
        {users.map((user) => (
          <div
            key={user.id}
            className={`p-4 cursor-pointer hover:bg-gray-50 ${
              selectedUser === user.id ? "bg-blue-50 border-l-4 border-blue-500" : ""
            }`}
            onClick={() => onSelectUser(user.id)}
          >
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-gray-600">
                  {user.name.split(" ").map(n => n[0]).join("")}
                </span>
              </div>
              <div>
                <p className="font-medium text-gray-900">{user.name}</p>
                <p className="text-sm text-gray-500">{user.email}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// User role details component
function UserRoleDetails({
  userId,
  user,
  userRoles,
  roles,
  onRevokeRole,
}: {
  userId: string;
  user?: { id: string; name: string; email: string };
  userRoles: UserRole[];
  roles: Role[];
  onRevokeRole: (userRoleId: string) => Promise<void>;
}) {
  if (!user) return null;

  const activeRoles = userRoles.filter(ur => ur.isActive);

  return (
    <div className="bg-white rounded-lg border">
      <div className="px-6 py-4 border-b">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
            <span className="text-lg font-medium text-gray-600">
              {user.name.split(" ").map(n => n[0]).join("")}
            </span>
          </div>
          <div>
            <h2 className="text-lg font-semibold">{user.name}</h2>
            <p className="text-gray-600">{user.email}</p>
          </div>
        </div>
      </div>
      
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-md font-semibold">Assigned Roles ({activeRoles.length})</h3>
        </div>

        {activeRoles.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">No roles assigned to this user</p>
          </div>
        ) : (
          <div className="space-y-3">
            {activeRoles.map((userRole) => {
              const role = roles.find(r => r.id === userRole.roleId);
              if (!role) return null;

              return (
                <UserRoleCard
                  key={userRole.id}
                  userRole={userRole}
                  role={role}
                  onRevoke={() => onRevokeRole(userRole.id)}
                />
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}

// User role card component
function UserRoleCard({
  userRole,
  role,
  onRevoke,
}: {
  userRole: UserRole;
  role: Role;
  onRevoke: () => void;
}) {
  return (
    <div className="border rounded-lg p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {role.icon && <span className="text-xl">{role.icon}</span>}
          <div>
            <h4 className="font-medium">{role.name}</h4>
            <p className="text-sm text-gray-500">{role.slug}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 text-xs rounded ${
            role.level === "system" ? "bg-red-100 text-red-800" :
            role.level === "organization" ? "bg-purple-100 text-purple-800" :
            role.level === "workspace" ? "bg-blue-100 text-blue-800" :
            "bg-gray-100 text-gray-800"
          }`}>
            {role.level}
          </span>
          
          <button
            onClick={() => {
              if (confirm(`Are you sure you want to revoke the "${role.name}" role?`)) {
                onRevoke();
              }
            }}
            className="text-red-600 hover:text-red-800 text-sm"
          >
            Revoke
          </button>
        </div>
      </div>
      
      {role.description && (
        <p className="mt-2 text-sm text-gray-600">{role.description}</p>
      )}
      
      <div className="mt-3 flex items-center justify-between text-xs text-gray-500">
        <span>Assigned: {format(new Date(userRole.assignedAt), "MMM dd, yyyy")}</span>
        {userRole.expiresAt && (
          <span>Expires: {format(new Date(userRole.expiresAt), "MMM dd, yyyy")}</span>
        )}
      </div>
    </div>
  );
}

// Assign role modal
function AssignRoleModal({
  users,
  roles,
  onClose,
  onSuccess,
}: {
  users: Array<{ id: string; name: string; email: string }>;
  roles: Role[];
  onClose: () => void;
  onSuccess: () => void;
}) {
  const { assignRole, isLoading } = useRBAC();
  const { register, handleSubmit, formState: { errors }, watch } = useForm<{
    userId: string;
    roleId: string;
    workspaceId?: string;
    teamId?: string;
    expiresAt?: string;
  }>();

  const selectedRole = watch("roleId");
  const role = roles.find(r => r.id === selectedRole);

  const onSubmit = async (data: any) => {
    try {
      await assignRole(data.userId, data.roleId, {
        workspaceId: data.workspaceId || undefined,
        teamId: data.teamId || undefined,
        expiresAt: data.expiresAt ? new Date(data.expiresAt) : undefined,
      });
      onSuccess();
    } catch (error) {
      console.error("Failed to assign role:", error);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Assign Role to User</h3>
        
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">User</label>
            <select
              {...register("userId", { required: "User is required" })}
              className="w-full px-3 py-2 border rounded-md"
            >
              <option value="">Select user</option>
              {users.map((user) => (
                <option key={user.id} value={user.id}>
                  {user.name} ({user.email})
                </option>
              ))}
            </select>
            {errors.userId && <p className="text-red-500 text-sm mt-1">{errors.userId.message}</p>}
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Role</label>
            <select
              {...register("roleId", { required: "Role is required" })}
              className="w-full px-3 py-2 border rounded-md"
            >
              <option value="">Select role</option>
              {roles.filter(r => r.isActive).map((role) => (
                <option key={role.id} value={role.id}>
                  {role.name} ({role.level})
                </option>
              ))}
            </select>
            {errors.roleId && <p className="text-red-500 text-sm mt-1">{errors.roleId.message}</p>}
          </div>

          {role && role.level === "workspace" && (
            <div>
              <label className="block text-sm font-medium mb-1">Workspace ID (Optional)</label>
              <input
                {...register("workspaceId")}
                className="w-full px-3 py-2 border rounded-md"
                placeholder="Leave empty for all workspaces"
              />
            </div>
          )}

          {role && role.level === "team" && (
            <div>
              <label className="block text-sm font-medium mb-1">Team ID (Optional)</label>
              <input
                {...register("teamId")}
                className="w-full px-3 py-2 border rounded-md"
                placeholder="Leave empty for all teams"
              />
            </div>
          )}
          
          <div>
            <label className="block text-sm font-medium mb-1">Expires At (Optional)</label>
            <input
              type="datetime-local"
              {...register("expiresAt")}
              className="w-full px-3 py-2 border rounded-md"
            />
          </div>
          
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? "Assigning..." : "Assign Role"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
