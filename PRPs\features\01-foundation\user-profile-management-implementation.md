# User Profile Management Implementation PRP

## Feature Implementation Request

Create a comprehensive user profile management system for the NEXUS SaaS Starter that provides customizable user profiles, preferences management, and advanced profile settings with seamless integration into the multi-tenant architecture.

## Research Findings

### Better Auth Profile Management Patterns
Based on Context7 research of the official Better Auth library (874 code snippets, v1.2.9):

**User Profile Updates:**
```typescript
// Core profile update functionality
await authClient.updateUser({
    image: "https://example.com/image.jpg",
    name: "<PERSON>",
    username: "john.doe",
    customField: "customValue"
});
```

**Extended User Schema Configuration:**
```typescript
// Better Auth configuration for custom fields
export const auth = betterAuth({
  user: {
    additionalFields: {
      firstName: {
        type: "string",
        required: false,
        input: true,
      },
      lastName: {
        type: "string", 
        required: false,
        input: true,
      },
      bio: {
        type: "string",
        required: false,
        input: true,
      },
      location: {
        type: "string",
        required: false,
        input: true,
      },
      timezone: {
        type: "string",
        required: false,
        input: true,
      },
      language: {
        type: "string",
        required: false,
        defaultValue: "en",
        input: true,
      },
      notifications: {
        type: "json",
        required: false,
        defaultValue: {},
        input: true,
      },
      preferences: {
        type: "json",
        required: false,
        defaultValue: {},
        input: true,
      }
    }
  }
});
```

**Profile Mapping from Social Providers:**
```typescript
// Better Auth social provider profile mapping
export const auth = betterAuth({
  socialProviders: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      mapProfileToUser: (profile) => {
        return {
          firstName: profile.given_name,
          lastName: profile.family_name,
          image: profile.picture,
          location: profile.locale,
        };
      },
    },
    github: {
      clientId: process.env.GITHUB_CLIENT_ID,
      clientSecret: process.env.GITHUB_CLIENT_SECRET,
      mapProfileToUser: (profile) => {
        return {
          firstName: profile.name?.split(" ")[0],
          lastName: profile.name?.split(" ")[1],
          bio: profile.bio,
          location: profile.location,
          image: profile.avatar_url,
        };
      },
    },
  },
});
```

### React Aria Form Management Patterns
Based on Context7 research of React Aria (4,727 code snippets):

**Advanced Form Components:**
```typescript
// React Aria profile form with server validation
'use client'
import { useFormState } from 'react-dom'
import { Button, FieldError, Form, Input, Label, TextField } from 'react-aria-components'

export default function ProfileForm() {
  const [state, action, pending] = useFormState(updateProfile, { errors: {} })
  
  return (
    <Form action={action} validationErrors={state.errors}>
      <TextField name="firstName" isRequired>
        <Label>First Name</Label>
        <Input />
        <FieldError />
      </TextField>
      
      <TextField name="lastName" isRequired>
        <Label>Last Name</Label>
        <Input />
        <FieldError />
      </TextField>
      
      <TextField name="bio">
        <Label>Biography</Label>
        <Input />
        <FieldError />
      </TextField>
      
      <Button disabled={pending} type="submit">
        {pending ? 'Updating...' : 'Update Profile'}
      </Button>
    </Form>
  )
}
```

**Advanced Settings Management:**
```typescript
// React Aria user settings with controlled components
function UserSettings() {
  const [language, setLanguage] = useState('en')
  const [timezone, setTimezone] = useState('UTC')
  const [notifications, setNotifications] = useState({})

  return (
    <Form onSubmit={handleSettingsUpdate}>
      <Select value={language} onSelectionChange={setLanguage}>
        <Label>Language</Label>
        <Button>
          <SelectValue />
        </Button>
        <Popover>
          <ListBox>
            <ListBoxItem id="en">English</ListBoxItem>
            <ListBoxItem id="es">Spanish</ListBoxItem>
            <ListBoxItem id="fr">French</ListBoxItem>
          </ListBox>
        </Popover>
      </Select>
      
      <Switch isSelected={notifications.email} onChange={(checked) => 
        setNotifications({...notifications, email: checked})
      }>
        <Label>Email Notifications</Label>
      </Switch>
    </Form>
  )
}
```

### Multi-Tenant Profile Research

**Database Schema Considerations:**
- Profile data isolation per tenant
- Tenant-specific profile field configurations
- Role-based profile access control
- Profile audit logging for compliance

**Security Patterns:**
- Profile data validation and sanitization
- Image upload security with content type validation
- Privacy settings for profile visibility
- Data export/import for GDPR compliance

## Implementation Blueprint

### Data Models and Structure

**Extended User Profile Schema:**
```prisma
// Enhanced User model with comprehensive profile fields
model User {
  id                String    @id @default(cuid())
  email             String    @unique
  emailVerified     DateTime?
  name              String?
  image             String?
  
  // Profile fields
  firstName         String?
  lastName          String?
  username          String?   @unique
  bio               String?
  location          String?
  website           String?
  company           String?
  jobTitle          String?
  
  // Preferences
  language          String    @default("en")
  timezone          String    @default("UTC")
  theme             String    @default("system")
  
  // Notification settings
  emailNotifications Boolean   @default(true)
  pushNotifications Boolean   @default(true)
  marketingEmails   Boolean   @default(false)
  
  // Privacy settings
  profileVisibility String    @default("public") // public, private, team
  showEmail         Boolean   @default(false)
  showLocation      Boolean   @default(true)
  
  // Metadata
  lastProfileUpdate DateTime?
  profileCompletion Int       @default(0) // 0-100 percentage
  
  // Relations
  tenantMemberships TenantMembership[]
  profileImages     ProfileImage[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Profile image management
model ProfileImage {
  id        String   @id @default(cuid())
  userId    String
  imageUrl  String
  altText   String?
  isPrimary Boolean  @default(false)
  
  user      User     @relation(fields: [userId], references: [id])
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
```

**TypeScript Interfaces:**
```typescript
// User profile types
interface UserProfile {
  id: string;
  email: string;
  name?: string;
  image?: string;
  firstName?: string;
  lastName?: string;
  username?: string;
  bio?: string;
  location?: string;
  website?: string;
  company?: string;
  jobTitle?: string;
  language: string;
  timezone: string;
  theme: string;
  emailNotifications: boolean;
  pushNotifications: boolean;
  marketingEmails: boolean;
  profileVisibility: ProfileVisibility;
  showEmail: boolean;
  showLocation: boolean;
  profileCompletion: number;
  lastProfileUpdate?: Date;
}

interface ProfileUpdateData {
  firstName?: string;
  lastName?: string;
  bio?: string;
  location?: string;
  website?: string;
  company?: string;
  jobTitle?: string;
  language?: string;
  timezone?: string;
  theme?: string;
}

interface NotificationSettings {
  email: boolean;
  push: boolean;
  marketing: boolean;
  security: boolean;
  updates: boolean;
}

interface PrivacySettings {
  profileVisibility: 'public' | 'private' | 'team';
  showEmail: boolean;
  showLocation: boolean;
  showActivity: boolean;
}

type ProfileVisibility = 'public' | 'private' | 'team';
```

**Validation Schemas (Zod v4):**
```typescript
import { z } from 'zod';

export const ProfileUpdateSchema = v.object({
  firstName: v.optional(v.pipe(
    v.string(),
    v.minLength(1, 'First name is required'),
    v.maxLength(50, 'First name must be less than 50 characters')
  )),
  lastName: v.optional(v.pipe(
    v.string(),
    v.minLength(1, 'Last name is required'),
    v.maxLength(50, 'Last name must be less than 50 characters')
  )),
  bio: v.optional(v.pipe(
    v.string(),
    v.maxLength(500, 'Bio must be less than 500 characters')
  )),
  location: v.optional(v.pipe(
    v.string(),
    v.maxLength(100, 'Location must be less than 100 characters')
  )),
  website: v.optional(v.pipe(
    v.string(),
    v.url('Please enter a valid URL')
  )),
  company: v.optional(v.pipe(
    v.string(),
    v.maxLength(100, 'Company must be less than 100 characters')
  )),
  jobTitle: v.optional(v.pipe(
    v.string(),
    v.maxLength(100, 'Job title must be less than 100 characters')
  )),
  language: v.optional(v.pipe(
    v.string(),
    v.includes(['en', 'es', 'fr', 'de', 'it', 'pt', 'ja', 'ko', 'zh'], 'Invalid language')
  )),
  timezone: v.optional(v.string()),
  theme: v.optional(v.pipe(
    v.string(),
    v.includes(['light', 'dark', 'system'], 'Invalid theme')
  ))
});

export const NotificationSettingsSchema = v.object({
  email: v.boolean(),
  push: v.boolean(),
  marketing: v.boolean(),
  security: v.boolean(),
  updates: v.boolean()
});

export const PrivacySettingsSchema = v.object({
  profileVisibility: v.pipe(
    v.string(),
    v.includes(['public', 'private', 'team'], 'Invalid visibility setting')
  ),
  showEmail: v.boolean(),
  showLocation: v.boolean(),
  showActivity: v.boolean()
});
```

### Task Breakdown

**Phase 1: Profile Management Core**
```typescript
// Pseudocode approach:
// 1. Create profile management components with sections
// 2. Implement real-time profile completion tracking
// 3. Add image upload and management system
// 4. Create preference management system
// 5. Implement profile validation and sanitization
```

**Tasks:**
1. **Profile Overview Component** (`src/components/profile/profile-overview.tsx`)
   - Follow existing component patterns from `src/components/ui/`
   - Display profile completion percentage
   - Show quick profile stats and summary
   - Include profile image with upload functionality
   - Add profile completion suggestions

2. **Profile Edit Form** (`src/components/profile/profile-edit-form.tsx`)
   - Use React Aria form components for accessibility
   - Implement multi-section form (Personal, Contact, Preferences)
   - Add real-time validation with Zod v4
   - Include auto-save functionality
   - Add profile completion tracking

3. **Profile Image Management** (`src/components/profile/profile-image-manager.tsx`)
   - Implement drag-and-drop image upload
   - Add image cropping and resizing
   - Support multiple image formats
   - Include image optimization
   - Add accessibility features

4. **Notification Settings** (`src/components/profile/notification-settings.tsx`)
   - Create toggle switches for different notification types
   - Add email/push notification preferences
   - Include marketing consent management
   - Add notification testing functionality

5. **Privacy Settings** (`src/components/profile/privacy-settings.tsx`)
   - Implement profile visibility controls
   - Add field-level privacy settings
   - Include data export/deletion options
   - Add audit log viewing

**Phase 2: Advanced Features**

6. **Profile API Endpoints** (`src/app/api/profile/`)
   - `/api/profile/update` - Update profile information
   - `/api/profile/image` - Handle image uploads
   - `/api/profile/preferences` - Manage user preferences
   - `/api/profile/privacy` - Handle privacy settings
   - `/api/profile/export` - Export user data

7. **Profile Server Actions** (`src/app/actions/profile.ts`)
   - Implement profile update actions
   - Add image upload handling
   - Create preferences management
   - Add privacy settings management
   - Include audit logging

8. **Profile Completion System** (`src/lib/profile/completion.ts`)
   - Calculate profile completion percentage
   - Generate completion suggestions
   - Track completion milestones
   - Add gamification elements

9. **Profile Security** (`src/lib/profile/security.ts`)
   - Implement profile data validation
   - Add image security scanning
   - Include privacy compliance checks
   - Add data retention policies

**Phase 3: User Experience**

10. **Profile Dashboard** (`src/app/(dashboard)/profile/`)
    - Create profile management dashboard
    - Add profile analytics and insights
    - Include profile activity timeline
    - Add profile sharing features

11. **Profile Widgets** (`src/components/profile/widgets/`)
    - Profile completion widget
    - Recent activity widget
    - Profile stats widget
    - Achievement badges widget

12. **Profile Search & Discovery** (`src/components/profile/search/`)
    - Implement profile search functionality
    - Add profile directory (with privacy controls)
    - Include team member discovery
    - Add profile recommendations

### Integration Points

**Database Changes:**
- Extend User model with profile fields
- Create ProfileImage model
- Add profile audit logging
- Implement profile completion tracking

**API Endpoint Modifications:**
- `/api/profile/*` - Complete profile management API
- `/api/user/profile` - User profile retrieval
- `/api/upload/profile-image` - Image upload handling
- `/api/profile/activity` - Profile activity tracking

**Frontend Component Updates:**
- Profile management dashboard
- Profile edit forms with validation
- Image upload and management
- Notification and privacy settings
- Profile completion tracking

**Authentication and Authorization:**
- Profile update permissions
- Image upload security
- Privacy setting enforcement
- Audit logging for compliance

**Multi-Tenant Integration:**
- Tenant-specific profile fields
- Role-based profile access
- Tenant branding in profiles
- Profile data isolation

## Validation Gates

### Level 1: Syntax & Style
```bash
npm run lint                    # ESLint checks
npx tsc --noEmit               # TypeScript type checking
npm run format                 # Prettier formatting
```

### Level 2: Unit Tests
```bash
npm test                       # Jest/React Testing Library tests

# Test files to create:
# __tests__/components/profile/profile-edit-form.test.tsx
# __tests__/components/profile/profile-image-manager.test.tsx
# __tests__/lib/profile/completion.test.ts
# __tests__/lib/profile/security.test.ts
```

### Level 3: Integration Tests
```bash
npm run dev                    # Start development server

# Test profile management endpoints:
curl -X PATCH http://localhost:3000/api/profile/update \
  -H "Authorization: Bearer {session-token}" \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "bio": "Software developer",
    "location": "New York, NY"
  }'

# Test image upload:
curl -X POST http://localhost:3000/api/profile/image \
  -H "Authorization: Bearer {session-token}" \
  -F "image=@profile.jpg"

# Test notification settings:
curl -X PATCH http://localhost:3000/api/profile/preferences \
  -H "Authorization: Bearer {session-token}" \
  -H "Content-Type: application/json" \
  -d '{
    "emailNotifications": true,
    "pushNotifications": false,
    "marketingEmails": false
  }'
```

### Level 4: End-to-End Tests
```bash
npm run build                  # Production build validation
npm run start                  # Production server testing

# Playwright tests:
# tests/e2e/profile/profile-management.spec.ts
# tests/e2e/profile/image-upload.spec.ts
# tests/e2e/profile/privacy-settings.spec.ts
```

### Feature-Specific Validation

**Profile Management Testing:**
- Profile form validation with various input combinations
- Image upload functionality and security
- Profile completion tracking accuracy
- Privacy settings enforcement
- Notification preferences persistence

**Multi-Tenant Testing:**
- Profile data isolation between tenants
- Tenant-specific profile field configurations
- Role-based profile access control
- Profile visibility within tenant context

**Performance Testing:**
- Profile page load time < 300ms
- Image upload performance
- Profile search response time
- Database query optimization

**Security Testing:**
- Input validation and sanitization
- Image upload security scanning
- Privacy setting enforcement
- Data export/deletion compliance
- Audit logging accuracy

**Accessibility Testing:**
- WCAG 2.1 AA compliance
- Screen reader compatibility
- Keyboard navigation support
- Focus management in forms
- Color contrast validation

## Quality Standards Checklist

- [ ] All necessary context for one-pass implementation
- [ ] Validation gates that are executable by AI
- [ ] References to existing codebase patterns
- [ ] Clear implementation path with specific tasks
- [ ] Error handling and edge cases documented
- [ ] Multi-tenant architecture considerations
- [ ] Security and compliance requirements
- [ ] Performance and scalability considerations
- [ ] Integration testing scenarios
- [ ] Documentation and deployment steps

## Additional Context

### Codebase Patterns to Follow
- Use existing form components from `src/components/ui/`
- Follow authentication patterns from `src/lib/auth/`
- Implement tenant context from `src/lib/tenant/`
- Use existing error handling from `src/lib/errors/`
- Follow database patterns from `src/lib/db/`

### Technology Stack Requirements
- Next.js 15.4+ with App Router
- React 19 with Server Components
- TypeScript 5.8+ with strict mode
- Better Auth v1.2.9 for user management
- Prisma for database operations
- Zod v4 for validation
- React Aria for accessible components
- Tailwind CSS 4.0+ for styling
- Shadcn/ui for components

### Multi-Tenant Considerations
- Profile data isolation at the database level
- Tenant-specific profile field configurations
- Role-based profile access control
- Tenant branding in profile interfaces
- Profile visibility within tenant scope

### Performance Optimization
- Lazy loading for profile components
- Optimistic updates for profile changes
- Efficient image optimization and CDN
- Profile completion caching
- Database query optimization

### Security & Privacy
- Profile data validation and sanitization
- Image upload security with virus scanning
- Privacy setting enforcement
- GDPR compliance for data export/deletion
- Audit logging for profile changes

This PRP provides comprehensive guidance for implementing a production-ready user profile management system that integrates seamlessly with the existing NEXUS SaaS Starter architecture while maintaining security, performance, and accessibility standards.
