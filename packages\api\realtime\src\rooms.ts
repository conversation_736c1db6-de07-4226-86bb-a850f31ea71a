import { EventEmitter } from "events";
import { Socket } from "socket.io";
import { Room, RoomType, SocketContext } from "./types";

export class RoomManager extends EventEmitter {
  private rooms: Map<string, Room> = new Map();
  private socketRooms: Map<string, Set<string>> = new Map(); // socketId -> roomIds
  private maxRoomsPerSocket: number;
  private maxParticipantsPerRoom: number;

  constructor(config: {
    maxRoomsPerSocket?: number;
    maxParticipantsPerRoom?: number;
  } = {}) {
    super();
    this.maxRoomsPerSocket = config.maxRoomsPerSocket || 50;
    this.maxParticipantsPerRoom = config.maxParticipantsPerRoom || 1000;
  }

  // Create room
  createRoom(
    id: string,
    type: RoomType,
    name: string,
    metadata?: Record<string, any>
  ): Room {
    if (this.rooms.has(id)) {
      throw new Error(`Room ${id} already exists`);
    }

    const room: Room = {
      id,
      type,
      name,
      participants: new Map(),
      metadata,
      createdAt: new Date(),
      lastActivity: new Date(),
    };

    this.rooms.set(id, room);

    this.emit("room:created", { room });
    return room;
  }

  // Get or create room
  getOrCreateRoom(
    id: string,
    type: RoomType,
    name: string,
    metadata?: Record<string, any>
  ): Room {
    const existingRoom = this.rooms.get(id);
    if (existingRoom) {
      return existingRoom;
    }

    return this.createRoom(id, type, name, metadata);
  }

  // Join room
  async joinRoom(roomId: string, context: SocketContext): Promise<Room> {
    const room = this.rooms.get(roomId);
    if (!room) {
      throw new Error(`Room ${roomId} not found`);
    }

    const { socket, user } = context;
    const socketId = socket.id;

    // Check participant limit
    if (room.participants.size >= this.maxParticipantsPerRoom) {
      throw new Error("Room has reached maximum participants");
    }

    // Check socket room limit
    const socketRoomSet = this.socketRooms.get(socketId) || new Set();
    if (socketRoomSet.size >= this.maxRoomsPerSocket) {
      throw new Error("Socket has reached maximum rooms");
    }

    // Add participant to room
    room.participants.set(socketId, context);
    room.lastActivity = new Date();

    // Track socket rooms
    socketRoomSet.add(roomId);
    this.socketRooms.set(socketId, socketRoomSet);

    // Join socket.io room
    socket.join(roomId);

    // Add to context
    context.joinedRooms.add(roomId);

    // Emit join event
    this.emit("room:join", {
      roomId,
      room,
      userId: user.id,
      user,
      participantCount: room.participants.size,
    });

    return room;
  }

  // Leave room
  async leaveRoom(roomId: string, socketId: string): Promise<void> {
    const room = this.rooms.get(roomId);
    if (!room) return;

    const context = room.participants.get(socketId);
    if (!context) return;

    // Remove participant from room
    room.participants.delete(socketId);
    room.lastActivity = new Date();

    // Update socket rooms tracking
    const socketRoomSet = this.socketRooms.get(socketId);
    if (socketRoomSet) {
      socketRoomSet.delete(roomId);
      if (socketRoomSet.size === 0) {
        this.socketRooms.delete(socketId);
      }
    }

    // Leave socket.io room
    context.socket.leave(roomId);

    // Remove from context
    context.joinedRooms.delete(roomId);

    // Emit leave event
    this.emit("room:leave", {
      roomId,
      room,
      userId: context.user.id,
      user: context.user,
      participantCount: room.participants.size,
    });

    // Clean up empty rooms
    if (room.participants.size === 0) {
      this.cleanupRoom(roomId);
    }
  }

  // Leave all rooms for socket
  async leaveAllRooms(socketId: string): Promise<void> {
    const socketRoomSet = this.socketRooms.get(socketId);
    if (!socketRoomSet) return;

    const roomIds = Array.from(socketRoomSet);
    for (const roomId of roomIds) {
      await this.leaveRoom(roomId, socketId);
    }
  }

  // Get room
  getRoom(roomId: string): Room | null {
    return this.rooms.get(roomId) || null;
  }

  // Get room participants
  getRoomParticipants(roomId: string): SocketContext[] {
    const room = this.rooms.get(roomId);
    if (!room) return [];

    return Array.from(room.participants.values());
  }

  // Get rooms by type
  getRoomsByType(type: RoomType): Room[] {
    return Array.from(this.rooms.values()).filter(room => room.type === type);
  }

  // Get user rooms
  getUserRooms(userId: string): Room[] {
    const userRooms: Room[] = [];

    for (const room of this.rooms.values()) {
      for (const context of room.participants.values()) {
        if (context.user.id === userId) {
          userRooms.push(room);
          break;
        }
      }
    }

    return userRooms;
  }

  // Get socket rooms
  getSocketRooms(socketId: string): string[] {
    const socketRoomSet = this.socketRooms.get(socketId);
    return socketRoomSet ? Array.from(socketRoomSet) : [];
  }

  // Broadcast to room
  broadcastToRoom(
    roomId: string,
    event: string,
    data: any,
    excludeSocketId?: string
  ): void {
    const room = this.rooms.get(roomId);
    if (!room) return;

    for (const [socketId, context] of room.participants.entries()) {
      if (excludeSocketId && socketId === excludeSocketId) continue;
      
      context.socket.emit(event, data);
    }

    // Update room activity
    room.lastActivity = new Date();
  }

  // Broadcast to room type
  broadcastToRoomType(
    type: RoomType,
    event: string,
    data: any,
    filter?: (room: Room) => boolean
  ): void {
    const rooms = this.getRoomsByType(type);
    
    for (const room of rooms) {
      if (filter && !filter(room)) continue;
      
      this.broadcastToRoom(room.id, event, data);
    }
  }

  // Update room metadata
  updateRoomMetadata(roomId: string, metadata: Record<string, any>): void {
    const room = this.rooms.get(roomId);
    if (!room) return;

    room.metadata = { ...room.metadata, ...metadata };
    room.lastActivity = new Date();

    this.emit("room:updated", { room });
  }

  // Check if user is in room
  isUserInRoom(roomId: string, userId: string): boolean {
    const room = this.rooms.get(roomId);
    if (!room) return false;

    for (const context of room.participants.values()) {
      if (context.user.id === userId) {
        return true;
      }
    }

    return false;
  }

  // Get room statistics
  getRoomStats(roomId: string): {
    participantCount: number;
    createdAt: Date;
    lastActivity: Date;
    duration: number;
  } | null {
    const room = this.rooms.get(roomId);
    if (!room) return null;

    return {
      participantCount: room.participants.size,
      createdAt: room.createdAt,
      lastActivity: room.lastActivity,
      duration: Date.now() - room.createdAt.getTime(),
    };
  }

  // Clean up room
  private cleanupRoom(roomId: string): void {
    const room = this.rooms.get(roomId);
    if (!room) return;

    // Only cleanup if no participants
    if (room.participants.size === 0) {
      this.rooms.delete(roomId);

      this.emit("room:deleted", {
        roomId,
        room,
      });
    }
  }

  // Clean up inactive rooms
  cleanupInactiveRooms(maxInactiveTime: number = 24 * 60 * 60 * 1000): void {
    const now = Date.now();

    for (const [roomId, room] of this.rooms.entries()) {
      const inactiveTime = now - room.lastActivity.getTime();
      
      if (room.participants.size === 0 && inactiveTime > maxInactiveTime) {
        this.cleanupRoom(roomId);
      }
    }
  }

  // Get global statistics
  getStats(): {
    totalRooms: number;
    totalParticipants: number;
    roomsByType: Record<RoomType, number>;
    averageParticipants: number;
    activeRooms: number;
  } {
    let totalParticipants = 0;
    const roomsByType: Record<RoomType, number> = {
      tenant: 0,
      workspace: 0,
      team: 0,
      project: 0,
      document: 0,
      chat: 0,
      user: 0,
    };

    for (const room of this.rooms.values()) {
      totalParticipants += room.participants.size;
      roomsByType[room.type]++;
    }

    return {
      totalRooms: this.rooms.size,
      totalParticipants,
      roomsByType,
      averageParticipants: this.rooms.size > 0 ? totalParticipants / this.rooms.size : 0,
      activeRooms: Array.from(this.rooms.values()).filter(r => r.participants.size > 0).length,
    };
  }

  // Destroy room manager
  destroy(): void {
    this.rooms.clear();
    this.socketRooms.clear();
    this.removeAllListeners();
  }
}
