# User Profile Customization Implementation PRP

## Feature Implementation Request

Create an advanced user profile customization system for the NEXUS SaaS Starter that extends the existing user profile management with dynamic profile fields, advanced customization options, tenant-specific field configurations, and comprehensive profile analytics. This system should provide enterprise-grade personalization capabilities while maintaining security, performance, and multi-tenant architecture requirements.

## Research Findings

### React Hook Form Advanced Validation Patterns
Based on Context7 research of React Hook Form (265 code snippets, 9.1 trust score):

**Dynamic Form Field Generation:**
```typescript
// React Hook Form with dynamic field management
import { useForm, useFieldArray, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

const ProfileFieldSchema = z.object({
  fieldId: z.string(),
  label: z.string().min(1, 'Label is required'),
  type: z.enum(['text', 'textarea', 'select', 'boolean', 'date', 'number']),
  value: z.any(),
  isRequired: z.boolean(),
  options: z.array(z.string()).optional(),
  validation: z.object({
    minLength: z.number().optional(),
    maxLength: z.number().optional(),
    pattern: z.string().optional(),
    customMessage: z.string().optional()
  }).optional()
});

const DynamicProfileForm = () => {
  const { control, handleSubmit, formState: { errors } } = useForm({
    resolver: zodResolver(ProfileFieldSchema),
    defaultValues: {
      fields: []
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "fields"
  });

  const onSubmit = (data) => {
    // Handle dynamic profile data
    console.log('Profile data:', data);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      {fields.map((field, index) => (
        <Controller
          key={field.id}
          name={`fields.${index}.value`}
          control={control}
          render={({ field: { onChange, value } }) => (
            <DynamicField
              field={field}
              value={value}
              onChange={onChange}
              error={errors.fields?.[index]?.value}
            />
          )}
        />
      ))}
    </form>
  );
};
```

**Advanced Validation with Custom Rules:**
```typescript
// React Hook Form with custom validation
const ProfileCustomizationSchema = z.object({
  customFields: z.array(z.object({
    id: z.string(),
    value: z.any(),
    type: z.string()
  })).refine((fields) => {
    // Custom validation logic for dynamic fields
    return fields.every(field => {
      if (field.type === 'email') {
        return z.string().email().safeParse(field.value).success;
      }
      if (field.type === 'url') {
        return z.string().url().safeParse(field.value).success;
      }
      return true;
    });
  }, {
    message: "Invalid field values"
  }),
  theme: z.object({
    primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid hex color'),
    secondaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid hex color'),
    fontFamily: z.enum(['inter', 'roboto', 'poppins', 'opensans']),
    fontSize: z.enum(['small', 'medium', 'large']),
    layout: z.enum(['compact', 'comfortable', 'spacious'])
  }),
  preferences: z.object({
    dashboard: z.object({
      widgets: z.array(z.string()),
      layout: z.enum(['grid', 'list', 'cards']),
      density: z.enum(['compact', 'comfortable', 'spacious'])
    }),
    notifications: z.object({
      email: z.boolean(),
      push: z.boolean(),
      sms: z.boolean(),
      desktop: z.boolean(),
      frequency: z.enum(['immediate', 'daily', 'weekly', 'monthly'])
    })
  })
});
```

### React Aria Advanced Component Patterns
Based on Context7 research of React Aria (4,727 code snippets, 9.0 trust score):

**Complex Form Component with Accessibility:**
```typescript
// React Aria customizable profile form
import { 
  Form, 
  TextField, 
  Select, 
  Button, 
  Label, 
  Input, 
  ListBox, 
  ListBoxItem,
  Popover,
  SelectValue,
  FieldError,
  Switch,
  Slider,
  ColorPicker
} from 'react-aria-components';

const ProfileCustomizationForm = () => {
  const [preferences, setPreferences] = useState({
    theme: 'system',
    primaryColor: '#3b82f6',
    layout: 'comfortable',
    widgets: []
  });

  return (
    <Form onSubmit={handleSubmit}>
      <fieldset>
        <legend>Theme Customization</legend>
        
        <TextField name="primaryColor" isRequired>
          <Label>Primary Color</Label>
          <ColorPicker value={preferences.primaryColor}>
            <Input />
          </ColorPicker>
          <FieldError />
        </TextField>
        
        <Select name="theme" defaultSelectedKey="system">
          <Label>Theme</Label>
          <Button>
            <SelectValue />
          </Button>
          <Popover>
            <ListBox>
              <ListBoxItem id="light">Light</ListBoxItem>
              <ListBoxItem id="dark">Dark</ListBoxItem>
              <ListBoxItem id="system">System</ListBoxItem>
            </ListBox>
          </Popover>
        </Select>
        
        <TextField name="fontSize">
          <Label>Font Size</Label>
          <Slider minValue={12} maxValue={24} step={1}>
            <SliderTrack>
              <SliderThumb />
            </SliderTrack>
          </Slider>
        </TextField>
      </fieldset>
      
      <fieldset>
        <legend>Dashboard Layout</legend>
        
        <Switch name="compactMode">
          <Label>Compact Mode</Label>
        </Switch>
        
        <Select name="layout" defaultSelectedKey="grid">
          <Label>Layout Style</Label>
          <Button>
            <SelectValue />
          </Button>
          <Popover>
            <ListBox>
              <ListBoxItem id="grid">Grid</ListBoxItem>
              <ListBoxItem id="list">List</ListBoxItem>
              <ListBoxItem id="cards">Cards</ListBoxItem>
            </ListBox>
          </Popover>
        </Select>
      </fieldset>
      
      <Button type="submit">Save Preferences</Button>
    </Form>
  );
};
```

**Widget Management System:**
```typescript
// React Aria drag-and-drop widget customization
import { 
  GridList, 
  GridListItem, 
  useDragAndDrop,
  DropIndicator
} from 'react-aria-components';

const WidgetCustomization = () => {
  const [widgets, setWidgets] = useState([
    { id: 'stats', name: 'Statistics', enabled: true },
    { id: 'activity', name: 'Recent Activity', enabled: true },
    { id: 'tasks', name: 'My Tasks', enabled: false },
    { id: 'calendar', name: 'Calendar', enabled: true }
  ]);

  const { dragAndDropHooks } = useDragAndDrop({
    getItems: (keys) => [...keys].map(key => ({ 'text/plain': key })),
    onReorder: (e) => {
      const newOrder = e.keys.map(key => 
        widgets.find(w => w.id === key)
      );
      setWidgets(newOrder);
    }
  });

  return (
    <GridList 
      aria-label="Widget Configuration"
      dragAndDropHooks={dragAndDropHooks}
    >
      {widgets.map(widget => (
        <GridListItem key={widget.id} id={widget.id}>
          <div className="widget-item">
            <span>{widget.name}</span>
            <Switch 
              isSelected={widget.enabled}
              onChange={(checked) => toggleWidget(widget.id, checked)}
            >
              <Label>Enable</Label>
            </Switch>
          </div>
          <DropIndicator target={{ type: 'item', key: widget.id }} />
        </GridListItem>
      ))}
    </GridList>
  );
};
```

### Next.js 15.4+ Advanced Patterns
Based on Context7 research of Next.js patterns (19 code snippets, 8.8 trust score):

**Server Actions for Profile Customization:**
```typescript
// Next.js 15.4+ server actions for profile customization
'use server'
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';
import { z } from 'zod';
import { db } from '@/lib/db';
import { auth } from '@/lib/auth';
import { auditLog } from '@/lib/audit';

const ProfileCustomizationSchema = z.object({
  customFields: z.record(z.any()),
  theme: z.object({
    primaryColor: z.string(),
    secondaryColor: z.string(),
    fontFamily: z.string(),
    fontSize: z.string(),
    layout: z.string()
  }),
  dashboardConfig: z.object({
    widgets: z.array(z.string()),
    layout: z.string(),
    density: z.string()
  }),
  notifications: z.object({
    email: z.boolean(),
    push: z.boolean(),
    sms: z.boolean(),
    frequency: z.string()
  })
});

export async function updateProfileCustomization(
  prevState: any,
  formData: FormData
) {
  const { user } = await auth();
  
  if (!user) {
    return { error: 'Unauthorized' };
  }

  const validatedFields = ProfileCustomizationSchema.safeParse({
    customFields: JSON.parse(formData.get('customFields') as string),
    theme: JSON.parse(formData.get('theme') as string),
    dashboardConfig: JSON.parse(formData.get('dashboardConfig') as string),
    notifications: JSON.parse(formData.get('notifications') as string)
  });

  if (!validatedFields.success) {
    return { 
      error: 'Invalid form data',
      fieldErrors: validatedFields.error.flatten().fieldErrors
    };
  }

  try {
    const { customFields, theme, dashboardConfig, notifications } = validatedFields.data;
    
    // Update user profile with customization
    await db.user.update({
      where: { id: user.id },
      data: {
        customFields,
        theme,
        dashboardConfig,
        notifications,
        updatedAt: new Date()
      }
    });

    // Log the customization change
    await auditLog({
      userId: user.id,
      action: 'profile_customization_updated',
      details: {
        changedFields: Object.keys(customFields),
        themeUpdated: !!theme,
        dashboardConfigUpdated: !!dashboardConfig
      }
    });

    revalidatePath('/profile');
    return { success: true };
  } catch (error) {
    return { 
      error: 'Failed to update profile customization',
      details: error.message
    };
  }
}
```

### Multi-Tenant Customization Research

**Tenant-Specific Field Configurations:**
```typescript
// Tenant-specific profile field management
interface TenantProfileConfig {
  id: string;
  tenantId: string;
  fieldDefinitions: ProfileFieldDefinition[];
  themeRestrictions: ThemeRestrictions;
  featureFlags: FeatureFlags;
  customizations: {
    branding: BrandingConfig;
    layouts: LayoutConfig[];
    widgets: WidgetConfig[];
  };
}

interface ProfileFieldDefinition {
  id: string;
  name: string;
  type: 'text' | 'textarea' | 'select' | 'boolean' | 'date' | 'number' | 'file';
  label: string;
  placeholder?: string;
  helpText?: string;
  isRequired: boolean;
  isVisible: boolean;
  isEditable: boolean;
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: string;
    customRule?: string;
  };
  options?: string[]; // for select fields
  defaultValue?: any;
  order: number;
  section: string; // 'personal' | 'contact' | 'preferences' | 'custom'
}

interface ThemeRestrictions {
  allowedColors: string[];
  allowedFonts: string[];
  allowedLayouts: string[];
  maxCustomFields: number;
  allowedWidgets: string[];
}
```

**Security Patterns for Customization:**
```typescript
// Profile customization security validation
export const validateProfileCustomization = (
  data: any,
  tenantConfig: TenantProfileConfig,
  userRole: string
) => {
  const errors: Record<string, string> = {};

  // Validate custom fields against tenant configuration
  if (data.customFields) {
    Object.entries(data.customFields).forEach(([fieldId, value]) => {
      const fieldDef = tenantConfig.fieldDefinitions.find(f => f.id === fieldId);
      
      if (!fieldDef) {
        errors[fieldId] = 'Field not allowed for this tenant';
        return;
      }

      if (!fieldDef.isEditable) {
        errors[fieldId] = 'Field is not editable';
        return;
      }

      // Validate based on field type and rules
      if (fieldDef.type === 'text' && fieldDef.validation?.maxLength) {
        if (value.length > fieldDef.validation.maxLength) {
          errors[fieldId] = `Value exceeds maximum length of ${fieldDef.validation.maxLength}`;
        }
      }

      // Sanitize input
      if (typeof value === 'string') {
        // Remove potentially dangerous content
        const sanitizedValue = value.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
        data.customFields[fieldId] = sanitizedValue;
      }
    });
  }

  // Validate theme restrictions
  if (data.theme) {
    const { allowedColors, allowedFonts, allowedLayouts } = tenantConfig.themeRestrictions;
    
    if (data.theme.primaryColor && !allowedColors.includes(data.theme.primaryColor)) {
      errors.theme = 'Primary color not allowed';
    }
    
    if (data.theme.fontFamily && !allowedFonts.includes(data.theme.fontFamily)) {
      errors.theme = 'Font family not allowed';
    }
    
    if (data.theme.layout && !allowedLayouts.includes(data.theme.layout)) {
      errors.theme = 'Layout not allowed';
    }
  }

  return { isValid: Object.keys(errors).length === 0, errors };
};
```

## Implementation Blueprint

### Data Models and Structure

**Enhanced User Profile Schema:**
```prisma
// Extended User model with advanced customization
model User {
  id                String    @id @default(cuid())
  email             String    @unique
  emailVerified     DateTime?
  name              String?
  image             String?
  
  // Existing profile fields
  firstName         String?
  lastName          String?
  username          String?   @unique
  bio               String?
  location          String?
  website           String?
  company           String?
  jobTitle          String?
  
  // Advanced customization fields
  customFields      Json?     // Dynamic fields per tenant
  theme             Json?     // User theme preferences
  dashboardConfig   Json?     // Dashboard layout and widgets
  preferences       Json?     // User preferences
  
  // Tenant-specific customizations
  tenantCustomizations TenantUserCustomization[]
  
  // Audit and tracking
  lastCustomizationUpdate DateTime?
  customizationVersion    Int      @default(1)
  
  // Relations
  profileImages     ProfileImage[]
  profileHistory    ProfileHistory[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Tenant-specific profile configuration
model TenantProfileConfig {
  id            String @id @default(cuid())
  tenantId      String
  
  // Field definitions
  fieldDefinitions      Json  // ProfileFieldDefinition[]
  themeRestrictions     Json  // ThemeRestrictions
  featureFlags          Json  // FeatureFlags
  
  // Customization options
  allowedWidgets        String[]
  maxCustomFields       Int      @default(10)
  allowedThemes         String[]
  allowedLayouts        String[]
  
  // Branding
  brandingConfig        Json?
  
  // Relations
  tenant                Tenant @relation(fields: [tenantId], references: [id])
  userCustomizations    TenantUserCustomization[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@unique([tenantId])
}

// Per-tenant user customizations
model TenantUserCustomization {
  id            String @id @default(cuid())
  userId        String
  tenantId      String
  
  // Tenant-specific customizations
  customFields  Json?
  theme         Json?
  dashboardConfig Json?
  widgets       String[]
  
  // Relations
  user          User   @relation(fields: [userId], references: [id])
  tenantConfig  TenantProfileConfig @relation(fields: [tenantId], references: [tenantId])
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@unique([userId, tenantId])
}

// Profile customization history
model ProfileHistory {
  id        String   @id @default(cuid())
  userId    String
  changes   Json     // What was changed
  version   Int      // Version number
  changeType String  // 'theme' | 'fields' | 'dashboard' | 'preferences'
  
  user      User     @relation(fields: [userId], references: [id])
  
  createdAt DateTime @default(now())
  
  @@index([userId, createdAt])
}

// Widget definitions
model WidgetDefinition {
  id          String @id @default(cuid())
  name        String
  type        String
  description String?
  category    String
  
  // Configuration
  defaultConfig Json?
  configSchema  Json? // JSON schema for widget configuration
  
  // Permissions
  requiredRole  String?
  tenantOnly    Boolean @default(false)
  
  // Relations
  userWidgets   UserWidget[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@unique([name])
}

// User widget instances
model UserWidget {
  id        String @id @default(cuid())
  userId    String
  widgetId  String
  
  // Configuration
  config    Json?
  position  Json? // { x: number, y: number, width: number, height: number }
  isEnabled Boolean @default(true)
  
  // Relations
  user      User @relation(fields: [userId], references: [id])
  widget    WidgetDefinition @relation(fields: [widgetId], references: [id])
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@unique([userId, widgetId])
}
```

**TypeScript Interfaces:**
```typescript
// Advanced profile customization types
interface ProfileCustomization {
  id: string;
  userId: string;
  tenantId: string;
  
  // Dynamic fields
  customFields: Record<string, any>;
  
  // Theme customization
  theme: {
    primaryColor: string;
    secondaryColor: string;
    accentColor: string;
    fontFamily: string;
    fontSize: 'small' | 'medium' | 'large';
    layout: 'compact' | 'comfortable' | 'spacious';
    darkMode: boolean;
    highContrast: boolean;
  };
  
  // Dashboard customization
  dashboardConfig: {
    layout: 'grid' | 'list' | 'cards';
    density: 'compact' | 'comfortable' | 'spacious';
    widgets: WidgetConfig[];
    quickActions: string[];
    defaultView: string;
  };
  
  // Preferences
  preferences: {
    language: string;
    timezone: string;
    dateFormat: string;
    timeFormat: '12h' | '24h';
    currency: string;
    numberFormat: string;
    
    // Notification preferences
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
      desktop: boolean;
      frequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
      categories: Record<string, boolean>;
    };
    
    // Privacy preferences
    privacy: {
      profileVisibility: 'public' | 'private' | 'team';
      showActivity: boolean;
      showOnlineStatus: boolean;
      allowDirectMessages: boolean;
      allowMentions: boolean;
    };
  };
  
  // Advanced settings
  advanced: {
    enabledFeatures: string[];
    experimentalFeatures: string[];
    developerMode: boolean;
    debugMode: boolean;
    performanceMode: boolean;
  };
}

interface WidgetConfig {
  id: string;
  type: string;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  config: Record<string, any>;
  isEnabled: boolean;
}

interface ProfileFieldDefinition {
  id: string;
  name: string;
  type: 'text' | 'textarea' | 'select' | 'multiselect' | 'boolean' | 'date' | 'datetime' | 'number' | 'email' | 'url' | 'color' | 'file' | 'image';
  label: string;
  placeholder?: string;
  helpText?: string;
  isRequired: boolean;
  isVisible: boolean;
  isEditable: boolean;
  validation?: {
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    pattern?: string;
    customRule?: string;
    allowedFileTypes?: string[];
    maxFileSize?: number;
  };
  options?: Array<{
    value: string;
    label: string;
    description?: string;
  }>;
  defaultValue?: any;
  order: number;
  section: string;
  dependsOn?: string; // Field dependency
  conditional?: {
    field: string;
    value: any;
    operator: '==' | '!=' | '>' | '<' | 'includes' | 'excludes';
  };
}
```

**Validation Schemas (Zod v4):**
```typescript
import { z } from 'zod';

export const ProfileCustomizationSchema = z.object({
  customFields: z.record(z.any()),
  theme: z.object({
    primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid hex color'),
    secondaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid hex color'),
    accentColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid hex color'),
    fontFamily: z.enum(['inter', 'roboto', 'poppins', 'opensans', 'system']),
    fontSize: z.enum(['small', 'medium', 'large']),
    layout: z.enum(['compact', 'comfortable', 'spacious']),
    darkMode: z.boolean(),
    highContrast: z.boolean()
  }),
  dashboardConfig: z.object({
    layout: z.enum(['grid', 'list', 'cards']),
    density: z.enum(['compact', 'comfortable', 'spacious']),
    widgets: z.array(z.object({
      id: z.string(),
      type: z.string(),
      position: z.object({
        x: z.number(),
        y: z.number(),
        width: z.number().min(1).max(12),
        height: v.pipe(v.number(), v.minValue(1), v.maxValue(12))
      }),
      config: v.record(v.any()),
      isEnabled: v.boolean()
    })),
    quickActions: v.array(v.string()),
    defaultView: v.string()
  }),
  preferences: v.object({
    language: v.pipe(v.string(), v.minLength(2), v.maxLength(5)),
    timezone: v.string(),
    dateFormat: v.pipe(v.string(), v.includes(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD'])),
    timeFormat: v.pipe(v.string(), v.includes(['12h', '24h'])),
    currency: v.pipe(v.string(), v.minLength(3), v.maxLength(3)),
    numberFormat: v.string(),
    notifications: v.object({
      email: v.boolean(),
      push: v.boolean(),
      sms: v.boolean(),
      desktop: v.boolean(),
      frequency: v.pipe(v.string(), v.includes(['immediate', 'hourly', 'daily', 'weekly'])),
      categories: v.record(v.boolean())
    }),
    privacy: v.object({
      profileVisibility: v.pipe(v.string(), v.includes(['public', 'private', 'team'])),
      showActivity: v.boolean(),
      showOnlineStatus: v.boolean(),
      allowDirectMessages: v.boolean(),
      allowMentions: v.boolean()
    })
  }),
  advanced: v.object({
    enabledFeatures: v.array(v.string()),
    experimentalFeatures: v.array(v.string()),
    developerMode: v.boolean(),
    debugMode: v.boolean(),
    performanceMode: v.boolean()
  })
});

export const DynamicFieldSchema = v.object({
  fieldId: v.string(),
  value: v.any(),
  type: v.pipe(v.string(), v.includes(['text', 'textarea', 'select', 'multiselect', 'boolean', 'date', 'datetime', 'number', 'email', 'url', 'color', 'file', 'image'])),
  validation: v.optional(v.object({
    isRequired: v.boolean(),
    minLength: v.optional(v.number()),
    maxLength: v.optional(v.number()),
    pattern: v.optional(v.string()),
    customRule: v.optional(v.string())
  }))
});

export const WidgetConfigSchema = v.object({
  id: v.string(),
  type: v.string(),
  position: v.object({
    x: v.pipe(v.number(), v.minValue(0)),
    y: v.pipe(v.number(), v.minValue(0)),
    width: v.pipe(v.number(), v.minValue(1), v.maxValue(12)),
    height: v.pipe(v.number(), v.minValue(1), v.maxValue(12))
  }),
  config: v.record(v.any()),
  isEnabled: v.boolean()
});
```

### Task Breakdown

**Phase 1: Advanced Customization Core**
```typescript
// Pseudocode approach:
// 1. Create dynamic field management system with tenant-specific configurations
// 2. Implement advanced theme customization with color picker and live preview
// 3. Build dashboard widget system with drag-and-drop functionality
// 4. Create comprehensive preference management system
// 5. Implement tenant-specific customization restrictions and validation
// 6. Add profile customization history and versioning
// 7. Create customization analytics and insights
```

**Tasks:**
1. **Dynamic Field Manager** (`src/components/profile/dynamic-field-manager.tsx`)
   - Create dynamic field configuration interface
   - Implement field type selection (text, select, boolean, date, etc.)
   - Add field validation rule builder
   - Include field dependency management
   - Add field preview functionality
   - Support conditional field display

2. **Advanced Theme Customizer** (`src/components/profile/theme-customizer.tsx`)
   - Implement color picker with accessibility support
   - Add font family selection with Google Fonts integration
   - Create layout density controls
   - Add dark mode toggle with system preference detection
   - Include high contrast mode support
   - Add theme preview with live updates

3. **Dashboard Widget System** (`src/components/profile/dashboard-widgets/`)
   - Create widget catalog with categories
   - Implement drag-and-drop widget placement
   - Add widget configuration panels
   - Support widget resizing and positioning
   - Include widget state management
   - Add widget performance monitoring

4. **Preference Management Hub** (`src/components/profile/preferences/`)
   - Create comprehensive preference categories
   - Implement notification preference matrix
   - Add privacy control granular settings
   - Include localization preferences
   - Add accessibility preferences
   - Support preference import/export

**Phase 2: Tenant-Specific Customization**

5. **Tenant Configuration System** (`src/components/admin/tenant-customization/`)
   - Create tenant profile field configurator
   - Implement theme restriction management
   - Add widget availability controls
   - Include branding customization options
   - Add role-based customization permissions
   - Support tenant-specific field templates

6. **Profile Customization API** (`src/app/api/profile/customization/`)
   - `/api/profile/customization/fields` - Dynamic field CRUD operations
   - `/api/profile/customization/theme` - Theme update and validation
   - `/api/profile/customization/dashboard` - Dashboard configuration
   - `/api/profile/customization/preferences` - Preference management
   - `/api/profile/customization/history` - Customization history
   - `/api/profile/customization/export` - Export customizations

7. **Customization Server Actions** (`src/app/actions/profile-customization.ts`)
   - Implement dynamic field updates with validation
   - Add theme customization with live preview
   - Create dashboard widget management
   - Add preference sync across devices
   - Include customization history tracking
   - Add bulk customization operations

**Phase 3: Advanced Features**

8. **Profile Analytics Dashboard** (`src/components/profile/analytics/`)
   - Create customization completion tracking
   - Add usage analytics for features
   - Include performance metrics
   - Add user engagement insights
   - Support customization recommendations
   - Include A/B testing for customization options

9. **Customization Templates** (`src/components/profile/templates/`)
   - Create pre-built customization templates
   - Add role-based template suggestions
   - Include industry-specific templates
   - Support custom template creation
   - Add template sharing between tenants
   - Include template version management

10. **Advanced Validation Engine** (`src/lib/profile/validation.ts`)
    - Implement dynamic field validation
    - Add custom validation rule engine
    - Create tenant-specific validation rules
    - Add cross-field validation support
    - Include async validation for unique constraints
    - Support conditional validation rules

11. **Profile Migration System** (`src/lib/profile/migration.ts`)
    - Create profile customization migration tools
    - Add tenant transfer customization handling
    - Include customization backup and restore
    - Support batch customization updates
    - Add customization conflict resolution
    - Include customization audit trail

12. **Real-time Customization** (`src/lib/profile/realtime.ts`)
    - Implement real-time customization sync
    - Add collaborative profile editing
    - Include customization conflict resolution
    - Support offline customization with sync
    - Add customization broadcasting
    - Include customization locking

### Integration Points

**Database Changes:**
- Add customization-specific tables and fields
- Create tenant configuration schema
- Implement profile history tracking
- Add widget management system
- Create customization audit logs

**API Endpoint Modifications:**
- Extend profile API with customization endpoints
- Add tenant configuration management
- Create widget management API
- Add customization history endpoints
- Include bulk customization operations

**Frontend Component Updates:**
- Advanced profile customization interface
- Dashboard widget management system
- Theme customization with live preview
- Preference management hub
- Tenant configuration interface

**Authentication and Authorization:**
- Tenant-specific customization permissions
- Role-based customization access
- Customization feature flags
- Audit logging for customization changes
- Security validation for custom fields

**Multi-Tenant Integration:**
- Tenant-specific field configurations
- Customization restriction enforcement
- Role-based customization access
- Tenant branding in customization interface
- Customization data isolation

**Performance Optimization:**
- Lazy loading for customization components
- Optimistic updates for customization changes
- Caching for tenant configurations
- Efficient widget rendering
- Database query optimization for customizations

## Validation Gates

### Level 1: Syntax & Style
```bash
npm run lint                    # ESLint checks
npx tsc --noEmit               # TypeScript type checking
npm run format                 # Prettier formatting
```

### Level 2: Unit Tests
```bash
npm test                       # Jest/React Testing Library tests

# Test files to create:
# __tests__/components/profile/dynamic-field-manager.test.tsx
# __tests__/components/profile/theme-customizer.test.tsx
# __tests__/components/profile/dashboard-widgets.test.tsx
# __tests__/lib/profile/customization-validation.test.ts
# __tests__/lib/profile/widget-management.test.ts
```

### Level 3: Integration Tests
```bash
npm run dev                    # Start development server

# Test customization endpoints:
curl -X POST http://localhost:3000/api/profile/customization/fields \
  -H "Content-Type: application/json" \
  -d '{"fieldId": "custom1", "value": "test", "type": "text"}'

curl -X PUT http://localhost:3000/api/profile/customization/theme \
  -H "Content-Type: application/json" \
  -d '{"primaryColor": "#3b82f6", "layout": "comfortable"}'

curl -X POST http://localhost:3000/api/profile/customization/dashboard \
  -H "Content-Type: application/json" \
  -d '{"layout": "grid", "widgets": [{"id": "stats", "position": {"x": 0, "y": 0, "width": 6, "height": 4}}]}'
```

### Level 4: End-to-End Tests
```bash
npm run build                  # Production build validation
npm run start                  # Production server testing
npm run test:e2e               # Playwright end-to-end tests
```

### Feature-Specific Validation

**Profile Customization Testing:**
- Dynamic field creation and validation
- Theme customization with live preview
- Dashboard widget drag-and-drop functionality
- Preference synchronization across devices
- Tenant-specific customization restrictions
- Customization history and versioning

**Multi-Tenant Testing:**
- Tenant configuration enforcement
- Role-based customization access
- Customization data isolation
- Tenant-specific field validation
- Branding customization per tenant

**Performance Testing:**
- Customization interface load time < 300ms
- Widget rendering performance
- Real-time customization sync
- Database query optimization
- Memory usage during customization

**Security Testing:**
- Custom field input validation and sanitization
- Tenant customization isolation
- Role-based access control
- Customization audit logging
- XSS prevention in custom fields

**Accessibility Testing:**
- WCAG 2.1 AA compliance for customization interface
- Screen reader compatibility
- Keyboard navigation support
- High contrast mode support
- Color picker accessibility

## Quality Standards Checklist

- [ ] All necessary context for one-pass implementation
- [ ] Validation gates that are executable by AI
- [ ] References to existing codebase patterns
- [ ] Clear implementation path with specific tasks
- [ ] Error handling and edge cases documented
- [ ] Multi-tenant architecture considerations
- [ ] Security and compliance requirements
- [ ] Performance and scalability considerations
- [ ] Integration testing scenarios
- [ ] Documentation and deployment steps

## Additional Context

### Codebase Patterns to Follow
- Use existing form components from `src/components/ui/`
- Follow authentication patterns from `src/lib/auth/`
- Implement tenant context from `src/lib/tenant/`
- Use existing error handling from `src/lib/errors/`
- Follow database patterns from `src/lib/db/`
- Use existing validation patterns from existing profile management
- Follow component structure from `src/components/profile/`

### Technology Stack Requirements
- Next.js 15.4+ with App Router
- React 19 with Server Components
- TypeScript 5.8+ with strict mode
- React Hook Form with Zod v4 validation
- React Aria for accessible components
- Tailwind CSS 4.0+ for styling
- Shadcn/ui for components
- Prisma for database operations
- Better Auth for user management

### Multi-Tenant Considerations
- Tenant-specific field configurations
- Customization restriction enforcement
- Role-based customization access
- Tenant branding in customization interface
- Customization data isolation
- Tenant-specific widget availability
- Branding customization per tenant

### Performance Optimization
- Lazy loading for customization components
- Optimistic updates for customization changes
- Efficient widget rendering and management
- Real-time customization sync optimization
- Database query optimization for customizations
- Caching for tenant configurations
- Memory management for dynamic fields

### Security & Privacy
- Custom field validation and sanitization
- Tenant customization isolation
- Role-based access control enforcement
- Customization audit logging
- XSS prevention in custom fields
- File upload security for custom images
- Privacy controls for customization sharing

This PRP provides comprehensive guidance for implementing an advanced user profile customization system that extends the existing profile management with enterprise-grade personalization capabilities while maintaining security, performance, and multi-tenant architecture requirements.
