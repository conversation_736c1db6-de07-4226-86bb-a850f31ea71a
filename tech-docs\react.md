TITLE: React v19.1 Core APIs Reference
DESCRIPTION: A reference guide to various core and experimental APIs in React v19.1, providing insights into advanced functionalities and testing utilities.
SOURCE: https://react.dev/reference/react/cache

LANGUAGE: APIDOC
CODE:
```
act
```

LANGUAGE: APIDOC
CODE:
```
cache
```

LANGUAGE: APIDOC
CODE:
```
captureOwnerStack
```

LANGUAGE: APIDOC
CODE:
```
createContext
```

LANGUAGE: APIDOC
CODE:
```
lazy
```

LANGUAGE: APIDOC
CODE:
```
memo
```

LANGUAGE: APIDOC
CODE:
```
startTransition
```

LANGUAGE: APIDOC
CODE:
```
use
```

LANGUAGE: APIDOC
CODE:
```
experimental_taintObjectReference (Experimental)
```

LANGUAGE: APIDOC
CODE:
```
experimental_taintUniqueValue (Experimental)
```

LANGUAGE: APIDOC
CODE:
```
unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: React v19.1 API Reference: Hooks, Components, and APIs
DESCRIPTION: A structured list of all major Hooks, Components, and APIs introduced or available in React version 19.1, serving as a quick reference and navigation guide.
SOURCE: https://react.dev/reference/react-dom/server/renderToReadableStream

LANGUAGE: APIDOC
CODE:
```
Hooks:
  - useActionState
  - useCallback
  - useContext
  - useDebugValue
  - useDeferredValue
  - useEffect
  - useId
  - useImperativeHandle
  - useInsertionEffect
  - useLayoutEffect
  - useMemo
  - useOptimistic
  - useReducer
  - useRef
  - useState
  - useSyncExternalStore
  - useTransition
Components:
  - <Fragment> (<>)
  - <Profiler>
  - <StrictMode>
  - <Suspense>
  - <Activity> (Experimental)
  - <ViewTransition> (Experimental)
APIs:
  - act
  - cache
  - captureOwnerStack
  - createContext
  - lazy
  - memo
  - startTransition
  - use
  - experimental_taintObjectReference (Experimental)
  - experimental_taintUniqueValue (Experimental)
  - unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: React v19.1 APIs Reference
DESCRIPTION: Lists various utility APIs available in React v19.1 for advanced functionalities and testing, including experimental APIs.
SOURCE: https://react.dev/reference/react/Suspense

LANGUAGE: APIDOC
CODE:
```
APIs:
  act
  cache
  captureOwnerStack
  createContext
  lazy
  memo
  startTransition
  use
  experimental_taintObjectReference - This feature is available in the latest Experimental version of React
  experimental_taintUniqueValue - This feature is available in the latest Experimental version of React
  unstable_addTransitionType - This feature is available in the latest Experimental version of React
```

----------------------------------------

TITLE: React v19.1 APIs Reference
DESCRIPTION: Lists all top-level APIs available in React v19.1, including experimental ones, with their names and links to their detailed documentation.
SOURCE: https://react.dev/reference/rsc/server-actions

LANGUAGE: APIDOC
CODE:
```
APIs:
  - act
  - cache
  - captureOwnerStack
  - createContext
  - lazy
  - memo
  - startTransition
  - use
  - experimental_taintObjectReference (Experimental)
  - experimental_taintUniqueValue (Experimental)
  - unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: React 19.1 APIs Reference
DESCRIPTION: A comprehensive list of all available React APIs in version 19.1, including experimental ones, providing an overview of their names and links to their detailed documentation.
SOURCE: https://react.dev/reference/react/useMemo

LANGUAGE: APIDOC
CODE:
```
APIs:
  - act
  - cache
  - captureOwnerStack
  - createContext
  - lazy
  - memo
  - startTransition
  - use
  - experimental_taintObjectReference (Experimental)
  - experimental_taintUniqueValue (Experimental)
  - unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: React v19.1 APIs Reference
DESCRIPTION: Lists all available top-level APIs in React v19.1, including experimental ones, providing a quick overview of their names and purpose.
SOURCE: https://react.dev/reference/react/useCallback

LANGUAGE: APIDOC
CODE:
```
APIs:
  act
  cache
  captureOwnerStack
  createContext
  lazy
  memo
  startTransition
  use
  experimental_taintObjectReference - This feature is available in the latest Experimental version of React
  experimental_taintUniqueValue - This feature is available in the latest Experimental version of React
  unstable_addTransitionType - This feature is available in the latest Experimental version of React
```

----------------------------------------

TITLE: React-DOM and React API Reference Overview
DESCRIPTION: Lists the available hooks, components, and APIs within React-DOM and React, categorized by their functionality (e.g., Hooks, Components, Client APIs, Server APIs, Legacy APIs). This section serves as a table of contents for the React ecosystem.
SOURCE: https://react.dev/reference/react/Profiler

LANGUAGE: APIDOC
CODE:
```
react-dom@19.1:
  Hooks:
    - useFormStatus
  Components:
    - Common (e.g. <div>)
    - <form>
    - <input>
    - <option>
    - <progress>
    - <select>
    - <textarea>
    - <link>
    - <meta>
    - <script>
    - <style>
    - <title>
  APIs:
    - createPortal
    - flushSync
    - preconnect
    - prefetchDNS
    - preinit
    - preinitModule
    - preload
    - preloadModule
  Client APIs:
    - createRoot
    - hydrateRoot
  Server APIs:
    - renderToPipeableStream
    - renderToReadableStream
    - renderToStaticMarkup
    - renderToString
  Static APIs:
    - prerender
    - prerenderToNodeStream
Rules of React:
  Overview:
    - Components and Hooks must be pure
    - React calls Components and Hooks
    - Rules of Hooks
React Server Components:
  - Server Components
  - Server Functions
  Directives:
    - 'use client'
    - 'use server'
Legacy APIs:
  Legacy React APIs:
    - Children
    - cloneElement
    - Component
    - createElement
    - createRef
    - forwardRef
    - isValidElement
    - PureComponent
```

----------------------------------------

TITLE: React 19.1 APIs Reference
DESCRIPTION: A comprehensive list of built-in React APIs available in version 19.1, detailing their names and purpose, including experimental ones.
SOURCE: https://react.dev/reference/react-dom/createPortal

LANGUAGE: APIDOC
CODE:
```
APIs:
  - act
  - cache
  - captureOwnerStack
  - createContext
  - lazy
  - memo
  - startTransition
  - use
  - experimental_taintObjectReference (Experimental)
  - experimental_taintUniqueValue (Experimental)
  - unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: React DOM API Reference Overview
DESCRIPTION: This section provides an overview of the React DOM API, including Hooks, Components, general APIs, Client APIs, Server APIs, Static APIs, Rules of React, React Server Components, and Legacy APIs, as of react-dom@19.1.
SOURCE: https://react.dev/reference/react-dom/components/script

LANGUAGE: APIDOC
CODE:
```
react-dom@19.1:
  Hooks:
    - useFormStatus
  Components:
    - Common (e.g. <div>)
    - <form>
    - <input>
    - <option>
    - <progress>
    - <select>
    - <textarea>
    - <link>
    - <meta>
    - <script>
    - <style>
    - <title>
  APIs:
    - createPortal
    - flushSync
    - preconnect
    - prefetchDNS
    - preinit
    - preinitModule
    - preload
    - preloadModule
  Client APIs:
    - createRoot
    - hydrateRoot
  Server APIs:
    - renderToPipeableStream
    - renderToReadableStream
    - renderToStaticMarkup
    - renderToString
  Static APIs:
    - prerender
    - prerenderToNodeStream
Rules of React:
  - Overview
  - Components and Hooks must be pure
  - React calls Components and Hooks
  - Rules of Hooks
React Server Components:
  - Server Components
  - Server Functions
  - Directives:
    - 'use client'
    - 'use server'
Legacy APIs:
  - Legacy React APIs:
    - Children
    - cloneElement
    - Component
    - createElement
    - createRef
    - forwardRef
    - isValidElement
    - PureComponent
```

----------------------------------------

TITLE: Example package.json for React Application
DESCRIPTION: This `package.json` file outlines the dependencies and scripts for a typical React application. It includes core React libraries, `react-scripts` for development, and `remarkable` as an example utility, along with standard npm scripts for starting, building, testing, and ejecting the application.
SOURCE: https://react.dev/reference/react-dom/components/textarea

LANGUAGE: JSON
CODE:
```
{
  "dependencies": {
    "react": "latest",
    "react-dom": "latest",
    "react-scripts": "latest",
    "remarkable": "2.0.1"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test --env=jsdom",
    "eject": "react-scripts eject"
  },
  "devDependencies": {}
}
```

----------------------------------------

TITLE: React DOM 19.1 API Overview
DESCRIPTION: Lists the main categories of APIs, Hooks, and Components available in React DOM version 19.1, including client, server, and static APIs.
SOURCE: https://react.dev/reference/react-dom/server

LANGUAGE: APIDOC
CODE:
```
react-dom@19.1:
  Hooks:
    - useFormStatus
  Components:
    - Common (e.g. <div>)
    - <form>
    - <input>
    - <option>
    - <progress>
    - <select>
    - <textarea>
    - <link>
    - <meta>
    - <script>
    - <style>
    - <title>
  APIs:
    - createPortal
    - flushSync
    - preconnect
    - prefetchDNS
    - preinit
    - preinitModule
    - preload
    - preloadModule
  Client APIs:
    - createRoot
    - hydrateRoot
  Server APIs:
    - renderToPipeableStream
    - renderToReadableStream
    - renderToStaticMarkup
    - renderToString
  Static APIs:
    - prerender
    - prerenderToNodeStream
```

----------------------------------------

TITLE: React v19.1 APIs Reference
DESCRIPTION: This section lists all available React APIs in version 19.1, providing a quick reference to their names and indicating any experimental status.
SOURCE: https://react.dev/reference/react/createContext

LANGUAGE: APIDOC
CODE:
```
APIs:
  - act
  - cache
  - captureOwnerStack
  - createContext
  - lazy
  - memo
  - startTransition
  - use
  - experimental_taintObjectReference (Experimental)
  - experimental_taintUniqueValue (Experimental)
  - unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: React v19.1 Core APIs Reference
DESCRIPTION: A list of global APIs and utilities provided by React version 19.1, including experimental APIs, for advanced control over React applications.
SOURCE: https://react.dev/reference/react-dom/components/title

LANGUAGE: APIDOC
CODE:
```
APIs:
  - act
  - cache
  - captureOwnerStack
  - createContext
  - lazy
  - memo
  - startTransition
  - use
  - experimental_taintObjectReference (Experimental)
  - experimental_taintUniqueValue (Experimental)
  - unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: React v19.1 API Index
DESCRIPTION: A comprehensive listing of all available Hooks, Components, and APIs in React version 19.1, including experimental features.
SOURCE: https://react.dev/reference/react/memo

LANGUAGE: APIDOC
CODE:
```
React v19.1 Reference:

Hooks:
  - useActionState
  - useCallback
  - useContext
  - useDebugValue
  - useDeferredValue
  - useEffect
  - useId
  - useImperativeHandle
  - useInsertionEffect
  - useLayoutEffect
  - useMemo
  - useOptimistic
  - useReducer
  - useRef
  - useState
  - useSyncExternalStore
  - useTransition

Components:
  - <Fragment> (<>)
  - <Profiler>
  - <StrictMode>
  - <Suspense>
  - <Activity> (Experimental)
  - <ViewTransition> (Experimental)

APIs:
  - act
  - cache
  - captureOwnerStack
  - createContext
  - lazy
  - memo
  - startTransition
  - use
  - experimental_taintObjectReference (Experimental)
  - experimental_taintUniqueValue (Experimental)
  - unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: Example: Cloning a React Element with cloneElement
DESCRIPTION: This example demonstrates how to use `cloneElement` to take an existing React element, modify its props, and replace its children, resulting in a new element with the specified changes.
SOURCE: https://react.dev/reference/react/cloneElement

LANGUAGE: javascript
CODE:
```
import { cloneElement } from 'react';

// ...

const clonedElement = cloneElement(

<Row title="Cabbage">

Hello

</Row>,

{ isHighlighted: true },

'Goodbye'

);

console.log(clonedElement); // <Row title="Cabbage" isHighlighted={true}>Goodbye</Row>
```

----------------------------------------

TITLE: React 19.1 Global APIs Reference
DESCRIPTION: Lists all available global APIs in React 19.1, including experimental ones, providing a quick reference to their names and links to detailed documentation.
SOURCE: https://react.dev/reference/react-dom/components/style

LANGUAGE: APIDOC
CODE:
```
APIs:
  - act
  - cache
  - captureOwnerStack
  - createContext
  - lazy
  - memo
  - startTransition
  - use
  - experimental_taintObjectReference (Experimental)
  - experimental_taintUniqueValue (Experimental)
  - unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: Example: Prerendering React App to Static HTML
DESCRIPTION: This example demonstrates how to use `prerender` to generate static HTML for a React application. It shows how to set up bootstrap scripts for client-side hydration, returning a `Response` with the generated HTML.
SOURCE: https://react.dev/reference/react-dom/static/prerender

LANGUAGE: javascript
CODE:
```
import { prerender } from 'react-dom/static';

async function handler(request) {
  const {prelude} = await prerender(<App />, {
    bootstrapScripts: ['/main.js']
  });

  return new Response(prelude, {
    headers: { 'content-type': 'text/html' },
  });
}
```

----------------------------------------

TITLE: React v19.1 API Reference Overview
DESCRIPTION: Lists all available Hooks, Components, and APIs in React version 19.1, including experimental features.
SOURCE: https://react.dev/reference/react-dom/client/createRoot

LANGUAGE: APIDOC
CODE:
```
React v19.1 Reference:

Hooks:
  - useActionState
  - useCallback
  - useContext
  - useDebugValue
  - useDeferredValue
  - useEffect
  - useId
  - useImperativeHandle
  - useInsertionEffect
  - useLayoutEffect
  - useMemo
  - useOptimistic
  - useReducer
  - useRef
  - useState
  - useSyncExternalStore
  - useTransition

Components:
  - <Fragment> (<>)
  - <Profiler>
  - <StrictMode>
  - <Suspense>
  - <Activity> (Experimental)
  - <ViewTransition> (Experimental)

APIs:
  - act
  - cache
  - captureOwnerStack
  - createContext
  - lazy
  - memo
  - startTransition
  - use
  - experimental_taintObjectReference (Experimental)
  - experimental_taintUniqueValue (Experimental)
  - unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: Original Class Component using createRef for Migration Example
DESCRIPTION: Presents the original React class component implementation that utilizes `createRef` to manage an input field, serving as the starting point for a migration example to functional components.
SOURCE: https://react.dev/reference/react/createRef

LANGUAGE: JavaScript
CODE:
```
import { Component, createRef } from 'react';

export default class Form extends Component {
  inputRef = createRef();

  handleClick = () => {
    this.inputRef.current.focus();
  }

  render() {
    return (
      <>
        <input ref={this.inputRef} />
        <button onClick={this.handleClick}>
          Focus the input
        </button>
      </>
    );
  }
}
```

----------------------------------------

TITLE: React API: use
DESCRIPTION: Documentation for the React `use` API, used for consuming resources like Promises or Context.
SOURCE: https://react.dev/reference/rsc/server-components

LANGUAGE: APIDOC
CODE:
```
use
```

----------------------------------------

TITLE: React API Reference Index
DESCRIPTION: A structured index of React DOM APIs, Hooks, Components, Rules, React Server Components, and Legacy APIs, categorized by their functionality and version.
SOURCE: https://react.dev/reference/rules/react-calls-components-and-hooks

LANGUAGE: APIDOC
CODE:
```
react-dom@19.1:
  Hooks:
    - useFormStatus
  Components:
    - Common (e.g. <div>)
    - <form>
    - <input>
    - <option>
    - <progress>
    - <select>
    - <textarea>
    - <link>
    - <meta>
    - <script>
    - <style>
    - <title>
  APIs:
    - createPortal
    - flushSync
    - preconnect
    - prefetchDNS
    - preinit
    - preinitModule
    - preload
    - preloadModule
  Client APIs:
    - createRoot
    - hydrateRoot
  Server APIs:
    - renderToPipeableStream
    - renderToReadableStream
    - renderToStaticMarkup
    - renderToString
  Static APIs:
    - prerender
    - prerenderToNodeStream
Rules of React:
  - Overview
  - Components and Hooks must be pure
  - React calls Components and Hooks
  - Rules of Hooks
React Server Components:
  - Server Components
  - Server Functions
  - Directives:
    - 'use client'
    - 'use server'
Legacy APIs:
  - Legacy React APIs:
    - Children
    - cloneElement
    - Component
    - createElement
    - createRef
    - forwardRef
    - isValidElement
    - PureComponent
```

----------------------------------------

TITLE: React DOM 19.1 API Reference Overview
DESCRIPTION: This section provides a structured overview of the `react-dom@19.1` APIs, including Hooks, Components, and various client, server, and static APIs, along with legacy React APIs and rules.
SOURCE: https://react.dev/reference/react/cloneElement

LANGUAGE: APIDOC
CODE:
```
react-dom@19.1:
  Hooks:
    - useFormStatus
  Components:
    - Common (e.g. <div>)
    - <form>
    - <input>
    - <option>
    - <progress>
    - <select>
    - <textarea>
    - <link>
    - <meta>
    - <script>
    - <style>
    - <title>
  APIs:
    - createPortal
    - flushSync
    - preconnect
    - prefetchDNS
    - preinit
    - preinitModule
    - preload
    - preloadModule
  Client APIs:
    - createRoot
    - hydrateRoot
  Server APIs:
    - renderToPipeableStream
    - renderToReadableStream
    - renderToStaticMarkup
    - renderToString
  Static APIs:
    - prerender
    - prerenderToNodeStream
Rules of React:
  Overview:
    - Components and Hooks must be pure
    - React calls Components and Hooks
    - Rules of Hooks
React Server Components:
  - Server Components
  - Server Functions
  Directives:
    - 'use client'
    - 'use server'
Legacy APIs:
  Legacy React APIs:
    - Children
    - cloneElement
    - Component
    - createElement
    - createRef
    - forwardRef
    - isValidElement
    - PureComponent
```

----------------------------------------

TITLE: Basic React Form Component JSX Example
DESCRIPTION: A simple JSX code example demonstrating the structure of a basic HTML form component within a React application, including an input field and a submit button.
SOURCE: https://react.dev/reference/react-dom/components/form

LANGUAGE: jsx
CODE:
```
<form action={search}>

<input name="query" />

<button type="submit">Search</button>

</form>
```

----------------------------------------

TITLE: React `use` API Definition
DESCRIPTION: Defines the `use` API in React, which allows reading values from resources such as Promises or context. It outlines its syntax, common usage patterns, and troubleshooting tips.
SOURCE: https://react.dev/reference/react/use

LANGUAGE: APIDOC
CODE:
```
use:
  Description: React API that lets you read the value of a resource like a Promise or context.
  Syntax:
    - use(resource)
  Usage:
    - Reading context with `use`
    - Streaming data from the server to the client
    - Dealing with rejected Promises
  Troubleshooting:
    - "Suspense Exception: This is not a real error!"
```

----------------------------------------

TITLE: Legacy React APIs Reference
DESCRIPTION: Documentation for older React APIs that are still available but might have modern alternatives.
SOURCE: https://react.dev/reference/react/lazy

LANGUAGE: APIDOC
CODE:
```
Legacy APIs:
  Legacy React APIs:
    Children
    cloneElement
    Component
    createElement
    createRef
    forwardRef
    isValidElement
    PureComponent
```

----------------------------------------

TITLE: React v19.1 Top-Level APIs Reference
DESCRIPTION: A collection of top-level APIs available in React v19.1, offering utilities for testing, context management, memoization, and experimental features.
SOURCE: https://react.dev/reference/rsc/use-client

LANGUAGE: APIDOC
CODE:
```
APIs:
- act
- cache
- captureOwnerStack
- createContext
- lazy
- memo
- startTransition
- use
- experimental_taintObjectReference (Experimental)
- experimental_taintUniqueValue (Experimental)
- unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: Example JSON API Data Structure for Products
DESCRIPTION: This JSON array represents the product data returned by a hypothetical API, including category, price, stock status, and product name. It serves as the data model for building the React UI components.
SOURCE: https://react.dev/learn/thinking-in-react

LANGUAGE: JSON
CODE:
```
[

{
  "category": "Fruits",
  "price": "$1",
  "stocked": true,
  "name": "Apple"
},

{
  "category": "Fruits",
  "price": "$1",
  "stocked": true,
  "name": "Dragonfruit"
},

{
  "category": "Fruits",
  "price": "$2",
  "stocked": false,
  "name": "Passionfruit"
},

{
  "category": "Vegetables",
  "price": "$2",
  "stocked": true,
  "name": "Spinach"
},

{
  "category": "Vegetables",
  "price": "$4",
  "stocked": false,
  "name": "Pumpkin"
},

{
  "category": "Vegetables",
  "price": "$1",
  "stocked": true,
  "name": "Peas"
}

]
```

----------------------------------------

TITLE: React Children.forEach API Reference and Usage
DESCRIPTION: Details the API for `React.Children.forEach`, a utility to iterate over children and execute a callback function for each. The example shows how to use `forEach` to insert a separator (`<hr>`) between child elements, building a new array of React nodes.
SOURCE: https://react.dev/reference/react/Children

LANGUAGE: APIDOC
CODE:
```
Children.forEach(children, fn, thisArg?)
  children: The value of the children prop received by your component.
  fn: The function you want to run for each child, similar to the array forEach method callback. It will be called with the child as the first argument and its index as the second argument. The index starts at 0 and increments on each call.
  thisArg (optional): The this value with which the fn function should be called. If omitted, it’s undefined.
Returns: undefined.
Caveats: Empty nodes (null, undefined, and Booleans), strings, numbers, and React elements count as individual nodes. Arrays don’t count as individual nodes, but their children do. The traversal does not go deeper than React elements: they don’t get rendered, and their children aren’t traversed. Fragments don’t get traversed.
```

LANGUAGE: JavaScript
CODE:
```
import { Children } from 'react';

function SeparatorList({ children }) {
  const result = [];
  Children.forEach(children, (child, index) => {
    result.push(child);
    result.push(<hr key={index} />);
  });
  // ...
```

----------------------------------------

TITLE: Migrate from Legacy Context API to New Context API
DESCRIPTION: Shows how to refactor class components using the deprecated `contextTypes` and `getChildContext` APIs to the modern `React.createContext` and `contextType` API in React 19. This improves consistency and removes subtle bugs.
SOURCE: https://react.dev/blog/2024/04/25/react-19-upgrade-guide

LANGUAGE: JavaScript
CODE:
```
// Before
import PropTypes from 'prop-types';

class Parent extends React.Component {
  static childContextTypes = {
    foo: PropTypes.string.isRequired,
  };

  getChildContext() {
    return { foo: 'bar' };
  }

  render() {
    return <Child />;
  }
}

class Child extends React.Component {
  static contextTypes = {
    foo: PropTypes.string.isRequired,
  };

  render() {
    return <div>{this.context.foo}</div>;
  }
}
```

LANGUAGE: JavaScript
CODE:
```
// After
const FooContext = React.createContext();

class Parent extends React.Component {
  render() {
    return (
      <FooContext value='bar'>
        <Child />
      </FooContext>
    );
  }
}

class Child extends React.Component {
  static contextType = FooContext;
  render() {
    return <div>{this.context}</div>;
  }
}
```

----------------------------------------

TITLE: React Server Components API Reference
DESCRIPTION: Documentation for React Server Components, Server Functions, and directives like 'use client' and 'use server'.
SOURCE: https://react.dev/reference/react/Activity

LANGUAGE: APIDOC
CODE:
```
React Server Components:
  - Server Components
  - Server Functions
  Directives:
    - 'use client'
    - 'use server'
```

----------------------------------------

TITLE: React DOM prerender API Definition
DESCRIPTION: Defines the `prerender` function from React DOM, which facilitates server-side rendering of React trees into static HTML strings using Web Streams. It includes the function signature, parameter descriptions, return type, and a practical example.
SOURCE: https://react.dev/reference/react-dom/static/prerender

LANGUAGE: APIDOC
CODE:
```
prerender(reactNode, options?)

Description:
  Renders a React tree to a static HTML string using a Web Stream.

Parameters:
  reactNode: ReactNode - The React element or tree to render.
  options?: object - Optional configuration object.

Returns:
  Promise<{prelude: string}> - A promise that resolves to an object containing the 'prelude' string.

Example:
const {prelude} = await prerender(reactNode, options?)
```

----------------------------------------

TITLE: React DOM 19.1 API Reference
DESCRIPTION: This section outlines the various APIs available in react-dom version 19.1, categorized into Hooks, Components, general APIs, Client APIs, Server APIs, and Static APIs. It provides a structured overview of available functionalities.
SOURCE: https://react.dev/reference/react/useActionState

LANGUAGE: APIDOC
CODE:
```
react-dom@19.1:
  Hooks:
    - useFormStatus
  Components:
    - Common (e.g. <div>)
    - <form>
    - <input>
    - <option>
    - <progress>
    - <select>
    - <textarea>
    - <link>
    - <meta>
    - <script>
    - <style>
    - <title>
  APIs:
    - createPortal
    - flushSync
    - preconnect
    - prefetchDNS
    - preinit
    - preinitModule
    - preload
    - preloadModule
  Client APIs:
    - createRoot
    - hydrateRoot
  Server APIs:
    - renderToPipeableStream
    - renderToReadableStream
    - renderToStaticMarkup
    - renderToString
  Static APIs:
    - prerender
    - prerenderToNodeStream
```

----------------------------------------

TITLE: React v19.1 API Reference Index
DESCRIPTION: Lists all available Hooks, Components, and APIs in React version 19.1, indicating experimental features and providing a structured overview of the library's public interface.
SOURCE: https://react.dev/reference/react/useContext

LANGUAGE: APIDOC
CODE:
```
React v19.1 API Reference:
  Hooks:
    - useActionState
    - useCallback
    - useContext
    - useDebugValue
    - useDeferredValue
    - useEffect
    - useId
    - useImperativeHandle
    - useInsertionEffect
    - useLayoutEffect
    - useMemo
    - useOptimistic
    - useReducer
    - useRef
    - useState
    - useSyncExternalStore
    - useTransition
  Components:
    - <Fragment> (<>)
    - <Profiler>
    - <StrictMode>
    - <Suspense>
    - <Activity> (Experimental)
    - <ViewTransition> (Experimental)
  APIs:
    - act
    - cache
    - captureOwnerStack
    - createContext
    - lazy
    - memo
    - startTransition
    - use
    - experimental_taintObjectReference (Experimental)
    - experimental_taintUniqueValue (Experimental)
    - unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: React v19.1 API Reference Overview
DESCRIPTION: This section provides an overview of the core Hooks, Components, and APIs available in React version 19.1, with links to their detailed documentation. It serves as a structured index for navigating the React API.
SOURCE: https://react.dev/reference/react/act

LANGUAGE: APIDOC
CODE:
```
React v19.1 API Reference:
  Hooks:
    - useActionState
    - useCallback
    - useContext
    - useDebugValue
    - useDeferredValue
    - useEffect
    - useId
    - useImperativeHandle
    - useInsertionEffect
    - useLayoutEffect
    - useMemo
    - useOptimistic
    - useReducer
    - useRef
    - useState
    - useSyncExternalStore
    - useTransition
  Components:
    - <Fragment> (<>)
    - <Profiler>
    - <StrictMode>
    - <Suspense>
    - <Activity> (Experimental)
    - <ViewTransition> (Experimental)
  APIs:
    - act
    - cache
    - captureOwnerStack
    - createContext
    - lazy
    - memo
    - startTransition
    - use
    - experimental_taintObjectReference (Experimental)
    - experimental_taintUniqueValue (Experimental)
    - unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: React 19.1 APIs Reference
DESCRIPTION: A list of utility APIs available in React 19.1 for advanced use cases such as testing, caching, context creation, lazy loading, memoization, and managing transitions. This section also includes experimental APIs for advanced features.
SOURCE: https://react.dev/reference/rules/react-calls-components-and-hooks

LANGUAGE: APIDOC
CODE:
```
APIs:
  - act
  - cache
  - captureOwnerStack
  - createContext
  - lazy
  - memo
  - startTransition
  - use
  - experimental_taintObjectReference (Experimental)
  - experimental_taintUniqueValue (Experimental)
  - unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: React v19.1 API Reference Overview
DESCRIPTION: An overview of the core API elements available in React version 19.1, categorized into Hooks, Components, and general APIs. This includes stable and experimental features.
SOURCE: https://react.dev/reference/rsc/directives

LANGUAGE: APIDOC
CODE:
```
React v19.1 Reference:
  Hooks:
    - useActionState
    - useCallback
    - useContext
    - useDebugValue
    - useDeferredValue
    - useEffect
    - useId
    - useImperativeHandle
    - useInsertionEffect
    - useLayoutEffect
    - useMemo
    - useOptimistic
    - useReducer
    - useRef
    - useState
    - useSyncExternalStore
    - useTransition
  Components:
    - <Fragment> (<>)
    - <Profiler>
    - <StrictMode>
    - <Suspense>
    - <Activity> (Experimental)
    - <ViewTransition> (Experimental)
  APIs:
    - act
    - cache
    - captureOwnerStack
    - createContext
    - lazy
    - memo
    - startTransition
    - use
    - experimental_taintObjectReference (Experimental)
    - experimental_taintUniqueValue (Experimental)
    - unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: React 19.1 APIs Reference
DESCRIPTION: This section details the core APIs available in React v19.1, providing functions for various advanced use cases, including testing utilities and experimental features.
SOURCE: https://react.dev/reference/rsc/use-server

LANGUAGE: APIDOC
CODE:
```
APIs:
  act
  cache
  captureOwnerStack
  createContext
  lazy
  memo
  startTransition
  use
  experimental_taintObjectReference (Experimental)
  experimental_taintUniqueValue (Experimental)
  unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: Legacy React APIs
DESCRIPTION: Provides a reference to older React APIs that are still available but may have modern alternatives, such as Children utilities, element creation, and component types. This section helps in understanding and migrating from older React codebases.
SOURCE: https://react.dev/reference/rsc/use-server

LANGUAGE: APIDOC
CODE:
```
Legacy React APIs:
  - Children
  - cloneElement
  - Component
  - createElement
  - createRef
  - forwardRef
  - isValidElement
  - PureComponent
```

----------------------------------------

TITLE: React DOM 19.1 API Reference Structure
DESCRIPTION: Hierarchical overview of the React DOM 19.1 API, categorizing Hooks, Components, general APIs, Client APIs, Server APIs, Static APIs, Rules of React, React Server Components, and Legacy APIs.
SOURCE: https://react.dev/reference/react/useOptimistic

LANGUAGE: APIDOC
CODE:
```
react-dom@19.1:
  Hooks:
    - useFormStatus
  Components:
    - Common (e.g. <div>)
    - <form>
    - <input>
    - <option>
    - <progress>
    - <select>
    - <textarea>
    - <link>
    - <meta>
    - <script>
    - <style>
    - <title>
  APIs:
    - createPortal
    - flushSync
    - preconnect
    - prefetchDNS
    - preinit
    - preinitModule
    - preload
    - preloadModule
  Client APIs:
    - createRoot
    - hydrateRoot
  Server APIs:
    - renderToPipeableStream
    - renderToReadableStream
    - renderToStaticMarkup
    - renderToString
  Static APIs:
    - prerender
    - prerenderToNodeStream
Rules of React:
  - Overview:
    - Components and Hooks must be pure
    - React calls Components and Hooks
    - Rules of Hooks
React Server Components:
  - Server Components
  - Server Functions
  - Directives:
    - 'use client'
    - 'use server'
Legacy APIs:
  - Legacy React APIs:
    - Children
    - cloneElement
    - Component
    - createElement
    - createRef
    - forwardRef
    - isValidElement
    - PureComponent
```

----------------------------------------

TITLE: React 19.1 API Reference Overview
DESCRIPTION: This section outlines the key hooks, components, and APIs available in React 19.1, providing a structured overview of the framework's core functionalities.
SOURCE: https://react.dev/reference/react-dom/static

LANGUAGE: APIDOC
CODE:
```
React 19.1 Reference:
  Hooks:
    - useActionState
    - useCallback
    - useContext
    - useDebugValue
    - useDeferredValue
    - useEffect
    - useId
    - useImperativeHandle
    - useInsertionEffect
    - useLayoutEffect
    - useMemo
    - useOptimistic
    - useReducer
    - useRef
    - useState
    - useSyncExternalStore
    - useTransition
  Components:
    - <Fragment> (<>)
    - <Profiler>
    - <StrictMode>
    - <Suspense>
    - <Activity> (Experimental)
    - <ViewTransition> (Experimental)
  APIs:
    - act
    - cache
    - captureOwnerStack
    - createContext
    - lazy
    - memo
    - startTransition
    - use
    - experimental_taintObjectReference (Experimental)
    - experimental_taintUniqueValue (Experimental)
    - unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: Import and Use renderToStaticMarkup Example
DESCRIPTION: Demonstrates how to import `renderToStaticMarkup` from 'react-dom/server' and use it to render a React component, such as `<Page />`, into a static HTML string on the server.
SOURCE: https://react.dev/reference/react-dom/server/renderToStaticMarkup

LANGUAGE: javascript
CODE:
```
import { renderToStaticMarkup } from 'react-dom/server';

const html = renderToStaticMarkup(<Page />);
```

----------------------------------------

TITLE: Legacy React APIs Reference
DESCRIPTION: Reference for older React APIs that are still available but may have modern alternatives, including utilities for children, element creation, and component types.
SOURCE: https://react.dev/reference/rsc/use-client

LANGUAGE: APIDOC
CODE:
```
Legacy React APIs:
  - Children
  - cloneElement
  - Component
  - createElement
  - createRef
  - forwardRef
  - isValidElement
  - PureComponent
```

----------------------------------------

TITLE: React DOM 19.1 API Reference Overview
DESCRIPTION: Provides a structured overview of Hooks, Components, and various APIs available in React DOM version 19.1, categorized by their functionality (e.g., Client, Server, Static APIs).
SOURCE: https://react.dev/reference/react/Fragment

LANGUAGE: APIDOC
CODE:
```
react-dom@19.1:
  Hooks:
    - useFormStatus
  Components:
    - Common (e.g. <div>)
    - <form>
    - <input>
    - <option>
    - <progress>
    - <select>
    - <textarea>
    - <link>
    - <meta>
    - <script>
    - <style>
    - <title>
  APIs:
    - createPortal
    - flushSync
    - preconnect
    - prefetchDNS
    - preinit
    - preinitModule
    - preload
    - preloadModule
  Client APIs:
    - createRoot
    - hydrateRoot
  Server APIs:
    - renderToPipeableStream
    - renderToReadableStream
    - renderToStaticMarkup
    - renderToString
  Static APIs:
    - prerender
    - prerenderToNodeStream
```

----------------------------------------

TITLE: preinit API
DESCRIPTION: Reference documentation for the preinit API in React DOM 19.1, used to pre-initialize resources like scripts or stylesheets.
SOURCE: https://react.dev/reference/react-dom/client/createRoot

LANGUAGE: APIDOC
CODE:
```
APIs:
  preinit
```

----------------------------------------

TITLE: React 19.1 API Reference Overview
DESCRIPTION: A structured overview of the Hooks, Components, and APIs available in React version 19.1, including experimental features and their availability.
SOURCE: https://react.dev/reference/react

LANGUAGE: APIDOC
CODE:
```
React v19.1 Reference:
  Hooks:
    - useActionState
    - useCallback
    - useContext
    - useDebugValue
    - useDeferredValue
    - useEffect
    - useId
    - useImperativeHandle
    - useInsertionEffect
    - useLayoutEffect
    - useMemo
    - useOptimistic
    - useReducer
    - useRef
    - useState
    - useSyncExternalStore
    - useTransition
  Components:
    - <Fragment> (<>)
    - <Profiler>
    - <StrictMode>
    - <Suspense>
    - <Activity> (Experimental)
    - <ViewTransition> (Experimental)
  APIs:
    - act
    - cache
    - captureOwnerStack
    - createContext
    - lazy
    - memo
    - startTransition
    - use
    - experimental_taintObjectReference (Experimental)
    - experimental_taintUniqueValue (Experimental)
    - unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: React APIs Reference (v19.1)
DESCRIPTION: A list of various React APIs available in React v19.1. These APIs provide utilities for testing, caching, context management, lazy loading, memoization, and managing transitions, including experimental features.
SOURCE: https://react.dev/reference/rules/rules-of-hooks

LANGUAGE: APIDOC
CODE:
```
APIs:
  - act
  - cache
  - captureOwnerStack
  - createContext
  - lazy
  - memo
  - startTransition
  - use
  - experimental_taintObjectReference (Experimental)
  - experimental_taintUniqueValue (Experimental)
  - unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: Legacy React APIs Reference
DESCRIPTION: Reference for older React APIs that are still available but may have modern alternatives, including Children utilities, element creation, and component types.
SOURCE: https://react.dev/reference/react/use

LANGUAGE: APIDOC
CODE:
```
Legacy APIs:
  Legacy React APIs:
    - Children
    - cloneElement
    - Component
    - createElement
    - createRef
    - forwardRef
    - isValidElement
    - PureComponent
```

----------------------------------------

TITLE: React v19.1 API Reference Index
DESCRIPTION: A comprehensive list of all Hooks, Components, and general APIs available in React version 19.1, including experimental features.
SOURCE: https://react.dev/reference/react/Profiler

LANGUAGE: APIDOC
CODE:
```
React v19.1 API Reference:

Hooks:
  - useActionState
  - useCallback
  - useContext
  - useDebugValue
  - useDeferredValue
  - useEffect
  - useId
  - useImperativeHandle
  - useInsertionEffect
  - useLayoutEffect
  - useMemo
  - useOptimistic
  - useReducer
  - useRef
  - useState
  - useSyncExternalStore
  - useTransition

Components:
  - <Fragment> (<>)
  - <Profiler>
  - <StrictMode>
  - <Suspense>
  - <Activity> (Experimental)
  - <ViewTransition> (Experimental)

APIs:
  - act
  - cache
  - captureOwnerStack
  - createContext
  - lazy
  - memo
  - startTransition
  - use
  - experimental_taintObjectReference (Experimental)
  - experimental_taintUniqueValue (Experimental)
  - unstable_addTransitionType (Experimental)
```

----------------------------------------

TITLE: React DOM 19.1 API Reference
DESCRIPTION: Comprehensive list of Hooks, Components, and APIs available in React DOM version 19.1, including client-side, server-side, and static APIs.
SOURCE: https://react.dev/reference/react/Activity

LANGUAGE: APIDOC
CODE:
```
react-dom@19.1:
  Hooks:
    - useFormStatus
  Components:
    - Common (e.g. <div>)
    - <form>
    - <input>
    - <option>
    - <progress>
    - <select>
    - <textarea>
    - <link>
    - <meta>
    - <script>
    - <style>
    - <title>
  APIs:
    - createPortal
    - flushSync
    - preconnect
    - prefetchDNS
    - preinit
    - preinitModule
    - preload
    - preloadModule
  Client APIs:
    - createRoot
    - hydrateRoot
  Server APIs:
    - renderToPipeableStream
    - renderToReadableStream
    - renderToStaticMarkup
    - renderToString
  Static APIs:
    - prerender
    - prerenderToNodeStream
```

----------------------------------------

TITLE: React DOM 19.1 API Overview
DESCRIPTION: A structured overview of the React DOM 19.1 API, including Hooks, Components, and various API categories like client, server, and static APIs, along with rules of React and React Server Components.
SOURCE: https://react.dev/reference/react/captureOwnerStack

LANGUAGE: APIDOC
CODE:
```
react-dom@19.1:
  Hooks:
    - useFormStatus
  Components:
    - Common (e.g. <div>)
    - <form>
    - <input>
    - <option>
    - <progress>
    - <select>
    - <textarea>
    - <link>
    - <meta>
    - <script>
    - <style>
    - <title>
  APIs:
    - createPortal
    - flushSync
    - preconnect
    - prefetchDNS
    - preinit
    - preinitModule
    - preload
    - preloadModule
  Client APIs:
    - createRoot
    - hydrateRoot
  Server APIs:
    - renderToPipeableStream
    - renderToReadableStream
    - renderToStaticMarkup
    - renderToString
  Static APIs:
    - prerender
    - prerenderToNodeStream
Rules of React:
  - Overview:
    - Components and Hooks must be pure
    - React calls Components and Hooks
    - Rules of Hooks
React Server Components:
  - Server Components
  - Server Functions
  - Directives:
    - 'use client'
    - 'use server'
Legacy APIs:
  - Legacy React APIs:
    - Children
    - cloneElement
    - Component
    - createElement
    - createRef
    - forwardRef
    - isValidElement
    - PureComponent
```

----------------------------------------

TITLE: React: `use` API for Reading Promises in Render
DESCRIPTION: This example illustrates the `use` API in React 19, which enables components to read the value of a promise directly within the render function. React will suspend rendering until the promise resolves, integrating with Suspense boundaries.
SOURCE: https://react.dev/blog/2024/04/25/react-19

LANGUAGE: JavaScript
CODE:
```
import {use} from 'react';

function Comments({commentsPromise}) {
  // `use` will suspend until the promise resolves.
  const comments = use(commentsPromise);
  return comments.map(comment => <p key={comment.id}>{comment}</p>);
}

function Page({commentsPromise}) {
  // When `use` suspends in Comments,
  // this Suspense boundary will be shown.
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <Comments commentsPromise={commentsPromise} />
    </Suspense>
  )
}
```

----------------------------------------

TITLE: React API: createContext
DESCRIPTION: Reference for the `createContext` React API in React 19.1. React APIs provide advanced functionalities for various use cases, including testing.
SOURCE: https://react.dev/reference/react-dom/components/input

LANGUAGE: APIDOC
CODE:
```
createContext
```