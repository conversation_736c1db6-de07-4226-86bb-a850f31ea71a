import { PrismaClient } from "@nexus/database-schema";
import { DATABASE_CONFIG, setTenantContext } from "@nexus/database-schema";

// Global Prisma client instance
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

// Create Prisma client with optimized configuration
export const prisma =
  globalForPrisma.prisma ??
  new PrismaClient({
    log: ["query", "error", "warn"],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  });

if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Multi-tenant Prisma client wrapper
export class TenantPrismaClient {
  private client: PrismaClient;
  private tenantId: string;

  constructor(tenantId: string, client: PrismaClient = prisma) {
    this.client = client;
    this.tenantId = tenantId;
  }

  // Execute query with tenant context
  async withTenantContext<T>(
    operation: (client: PrismaClient) => Promise<T>
  ): Promise<T> {
    return this.client.$transaction(async (tx) => {
      // Set tenant context for RLS
      await tx.$executeRawUnsafe(setTenantContext(this.tenantId));
      return operation(tx);
    });
  }

  // Get tenant-scoped client
  get tenant() {
    return {
      user: {
        findMany: (args?: any) =>
          this.withTenantContext((tx) =>
            tx.user.findMany({
              ...args,
              where: { ...args?.where, tenantId: this.tenantId },
            })
          ),
        findUnique: (args: any) =>
          this.withTenantContext((tx) =>
            tx.user.findUnique({
              ...args,
              where: { ...args.where, tenantId: this.tenantId },
            })
          ),
        create: (args: any) =>
          this.withTenantContext((tx) =>
            tx.user.create({
              ...args,
              data: { ...args.data, tenantId: this.tenantId },
            })
          ),
        update: (args: any) =>
          this.withTenantContext((tx) =>
            tx.user.update({
              ...args,
              where: { ...args.where, tenantId: this.tenantId },
            })
          ),
        delete: (args: any) =>
          this.withTenantContext((tx) =>
            tx.user.delete({
              ...args,
              where: { ...args.where, tenantId: this.tenantId },
            })
          ),
      },
      workspace: {
        findMany: (args?: any) =>
          this.withTenantContext((tx) =>
            tx.workspace.findMany({
              ...args,
              where: { ...args?.where, tenantId: this.tenantId },
            })
          ),
        findUnique: (args: any) =>
          this.withTenantContext((tx) =>
            tx.workspace.findUnique({
              ...args,
              where: { ...args.where, tenantId: this.tenantId },
            })
          ),
        create: (args: any) =>
          this.withTenantContext((tx) =>
            tx.workspace.create({
              ...args,
              data: { ...args.data, tenantId: this.tenantId },
            })
          ),
        update: (args: any) =>
          this.withTenantContext((tx) =>
            tx.workspace.update({
              ...args,
              where: { ...args.where, tenantId: this.tenantId },
            })
          ),
        delete: (args: any) =>
          this.withTenantContext((tx) =>
            tx.workspace.delete({
              ...args,
              where: { ...args.where, tenantId: this.tenantId },
            })
          ),
      },
      session: {
        findMany: (args?: any) =>
          this.withTenantContext((tx) =>
            tx.session.findMany({
              ...args,
              where: { ...args?.where, tenantId: this.tenantId },
            })
          ),
        findUnique: (args: any) =>
          this.withTenantContext((tx) =>
            tx.session.findUnique({
              ...args,
              where: { ...args.where, tenantId: this.tenantId },
            })
          ),
        create: (args: any) =>
          this.withTenantContext((tx) =>
            tx.session.create({
              ...args,
              data: { ...args.data, tenantId: this.tenantId },
            })
          ),
        update: (args: any) =>
          this.withTenantContext((tx) =>
            tx.session.update({
              ...args,
              where: { ...args.where, tenantId: this.tenantId },
            })
          ),
        delete: (args: any) =>
          this.withTenantContext((tx) =>
            tx.session.delete({
              ...args,
              where: { ...args.where, tenantId: this.tenantId },
            })
          ),
      },
    };
  }
}

// Factory function to create tenant-scoped client
export const createTenantClient = (tenantId: string): TenantPrismaClient => {
  return new TenantPrismaClient(tenantId);
};

// Database health check
export const checkDatabaseHealth = async (): Promise<boolean> => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error("Database health check failed:", error);
    return false;
  }
};

// Graceful shutdown
export const disconnectDatabase = async (): Promise<void> => {
  await prisma.$disconnect();
};
