import { FastifyInstance } from "fastify";
import { authRoutes } from "./auth";
import { userRoutes } from "./users";
import { roleRoutes } from "./roles";
import { workspaceRoutes } from "./workspaces";
import { teamRoutes } from "./teams";
import { projectRoutes } from "./projects";
import { fileRoutes } from "./files";
import { analyticsRoutes } from "./analytics";
import { billingRoutes } from "./billing";
import { adminRoutes } from "./admin";

export const registerRoutes = async (fastify: FastifyInstance) => {
  // API version prefix
  await fastify.register(async (fastify) => {
    // Authentication routes
    await fastify.register(authRoutes, { prefix: "/auth" });
    
    // Core resource routes
    await fastify.register(userRoutes, { prefix: "/users" });
    await fastify.register(roleRoutes, { prefix: "/roles" });
    await fastify.register(workspaceRoutes, { prefix: "/workspaces" });
    await fastify.register(teamRoutes, { prefix: "/teams" });
    await fastify.register(projectRoutes, { prefix: "/projects" });
    await fastify.register(fileRoutes, { prefix: "/files" });
    
    // Feature routes
    await fastify.register(analyticsRoutes, { prefix: "/analytics" });
    await fastify.register(billingRoutes, { prefix: "/billing" });
    
    // Admin routes
    await fastify.register(adminRoutes, { prefix: "/admin" });
  }, { prefix: "/api/v1" });
};
