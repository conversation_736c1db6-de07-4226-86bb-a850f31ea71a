import { type ClassValue } from "clsx";
/**
 * Utility function to merge Tailwind CSS classes
 */
export declare function cn(...inputs: ClassValue[]): string;
/**
 * Generate a slug from a string
 */
export declare function slugify(text: string): string;
/**
 * Capitalize the first letter of a string
 */
export declare function capitalize(text: string): string;
/**
 * Format a date to a readable string
 */
export declare function formatDate(date: Date | string): string;
/**
 * Truncate text to a specified length
 */
export declare function truncate(text: string, length: number): string;
/**
 * Check if a value is empty (null, undefined, empty string, empty array, empty object)
 */
export declare function isEmpty(value: any): boolean;
/**
 * Debounce function
 */
export declare function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void;
/**
 * Generate a random ID
 */
export declare function generateId(length?: number): string;
//# sourceMappingURL=index.d.ts.map