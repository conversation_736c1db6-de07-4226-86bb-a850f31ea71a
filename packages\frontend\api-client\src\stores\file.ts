import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { restApiClient } from "../rest";
import { endpoints } from "../config";
import { FileStore, FileItem, FileUploadInput, UpdateFileInput, PaginationParams, SearchParams } from "../types";

interface FileState extends FileStore {
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  addFile: (file: FileItem) => void;
  updateFileInList: (id: string, updates: Partial<FileItem>) => void;
  removeFile: (id: string) => void;
  setUploadProgress: (fileId: string, progress: number) => void;
  clearUploadProgress: (fileId: string) => void;
}

export const useFileStore = create<FileState>()(
  immer((set, get) => ({
    files: [],
    isLoading: false,
    error: null,
    uploadProgress: {},

    setLoading: (loading) =>
      set((state) => {
        state.isLoading = loading;
      }),

    setError: (error) =>
      set((state) => {
        state.error = error;
      }),

    addFile: (file) =>
      set((state) => {
        state.files.unshift(file); // Add to beginning for newest first
      }),

    updateFileInList: (id, updates) =>
      set((state) => {
        const index = state.files.findIndex(f => f.id === id);
        if (index !== -1) {
          state.files[index] = { ...state.files[index], ...updates };
        }
      }),

    removeFile: (id) =>
      set((state) => {
        state.files = state.files.filter(f => f.id !== id);
      }),

    setUploadProgress: (fileId, progress) =>
      set((state) => {
        state.uploadProgress[fileId] = progress;
      }),

    clearUploadProgress: (fileId) =>
      set((state) => {
        delete state.uploadProgress[fileId];
      }),

    fetchFiles: async (workspaceId?: string, projectId?: string, pagination?: PaginationParams, search?: SearchParams) => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (workspaceId) params.append("workspaceId", workspaceId);
        if (projectId) params.append("projectId", projectId);
        if (pagination?.page) params.append("page", pagination.page.toString());
        if (pagination?.limit) params.append("limit", pagination.limit.toString());
        if (pagination?.sort) params.append("sort", pagination.sort);
        if (pagination?.order) params.append("order", pagination.order);
        if (search?.query) params.append("query", search.query);
        if (search?.filters) {
          Object.entries(search.filters).forEach(([key, value]) => {
            params.append(`filters[${key}]`, value.toString());
          });
        }

        const response = await restApiClient.get(
          `${endpoints.files.list}?${params.toString()}`
        );

        set((state) => {
          state.files = response.data.items || response.data;
        });
      } catch (error: any) {
        setError(error.message || "Failed to fetch files");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    uploadFile: async (input: FileUploadInput) => {
      const { setError, addFile, setUploadProgress, clearUploadProgress } = get();
      const tempFileId = `temp_${Date.now()}`;
      
      try {
        setError(null);
        setUploadProgress(tempFileId, 0);

        const response = await restApiClient.uploadFile(
          endpoints.files.upload,
          input.file,
          {
            workspaceId: input.workspaceId,
            projectId: input.projectId,
            description: input.description,
            tags: input.tags,
            isPublic: input.isPublic,
          },
          (progress) => setUploadProgress(tempFileId, progress)
        );

        const file = response.data;
        addFile(file);
        clearUploadProgress(tempFileId);
        
        return file;
      } catch (error: any) {
        setError(error.message || "Failed to upload file");
        clearUploadProgress(tempFileId);
        throw error;
      }
    },

    updateFile: async (id: string, input: UpdateFileInput) => {
      const { setLoading, setError, updateFileInList } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.put(endpoints.files.update(id), input);
        const file = response.data;

        updateFileInList(id, file);
        return file;
      } catch (error: any) {
        setError(error.message || "Failed to update file");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    deleteFile: async (id: string) => {
      const { setLoading, setError, removeFile } = get();
      
      try {
        setLoading(true);
        setError(null);

        await restApiClient.delete(endpoints.files.delete(id));
        removeFile(id);
      } catch (error: any) {
        setError(error.message || "Failed to delete file");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    downloadFile: async (id: string) => {
      const { setError } = get();
      
      try {
        setError(null);
        
        const file = fileUtils.getFileById(id);
        const filename = file?.originalName || file?.filename || "download";
        
        await restApiClient.downloadFile(endpoints.files.download(id), filename);
      } catch (error: any) {
        setError(error.message || "Failed to download file");
        throw error;
      }
    },
  }))
);

// File utilities
export const fileUtils = {
  // Get file by ID
  getFileById: (id: string): FileItem | undefined => {
    const { files } = useFileStore.getState();
    return files.find(f => f.id === id);
  },

  // Get files by workspace
  getFilesByWorkspace: (workspaceId: string): FileItem[] => {
    const { files } = useFileStore.getState();
    return files.filter(f => f.workspaceId === workspaceId);
  },

  // Get files by project
  getFilesByProject: (projectId: string): FileItem[] => {
    const { files } = useFileStore.getState();
    return files.filter(f => f.projectId === projectId);
  },

  // Format file size
  formatFileSize: (bytes: number): string => {
    const units = ["B", "KB", "MB", "GB", "TB"];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  },

  // Get file type from mimetype
  getFileType: (mimetype: string): string => {
    if (mimetype.startsWith("image/")) return "image";
    if (mimetype.startsWith("video/")) return "video";
    if (mimetype.startsWith("audio/")) return "audio";
    if (mimetype.includes("pdf")) return "pdf";
    if (mimetype.includes("word") || mimetype.includes("document")) return "document";
    if (mimetype.includes("sheet") || mimetype.includes("excel")) return "spreadsheet";
    if (mimetype.includes("presentation") || mimetype.includes("powerpoint")) return "presentation";
    if (mimetype.includes("zip") || mimetype.includes("rar") || mimetype.includes("tar")) return "archive";
    if (mimetype.includes("text")) return "text";
    return "file";
  },

  // Get file icon based on type
  getFileIcon: (mimetype: string): string => {
    const type = fileUtils.getFileType(mimetype);
    
    switch (type) {
      case "image": return "🖼️";
      case "video": return "🎥";
      case "audio": return "🎵";
      case "pdf": return "📄";
      case "document": return "📝";
      case "spreadsheet": return "📊";
      case "presentation": return "📽️";
      case "archive": return "🗜️";
      case "text": return "📄";
      default: return "📁";
    }
  },

  // Check if file is image
  isImage: (mimetype: string): boolean => {
    return mimetype.startsWith("image/");
  },

  // Check if file is video
  isVideo: (mimetype: string): boolean => {
    return mimetype.startsWith("video/");
  },

  // Check if file is audio
  isAudio: (mimetype: string): boolean => {
    return mimetype.startsWith("audio/");
  },

  // Check if file has preview
  hasPreview: (mimetype: string): boolean => {
    return fileUtils.isImage(mimetype) || 
           fileUtils.isVideo(mimetype) || 
           mimetype.includes("pdf");
  },

  // Filter files by type
  filterByType: (type: string): FileItem[] => {
    const { files } = useFileStore.getState();
    return files.filter(f => fileUtils.getFileType(f.mimetype) === type);
  },

  // Filter files by tags
  filterByTags: (tags: string[]): FileItem[] => {
    const { files } = useFileStore.getState();
    return files.filter(f => 
      tags.some(tag => f.tags.includes(tag))
    );
  },

  // Search files
  searchFiles: (query: string): FileItem[] => {
    const { files } = useFileStore.getState();
    const lowercaseQuery = query.toLowerCase();
    
    return files.filter(f =>
      f.filename.toLowerCase().includes(lowercaseQuery) ||
      f.originalName.toLowerCase().includes(lowercaseQuery) ||
      f.description?.toLowerCase().includes(lowercaseQuery) ||
      f.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    );
  },

  // Get recent files
  getRecentFiles: (limit: number = 10): FileItem[] => {
    const { files } = useFileStore.getState();
    return [...files]
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, limit);
  },

  // Get file upload progress
  getUploadProgress: (fileId: string): number => {
    const { uploadProgress } = useFileStore.getState();
    return uploadProgress[fileId] || 0;
  },

  // Check if file is being uploaded
  isUploading: (fileId: string): boolean => {
    const { uploadProgress } = useFileStore.getState();
    return fileId in uploadProgress;
  },
};
