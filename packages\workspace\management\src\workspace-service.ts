import { createTenantClient } from "@nexus/database-service";
import { WorkspaceData, CreateWorkspaceData, UpdateWorkspaceData, WorkspaceStats } from "./workspace-types";
import { slugify } from "@nexus/utils";

export class WorkspaceService {
  private tenantId: string;

  constructor(tenantId: string) {
    this.tenantId = tenantId;
  }

  // Create a new workspace
  async create(data: CreateWorkspaceData): Promise<WorkspaceData> {
    const client = createTenantClient(this.tenantId);
    
    // Ensure slug is unique
    const slug = data.slug || slugify(data.name);
    await this.ensureSlugUnique(slug);

    const workspace = await client.tenant.workspace.create({
      data: {
        name: data.name,
        slug,
        description: data.description,
        settings: data.settings || this.getDefaultSettings(),
      },
    });

    return workspace as WorkspaceData;
  }

  // Get workspace by ID
  async findById(id: string): Promise<WorkspaceData | null> {
    const client = createTenantClient(this.tenantId);
    const workspace = await client.tenant.workspace.findUnique({
      where: { id },
    });
    return workspace as WorkspaceData | null;
  }

  // Get workspace by slug
  async findBySlug(slug: string): Promise<WorkspaceData | null> {
    const client = createTenantClient(this.tenantId);
    const workspace = await client.tenant.workspace.findUnique({
      where: { 
        tenantId_slug: {
          tenantId: this.tenantId,
          slug,
        },
      },
    });
    return workspace as WorkspaceData | null;
  }

  // Update workspace
  async update(id: string, data: UpdateWorkspaceData): Promise<WorkspaceData> {
    const client = createTenantClient(this.tenantId);
    
    // If slug is being updated, ensure it's unique
    if (data.slug) {
      await this.ensureSlugUnique(data.slug, id);
    }

    const workspace = await client.tenant.workspace.update({
      where: { id },
      data,
    });

    return workspace as WorkspaceData;
  }

  // Delete workspace (soft delete)
  async delete(id: string): Promise<void> {
    const client = createTenantClient(this.tenantId);
    await client.tenant.workspace.update({
      where: { id },
      data: { 
        status: "archived",
        updatedAt: new Date(),
      },
    });
  }

  // List workspaces with pagination
  async list(page: number = 1, limit: number = 10, status?: string) {
    const client = createTenantClient(this.tenantId);
    const skip = (page - 1) * limit;
    
    const where = status ? { status } : {};
    
    const [workspaces, total] = await Promise.all([
      client.tenant.workspace.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      client.tenant.workspace.count({ where }),
    ]);

    return {
      data: workspaces as WorkspaceData[],
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  // Get workspace statistics
  async getStats(workspaceId: string): Promise<WorkspaceStats> {
    // This would integrate with actual data sources
    return {
      memberCount: 0,
      projectCount: 0,
      storageUsed: 0,
      apiCallsThisMonth: 0,
      lastActivity: new Date(),
    };
  }

  // Check if slug is unique
  private async ensureSlugUnique(slug: string, excludeId?: string): Promise<void> {
    const client = createTenantClient(this.tenantId);
    const existing = await client.tenant.workspace.findUnique({
      where: { 
        tenantId_slug: {
          tenantId: this.tenantId,
          slug,
        },
      },
    });

    if (existing && existing.id !== excludeId) {
      throw new Error(`Workspace with slug "${slug}" already exists`);
    }
  }

  // Get default workspace settings
  private getDefaultSettings() {
    return {
      theme: "light" as const,
      notifications: {
        email: true,
        push: true,
        slack: false,
      },
      features: {
        analytics: true,
        api: true,
        integrations: false,
        customDomain: false,
      },
      limits: {
        members: 10,
        projects: 5,
        storage: 1024 * 1024 * 1024, // 1GB
      },
    };
  }
}

export const createWorkspaceService = (tenantId: string): WorkspaceService => {
  return new WorkspaceService(tenantId);
};
