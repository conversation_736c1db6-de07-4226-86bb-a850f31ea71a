import { FastifyInstance } from "fastify";
import { z } from "zod";
import { validate, validatePagination, validateId } from "../middleware";
import { requireAdmin, requireSystemAdmin } from "../middleware/auth";
import { ApiResponse } from "../types";

export const adminRoutes = async (fastify: FastifyInstance) => {
  // Get system stats
  fastify.get("/stats", {
    schema: {
      tags: ["Admin"],
      summary: "Get system statistics",
      description: "Get system-wide statistics (admin only)",
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                stats: {
                  type: "object",
                  properties: {
                    totalTenants: { type: "integer" },
                    totalUsers: { type: "integer" },
                    totalWorkspaces: { type: "integer" },
                    totalProjects: { type: "integer" },
                    totalFiles: { type: "integer" },
                    totalStorage: { type: "integer" },
                    activeSubscriptions: { type: "integer" },
                    revenue: { type: "number" },
                  },
                },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
      },
    },
    preHandler: [fastify.authenticate, requireAdmin],
    handler: async (request, reply): Promise<ApiResponse> => {
      // TODO: Implement actual system stats
      const mockStats = {
        totalTenants: 150,
        totalUsers: 2500,
        totalWorkspaces: 450,
        totalProjects: 1200,
        totalFiles: 15000,
        totalStorage: 107374182400, // 100GB
        activeSubscriptions: 120,
        revenue: 15000.00,
      };

      return {
        success: true,
        data: {
          stats: mockStats,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Get all tenants
  fastify.get("/tenants", {
    schema: {
      tags: ["Admin"],
      summary: "List all tenants",
      description: "Get a paginated list of all tenants (system admin only)",
      security: [{ bearerAuth: [] }],
      querystring: {
        type: "object",
        properties: {
          page: { type: "integer", minimum: 1, default: 1 },
          limit: { type: "integer", minimum: 1, maximum: 100, default: 20 },
          search: { type: "string" },
          status: { type: "string", enum: ["active", "suspended", "canceled"] },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                tenants: {
                  type: "array",
                  items: {
                    type: "object",
                    properties: {
                      id: { type: "string", format: "uuid" },
                      name: { type: "string" },
                      slug: { type: "string" },
                      status: { type: "string" },
                      userCount: { type: "integer" },
                      workspaceCount: { type: "integer" },
                      storageUsed: { type: "integer" },
                      subscriptionStatus: { type: "string" },
                      createdAt: { type: "string", format: "date-time" },
                    },
                  },
                },
                pagination: { $ref: "#/components/schemas/Pagination" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
      },
    },
    preHandler: [fastify.authenticate, requireSystemAdmin, validatePagination],
    handler: async (request, reply): Promise<ApiResponse> => {
      const query = request.query as any;
      
      // TODO: Implement actual tenant fetching
      const mockTenants = [
        {
          id: "tenant_1",
          name: "Acme Corp",
          slug: "acme-corp",
          status: "active",
          userCount: 25,
          workspaceCount: 3,
          storageUsed: 5368709120, // 5GB
          subscriptionStatus: "active",
          createdAt: new Date().toISOString(),
        },
      ];

      return {
        success: true,
        data: {
          tenants: mockTenants,
          pagination: {
            page: query.page || 1,
            limit: query.limit || 20,
            total: mockTenants.length,
            totalPages: 1,
          },
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Suspend tenant
  fastify.post("/tenants/:id/suspend", {
    schema: {
      tags: ["Admin"],
      summary: "Suspend tenant",
      description: "Suspend a tenant (system admin only)",
      security: [{ bearerAuth: [] }],
      params: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
        },
        required: ["id"],
      },
      body: {
        type: "object",
        properties: {
          reason: { type: "string" },
        },
        required: ["reason"],
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                message: { type: "string" },
                suspendedAt: { type: "string", format: "date-time" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [fastify.authenticate, requireSystemAdmin, validateId],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { id } = request.params as { id: string };
      const { reason } = request.body as { reason: string };
      
      // TODO: Implement actual tenant suspension
      
      return {
        success: true,
        data: {
          message: "Tenant suspended successfully",
          suspendedAt: new Date().toISOString(),
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Reactivate tenant
  fastify.post("/tenants/:id/reactivate", {
    schema: {
      tags: ["Admin"],
      summary: "Reactivate tenant",
      description: "Reactivate a suspended tenant (system admin only)",
      security: [{ bearerAuth: [] }],
      params: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
        },
        required: ["id"],
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                message: { type: "string" },
                reactivatedAt: { type: "string", format: "date-time" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [fastify.authenticate, requireSystemAdmin, validateId],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { id } = request.params as { id: string };
      
      // TODO: Implement actual tenant reactivation
      
      return {
        success: true,
        data: {
          message: "Tenant reactivated successfully",
          reactivatedAt: new Date().toISOString(),
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Get system health
  fastify.get("/health", {
    schema: {
      tags: ["Admin"],
      summary: "Get system health",
      description: "Get detailed system health information (admin only)",
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                health: {
                  type: "object",
                  properties: {
                    status: { type: "string", enum: ["healthy", "degraded", "unhealthy"] },
                    services: {
                      type: "object",
                      properties: {
                        database: {
                          type: "object",
                          properties: {
                            status: { type: "string" },
                            responseTime: { type: "number" },
                            connections: { type: "integer" },
                          },
                        },
                        redis: {
                          type: "object",
                          properties: {
                            status: { type: "string" },
                            responseTime: { type: "number" },
                            memory: { type: "integer" },
                          },
                        },
                        storage: {
                          type: "object",
                          properties: {
                            status: { type: "string" },
                            totalSpace: { type: "integer" },
                            usedSpace: { type: "integer" },
                            freeSpace: { type: "integer" },
                          },
                        },
                      },
                    },
                    metrics: {
                      type: "object",
                      properties: {
                        uptime: { type: "number" },
                        memoryUsage: { type: "number" },
                        cpuUsage: { type: "number" },
                        activeConnections: { type: "integer" },
                        requestsPerMinute: { type: "number" },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
      },
    },
    preHandler: [fastify.authenticate, requireAdmin],
    handler: async (request, reply): Promise<ApiResponse> => {
      // TODO: Implement actual health checks
      const mockHealth = {
        status: "healthy",
        services: {
          database: {
            status: "healthy",
            responseTime: 15.5,
            connections: 25,
          },
          redis: {
            status: "healthy",
            responseTime: 2.1,
            memory: 134217728, // 128MB
          },
          storage: {
            status: "healthy",
            totalSpace: 1073741824000, // 1TB
            usedSpace: 107374182400, // 100GB
            freeSpace: 966367641600, // 900GB
          },
        },
        metrics: {
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage().heapUsed,
          cpuUsage: 15.5,
          activeConnections: 150,
          requestsPerMinute: 450,
        },
      };

      return {
        success: true,
        data: {
          health: mockHealth,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Get audit logs
  fastify.get("/audit-logs", {
    schema: {
      tags: ["Admin"],
      summary: "Get audit logs",
      description: "Get system-wide audit logs (admin only)",
      security: [{ bearerAuth: [] }],
      querystring: {
        type: "object",
        properties: {
          page: { type: "integer", minimum: 1, default: 1 },
          limit: { type: "integer", minimum: 1, maximum: 100, default: 20 },
          userId: { type: "string", format: "uuid" },
          action: { type: "string" },
          resource: { type: "string" },
          startDate: { type: "string", format: "date-time" },
          endDate: { type: "string", format: "date-time" },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                logs: {
                  type: "array",
                  items: {
                    type: "object",
                    properties: {
                      id: { type: "string", format: "uuid" },
                      userId: { type: "string", format: "uuid" },
                      action: { type: "string" },
                      resource: { type: "string" },
                      resourceId: { type: "string" },
                      details: { type: "object" },
                      result: { type: "string", enum: ["success", "failure", "denied"] },
                      ipAddress: { type: "string" },
                      userAgent: { type: "string" },
                      createdAt: { type: "string", format: "date-time" },
                    },
                  },
                },
                pagination: { $ref: "#/components/schemas/Pagination" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
      },
    },
    preHandler: [fastify.authenticate, requireAdmin, validatePagination],
    handler: async (request, reply): Promise<ApiResponse> => {
      const query = request.query as any;
      
      // TODO: Implement actual audit log fetching
      const mockLogs = [
        {
          id: "log_1",
          userId: "user_123",
          action: "user_login",
          resource: "auth",
          resourceId: null,
          details: { method: "email" },
          result: "success",
          ipAddress: "*************",
          userAgent: "Mozilla/5.0...",
          createdAt: new Date().toISOString(),
        },
      ];

      return {
        success: true,
        data: {
          logs: mockLogs,
          pagination: {
            page: query.page || 1,
            limit: query.limit || 20,
            total: mockLogs.length,
            totalPages: 1,
          },
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });
};
