{"extends": "@nexus/tsconfig/nextjs.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "verbatimModuleSyntax": true, "noUncheckedSideEffectImports": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", ".src-ref", "src/data/mock-*.ts", "src/lib/services/*-service.ts", "src/components/ui/**/*", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"]}