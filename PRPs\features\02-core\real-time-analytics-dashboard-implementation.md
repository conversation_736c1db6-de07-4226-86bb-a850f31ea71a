# Real-Time Analytics Dashboard Implementation

## Overview
Implement a comprehensive real-time analytics dashboard for the NEXUS SaaS Starter that provides live insights into user behavior, business metrics, and system performance. The dashboard will feature interactive charts, real-time data streaming, customizable widgets, and automated reporting capabilities.

## Context Research Summary

### Supabase Realtime Patterns
- **WebSocket Integration**: Real-time data streaming using Supabase Realtime for live updates
- **Publication System**: Database table publications for real-time change notifications
- **Tenant Isolation**: Multi-tenant real-time streams with proper data isolation
- **Rate Limiting**: Built-in rate limiting and connection management
- **Error Handling**: Comprehensive error handling and reconnection strategies

### Chart.js Integration Patterns
- **Dynamic Charts**: Real-time chart updates with streaming data
- **Interactive Actions**: Chart manipulation through programmatic actions
- **Performance Optimization**: Efficient rendering with minimal resource usage
- **Multiple Chart Types**: Line, bar, pie, radar, scatter, and bubble charts
- **Animation Control**: Progress tracking and animation customization

### Dashboard Architecture Patterns
- **Component-Based Design**: Modular widget system for flexible layouts
- **Server-Side Rendering**: Next.js SSR for optimal performance
- **API Routes**: RESTful endpoints for data fetching and configuration
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Theme Integration**: MUI/Tailwind CSS theming system

## Implementation Plan

### Phase 1: Real-Time Data Infrastructure
1. **Supabase Realtime Setup**
   - Configure database tables for real-time analytics
   - Set up publication channels for different metric types
   - Implement tenant-specific data streams
   - Create connection pooling and rate limiting

2. **WebSocket Management**
   - Create robust WebSocket connection handling
   - Implement reconnection logic with exponential backoff
   - Add connection status monitoring
   - Handle browser tab visibility changes

### Phase 2: Chart Engine Integration
1. **Chart.js Integration**
   - Create dynamic chart components with real-time updates
   - Implement multiple chart types (line, bar, pie, doughnut)
   - Add interactive chart actions and controls
   - Optimize performance for high-frequency updates

2. **Data Transformation Pipeline**
   - Build data aggregation and filtering services
   - Create time-series data processing
   - Implement data normalization and validation
   - Add caching layer for improved performance

### Phase 3: Dashboard Framework
1. **Widget System**
   - Create modular widget architecture
   - Implement drag-and-drop dashboard builder
   - Add widget configuration and customization
   - Support for different widget types and sizes

2. **Layout Management**
   - Responsive grid system for dashboard layouts
   - Save and restore dashboard configurations
   - Template system for quick dashboard creation
   - Mobile-optimized responsive designs

### Phase 4: Advanced Analytics Features
1. **Real-Time Metrics**
   - Live user activity monitoring
   - Real-time revenue and subscription tracking
   - System performance metrics
   - Custom KPI dashboards

2. **Automated Insights**
   - AI-powered anomaly detection
   - Trend analysis and forecasting
   - Automated alert system
   - Performance recommendations

## Technical Implementation

### File Structure
```
src/
├── app/
│   ├── api/
│   │   └── v1/
│   │       ├── analytics/
│   │       │   ├── dashboard/
│   │       │   │   ├── route.ts                 # Dashboard configuration API
│   │       │   │   └── [dashboardId]/
│   │       │   │       ├── route.ts             # Specific dashboard API
│   │       │   │       └── widgets/
│   │       │   │           └── route.ts         # Widget management API
│   │       │   ├── metrics/
│   │       │   │   ├── route.ts                 # Metrics aggregation API
│   │       │   │   ├── users/
│   │       │   │   │   └── route.ts             # User metrics API
│   │       │   │   ├── revenue/
│   │       │   │   │   └── route.ts             # Revenue metrics API
│   │       │   │   └── system/
│   │       │   │       └── route.ts             # System metrics API
│   │       │   └── realtime/
│   │       │       ├── route.ts                 # Real-time connection endpoint
│   │       │       └── [channel]/
│   │       │           └── route.ts             # Channel-specific endpoints
│   │       └── dashboard/
│   │           ├── route.ts                     # Dashboard listing API
│   │           └── [id]/
│   │               └── route.ts                 # Dashboard CRUD operations
├── components/
│   ├── analytics/
│   │   ├── dashboard/
│   │   │   ├── AnalyticsDashboard.tsx           # Main dashboard component
│   │   │   ├── DashboardGrid.tsx                # Grid layout component
│   │   │   ├── DashboardSettings.tsx            # Dashboard configuration
│   │   │   └── DashboardTemplates.tsx           # Pre-built templates
│   │   ├── widgets/
│   │   │   ├── BaseWidget.tsx                   # Base widget component
│   │   │   ├── MetricWidget.tsx                 # KPI metric widget
│   │   │   ├── ChartWidget.tsx                  # Chart display widget
│   │   │   ├── TableWidget.tsx                  # Data table widget
│   │   │   ├── ProgressWidget.tsx               # Progress bar widget
│   │   │   └── AlertWidget.tsx                  # Alert/notification widget
│   │   ├── charts/
│   │   │   ├── BaseChart.tsx                    # Base chart component
│   │   │   ├── LineChart.tsx                    # Real-time line chart
│   │   │   ├── BarChart.tsx                     # Bar chart component
│   │   │   ├── PieChart.tsx                     # Pie chart component
│   │   │   ├── AreaChart.tsx                    # Area chart component
│   │   │   └── GaugeChart.tsx                   # Gauge chart component
│   │   ├── filters/
│   │   │   ├── DateRangeFilter.tsx              # Date range picker
│   │   │   ├── MetricFilter.tsx                 # Metric selection filter
│   │   │   └── TenantFilter.tsx                 # Tenant/workspace filter
│   │   └── controls/
│   │       ├── RefreshControl.tsx               # Manual refresh button
│   │       ├── ExportControl.tsx                # Data export controls
│   │       └── SettingsControl.tsx              # Dashboard settings
├── lib/
│   ├── analytics/
│   │   ├── services/
│   │   │   ├── analytics-service.ts             # Main analytics service
│   │   │   ├── real-time-service.ts             # Real-time data service
│   │   │   ├── metrics-service.ts               # Metrics computation service
│   │   │   └── export-service.ts                # Data export service
│   │   ├── realtime/
│   │   │   ├── realtime-client.ts               # Supabase realtime client
│   │   │   ├── connection-manager.ts            # WebSocket management
│   │   │   ├── channel-manager.ts               # Channel subscription management
│   │   │   └── event-handlers.ts                # Real-time event handlers
│   │   ├── aggregation/
│   │   │   ├── data-aggregator.ts               # Data aggregation logic
│   │   │   ├── time-series-processor.ts         # Time series processing
│   │   │   ├── metric-calculator.ts             # Metric calculations
│   │   │   └── trend-analyzer.ts                # Trend analysis
│   │   ├── widgets/
│   │   │   ├── widget-factory.ts                # Widget creation factory
│   │   │   ├── widget-config.ts                 # Widget configuration
│   │   │   ├── widget-registry.ts               # Widget type registry
│   │   │   └── widget-persistence.ts            # Widget state persistence
│   │   └── dashboard/
│   │       ├── dashboard-manager.ts             # Dashboard management
│   │       ├── layout-manager.ts                # Layout configuration
│   │       ├── theme-manager.ts                 # Dashboard theming
│   │       └── template-manager.ts              # Template system
│   ├── charts/
│   │   ├── chart-factory.ts                     # Chart creation factory
│   │   ├── chart-config.ts                      # Chart configuration
│   │   ├── chart-themes.ts                      # Chart theming
│   │   ├── chart-animations.ts                  # Chart animations
│   │   └── chart-utils.ts                       # Chart utilities
│   └── websocket/
│       ├── websocket-client.ts                  # WebSocket client wrapper
│       ├── connection-pool.ts                   # Connection pooling
│       ├── message-handler.ts                   # Message processing
│       └── reconnection-manager.ts              # Reconnection logic
```

### Core Analytics Service

```typescript
// src/lib/analytics/services/analytics-service.ts
import { z } from 'zod';
import { createId } from '@paralleldrive/cuid2';
import { realtimeService } from './real-time-service';
import { metricsService } from './metrics-service';
import { dashboardManager } from '../dashboard/dashboard-manager';
import { auditLogger } from '@/lib/audit/audit-logger';

export const AnalyticsConfigSchema = z.object({
  dashboardId: z.string(),
  widgets: z.array(z.object({
    id: z.string(),
    type: z.enum(['metric', 'chart', 'table', 'progress', 'alert']),
    position: z.object({
      x: z.number(),
      y: z.number(),
      width: z.number(),
      height: z.number(),
    }),
    config: z.record(z.any()),
  })),
  layout: z.object({
    columns: z.number().min(1).max(12).default(12),
    rowHeight: z.number().default(150),
    margin: z.array(z.number()).length(2).default([10, 10]),
  }),
  refreshInterval: z.number().min(1000).default(30000),
  realtime: z.boolean().default(true),
});

export const MetricQuerySchema = z.object({
  metricType: z.enum(['users', 'revenue', 'system', 'custom']),
  timeRange: z.object({
    from: z.date(),
    to: z.date(),
  }),
  granularity: z.enum(['minute', 'hour', 'day', 'week', 'month']).default('hour'),
  filters: z.record(z.any()).optional(),
  aggregation: z.enum(['sum', 'avg', 'min', 'max', 'count']).default('sum'),
});

export type AnalyticsConfig = z.infer<typeof AnalyticsConfigSchema>;
export type MetricQuery = z.infer<typeof MetricQuerySchema>;

export class AnalyticsService {
  private realtimeConnections = new Map<string, any>();
  private dashboardConfigs = new Map<string, AnalyticsConfig>();

  async createDashboard(
    userId: string,
    tenantId: string,
    config: Partial<AnalyticsConfig>
  ): Promise<AnalyticsConfig> {
    const dashboardId = createId();
    
    const fullConfig: AnalyticsConfig = {
      dashboardId,
      widgets: [],
      layout: {
        columns: 12,
        rowHeight: 150,
        margin: [10, 10],
      },
      refreshInterval: 30000,
      realtime: true,
      ...config,
    };

    // Save dashboard configuration
    await dashboardManager.saveDashboard(dashboardId, fullConfig, userId, tenantId);

    // Cache configuration
    this.dashboardConfigs.set(dashboardId, fullConfig);

    // Log dashboard creation
    await auditLogger.log({
      action: 'analytics_dashboard_created',
      userId,
      tenantId,
      metadata: {
        dashboardId,
        widgetCount: fullConfig.widgets.length,
        realtime: fullConfig.realtime,
      },
    });

    return fullConfig;
  }

  async getDashboard(dashboardId: string): Promise<AnalyticsConfig | null> {
    // Check cache first
    const cached = this.dashboardConfigs.get(dashboardId);
    if (cached) {
      return cached;
    }

    // Load from database
    const config = await dashboardManager.getDashboard(dashboardId);
    if (config) {
      this.dashboardConfigs.set(dashboardId, config);
    }

    return config;
  }

  async updateDashboard(
    dashboardId: string,
    updates: Partial<AnalyticsConfig>
  ): Promise<AnalyticsConfig> {
    const existing = await this.getDashboard(dashboardId);
    if (!existing) {
      throw new Error('Dashboard not found');
    }

    const updated = { ...existing, ...updates };
    
    // Save updates
    await dashboardManager.updateDashboard(dashboardId, updated);

    // Update cache
    this.dashboardConfigs.set(dashboardId, updated);

    // Notify real-time subscribers
    await this.notifyDashboardUpdate(dashboardId, updated);

    return updated;
  }

  async getMetrics(
    tenantId: string,
    query: MetricQuery
  ): Promise<any[]> {
    const { metricType, timeRange, granularity, filters, aggregation } = query;

    // Get base metrics data
    const rawData = await metricsService.getMetrics(
      tenantId,
      metricType,
      timeRange,
      granularity,
      filters
    );

    // Apply aggregation
    const aggregatedData = await metricsService.aggregateData(
      rawData,
      aggregation,
      granularity
    );

    // Transform for frontend consumption
    return this.transformMetricsData(aggregatedData, metricType);
  }

  async subscribeToRealtime(
    dashboardId: string,
    userId: string,
    tenantId: string
  ): Promise<void> {
    const config = await this.getDashboard(dashboardId);
    if (!config || !config.realtime) {
      return;
    }

    // Create real-time connection
    const connection = await realtimeService.createConnection({
      dashboardId,
      userId,
      tenantId,
      channels: this.getDashboardChannels(config),
      onMessage: (message) => this.handleRealtimeMessage(dashboardId, message),
      onError: (error) => this.handleRealtimeError(dashboardId, error),
    });

    this.realtimeConnections.set(dashboardId, connection);
  }

  async unsubscribeFromRealtime(dashboardId: string): Promise<void> {
    const connection = this.realtimeConnections.get(dashboardId);
    if (connection) {
      await realtimeService.closeConnection(connection);
      this.realtimeConnections.delete(dashboardId);
    }
  }

  private getDashboardChannels(config: AnalyticsConfig): string[] {
    const channels = new Set<string>();
    
    config.widgets.forEach(widget => {
      if (widget.type === 'metric' || widget.type === 'chart') {
        // Add channels based on widget configuration
        const metricType = widget.config.metricType;
        channels.add(`analytics:${metricType}`);
      }
    });

    return Array.from(channels);
  }

  private async handleRealtimeMessage(
    dashboardId: string,
    message: any
  ): Promise<void> {
    const config = await this.getDashboard(dashboardId);
    if (!config) return;

    // Process real-time message and update relevant widgets
    const updates = await this.processRealtimeUpdate(message, config);
    
    // Emit updates to dashboard subscribers
    await this.emitDashboardUpdate(dashboardId, updates);
  }

  private async handleRealtimeError(
    dashboardId: string,
    error: any
  ): Promise<void> {
    console.error(`Real-time error for dashboard ${dashboardId}:`, error);
    
    // Attempt reconnection
    setTimeout(() => {
      this.attemptReconnection(dashboardId);
    }, 5000);
  }

  private async attemptReconnection(dashboardId: string): Promise<void> {
    const connection = this.realtimeConnections.get(dashboardId);
    if (connection) {
      await realtimeService.reconnect(connection);
    }
  }

  private async processRealtimeUpdate(
    message: any,
    config: AnalyticsConfig
  ): Promise<any> {
    // Process the real-time message and generate widget updates
    const updates = {};
    
    config.widgets.forEach(widget => {
      if (this.shouldUpdateWidget(widget, message)) {
        updates[widget.id] = this.generateWidgetUpdate(widget, message);
      }
    });

    return updates;
  }

  private shouldUpdateWidget(widget: any, message: any): boolean {
    // Determine if widget should be updated based on message content
    return widget.config.metricType === message.metricType;
  }

  private generateWidgetUpdate(widget: any, message: any): any {
    // Generate specific update for widget based on message
    return {
      data: message.data,
      timestamp: new Date(),
      type: 'realtime_update',
    };
  }

  private async emitDashboardUpdate(
    dashboardId: string,
    updates: any
  ): Promise<void> {
    // Emit updates to all dashboard subscribers
    await realtimeService.broadcast(`dashboard:${dashboardId}`, {
      type: 'widget_updates',
      updates,
      timestamp: new Date(),
    });
  }

  private async notifyDashboardUpdate(
    dashboardId: string,
    config: AnalyticsConfig
  ): Promise<void> {
    // Notify subscribers of dashboard configuration changes
    await realtimeService.broadcast(`dashboard:${dashboardId}`, {
      type: 'config_update',
      config,
      timestamp: new Date(),
    });
  }

  private transformMetricsData(data: any[], metricType: string): any[] {
    // Transform raw metrics data for frontend consumption
    return data.map(item => ({
      ...item,
      metricType,
      formattedValue: this.formatMetricValue(item.value, metricType),
    }));
  }

  private formatMetricValue(value: any, metricType: string): string {
    switch (metricType) {
      case 'revenue':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
        }).format(value);
      case 'users':
        return new Intl.NumberFormat('en-US').format(value);
      case 'system':
        return `${value}%`;
      default:
        return String(value);
    }
  }
}

export const analyticsService = new AnalyticsService();
```

### Real-Time Service Implementation

```typescript
// src/lib/analytics/services/real-time-service.ts
import { createClient } from '@supabase/supabase-js';
import { z } from 'zod';

export const RealtimeConnectionSchema = z.object({
  dashboardId: z.string(),
  userId: z.string(),
  tenantId: z.string(),
  channels: z.array(z.string()),
  onMessage: z.function(),
  onError: z.function(),
});

export type RealtimeConnection = z.infer<typeof RealtimeConnectionSchema>;

export class RealtimeService {
  private supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  private connections = new Map<string, any>();

  async createConnection(config: RealtimeConnection): Promise<any> {
    const { dashboardId, userId, tenantId, channels, onMessage, onError } = config;

    // Create Supabase realtime channel
    const channel = this.supabase
      .channel(`dashboard:${dashboardId}`)
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'analytics_events',
          filter: `tenant_id=eq.${tenantId}` 
        },
        (payload) => {
          this.handleDatabaseChange(payload, onMessage);
        }
      )
      .on('broadcast', 
        { event: 'metric_update' },
        (payload) => {
          onMessage(payload);
        }
      )
      .on('presence', 
        { event: 'sync' },
        () => {
          console.log('Presence sync for dashboard:', dashboardId);
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('Real-time connection established for dashboard:', dashboardId);
        } else if (status === 'CHANNEL_ERROR') {
          onError(new Error('Channel subscription failed'));
        }
      });

    // Store connection
    this.connections.set(dashboardId, {
      channel,
      config,
      status: 'connecting',
    });

    return channel;
  }

  async closeConnection(dashboardId: string): Promise<void> {
    const connection = this.connections.get(dashboardId);
    if (connection) {
      await this.supabase.removeChannel(connection.channel);
      this.connections.delete(dashboardId);
    }
  }

  async broadcast(channel: string, message: any): Promise<void> {
    const channelInstance = this.supabase.channel(channel);
    await channelInstance.send({
      type: 'broadcast',
      event: 'metric_update',
      payload: message,
    });
  }

  async reconnect(dashboardId: string): Promise<void> {
    const connection = this.connections.get(dashboardId);
    if (connection) {
      // Close existing connection
      await this.closeConnection(dashboardId);
      
      // Create new connection
      await this.createConnection(connection.config);
    }
  }

  private handleDatabaseChange(payload: any, onMessage: Function): void {
    // Transform database change event to analytics message
    const message = {
      type: 'database_change',
      event: payload.eventType,
      table: payload.table,
      data: payload.new || payload.old,
      timestamp: new Date(),
    };

    onMessage(message);
  }

  getConnectionStatus(dashboardId: string): string {
    const connection = this.connections.get(dashboardId);
    return connection?.status || 'disconnected';
  }

  getAllConnections(): Map<string, any> {
    return this.connections;
  }
}

export const realtimeService = new RealtimeService();
```

### Chart Component Implementation

```typescript
// src/components/analytics/charts/LineChart.tsx
'use client';

import { useEffect, useRef, useState } from 'react';
import { Card, CardContent, CardHeader, Typography } from '@mui/material';
import { Chart, registerables } from 'chart.js';
import { useTheme } from '@mui/material/styles';
import { useAnalytics } from '@/hooks/useAnalytics';

Chart.register(...registerables);

interface LineChartProps {
  title: string;
  data: any[];
  metricType: string;
  realtime?: boolean;
  height?: number;
  refreshInterval?: number;
}

export const LineChart: React.FC<LineChartProps> = ({
  title,
  data: initialData,
  metricType,
  realtime = false,
  height = 400,
  refreshInterval = 30000,
}) => {
  const chartRef = useRef<HTMLCanvasElement>(null);
  const chartInstanceRef = useRef<Chart | null>(null);
  const theme = useTheme();
  const { subscribeToMetric, unsubscribeFromMetric } = useAnalytics();

  const [data, setData] = useState(initialData);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!chartRef.current) return;

    const ctx = chartRef.current.getContext('2d');
    if (!ctx) return;

    // Create chart instance
    chartInstanceRef.current = new Chart(ctx, {
      type: 'line',
      data: {
        labels: data.map(item => new Date(item.timestamp).toLocaleTimeString()),
        datasets: [{
          label: title,
          data: data.map(item => item.value),
          borderColor: theme.palette.primary.main,
          backgroundColor: theme.palette.primary.main + '20',
          fill: true,
          tension: 0.4,
          pointRadius: 2,
          pointHoverRadius: 4,
        }],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false,
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              label: (context) => {
                const value = context.parsed.y;
                return `${title}: ${formatValue(value, metricType)}`;
              },
            },
          },
        },
        scales: {
          x: {
            grid: {
              display: false,
            },
            ticks: {
              maxTicksLimit: 6,
            },
          },
          y: {
            beginAtZero: true,
            grid: {
              color: theme.palette.divider,
            },
            ticks: {
              callback: (value) => formatValue(value, metricType),
            },
          },
        },
        interaction: {
          mode: 'nearest',
          axis: 'x',
          intersect: false,
        },
        animation: {
          duration: 750,
          easing: 'easeInOutQuart',
        },
      },
    });

    return () => {
      chartInstanceRef.current?.destroy();
    };
  }, []);

  useEffect(() => {
    if (chartInstanceRef.current) {
      // Update chart data
      chartInstanceRef.current.data.labels = data.map(item => 
        new Date(item.timestamp).toLocaleTimeString()
      );
      chartInstanceRef.current.data.datasets[0].data = data.map(item => item.value);
      chartInstanceRef.current.update('none');
    }
  }, [data]);

  useEffect(() => {
    if (realtime) {
      // Subscribe to real-time updates
      const unsubscribe = subscribeToMetric(metricType, (newData) => {
        setData(prevData => {
          const updated = [...prevData, newData];
          // Keep only last 20 data points for performance
          return updated.slice(-20);
        });
      });

      return unsubscribe;
    }
  }, [realtime, metricType]);

  const formatValue = (value: any, type: string): string => {
    switch (type) {
      case 'revenue':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
        }).format(value);
      case 'users':
        return new Intl.NumberFormat('en-US').format(value);
      case 'system':
        return `${value}%`;
      default:
        return String(value);
    }
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader
        title={
          <Typography variant="h6" component="h2">
            {title}
          </Typography>
        }
        action={
          realtime && (
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <Typography variant="caption" color="text.secondary">
                Live
              </Typography>
            </div>
          )
        }
      />
      <CardContent>
        <div style={{ height: height, position: 'relative' }}>
          <canvas
            ref={chartRef}
            style={{
              width: '100%',
              height: '100%',
            }}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default LineChart;
```

### Dashboard Grid Component

```typescript
// src/components/analytics/dashboard/DashboardGrid.tsx
'use client';

import { useState, useCallback } from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import { Box, Paper, IconButton, Menu, MenuItem } from '@mui/material';
import { MoreVert, Edit, Delete, Settings } from '@mui/icons-material';
import { LineChart } from '../charts/LineChart';
import { MetricWidget } from '../widgets/MetricWidget';
import { TableWidget } from '../widgets/TableWidget';
import { ProgressWidget } from '../widgets/ProgressWidget';
import { AlertWidget } from '../widgets/AlertWidget';
import type { AnalyticsConfig } from '@/lib/analytics/services/analytics-service';

import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

const ResponsiveGridLayout = WidthProvider(Responsive);

interface DashboardGridProps {
  config: AnalyticsConfig;
  onConfigChange: (config: AnalyticsConfig) => void;
  editable?: boolean;
  data: Record<string, any>;
}

export const DashboardGrid: React.FC<DashboardGridProps> = ({
  config,
  onConfigChange,
  editable = false,
  data,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedWidget, setSelectedWidget] = useState<string | null>(null);

  const handleLayoutChange = useCallback((layout: any[], layouts: any) => {
    const updatedWidgets = config.widgets.map(widget => {
      const layoutItem = layout.find(item => item.i === widget.id);
      return layoutItem ? {
        ...widget,
        position: {
          x: layoutItem.x,
          y: layoutItem.y,
          width: layoutItem.w,
          height: layoutItem.h,
        },
      } : widget;
    });

    onConfigChange({
      ...config,
      widgets: updatedWidgets,
    });
  }, [config, onConfigChange]);

  const handleWidgetMenuOpen = (event: React.MouseEvent<HTMLElement>, widgetId: string) => {
    setAnchorEl(event.currentTarget);
    setSelectedWidget(widgetId);
  };

  const handleWidgetMenuClose = () => {
    setAnchorEl(null);
    setSelectedWidget(null);
  };

  const handleWidgetEdit = () => {
    if (selectedWidget) {
      // Open widget edit dialog
      console.log('Edit widget:', selectedWidget);
    }
    handleWidgetMenuClose();
  };

  const handleWidgetDelete = () => {
    if (selectedWidget) {
      const updatedWidgets = config.widgets.filter(w => w.id !== selectedWidget);
      onConfigChange({
        ...config,
        widgets: updatedWidgets,
      });
    }
    handleWidgetMenuClose();
  };

  const renderWidget = (widget: any) => {
    const widgetData = data[widget.id] || {};

    const widgetContent = (() => {
      switch (widget.type) {
        case 'metric':
          return (
            <MetricWidget
              title={widget.config.title}
              value={widgetData.value}
              change={widgetData.change}
              icon={widget.config.icon}
              color={widget.config.color}
            />
          );
        case 'chart':
          return (
            <LineChart
              title={widget.config.title}
              data={widgetData.data || []}
              metricType={widget.config.metricType}
              realtime={widget.config.realtime}
              height={widget.position.height * config.layout.rowHeight - 100}
            />
          );
        case 'table':
          return (
            <TableWidget
              title={widget.config.title}
              data={widgetData.data || []}
              columns={widget.config.columns}
            />
          );
        case 'progress':
          return (
            <ProgressWidget
              title={widget.config.title}
              value={widgetData.value}
              target={widget.config.target}
              color={widget.config.color}
            />
          );
        case 'alert':
          return (
            <AlertWidget
              title={widget.config.title}
              alerts={widgetData.alerts || []}
              severity={widget.config.severity}
            />
          );
        default:
          return <div>Unknown widget type</div>;
      }
    })();

    return (
      <Paper
        key={widget.id}
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {editable && (
          <Box
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              zIndex: 1000,
            }}
          >
            <IconButton
              size="small"
              onClick={(e) => handleWidgetMenuOpen(e, widget.id)}
            >
              <MoreVert />
            </IconButton>
          </Box>
        )}
        {widgetContent}
      </Paper>
    );
  };

  const layout = config.widgets.map(widget => ({
    i: widget.id,
    x: widget.position.x,
    y: widget.position.y,
    w: widget.position.width,
    h: widget.position.height,
    minW: 1,
    minH: 1,
  }));

  return (
    <Box sx={{ width: '100%', height: '100%' }}>
      <ResponsiveGridLayout
        layouts={{ lg: layout }}
        breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
        cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
        rowHeight={config.layout.rowHeight}
        margin={config.layout.margin}
        isDraggable={editable}
        isResizable={editable}
        onLayoutChange={handleLayoutChange}
        draggableHandle=".drag-handle"
      >
        {config.widgets.map(renderWidget)}
      </ResponsiveGridLayout>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleWidgetMenuClose}
      >
        <MenuItem onClick={handleWidgetEdit}>
          <Edit sx={{ mr: 2 }} />
          Edit Widget
        </MenuItem>
        <MenuItem onClick={handleWidgetDelete}>
          <Delete sx={{ mr: 2 }} />
          Delete Widget
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default DashboardGrid;
```

### API Route Implementation

```typescript
// src/app/api/v1/analytics/dashboard/route.ts
import { NextRequest } from 'next/server';
import { defineRoute } from 'next-openapi-route-handler';
import { z } from 'zod';
import { authMiddleware } from '@/lib/api/middleware/auth';
import { rateLimitMiddleware } from '@/lib/api/middleware/rate-limit';
import { analyticsService, AnalyticsConfigSchema } from '@/lib/analytics/services/analytics-service';
import { createResponse, createErrorResponse } from '@/lib/api/utils/responses';

export const POST = defineRoute({
  method: 'POST',
  path: '/api/v1/analytics/dashboard',
  tags: ['Analytics'],
  summary: 'Create analytics dashboard',
  description: 'Create a new analytics dashboard with custom configuration',
  requestSchema: AnalyticsConfigSchema.partial(),
  responseSchema: z.object({
    dashboardId: z.string(),
    config: AnalyticsConfigSchema,
    createdAt: z.date(),
  }),
  middleware: [authMiddleware, rateLimitMiddleware],
  handler: async (req: NextRequest) => {
    try {
      const user = (req as any).user;
      const body = await req.json();
      const config = AnalyticsConfigSchema.partial().parse(body);

      const dashboard = await analyticsService.createDashboard(
        user.id,
        user.tenantId,
        config
      );

      return createResponse({
        dashboardId: dashboard.dashboardId,
        config: dashboard,
        createdAt: new Date(),
      }, 201);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return createErrorResponse('Invalid dashboard configuration', 400, error.errors);
      }
      
      console.error('Error creating dashboard:', error);
      return createErrorResponse('Failed to create dashboard', 500);
    }
  },
});

export const GET = defineRoute({
  method: 'GET',
  path: '/api/v1/analytics/dashboard',
  tags: ['Analytics'],
  summary: 'List analytics dashboards',
  description: 'Get all analytics dashboards for the authenticated user',
  querySchema: z.object({
    page: z.coerce.number().min(1).default(1),
    limit: z.coerce.number().min(1).max(50).default(10),
    search: z.string().optional(),
  }),
  responseSchema: z.object({
    dashboards: z.array(z.object({
      dashboardId: z.string(),
      name: z.string(),
      description: z.string().optional(),
      widgetCount: z.number(),
      realtime: z.boolean(),
      createdAt: z.date(),
      updatedAt: z.date(),
    })),
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
    }),
  }),
  middleware: [authMiddleware, rateLimitMiddleware],
  handler: async (req: NextRequest) => {
    try {
      const user = (req as any).user;
      const searchParams = Object.fromEntries(req.nextUrl.searchParams);
      const query = z.object({
        page: z.coerce.number().min(1).default(1),
        limit: z.coerce.number().min(1).max(50).default(10),
        search: z.string().optional(),
      }).parse(searchParams);

      const { dashboards, total } = await analyticsService.listDashboards(
        user.id,
        user.tenantId,
        query
      );

      return createResponse({
        dashboards,
        pagination: {
          page: query.page,
          limit: query.limit,
          total,
          totalPages: Math.ceil(total / query.limit),
        },
      });
    } catch (error) {
      console.error('Error listing dashboards:', error);
      return createErrorResponse('Failed to list dashboards', 500);
    }
  },
});
```

### Metrics API Implementation

```typescript
// src/app/api/v1/analytics/metrics/route.ts
import { NextRequest } from 'next/server';
import { defineRoute } from 'next-openapi-route-handler';
import { z } from 'zod';
import { authMiddleware } from '@/lib/api/middleware/auth';
import { rateLimitMiddleware } from '@/lib/api/middleware/rate-limit';
import { analyticsService, MetricQuerySchema } from '@/lib/analytics/services/analytics-service';
import { createResponse, createErrorResponse } from '@/lib/api/utils/responses';

export const POST = defineRoute({
  method: 'POST',
  path: '/api/v1/analytics/metrics',
  tags: ['Analytics'],
  summary: 'Query analytics metrics',
  description: 'Get analytics metrics based on query parameters',
  requestSchema: MetricQuerySchema,
  responseSchema: z.object({
    metrics: z.array(z.object({
      timestamp: z.date(),
      value: z.number(),
      metricType: z.string(),
      formattedValue: z.string(),
      metadata: z.record(z.any()).optional(),
    })),
    aggregation: z.object({
      sum: z.number(),
      avg: z.number(),
      min: z.number(),
      max: z.number(),
      count: z.number(),
    }),
    timeRange: z.object({
      from: z.date(),
      to: z.date(),
    }),
  }),
  middleware: [authMiddleware, rateLimitMiddleware],
  handler: async (req: NextRequest) => {
    try {
      const user = (req as any).user;
      const body = await req.json();
      const query = MetricQuerySchema.parse(body);

      const metrics = await analyticsService.getMetrics(user.tenantId, query);

      // Calculate aggregations
      const values = metrics.map(m => m.value);
      const aggregation = {
        sum: values.reduce((a, b) => a + b, 0),
        avg: values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0,
        min: Math.min(...values),
        max: Math.max(...values),
        count: values.length,
      };

      return createResponse({
        metrics,
        aggregation,
        timeRange: query.timeRange,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return createErrorResponse('Invalid metrics query', 400, error.errors);
      }
      
      console.error('Error querying metrics:', error);
      return createErrorResponse('Failed to query metrics', 500);
    }
  },
});
```

### Custom Hook for Analytics

```typescript
// src/hooks/useAnalytics.ts
import { useEffect, useState, useCallback } from 'react';
import { analyticsService } from '@/lib/analytics/services/analytics-service';
import { realtimeService } from '@/lib/analytics/services/real-time-service';
import type { AnalyticsConfig, MetricQuery } from '@/lib/analytics/services/analytics-service';

export interface UseAnalyticsOptions {
  dashboardId?: string;
  realtime?: boolean;
  refreshInterval?: number;
}

export const useAnalytics = (options: UseAnalyticsOptions = {}) => {
  const [dashboard, setDashboard] = useState<AnalyticsConfig | null>(null);
  const [metrics, setMetrics] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');

  const { dashboardId, realtime = false, refreshInterval = 30000 } = options;

  // Load dashboard configuration
  const loadDashboard = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const config = await analyticsService.getDashboard(id);
      setDashboard(config);
      
      if (config && realtime) {
        await initializeRealtime(config);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load dashboard');
    } finally {
      setLoading(false);
    }
  }, [realtime]);

  // Initialize real-time connection
  const initializeRealtime = useCallback(async (config: AnalyticsConfig) => {
    try {
      setConnectionStatus('connecting');
      
      await analyticsService.subscribeToRealtime(
        config.dashboardId,
        'user-id', // This should come from auth context
        'tenant-id' // This should come from auth context
      );
      
      setConnectionStatus('connected');
    } catch (err) {
      setConnectionStatus('disconnected');
      setError('Failed to establish real-time connection');
    }
  }, []);

  // Query metrics
  const queryMetrics = useCallback(async (query: MetricQuery) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await analyticsService.getMetrics('tenant-id', query);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to query metrics');
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  // Subscribe to specific metric updates
  const subscribeToMetric = useCallback((metricType: string, callback: (data: any) => void) => {
    // Implementation would depend on real-time service
    // This is a placeholder for the subscription logic
    const unsubscribe = () => {
      // Cleanup subscription
    };
    
    return unsubscribe;
  }, []);

  // Unsubscribe from metric updates
  const unsubscribeFromMetric = useCallback((metricType: string) => {
    // Implementation for unsubscribing from specific metrics
  }, []);

  // Update dashboard configuration
  const updateDashboard = useCallback(async (updates: Partial<AnalyticsConfig>) => {
    if (!dashboard) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const updated = await analyticsService.updateDashboard(dashboard.dashboardId, updates);
      setDashboard(updated);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update dashboard');
    } finally {
      setLoading(false);
    }
  }, [dashboard]);

  // Load dashboard on mount
  useEffect(() => {
    if (dashboardId) {
      loadDashboard(dashboardId);
    }
  }, [dashboardId, loadDashboard]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (dashboardId) {
        analyticsService.unsubscribeFromRealtime(dashboardId);
      }
    };
  }, [dashboardId]);

  return {
    dashboard,
    metrics,
    loading,
    error,
    connectionStatus,
    loadDashboard,
    queryMetrics,
    subscribeToMetric,
    unsubscribeFromMetric,
    updateDashboard,
  };
};
```

## Testing Strategy

### Unit Tests
```typescript
// src/lib/analytics/services/__tests__/analytics-service.test.ts
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { analyticsService } from '../analytics-service';
import { realtimeService } from '../real-time-service';
import { metricsService } from '../metrics-service';

vi.mock('../real-time-service');
vi.mock('../metrics-service');

describe('AnalyticsService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('createDashboard', () => {
    it('should create dashboard with default configuration', async () => {
      const config = {
        widgets: [],
        realtime: true,
      };

      const result = await analyticsService.createDashboard(
        'user-123',
        'tenant-123',
        config
      );

      expect(result).toMatchObject({
        dashboardId: expect.any(String),
        widgets: [],
        realtime: true,
        layout: {
          columns: 12,
          rowHeight: 150,
          margin: [10, 10],
        },
        refreshInterval: 30000,
      });
    });

    it('should handle custom configuration', async () => {
      const config = {
        widgets: [{
          id: 'widget-1',
          type: 'metric' as const,
          position: { x: 0, y: 0, width: 6, height: 2 },
          config: { title: 'Test Metric' },
        }],
        layout: {
          columns: 6,
          rowHeight: 200,
          margin: [20, 20],
        },
        refreshInterval: 60000,
      };

      const result = await analyticsService.createDashboard(
        'user-123',
        'tenant-123',
        config
      );

      expect(result.widgets).toHaveLength(1);
      expect(result.layout.columns).toBe(6);
      expect(result.refreshInterval).toBe(60000);
    });
  });

  describe('getMetrics', () => {
    it('should query and transform metrics data', async () => {
      const mockMetrics = [
        { timestamp: new Date(), value: 100, metricType: 'users' },
        { timestamp: new Date(), value: 200, metricType: 'users' },
      ];

      vi.mocked(metricsService.getMetrics).mockResolvedValue(mockMetrics);
      vi.mocked(metricsService.aggregateData).mockResolvedValue(mockMetrics);

      const query = {
        metricType: 'users' as const,
        timeRange: {
          from: new Date('2023-01-01'),
          to: new Date('2023-01-31'),
        },
        granularity: 'day' as const,
        aggregation: 'sum' as const,
      };

      const result = await analyticsService.getMetrics('tenant-123', query);

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        timestamp: expect.any(Date),
        value: 100,
        metricType: 'users',
        formattedValue: expect.any(String),
      });
    });
  });

  describe('subscribeToRealtime', () => {
    it('should create real-time subscription for dashboard', async () => {
      const mockDashboard = {
        dashboardId: 'dash-123',
        widgets: [
          {
            id: 'widget-1',
            type: 'metric' as const,
            config: { metricType: 'users' },
            position: { x: 0, y: 0, width: 6, height: 2 },
          },
        ],
        layout: { columns: 12, rowHeight: 150, margin: [10, 10] },
        refreshInterval: 30000,
        realtime: true,
      };

      vi.spyOn(analyticsService, 'getDashboard').mockResolvedValue(mockDashboard);
      vi.mocked(realtimeService.createConnection).mockResolvedValue({});

      await analyticsService.subscribeToRealtime('dash-123', 'user-123', 'tenant-123');

      expect(realtimeService.createConnection).toHaveBeenCalledWith({
        dashboardId: 'dash-123',
        userId: 'user-123',
        tenantId: 'tenant-123',
        channels: ['analytics:users'],
        onMessage: expect.any(Function),
        onError: expect.any(Function),
      });
    });
  });
});
```

### Integration Tests
```typescript
// src/components/analytics/charts/__tests__/LineChart.integration.test.ts
import { render, screen, waitFor } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { LineChart } from '../LineChart';
import { createTheme } from '@mui/material/styles';

const theme = createTheme();

const mockData = [
  { timestamp: '2023-01-01T00:00:00Z', value: 100 },
  { timestamp: '2023-01-01T01:00:00Z', value: 150 },
  { timestamp: '2023-01-01T02:00:00Z', value: 120 },
];

describe('LineChart Integration', () => {
  it('should render chart with data', async () => {
    render(
      <ThemeProvider theme={theme}>
        <LineChart
          title="Test Chart"
          data={mockData}
          metricType="users"
          height={400}
        />
      </ThemeProvider>
    );

    expect(screen.getByText('Test Chart')).toBeInTheDocument();
    
    // Wait for chart to render
    await waitFor(() => {
      const canvas = screen.getByRole('img', { hidden: true });
      expect(canvas).toBeInTheDocument();
    });
  });

  it('should handle real-time updates', async () => {
    const { rerender } = render(
      <ThemeProvider theme={theme}>
        <LineChart
          title="Real-time Chart"
          data={mockData}
          metricType="users"
          realtime={true}
          height={400}
        />
      </ThemeProvider>
    );

    expect(screen.getByText('Live')).toBeInTheDocument();

    // Simulate real-time data update
    const updatedData = [
      ...mockData,
      { timestamp: '2023-01-01T03:00:00Z', value: 180 },
    ];

    rerender(
      <ThemeProvider theme={theme}>
        <LineChart
          title="Real-time Chart"
          data={updatedData}
          metricType="users"
          realtime={true}
          height={400}
        />
      </ThemeProvider>
    );

    await waitFor(() => {
      // Chart should update with new data
      expect(screen.getByText('Live')).toBeInTheDocument();
    });
  });
});
```

## Performance Considerations

### Real-Time Optimization
- **Connection Pooling**: Efficient WebSocket connection management
- **Data Throttling**: Rate limiting for high-frequency updates
- **Selective Updates**: Only update changed widgets
- **Background Processing**: Offload heavy calculations to web workers

### Chart Performance
- **Data Sampling**: Limit data points for large datasets
- **Animation Control**: Disable animations for real-time updates
- **Canvas Optimization**: Use Chart.js performance optimizations
- **Memory Management**: Proper cleanup of chart instances

### Caching Strategy
- **Redis Cache**: Dashboard configurations and computed metrics
- **Browser Cache**: Static assets and chart configurations
- **CDN**: Global distribution of chart libraries
- **Service Worker**: Offline capability for dashboards

## Security Considerations

### Data Access Control
- **Tenant Isolation**: Strict tenant-based data filtering
- **RBAC Integration**: Role-based access to dashboard features
- **API Security**: Rate limiting and authentication
- **Data Encryption**: Encrypted data transmission

### Real-Time Security
- **WebSocket Authentication**: JWT-based WebSocket authentication
- **Channel Authorization**: Per-channel access control
- **Message Validation**: Incoming message validation
- **Connection Limits**: Per-tenant connection limits

## Monitoring & Analytics

### Dashboard Metrics
- **Usage Analytics**: Track dashboard views and interactions
- **Performance Metrics**: Chart rendering times and data load speeds
- **Error Tracking**: Real-time error monitoring and alerting
- **User Behavior**: Dashboard customization patterns

### System Health
- **WebSocket Health**: Connection status monitoring
- **Data Pipeline**: Metrics processing performance
- **Cache Hit Rates**: Cache performance optimization
- **Resource Usage**: Memory and CPU utilization

## Success Metrics

### User Engagement
- **Dashboard Creation**: Number of custom dashboards created
- **Widget Usage**: Most popular widget types and configurations
- **Real-Time Adoption**: Percentage of users using real-time features
- **Session Duration**: Time spent on analytics dashboards

### Performance Metrics
- **Page Load Time**: < 2 seconds for dashboard rendering
- **Real-Time Latency**: < 500ms for data updates
- **Chart Render Time**: < 200ms for chart updates
- **System Uptime**: 99.9% availability for analytics services

This comprehensive Real-Time Analytics Dashboard implementation provides a powerful, scalable, and user-friendly analytics solution with real-time capabilities, interactive visualizations, and enterprise-grade performance for the NEXUS SaaS Starter.
