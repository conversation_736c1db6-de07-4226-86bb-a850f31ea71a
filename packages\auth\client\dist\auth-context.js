"use client";
import { jsx as _jsx } from "react/jsx-runtime";
import { createContext, useContext, useEffect, useState } from "react";
import { authClient } from "./auth-client";
const AuthContext = createContext(undefined);
export function AuthProvider({ children }) {
    const [state, setState] = useState({
        user: null,
        session: null,
        isLoading: true,
        isAuthenticated: false,
    });
    // Initialize auth state
    useEffect(() => {
        const initializeAuth = async () => {
            try {
                const session = await authClient.getSession();
                if (session?.data?.user) {
                    setState({
                        user: session.data.user,
                        session: session.data,
                        isLoading: false,
                        isAuthenticated: true,
                    });
                }
                else {
                    setState({
                        user: null,
                        session: null,
                        isLoading: false,
                        isAuthenticated: false,
                    });
                }
            }
            catch (error) {
                console.error("Failed to initialize auth:", error);
                setState({
                    user: null,
                    session: null,
                    isLoading: false,
                    isAuthenticated: false,
                });
            }
        };
        initializeAuth();
    }, []);
    const login = async (credentials) => {
        setState(prev => ({ ...prev, isLoading: true }));
        try {
            const result = await authClient.signIn.email({
                email: credentials.email,
                password: credentials.password,
                rememberMe: credentials.remember,
            });
            if (result.data?.user) {
                setState({
                    user: result.data.user,
                    session: result.data,
                    isLoading: false,
                    isAuthenticated: true,
                });
            }
        }
        catch (error) {
            setState(prev => ({ ...prev, isLoading: false }));
            throw error;
        }
    };
    const register = async (credentials) => {
        setState(prev => ({ ...prev, isLoading: true }));
        try {
            const result = await authClient.signUp.email({
                email: credentials.email,
                password: credentials.password,
                name: credentials.name,
            });
            if (result.data?.user) {
                setState({
                    user: result.data.user,
                    session: result.data,
                    isLoading: false,
                    isAuthenticated: true,
                });
            }
        }
        catch (error) {
            setState(prev => ({ ...prev, isLoading: false }));
            throw error;
        }
    };
    const logout = async () => {
        setState(prev => ({ ...prev, isLoading: true }));
        try {
            await authClient.signOut();
            setState({
                user: null,
                session: null,
                isLoading: false,
                isAuthenticated: false,
            });
        }
        catch (error) {
            console.error("Logout failed:", error);
            setState(prev => ({ ...prev, isLoading: false }));
        }
    };
    const refreshSession = async () => {
        try {
            const session = await authClient.getSession();
            if (session?.data?.user) {
                setState(prev => ({
                    ...prev,
                    user: session.data.user,
                    session: session.data,
                    isAuthenticated: true,
                }));
            }
        }
        catch (error) {
            console.error("Failed to refresh session:", error);
        }
    };
    const value = {
        ...state,
        login,
        register,
        logout,
        refreshSession,
    };
    return _jsx(AuthContext.Provider, { value: value, children: children });
}
export function useAuth() {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error("useAuth must be used within an AuthProvider");
    }
    return context;
}
