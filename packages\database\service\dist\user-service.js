import { createTenantClient } from "./client";
export class UserService {
    tenantId;
    constructor(tenantId) {
        this.tenantId = tenantId;
    }
    // Create a new user
    async create(data) {
        const client = createTenantClient(this.tenantId);
        return client.tenant.user.create({
            data: {
                ...data,
                role: "MEMBER",
                status: "ACTIVE",
            },
        });
    }
    // Find user by ID
    async findById(id) {
        const client = createTenantClient(this.tenantId);
        return client.tenant.user.findUnique({
            where: { id },
        });
    }
    // Find user by email
    async findByEmail(email) {
        const client = createTenantClient(this.tenantId);
        return client.tenant.user.findUnique({
            where: {
                tenantId_email: {
                    tenantId: this.tenantId,
                    email,
                },
            },
        });
    }
    // Update user
    async update(id, data) {
        const client = createTenantClient(this.tenantId);
        return client.tenant.user.update({
            where: { id },
            data,
        });
    }
    // Update user role
    async updateRole(id, role) {
        const client = createTenantClient(this.tenantId);
        return client.tenant.user.update({
            where: { id },
            data: { role },
        });
    }
    // Update user status
    async updateStatus(id, status) {
        const client = createTenantClient(this.tenantId);
        return client.tenant.user.update({
            where: { id },
            data: { status },
        });
    }
    // Delete user
    async delete(id) {
        const client = createTenantClient(this.tenantId);
        return client.tenant.user.delete({
            where: { id },
        });
    }
    // List users with pagination
    async list(page = 1, limit = 10) {
        const client = createTenantClient(this.tenantId);
        const skip = (page - 1) * limit;
        const [users, total] = await Promise.all([
            client.tenant.user.findMany({
                skip,
                take: limit,
                orderBy: { createdAt: "desc" },
            }),
            client.tenant.user.count(),
        ]);
        return {
            data: users,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    // Check if email is available
    async isEmailAvailable(email) {
        const user = await this.findByEmail(email);
        return !user;
    }
    // Get users by role
    async findByRole(role) {
        const client = createTenantClient(this.tenantId);
        return client.tenant.user.findMany({
            where: { role },
            orderBy: { createdAt: "desc" },
        });
    }
    // Get users by status
    async findByStatus(status) {
        const client = createTenantClient(this.tenantId);
        return client.tenant.user.findMany({
            where: { status },
            orderBy: { createdAt: "desc" },
        });
    }
}
export const createUserService = (tenantId) => {
    return new UserService(tenantId);
};
