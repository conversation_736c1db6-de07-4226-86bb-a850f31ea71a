{"name": "@nexus/user-profile", "version": "0.1.0", "description": "User profile management system for Nexus SaaS", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "jest", "type-check": "tsc --noEmit"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "@nexus/utils": "workspace:*", "@nexus/validation": "workspace:*", "@nexus/database-schema": "workspace:*", "@nexus/database-service": "workspace:*", "@nexus/auth-client": "workspace:*", "react": "^19.1.0", "react-hook-form": "^7.60.0", "zustand": "^5.0.6", "zod": "^4.0.5"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@types/jest": "^29.5.0", "@types/react": "^19.1.8", "jest": "^29.5.0", "typescript": "^5.8.0"}}