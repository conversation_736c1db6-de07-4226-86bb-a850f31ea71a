// API Client Types

export interface ApiConfig {
  baseURL: string;
  graphqlURL: string;
  timeout: number;
  retries: number;
  retryDelay: number;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

export interface ApiError {
  message: string;
  code?: string;
  status?: number;
  details?: any;
}

export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: "ASC" | "DESC";
}

export interface SearchParams {
  query?: string;
  filters?: Record<string, any>;
}

// User types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  tenantId: string;
  roles: string[];
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateUserInput {
  email: string;
  name: string;
  password?: string;
  roles?: string[];
  avatar?: string;
}

export interface UpdateUserInput {
  name?: string;
  avatar?: string;
  isActive?: boolean;
}

// Workspace types
export interface Workspace {
  id: string;
  name: string;
  slug: string;
  description?: string;
  tenantId: string;
  ownerId: string;
  settings: Record<string, any>;
  isActive: boolean;
  memberCount: number;
  teamCount: number;
  projectCount: number;
  storageUsed: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateWorkspaceInput {
  name: string;
  slug: string;
  description?: string;
  settings?: Record<string, any>;
}

export interface UpdateWorkspaceInput {
  name?: string;
  description?: string;
  settings?: Record<string, any>;
  isActive?: boolean;
}

// Team types
export interface Team {
  id: string;
  name: string;
  description?: string;
  workspaceId: string;
  leadId?: string;
  isActive: boolean;
  memberCount: number;
  projectCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTeamInput {
  name: string;
  description?: string;
  workspaceId: string;
  leadId?: string;
}

export interface UpdateTeamInput {
  name?: string;
  description?: string;
  leadId?: string;
  isActive?: boolean;
}

// Project types
export interface Project {
  id: string;
  name: string;
  description?: string;
  workspaceId: string;
  teamId?: string;
  ownerId: string;
  settings: Record<string, any>;
  status: "ACTIVE" | "ARCHIVED" | "COMPLETED" | "ON_HOLD";
  isActive: boolean;
  fileCount: number;
  storageUsed: number;
  lastActivity?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateProjectInput {
  name: string;
  description?: string;
  workspaceId: string;
  teamId?: string;
  settings?: Record<string, any>;
  status?: "ACTIVE" | "ARCHIVED" | "COMPLETED" | "ON_HOLD";
}

export interface UpdateProjectInput {
  name?: string;
  description?: string;
  settings?: Record<string, any>;
  status?: "ACTIVE" | "ARCHIVED" | "COMPLETED" | "ON_HOLD";
  isActive?: boolean;
}

// File types
export interface FileItem {
  id: string;
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  url: string;
  downloadUrl: string;
  thumbnailUrl?: string;
  workspaceId: string;
  projectId?: string;
  uploadedById: string;
  description?: string;
  tags: string[];
  isPublic: boolean;
  metadata: Record<string, any>;
  checksum: string;
  version: number;
  parentFileId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface FileUploadInput {
  file: File;
  workspaceId: string;
  projectId?: string;
  description?: string;
  tags?: string[];
  isPublic?: boolean;
}

export interface UpdateFileInput {
  filename?: string;
  description?: string;
  tags?: string[];
  isPublic?: boolean;
}

// Role types
export interface Role {
  id: string;
  name: string;
  slug: string;
  description?: string;
  level: "SYSTEM" | "ORGANIZATION" | "WORKSPACE" | "TEAM" | "USER";
  permissions: Permission[];
  isSystem: boolean;
  isActive: boolean;
  tenantId?: string;
  icon?: string;
  color?: string;
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface Permission {
  id: string;
  resource: string;
  action: string;
  scope: "OWN" | "TEAM" | "WORKSPACE" | "ORGANIZATION" | "SYSTEM";
  conditions?: Record<string, any>;
  attributes?: string[];
  description?: string;
  createdAt: string;
  updatedAt: string;
}

// Analytics types
export interface DashboardMetrics {
  totalUsers: number;
  activeUsers: number;
  totalProjects: number;
  totalFiles: number;
  storageUsed: number;
  apiCalls: number;
  trends: {
    userGrowth: number;
    projectGrowth: number;
    storageGrowth: number;
    apiGrowth: number;
  };
  period: {
    start: string;
    end: string;
  };
}

// Store types
export interface AuthStore {
  user: User | null;
  tokens: AuthTokens | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  updateUser: (updates: Partial<User>) => void;
}

export interface WorkspaceStore {
  workspaces: Workspace[];
  currentWorkspace: Workspace | null;
  isLoading: boolean;
  error: string | null;
  fetchWorkspaces: () => Promise<void>;
  createWorkspace: (input: CreateWorkspaceInput) => Promise<Workspace>;
  updateWorkspace: (id: string, input: UpdateWorkspaceInput) => Promise<Workspace>;
  deleteWorkspace: (id: string) => Promise<void>;
  setCurrentWorkspace: (workspace: Workspace | null) => void;
}

export interface TeamStore {
  teams: Team[];
  currentTeam: Team | null;
  isLoading: boolean;
  error: string | null;
  fetchTeams: (workspaceId?: string) => Promise<void>;
  createTeam: (input: CreateTeamInput) => Promise<Team>;
  updateTeam: (id: string, input: UpdateTeamInput) => Promise<Team>;
  deleteTeam: (id: string) => Promise<void>;
  setCurrentTeam: (team: Team | null) => void;
}

export interface ProjectStore {
  projects: Project[];
  currentProject: Project | null;
  isLoading: boolean;
  error: string | null;
  fetchProjects: (workspaceId?: string, teamId?: string) => Promise<void>;
  createProject: (input: CreateProjectInput) => Promise<Project>;
  updateProject: (id: string, input: UpdateProjectInput) => Promise<Project>;
  deleteProject: (id: string) => Promise<void>;
  setCurrentProject: (project: Project | null) => void;
}

export interface FileStore {
  files: FileItem[];
  isLoading: boolean;
  error: string | null;
  uploadProgress: Record<string, number>;
  fetchFiles: (workspaceId?: string, projectId?: string) => Promise<void>;
  uploadFile: (input: FileUploadInput) => Promise<FileItem>;
  updateFile: (id: string, input: UpdateFileInput) => Promise<FileItem>;
  deleteFile: (id: string) => Promise<void>;
  downloadFile: (id: string) => Promise<void>;
}
