#!/usr/bin/env node

import express from "express";
import { createServer } from "http";
import { Server as SocketIOServer } from "socket.io";
import { createAdapter } from "@socket.io/redis-adapter";
import { createClient } from "redis";
import cors from "cors";
import helmet from "helmet";
import compression from "compression";
import winston from "winston";

import { AuthService, AuthRateLimiter } from "./auth";
import { PresenceManager } from "./presence";
import { CollaborationManager } from "./collaboration";
import { ChatManager } from "./chat";
import { RoomManager } from "./rooms";
import { RealtimeConfig, SocketContext, RealtimeEvents } from "./types";

// Configuration
const config: RealtimeConfig = {
  port: parseInt(process.env.REALTIME_PORT || "3002", 10),
  cors: {
    origin: process.env.CORS_ORIGIN?.split(",") || ["http://localhost:3000"],
    credentials: true,
  },
  redis: {
    host: process.env.REDIS_HOST || "localhost",
    port: parseInt(process.env.REDIS_PORT || "6379", 10),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || "0", 10),
  },
  jwt: {
    secret: process.env.JWT_SECRET || "your-secret-key",
    expiresIn: "24h",
  },
  rateLimit: {
    connection: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 100,
    },
    message: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 60,
    },
    join: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 20,
    },
  },
  collaboration: {
    maxDocumentSize: 10 * 1024 * 1024, // 10MB
    maxParticipants: 50,
    operationTimeout: 5000,
    snapshotInterval: 60000,
  },
  presence: {
    updateInterval: 30000,
    offlineTimeout: 300000,
    cleanupInterval: 60000,
  },
};

// Logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || "info",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    }),
  ],
});

export class RealtimeServer {
  private app: express.Application;
  private server: any;
  private io: SocketIOServer;
  private authService: AuthService;
  private authRateLimiter: AuthRateLimiter;
  private presenceManager: PresenceManager;
  private collaborationManager: CollaborationManager;
  private chatManager: ChatManager;
  private roomManager: RoomManager;
  private connectedSockets: Map<string, SocketContext> = new Map();

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: config.cors,
      transports: ["websocket", "polling"],
      pingTimeout: 60000,
      pingInterval: 25000,
    });

    // Initialize services
    this.authService = new AuthService(config.jwt.secret);
    this.authRateLimiter = new AuthRateLimiter();
    this.presenceManager = new PresenceManager(
      config.presence.updateInterval,
      config.presence.offlineTimeout
    );
    this.collaborationManager = new CollaborationManager(config.collaboration);
    this.chatManager = new ChatManager();
    this.roomManager = new RoomManager();

    this.setupExpress();
    this.setupRedis();
    this.setupSocketIO();
    this.setupEventHandlers();
  }

  private setupExpress(): void {
    // Security middleware
    this.app.use(helmet());
    this.app.use(compression());
    this.app.use(cors(config.cors));
    this.app.use(express.json({ limit: "10mb" }));

    // Health check endpoint
    this.app.get("/health", (req, res) => {
      res.json({
        status: "healthy",
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        connections: this.connectedSockets.size,
        rooms: this.roomManager.getStats(),
        presence: this.presenceManager.getStats(),
        collaboration: this.collaborationManager.getStats(),
        chat: this.chatManager.getStats(),
      });
    });

    // Metrics endpoint
    this.app.get("/metrics", (req, res) => {
      res.json({
        connections: this.connectedSockets.size,
        rooms: this.roomManager.getStats(),
        presence: this.presenceManager.getStats(),
        collaboration: this.collaborationManager.getStats(),
        chat: this.chatManager.getStats(),
        memory: process.memoryUsage(),
        uptime: process.uptime(),
      });
    });
  }

  private async setupRedis(): Promise<void> {
    try {
      // Create Redis clients for Socket.IO adapter
      const pubClient = createClient({
        host: config.redis.host,
        port: config.redis.port,
        password: config.redis.password,
        db: config.redis.db,
      });

      const subClient = pubClient.duplicate();

      await Promise.all([pubClient.connect(), subClient.connect()]);

      // Use Redis adapter for Socket.IO
      this.io.adapter(createAdapter(pubClient, subClient));

      logger.info("Redis adapter configured");
    } catch (error) {
      logger.error("Failed to setup Redis:", error);
      // Continue without Redis adapter for development
    }
  }

  private setupSocketIO(): void {
    // Authentication middleware
    this.io.use(async (socket, next) => {
      try {
        const ip = socket.handshake.address;

        // Check rate limiting
        if (this.authRateLimiter.isRateLimited(ip)) {
          throw new Error("Rate limited");
        }

        // Authenticate user
        const user = await this.authService.authenticateSocket(socket);

        // Create socket context
        const context: SocketContext = {
          socket,
          user,
          tenantId: user.tenantId,
          permissions: [], // TODO: Load from user roles
          joinedRooms: new Set(),
          presence: {
            userId: user.id,
            user: {
              id: user.id,
              name: user.name,
              email: user.email,
              avatar: user.avatar,
            },
            status: "online",
            lastSeen: new Date(),
          },
        };

        // Store context
        this.connectedSockets.set(socket.id, context);

        next();
      } catch (error: any) {
        logger.error("Socket authentication failed:", error);
        this.authRateLimiter.recordFailedAttempt(socket.handshake.address);
        next(new Error("Authentication failed"));
      }
    });

    // Connection handler
    this.io.on("connection", (socket) => {
      this.handleConnection(socket);
    });
  }

  private handleConnection(socket: any): void {
    const context = this.connectedSockets.get(socket.id);
    if (!context) return;

    logger.info(`User ${context.user.id} connected`, {
      socketId: socket.id,
      userId: context.user.id,
      tenantId: context.tenantId,
    });

    // Add to presence
    this.presenceManager.addUser(socket.id, context);

    // Auto-join tenant room
    this.roomManager.getOrCreateRoom(
      `tenant:${context.tenantId}`,
      "tenant",
      `Tenant ${context.tenantId}`
    );
    this.roomManager.joinRoom(`tenant:${context.tenantId}`, context);

    // Setup event handlers
    this.setupSocketEventHandlers(socket, context);

    // Handle disconnection
    socket.on("disconnect", (reason) => {
      this.handleDisconnection(socket.id, reason);
    });
  }

  private setupSocketEventHandlers(socket: any, context: SocketContext): void {
    // Presence events
    socket.on("presence:update", (data: { status: string; location?: any }) => {
      this.presenceManager.updateUserStatus(context.user.id, data.status as any);
      if (data.location) {
        this.presenceManager.updateUserLocation(context.user.id, data.location);
      }
    });

    // Room events
    socket.on("room:join", async (data: { roomId: string; roomType?: string }) => {
      try {
        if (data.roomType) {
          this.roomManager.getOrCreateRoom(data.roomId, data.roomType as any, data.roomId);
        }
        await this.roomManager.joinRoom(data.roomId, context);
        socket.emit("room:joined", { roomId: data.roomId });
      } catch (error: any) {
        socket.emit("error", { message: error.message });
      }
    });

    socket.on("room:leave", async (data: { roomId: string }) => {
      try {
        await this.roomManager.leaveRoom(data.roomId, socket.id);
        socket.emit("room:left", { roomId: data.roomId });
      } catch (error: any) {
        socket.emit("error", { message: error.message });
      }
    });

    // Chat events
    socket.on("chat:message", async (data: any) => {
      try {
        const message = await this.chatManager.sendMessage(
          data.channelId,
          data.channelType,
          data.content,
          context,
          data.options
        );
        
        // Broadcast to room
        this.roomManager.broadcastToRoom(
          data.channelId,
          "chat:message",
          { message }
        );
      } catch (error: any) {
        socket.emit("error", { message: error.message });
      }
    });

    socket.on("chat:typing", (data: { channelId: string }) => {
      this.chatManager.setTyping(data.channelId, context);
      
      // Broadcast to room
      this.roomManager.broadcastToRoom(
        data.channelId,
        "chat:typing",
        {
          channelId: data.channelId,
          userId: context.user.id,
          user: context.user,
          isTyping: true,
        },
        socket.id
      );
    });

    // Collaboration events
    socket.on("document:join", async (data: { documentId: string }) => {
      try {
        const document = await this.collaborationManager.joinDocument(data.documentId, context);
        socket.emit("document:joined", { document });
        
        // Broadcast to document room
        this.roomManager.broadcastToRoom(
          `document:${data.documentId}`,
          "document:participant:join",
          {
            documentId: data.documentId,
            user: context.user,
          },
          socket.id
        );
      } catch (error: any) {
        socket.emit("error", { message: error.message });
      }
    });

    socket.on("document:operation", async (data: any) => {
      try {
        await this.collaborationManager.applyOperation(data.documentId, data.operation, context);
        
        // Broadcast to document room
        this.roomManager.broadcastToRoom(
          `document:${data.documentId}`,
          "document:operation",
          data,
          socket.id
        );
      } catch (error: any) {
        socket.emit("error", { message: error.message });
      }
    });

    socket.on("document:cursor", (data: any) => {
      this.collaborationManager.updateCursor(data.documentId, data.cursor, context);
      
      // Broadcast to document room
      this.roomManager.broadcastToRoom(
        `document:${data.documentId}`,
        "document:cursor",
        data,
        socket.id
      );
    });
  }

  private setupEventHandlers(): void {
    // Presence events
    this.presenceManager.on("presence:update", (presence) => {
      // Broadcast presence updates to relevant rooms
      this.roomManager.broadcastToRoomType("workspace", "presence:update", { presence });
    });

    // Room events
    this.roomManager.on("room:join", (data) => {
      logger.info(`User ${data.userId} joined room ${data.roomId}`);
    });

    this.roomManager.on("room:leave", (data) => {
      logger.info(`User ${data.userId} left room ${data.roomId}`);
    });

    // Chat events
    this.chatManager.on("chat:message", (data) => {
      logger.info(`Message sent in channel ${data.channelId}`);
    });

    // Collaboration events
    this.collaborationManager.on("document:join", (data) => {
      logger.info(`User ${data.userId} joined document ${data.documentId}`);
    });
  }

  private handleDisconnection(socketId: string, reason: string): void {
    const context = this.connectedSockets.get(socketId);
    if (!context) return;

    logger.info(`User ${context.user.id} disconnected`, {
      socketId,
      userId: context.user.id,
      reason,
    });

    // Remove from presence
    this.presenceManager.removeUser(socketId);

    // Leave all rooms
    this.roomManager.leaveAllRooms(socketId);

    // Remove from connected sockets
    this.connectedSockets.delete(socketId);
  }

  public async start(): Promise<void> {
    return new Promise((resolve) => {
      this.server.listen(config.port, () => {
        logger.info(`Realtime server running on port ${config.port}`);
        resolve();
      });
    });
  }

  public async stop(): Promise<void> {
    return new Promise((resolve) => {
      this.server.close(() => {
        this.presenceManager.destroy();
        this.collaborationManager.destroy();
        this.chatManager.destroy();
        this.roomManager.destroy();
        logger.info("Realtime server stopped");
        resolve();
      });
    });
  }
}

// Start server if this file is run directly
if (require.main === module) {
  const server = new RealtimeServer();
  
  server.start().catch((error) => {
    logger.error("Failed to start realtime server:", error);
    process.exit(1);
  });

  // Graceful shutdown
  const gracefulShutdown = async (signal: string) => {
    logger.info(`Received ${signal}, shutting down gracefully`);
    
    try {
      await server.stop();
      process.exit(0);
    } catch (error) {
      logger.error("Error during shutdown:", error);
      process.exit(1);
    }
  };

  process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
  process.on("SIGINT", () => gracefulShutdown("SIGINT"));
}

export default RealtimeServer;
