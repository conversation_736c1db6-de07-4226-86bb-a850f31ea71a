import { useState, useEffect, useCallback } from "react";

// Hook for localStorage with TypeScript support
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void, () => void] {
  // Get value from localStorage or use initial value
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === "undefined") {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Set value in localStorage and state
  const setValue = useCallback(
    (value: T | ((val: T) => T)) => {
      try {
        // Allow value to be a function so we have the same API as useState
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        
        setStoredValue(valueToStore);
        
        if (typeof window !== "undefined") {
          window.localStorage.setItem(key, JSON.stringify(valueToStore));
        }
      } catch (error) {
        console.error(`Error setting localStorage key "${key}":`, error);
      }
    },
    [key, storedValue]
  );

  // Remove value from localStorage
  const removeValue = useCallback(() => {
    try {
      setStoredValue(initialValue);
      
      if (typeof window !== "undefined") {
        window.localStorage.removeItem(key);
      }
    } catch (error) {
      console.error(`Error removing localStorage key "${key}":`, error);
    }
  }, [key, initialValue]);

  // Listen for changes to localStorage from other tabs/windows
  useEffect(() => {
    if (typeof window === "undefined") return;

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          setStoredValue(JSON.parse(e.newValue));
        } catch (error) {
          console.error(`Error parsing localStorage value for key "${key}":`, error);
        }
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, [key]);

  return [storedValue, setValue, removeValue];
}

// Hook for sessionStorage
export function useSessionStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void, () => void] {
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === "undefined") {
      return initialValue;
    }

    try {
      const item = window.sessionStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading sessionStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = useCallback(
    (value: T | ((val: T) => T)) => {
      try {
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        
        setStoredValue(valueToStore);
        
        if (typeof window !== "undefined") {
          window.sessionStorage.setItem(key, JSON.stringify(valueToStore));
        }
      } catch (error) {
        console.error(`Error setting sessionStorage key "${key}":`, error);
      }
    },
    [key, storedValue]
  );

  const removeValue = useCallback(() => {
    try {
      setStoredValue(initialValue);
      
      if (typeof window !== "undefined") {
        window.sessionStorage.removeItem(key);
      }
    } catch (error) {
      console.error(`Error removing sessionStorage key "${key}":`, error);
    }
  }, [key, initialValue]);

  return [storedValue, setValue, removeValue];
}

// Hook for preferences with localStorage
export function usePreferences<T extends Record<string, any>>(
  defaultPreferences: T
): [T, (key: keyof T, value: T[keyof T]) => void, (preferences: Partial<T>) => void, () => void] {
  const [preferences, setPreferences, clearPreferences] = useLocalStorage(
    "user_preferences",
    defaultPreferences
  );

  const updatePreference = useCallback(
    (key: keyof T, value: T[keyof T]) => {
      setPreferences(prev => ({
        ...prev,
        [key]: value,
      }));
    },
    [setPreferences]
  );

  const updatePreferences = useCallback(
    (updates: Partial<T>) => {
      setPreferences(prev => ({
        ...prev,
        ...updates,
      }));
    },
    [setPreferences]
  );

  const resetPreferences = useCallback(() => {
    setPreferences(defaultPreferences);
  }, [setPreferences, defaultPreferences]);

  return [preferences, updatePreference, updatePreferences, resetPreferences];
}

// Hook for recent items with localStorage
export function useRecentItems<T extends { id: string }>(
  key: string,
  maxItems: number = 10
): [T[], (item: T) => void, (id: string) => void, () => void] {
  const [recentItems, setRecentItems] = useLocalStorage<T[]>(key, []);

  const addRecentItem = useCallback(
    (item: T) => {
      setRecentItems(prev => {
        // Remove existing item if it exists
        const filtered = prev.filter(existing => existing.id !== item.id);
        
        // Add new item to the beginning
        const updated = [item, ...filtered];
        
        // Limit to maxItems
        return updated.slice(0, maxItems);
      });
    },
    [setRecentItems, maxItems]
  );

  const removeRecentItem = useCallback(
    (id: string) => {
      setRecentItems(prev => prev.filter(item => item.id !== id));
    },
    [setRecentItems]
  );

  const clearRecentItems = useCallback(() => {
    setRecentItems([]);
  }, [setRecentItems]);

  return [recentItems, addRecentItem, removeRecentItem, clearRecentItems];
}

// Hook for form data persistence
export function useFormPersistence<T extends Record<string, any>>(
  formId: string,
  initialData: T,
  options: {
    clearOnSubmit?: boolean;
    debounceMs?: number;
  } = {}
) {
  const { clearOnSubmit = true, debounceMs = 500 } = options;
  const storageKey = `form_${formId}`;
  
  const [formData, setFormData] = useLocalStorage(storageKey, initialData);
  const [isDirty, setIsDirty] = useState(false);

  // Debounced save to localStorage
  useEffect(() => {
    if (!isDirty) return;

    const timeoutId = setTimeout(() => {
      setIsDirty(false);
    }, debounceMs);

    return () => clearTimeout(timeoutId);
  }, [formData, isDirty, debounceMs]);

  const updateField = useCallback(
    (field: keyof T, value: T[keyof T]) => {
      setFormData(prev => ({
        ...prev,
        [field]: value,
      }));
      setIsDirty(true);
    },
    [setFormData]
  );

  const updateFields = useCallback(
    (updates: Partial<T>) => {
      setFormData(prev => ({
        ...prev,
        ...updates,
      }));
      setIsDirty(true);
    },
    [setFormData]
  );

  const resetForm = useCallback(() => {
    setFormData(initialData);
    setIsDirty(false);
  }, [setFormData, initialData]);

  const clearPersistedData = useCallback(() => {
    if (typeof window !== "undefined") {
      window.localStorage.removeItem(storageKey);
    }
    resetForm();
  }, [storageKey, resetForm]);

  const handleSubmit = useCallback(() => {
    if (clearOnSubmit) {
      clearPersistedData();
    }
  }, [clearOnSubmit, clearPersistedData]);

  return {
    formData,
    updateField,
    updateFields,
    resetForm,
    clearPersistedData,
    handleSubmit,
    isDirty,
    hasPersistedData: JSON.stringify(formData) !== JSON.stringify(initialData),
  };
}
