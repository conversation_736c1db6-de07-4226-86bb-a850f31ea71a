// User profile management types

export type ProfileVisibility = "public" | "private" | "team";
export type Theme = "light" | "dark" | "system";
export type Language = "en" | "es" | "fr" | "de" | "ja" | "zh";

export interface UserProfile {
  id: string;
  email: string;
  name?: string;
  image?: string;
  firstName?: string;
  lastName?: string;
  username?: string;
  bio?: string;
  location?: string;
  website?: string;
  company?: string;
  jobTitle?: string;
  language: Language;
  timezone: string;
  theme: Theme;
  emailNotifications: boolean;
  pushNotifications: boolean;
  marketingEmails: boolean;
  profileVisibility: ProfileVisibility;
  showEmail: boolean;
  showLocation: boolean;
  profileCompletion: number;
  lastProfileUpdate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProfileUpdateData {
  firstName?: string;
  lastName?: string;
  name?: string;
  bio?: string;
  location?: string;
  website?: string;
  company?: string;
  jobTitle?: string;
  username?: string;
}

export interface PreferencesUpdateData {
  language?: Language;
  timezone?: string;
  theme?: Theme;
  emailNotifications?: boolean;
  pushNotifications?: boolean;
  marketingEmails?: boolean;
}

export interface PrivacyUpdateData {
  profileVisibility?: ProfileVisibility;
  showEmail?: boolean;
  showLocation?: boolean;
}

export interface ProfileImage {
  id: string;
  userId: string;
  imageUrl: string;
  altText?: string;
  isPrimary: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface AvatarUploadData {
  file: File;
  altText?: string;
}

export interface ProfileStats {
  completionPercentage: number;
  missingFields: string[];
  lastUpdated: Date;
  profileViews: number;
}

export interface NotificationSettings {
  email: {
    security: boolean;
    updates: boolean;
    marketing: boolean;
    mentions: boolean;
    comments: boolean;
  };
  push: {
    security: boolean;
    updates: boolean;
    mentions: boolean;
    comments: boolean;
  };
  inApp: {
    security: boolean;
    updates: boolean;
    mentions: boolean;
    comments: boolean;
  };
}

export interface ProfileActivity {
  id: string;
  userId: string;
  action: string;
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
}

export interface ProfileExportData {
  profile: UserProfile;
  images: ProfileImage[];
  activities: ProfileActivity[];
  preferences: NotificationSettings;
  exportedAt: Date;
}
