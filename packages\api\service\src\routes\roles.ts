import { FastifyInstance } from "fastify";
import { z } from "zod";
import { validate, validatePagination, validateId } from "../middleware";
import { canRead, canCreate, canWrite, canDelete } from "../middleware/rbac";
import { ApiResponse } from "../types";

const createRoleSchema = z.object({
  name: z.string().min(2),
  slug: z.string().min(2),
  description: z.string().optional(),
  level: z.enum(["system", "organization", "workspace", "team", "user"]),
  permissions: z.array(z.object({
    resource: z.string(),
    action: z.string(),
    scope: z.string(),
  })).optional(),
});

const updateRoleSchema = z.object({
  name: z.string().min(2).optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
  permissions: z.array(z.object({
    resource: z.string(),
    action: z.string(),
    scope: z.string(),
  })).optional(),
});

export const roleRoutes = async (fastify: FastifyInstance) => {
  // Get roles
  fastify.get("/", {
    schema: {
      tags: ["Roles"],
      summary: "List roles",
      description: "Get a paginated list of roles",
      security: [{ bearerAuth: [] }],
      querystring: {
        type: "object",
        properties: {
          page: { type: "integer", minimum: 1, default: 1 },
          limit: { type: "integer", minimum: 1, maximum: 100, default: 20 },
          level: { type: "string", enum: ["system", "organization", "workspace", "team", "user"] },
          isActive: { type: "boolean" },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                roles: {
                  type: "array",
                  items: { $ref: "#/components/schemas/Role" },
                },
                pagination: { $ref: "#/components/schemas/Pagination" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
      },
    },
    preHandler: [fastify.authenticate, canRead("role"), validatePagination],
    handler: async (request, reply): Promise<ApiResponse> => {
      const query = request.query as any;
      
      // TODO: Implement actual role fetching
      const mockRoles = [
        {
          id: "role_1",
          name: "Administrator",
          slug: "admin",
          description: "Full administrative access",
          level: "organization",
          permissions: [],
          isSystem: true,
          isActive: true,
          tenantId: "tenant_123",
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: "role_2",
          name: "Member",
          slug: "member",
          description: "Standard member access",
          level: "workspace",
          permissions: [],
          isSystem: true,
          isActive: true,
          tenantId: "tenant_123",
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      return {
        success: true,
        data: {
          roles: mockRoles,
          pagination: {
            page: query.page || 1,
            limit: query.limit || 20,
            total: mockRoles.length,
            totalPages: 1,
          },
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Get role by ID
  fastify.get("/:id", {
    schema: {
      tags: ["Roles"],
      summary: "Get role by ID",
      description: "Get a specific role by its ID",
      security: [{ bearerAuth: [] }],
      params: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
        },
        required: ["id"],
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                role: { $ref: "#/components/schemas/Role" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [fastify.authenticate, canRead("role"), validateId],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { id } = request.params as { id: string };
      
      // TODO: Implement actual role fetching
      const mockRole = {
        id,
        name: "Administrator",
        slug: "admin",
        description: "Full administrative access",
        level: "organization",
        permissions: [],
        isSystem: true,
        isActive: true,
        tenantId: "tenant_123",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return {
        success: true,
        data: {
          role: mockRole,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Create role
  fastify.post("/", {
    schema: {
      tags: ["Roles"],
      summary: "Create role",
      description: "Create a new role",
      security: [{ bearerAuth: [] }],
      body: {
        type: "object",
        properties: {
          name: { type: "string", minLength: 2 },
          slug: { type: "string", minLength: 2 },
          description: { type: "string" },
          level: { type: "string", enum: ["system", "organization", "workspace", "team", "user"] },
          permissions: {
            type: "array",
            items: {
              type: "object",
              properties: {
                resource: { type: "string" },
                action: { type: "string" },
                scope: { type: "string" },
              },
              required: ["resource", "action", "scope"],
            },
          },
        },
        required: ["name", "slug", "level"],
      },
      response: {
        201: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                role: { $ref: "#/components/schemas/Role" },
              },
            },
          },
        },
        400: { $ref: "#/components/responses/BadRequest" },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
      },
    },
    preHandler: [fastify.authenticate, canCreate("role"), validate({ body: createRoleSchema })],
    handler: async (request, reply): Promise<ApiResponse> => {
      const roleData = request.body as z.infer<typeof createRoleSchema>;
      
      // TODO: Implement actual role creation
      const newRole = {
        id: `role_${Date.now()}`,
        name: roleData.name,
        slug: roleData.slug,
        description: roleData.description,
        level: roleData.level,
        permissions: roleData.permissions?.map(p => ({
          id: `perm_${Date.now()}`,
          resource: p.resource,
          action: p.action,
          scope: p.scope,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })) || [],
        isSystem: false,
        isActive: true,
        tenantId: (request as any).user.tenantId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      reply.status(201);
      return {
        success: true,
        data: {
          role: newRole,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Update role
  fastify.patch("/:id", {
    schema: {
      tags: ["Roles"],
      summary: "Update role",
      description: "Update an existing role",
      security: [{ bearerAuth: [] }],
      params: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
        },
        required: ["id"],
      },
      body: {
        type: "object",
        properties: {
          name: { type: "string", minLength: 2 },
          description: { type: "string" },
          isActive: { type: "boolean" },
          permissions: {
            type: "array",
            items: {
              type: "object",
              properties: {
                resource: { type: "string" },
                action: { type: "string" },
                scope: { type: "string" },
              },
              required: ["resource", "action", "scope"],
            },
          },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                role: { $ref: "#/components/schemas/Role" },
              },
            },
          },
        },
        400: { $ref: "#/components/responses/BadRequest" },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [
      fastify.authenticate,
      canWrite("role"),
      validateId,
      validate({ body: updateRoleSchema }),
    ],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { id } = request.params as { id: string };
      const updates = request.body as z.infer<typeof updateRoleSchema>;
      
      // TODO: Implement actual role update
      const updatedRole = {
        id,
        name: updates.name || "Role Name",
        slug: "role_slug",
        description: updates.description,
        level: "workspace",
        permissions: updates.permissions?.map(p => ({
          id: `perm_${Date.now()}`,
          resource: p.resource,
          action: p.action,
          scope: p.scope,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })) || [],
        isSystem: false,
        isActive: updates.isActive ?? true,
        tenantId: "tenant_123",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return {
        success: true,
        data: {
          role: updatedRole,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Delete role
  fastify.delete("/:id", {
    schema: {
      tags: ["Roles"],
      summary: "Delete role",
      description: "Delete a role",
      security: [{ bearerAuth: [] }],
      params: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
        },
        required: ["id"],
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                message: { type: "string" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [fastify.authenticate, canDelete("role"), validateId],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { id } = request.params as { id: string };
      
      // TODO: Implement actual role deletion
      
      return {
        success: true,
        data: {
          message: "Role deleted successfully",
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });
};
