import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { getServerSession } from "./auth-adapter";

// Server-side auth utilities for App Router

export async function getCurrentUser() {
  const headersList = headers();
  const request = {
    headers: headersList,
  } as any;
  
  const session = await getServerSession(request);
  return session?.user || null;
}

export async function requireAuth() {
  const user = await getCurrentUser();
  
  if (!user) {
    redirect("/auth/login");
  }
  
  return user;
}

export async function requireRole(role: string) {
  const user = await requireAuth();

  if ((user as any).role !== role) {
    redirect("/unauthorized");
  }

  return user;
}

export async function requireAdmin() {
  const user = await requireAuth();

  if ((user as any).role !== "OWNER" && (user as any).role !== "ADMIN") {
    redirect("/unauthorized");
  }

  return user;
}

export async function getTenantId(): Promise<string | null> {
  const user = await getCurrentUser();
  return (user as any)?.tenantId || null;
}

export async function requireTenant(): Promise<string> {
  const tenantId = await getTenantId();
  
  if (!tenantId) {
    redirect("/auth/login");
  }
  
  return tenantId;
}

// Permission checking utilities
export async function hasPermission(permission: string): Promise<boolean> {
  const user = await getCurrentUser();
  
  if (!user) return false;
  
  // Basic role-based permissions
  switch (user.role) {
    case "OWNER":
      return true;
    case "ADMIN":
      return ["read", "write", "delete"].includes(permission);
    case "MEMBER":
      return ["read", "write"].includes(permission);
    case "VIEWER":
      return permission === "read";
    default:
      return false;
  }
}

export async function requirePermission(permission: string) {
  const hasAccess = await hasPermission(permission);
  
  if (!hasAccess) {
    redirect("/unauthorized");
  }
}
