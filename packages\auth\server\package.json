{"name": "@nexus/auth-server", "version": "0.1.0", "description": "Authentication server for Nexus SaaS", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "@nexus/validation": "workspace:*", "@nexus/database-schema": "workspace:*", "@nexus/database-service": "workspace:*", "better-auth": "^1.2.12", "next": "^15.4.2"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "typescript": "^5.8.0"}}