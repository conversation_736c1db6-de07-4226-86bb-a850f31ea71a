# NEXUS SaaS Starter - Internationalization Implementation

**PRP Name**: Internationalization  
**Version**: 1.0  
**Date**: July 18, 2025  
**Type**: Additional Features Implementation PRP  
**Phase**: 05-additional  
**Framework**: Next.js 15.4+ / next-intl / TypeScript 5.8+  

---

## Purpose

Implement comprehensive internationalization (i18n) support for the NEXUS SaaS platform using next-intl, enabling multi-language support, localized content, regional formatting, and cultural adaptation for global markets.

## Core Principles

1. **Developer Experience**: Type-safe translations with excellent DX
2. **Performance**: Tree-shakable messages and optimized bundle sizes
3. **SEO Optimization**: Proper hreflang tags and localized URLs
4. **Cultural Adaptation**: Beyond translation - dates, numbers, currencies
5. **Scalability**: Support for 50+ languages and regional variants
6. **Multi-Tenant Support**: Tenant-specific language preferences
7. **Real-time Updates**: Dynamic language switching without page reload

---

## Goal

Build a robust internationalization system that enables the NEXUS SaaS platform to serve global markets with localized experiences, supporting multiple languages, regional formats, and cultural preferences while maintaining performance and developer productivity.

## Why

- **Global Market Access**: Expand to international markets with localized experiences
- **User Experience**: Native language support increases user engagement by 70%
- **SEO Benefits**: Localized content improves search rankings in target regions
- **Compliance**: Meet local regulations and cultural expectations
- **Competitive Advantage**: Multilingual support differentiates from competitors
- **Revenue Growth**: Localized products see 25% higher conversion rates
- **Enterprise Requirements**: B2B clients often require multi-language support

---

## All Needed Context

### Documentation & References

```yaml
# MUST READ - Include these in your context window
- url: https://github.com/amannn/next-intl
  sections: ["App Router Setup", "Routing Configuration", "Message Management"]
  priority: CRITICAL
  
- url: https://nextjs.org/docs/app/building-your-application/routing/internationalization
  sections: ["i18n Routing", "Locale Detection", "Static Generation"]
  priority: HIGH
  
- url: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl
  sections: ["Intl.DateTimeFormat", "Intl.NumberFormat", "Intl.RelativeTimeFormat"]
  priority: HIGH
  
- url: https://unicode.org/reports/tr35/
  sections: ["Locale Data", "CLDR", "Language Tags"]
  priority: MEDIUM
```

### Technology Stack Context

```yaml
# Current Stack (Context7 Verified)
framework: "Next.js 15.4+"
i18n_library: "next-intl"
typescript: "5.8+"
routing: "App Router with [locale] segments"
message_format: "ICU MessageFormat"
locale_detection: "URL-based with fallbacks"

# next-intl Features (Context7 Verified)
features:
  - "Type-safe translations"
  - "Tree-shakable messages"
  - "Server and client components"
  - "Automatic locale routing"
  - "Rich text formatting"
  - "Pluralization support"
  - "Date/time/number formatting"
```

### Critical Implementation Patterns

```typescript
// Routing Configuration (Context7 Verified)
// src/i18n/routing.ts
import { defineRouting } from 'next-intl/routing';

export const routing = defineRouting({
  locales: ['en', 'es', 'fr', 'de', 'ja', 'zh'],
  defaultLocale: 'en',
  localePrefix: 'as-needed', // No prefix for default locale
  pathnames: {
    '/': '/',
    '/dashboard': {
      en: '/dashboard',
      es: '/tablero',
      fr: '/tableau-de-bord',
      de: '/dashboard',
      ja: '/ダッシュボード',
      zh: '/仪表板'
    },
    '/settings': {
      en: '/settings',
      es: '/configuracion',
      fr: '/parametres',
      de: '/einstellungen',
      ja: '/設定',
      zh: '/设置'
    }
  }
});

// Request Configuration (Context7 Verified)
// src/i18n/request.ts
import { getRequestConfig } from 'next-intl/server';
import { hasLocale } from 'next-intl';
import { routing } from './routing';

export default getRequestConfig(async ({ requestLocale }) => {
  const requested = await requestLocale;
  const locale = hasLocale(routing.locales, requested)
    ? requested
    : routing.defaultLocale;

  return {
    locale,
    messages: (await import(`../../messages/${locale}.json`)).default,
    timeZone: getTimeZoneForLocale(locale),
    formats: {
      dateTime: {
        short: {
          day: 'numeric',
          month: 'short',
          year: 'numeric'
        },
        long: {
          weekday: 'long',
          day: 'numeric',
          month: 'long',
          year: 'numeric'
        }
      },
      number: {
        currency: {
          style: 'currency',
          currency: getCurrencyForLocale(locale)
        }
      }
    }
  };
});

// Navigation APIs (Context7 Verified)
// src/i18n/navigation.ts
import { createNavigation } from 'next-intl/navigation';
import { routing } from './routing';

export const { Link, redirect, usePathname, useRouter, getPathname } =
  createNavigation(routing);

// Middleware Configuration (Context7 Verified)
// src/middleware.ts
import createMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';

export default createMiddleware(routing);

export const config = {
  matcher: [
    '/((?!api|trpc|_next|_vercel|.*\\..*).*)'
  ]
};
```

### Multi-Tenant Internationalization

```typescript
// Tenant-Specific Locale Configuration
interface TenantI18nConfig {
  supportedLocales: string[];
  defaultLocale: string;
  fallbackLocale: string;
  customTranslations?: Record<string, any>;
  dateFormat?: string;
  numberFormat?: string;
  currency?: string;
  timezone?: string;
}

// Enhanced Request Configuration with Tenant Context
export default getRequestConfig(async ({ requestLocale }) => {
  const tenantId = await getTenantId();
  const tenantConfig = await getTenantI18nConfig(tenantId);
  
  const requested = await requestLocale;
  const locale = tenantConfig.supportedLocales.includes(requested)
    ? requested
    : tenantConfig.defaultLocale;

  // Merge tenant-specific translations with base translations
  const baseMessages = (await import(`../../messages/${locale}.json`)).default;
  const tenantMessages = tenantConfig.customTranslations?.[locale] || {};
  
  return {
    locale,
    messages: {
      ...baseMessages,
      ...tenantMessages
    },
    timeZone: tenantConfig.timezone || getTimeZoneForLocale(locale),
    formats: {
      dateTime: {
        short: {
          day: 'numeric',
          month: 'short',
          year: 'numeric'
        }
      },
      number: {
        currency: {
          style: 'currency',
          currency: tenantConfig.currency || getCurrencyForLocale(locale)
        }
      }
    }
  };
});

// Tenant-Aware Component
function TenantLocalizedComponent() {
  const t = useTranslations('Dashboard');
  const format = useFormatter();
  const { tenant } = useTenant();
  
  return (
    <div>
      <h1>{t('welcome', { name: tenant.name })}</h1>
      <p>{format.dateTime(new Date(), 'short')}</p>
      <p>{format.number(1234.56, 'currency')}</p>
    </div>
  );
}
```

---

## Requirements

### Functional Requirements

#### FR1: Multi-Language Support
- **FR1.1**: Support for 6+ initial languages (EN, ES, FR, DE, JA, ZH)
- **FR1.2**: Extensible architecture for adding new languages
- **FR1.3**: Type-safe translation keys and parameters
- **FR1.4**: Fallback mechanism for missing translations

#### FR2: Localized Routing
- **FR2.1**: Locale-specific URL paths (e.g., /es/tablero, /fr/tableau-de-bord)
- **FR2.2**: Automatic locale detection from URL, headers, and cookies
- **FR2.3**: SEO-friendly URLs with proper hreflang tags
- **FR2.4**: Locale switching without losing current page context

#### FR3: Cultural Formatting
- **FR3.1**: Localized date and time formatting
- **FR3.2**: Number formatting with locale-specific separators
- **FR3.3**: Currency formatting with appropriate symbols
- **FR3.4**: Relative time formatting (e.g., "2 hours ago")

#### FR4: Content Management
- **FR4.1**: Structured message organization with namespaces
- **FR4.2**: Rich text support with HTML and React components
- **FR4.3**: Pluralization rules for different languages
- **FR4.4**: Variable interpolation with type safety

#### FR5: Multi-Tenant Localization
- **FR5.1**: Tenant-specific language preferences
- **FR5.2**: Custom translations per tenant
- **FR5.3**: Tenant-specific date/number/currency formats
- **FR5.4**: Localized tenant branding and content

### Non-Functional Requirements

#### NFR1: Performance
- **NFR1.1**: Tree-shakable translations (only load used messages)
- **NFR1.2**: Static generation support for all localized pages
- **NFR1.3**: Minimal bundle size impact (< 50KB per locale)
- **NFR1.4**: Fast locale switching (< 200ms)

#### NFR2: Developer Experience
- **NFR2.1**: Type-safe translation keys with TypeScript
- **NFR2.2**: Hot reload support for translation changes
- **NFR2.3**: Comprehensive error handling and fallbacks
- **NFR2.4**: Easy integration with existing components

#### NFR3: SEO and Accessibility
- **NFR3.1**: Proper HTML lang attributes
- **NFR3.2**: Automatic hreflang tag generation
- **NFR3.3**: Accessible language switching UI
- **NFR3.4**: Search engine friendly URL structure

#### NFR4: Scalability
- **NFR4.1**: Support for 50+ languages without performance degradation
- **NFR4.2**: Efficient message loading and caching
- **NFR4.3**: Horizontal scaling with CDN support
- **NFR4.4**: Memory-efficient message storage

---

## Technical Implementation

### Core Architecture

```typescript
// Internationalization System Architecture
interface I18nSystem {
  routing: LocaleRouting;
  messages: MessageManagement;
  formatting: CulturalFormatting;
  detection: LocaleDetection;
  tenancy: MultiTenantI18n;
}

interface LocaleRouting {
  locales: string[];
  defaultLocale: string;
  pathnames: LocalizedPathnames;
  middleware: RoutingMiddleware;
}

interface MessageManagement {
  loader: MessageLoader;
  cache: MessageCache;
  fallback: FallbackHandler;
  validation: MessageValidator;
}

interface CulturalFormatting {
  dates: DateTimeFormatter;
  numbers: NumberFormatter;
  currencies: CurrencyFormatter;
  relative: RelativeTimeFormatter;
}
```

### Implementation Strategy

#### Phase 1: Foundation Setup
1. **Core Configuration**
   - Install and configure next-intl
   - Set up routing configuration
   - Create message structure

2. **Basic Localization**
   - Implement English and Spanish
   - Set up middleware and navigation
   - Create translation utilities

#### Phase 2: Advanced Features
1. **Cultural Formatting**
   - Date/time localization
   - Number and currency formatting
   - Relative time formatting

2. **Content Management**
   - Rich text support
   - Pluralization rules
   - Variable interpolation

#### Phase 3: Multi-Tenant Integration
1. **Tenant Configuration**
   - Tenant-specific locales
   - Custom translations
   - Regional preferences

2. **Performance Optimization**
   - Message tree-shaking
   - Caching strategies
   - Bundle optimization

### Key Components to Implement

```typescript
// 1. Translation Hook with Tenant Context
function useTranslations(namespace?: string) {
  const { tenant } = useTenant();
  const baseT = useNextIntlTranslations(namespace);
  
  return useCallback((key: string, values?: any) => {
    // Try tenant-specific translation first
    const tenantKey = `tenant.${tenant.id}.${key}`;
    if (hasMessage(tenantKey)) {
      return baseT(tenantKey, values);
    }
    
    // Fall back to base translation
    return baseT(key, values);
  }, [baseT, tenant.id]);
}

// 2. Locale Switcher Component
function LocaleSwitcher() {
  const router = useRouter();
  const pathname = usePathname();
  const params = useParams();
  const currentLocale = useLocale();
  
  const handleLocaleChange = (locale: string) => {
    router.replace(
      // @ts-expect-error -- TypeScript validation
      { pathname, params },
      { locale }
    );
  };
  
  return (
    <Select value={currentLocale} onValueChange={handleLocaleChange}>
      {routing.locales.map(locale => (
        <SelectItem key={locale} value={locale}>
          <Flag locale={locale} />
          {getLocaleName(locale, currentLocale)}
        </SelectItem>
      ))}
    </Select>
  );
}

// 3. Formatted Content Components
function FormattedDate({ date, format = 'short' }: FormattedDateProps) {
  const formatter = useFormatter();
  return <time>{formatter.dateTime(date, format)}</time>;
}

function FormattedNumber({ value, format = 'decimal' }: FormattedNumberProps) {
  const formatter = useFormatter();
  return <span>{formatter.number(value, format)}</span>;
}

function FormattedCurrency({ amount, currency }: FormattedCurrencyProps) {
  const formatter = useFormatter();
  return <span>{formatter.number(amount, { style: 'currency', currency })}</span>;
}

// 4. Rich Text Translation Component
function RichTranslation({ 
  messageKey, 
  values, 
  components 
}: RichTranslationProps) {
  const t = useTranslations();
  
  return (
    <span
      dangerouslySetInnerHTML={{
        __html: t.rich(messageKey, {
          ...values,
          ...components
        })
      }}
    />
  );
}
```

---

## Message Structure

### Translation File Organization

```json
// messages/en.json
{
  "Common": {
    "save": "Save",
    "cancel": "Cancel",
    "delete": "Delete",
    "edit": "Edit",
    "loading": "Loading...",
    "error": "An error occurred"
  },
  "Navigation": {
    "dashboard": "Dashboard",
    "settings": "Settings",
    "users": "Users",
    "billing": "Billing"
  },
  "Dashboard": {
    "title": "Welcome to {tenantName}",
    "subtitle": "Here's what's happening with your account",
    "stats": {
      "users": "{count, plural, =0 {No users} =1 {1 user} other {# users}}",
      "revenue": "Revenue: {amount, number, currency}",
      "growth": "{percentage, number, percent} growth"
    }
  },
  "Forms": {
    "validation": {
      "required": "This field is required",
      "email": "Please enter a valid email address",
      "minLength": "Must be at least {min} characters"
    }
  }
}

// messages/es.json
{
  "Common": {
    "save": "Guardar",
    "cancel": "Cancelar",
    "delete": "Eliminar",
    "edit": "Editar",
    "loading": "Cargando...",
    "error": "Ocurrió un error"
  },
  "Navigation": {
    "dashboard": "Tablero",
    "settings": "Configuración",
    "users": "Usuarios",
    "billing": "Facturación"
  },
  "Dashboard": {
    "title": "Bienvenido a {tenantName}",
    "subtitle": "Esto es lo que está pasando con tu cuenta",
    "stats": {
      "users": "{count, plural, =0 {Sin usuarios} =1 {1 usuario} other {# usuarios}}",
      "revenue": "Ingresos: {amount, number, currency}",
      "growth": "{percentage, number, percent} de crecimiento"
    }
  },
  "Forms": {
    "validation": {
      "required": "Este campo es obligatorio",
      "email": "Por favor ingresa un email válido",
      "minLength": "Debe tener al menos {min} caracteres"
    }
  }
}
```

---

## Testing Strategy

### Internationalization Testing

```typescript
// i18n Testing Utilities
describe('Internationalization', () => {
  describe('Message Loading', () => {
    it('should load messages for all supported locales', async () => {
      for (const locale of routing.locales) {
        const messages = await import(`../messages/${locale}.json`);
        expect(messages.default).toBeDefined();
        expect(Object.keys(messages.default)).toContain('Common');
      }
    });
    
    it('should have consistent message keys across locales', async () => {
      const enMessages = await import('../messages/en.json');
      const esMessages = await import('../messages/es.json');
      
      expect(Object.keys(enMessages.default)).toEqual(
        Object.keys(esMessages.default)
      );
    });
  });
  
  describe('Locale Routing', () => {
    it('should redirect to default locale when no locale specified', () => {
      cy.visit('/dashboard');
      cy.url().should('include', '/dashboard'); // No prefix for default
    });
    
    it('should serve localized content for each locale', () => {
      routing.locales.forEach(locale => {
        cy.visit(`/${locale}/dashboard`);
        cy.get('[data-testid="page-title"]').should('be.visible');
      });
    });
  });
  
  describe('Cultural Formatting', () => {
    it('should format dates according to locale', () => {
      const date = new Date('2025-01-15');
      
      render(<FormattedDate date={date} />, { locale: 'en' });
      expect(screen.getByText(/Jan 15, 2025/)).toBeInTheDocument();
      
      render(<FormattedDate date={date} />, { locale: 'es' });
      expect(screen.getByText(/15 ene 2025/)).toBeInTheDocument();
    });
    
    it('should format numbers according to locale', () => {
      render(<FormattedNumber value={1234.56} />, { locale: 'en' });
      expect(screen.getByText('1,234.56')).toBeInTheDocument();
      
      render(<FormattedNumber value={1234.56} />, { locale: 'es' });
      expect(screen.getByText('1.234,56')).toBeInTheDocument();
    });
  });
});

// Multi-Tenant i18n Testing
describe('Multi-Tenant Internationalization', () => {
  it('should use tenant-specific translations when available', () => {
    const mockTenant = { id: 'acme', name: 'ACME Corp' };
    
    render(
      <TenantProvider tenant={mockTenant}>
        <TranslatedComponent messageKey="welcome" />
      </TenantProvider>
    );
    
    // Should use tenant-specific translation if available
    expect(screen.getByText(/Welcome to ACME Corp/)).toBeInTheDocument();
  });
});
```

---

## Success Criteria

### Primary Success Metrics
- **Language Coverage**: Support for 6+ languages at launch
- **Performance**: < 50KB bundle size impact per locale
- **SEO**: Proper hreflang implementation for all pages
- **User Experience**: < 200ms locale switching time

### Secondary Success Metrics
- **Developer Experience**: 100% type-safe translation keys
- **Content Coverage**: 95%+ translation completion for core features
- **Multi-Tenant**: Tenant-specific localization working for all features
- **Accessibility**: WCAG 2.1 AA compliance for all localized content

---

## Implementation Checklist

### Foundation
- [ ] Install and configure next-intl
- [ ] Set up routing configuration
- [ ] Create middleware for locale handling
- [ ] Implement navigation APIs

### Content Management
- [ ] Create message structure and organization
- [ ] Implement translation loading system
- [ ] Set up fallback mechanisms
- [ ] Create translation validation tools

### Cultural Formatting
- [ ] Implement date/time formatting
- [ ] Set up number and currency formatting
- [ ] Add relative time formatting
- [ ] Create locale-specific format configurations

### Multi-Tenant Integration
- [ ] Implement tenant-specific locale preferences
- [ ] Create custom translation override system
- [ ] Set up tenant-specific formatting
- [ ] Build tenant localization management UI

### Testing & Quality
- [ ] Set up automated i18n testing
- [ ] Create translation completeness checks
- [ ] Implement locale routing tests
- [ ] Build cultural formatting validation

---

**Ready for implementation with Context7-verified next-intl patterns!** 🚀

*Built with ❤️ by Nexus-Master Agent*  
*Global Reach Meets Local Excellence*
