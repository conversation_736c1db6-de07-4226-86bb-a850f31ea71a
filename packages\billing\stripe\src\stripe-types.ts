import type Stripe from "stripe";

// Payment types
export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: Stripe.PaymentIntent.Status;
  clientSecret: string;
  customerId?: string;
  paymentMethodId?: string;
  metadata: Record<string, string>;
  createdAt: Date;
  updatedAt: Date;
}

export interface PaymentMethod {
  id: string;
  type: Stripe.PaymentMethod.Type;
  card?: {
    brand: string;
    last4: string;
    expMonth: number;
    expYear: number;
  };
  isDefault: boolean;
  customerId: string;
  createdAt: Date;
}

export interface Customer {
  id: string;
  stripeCustomerId: string;
  email: string;
  name?: string;
  defaultPaymentMethodId?: string;
  metadata: Record<string, string>;
  createdAt: Date;
  updatedAt: Date;
}

// Subscription types
export interface Subscription {
  id: string;
  stripeSubscriptionId: string;
  customerId: string;
  priceId: string;
  status: Stripe.Subscription.Status;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  trialStart?: Date;
  trialEnd?: Date;
  cancelAtPeriodEnd: boolean;
  canceledAt?: Date;
  metadata: Record<string, string>;
  createdAt: Date;
  updatedAt: Date;
}

export interface Price {
  id: string;
  stripePriceId: string;
  productId: string;
  amount: number;
  currency: string;
  interval?: "month" | "year" | "week" | "day";
  intervalCount?: number;
  type: "one_time" | "recurring";
  active: boolean;
  metadata: Record<string, string>;
  createdAt: Date;
  updatedAt: Date;
}

export interface Product {
  id: string;
  stripeProductId: string;
  name: string;
  description?: string;
  active: boolean;
  metadata: Record<string, string>;
  prices: Price[];
  createdAt: Date;
  updatedAt: Date;
}

// Invoice types
export interface Invoice {
  id: string;
  stripeInvoiceId: string;
  customerId: string;
  subscriptionId?: string;
  amount: number;
  currency: string;
  status: Stripe.Invoice.Status;
  paidAt?: Date;
  dueDate?: Date;
  hostedInvoiceUrl?: string;
  invoicePdf?: string;
  metadata: Record<string, string>;
  createdAt: Date;
  updatedAt: Date;
}

// Payment request types
export interface CreatePaymentIntentRequest {
  amount: number;
  currency?: string;
  paymentMethodTypes?: string[];
  customerId?: string;
  metadata?: Record<string, string>;
}

export interface CreateSubscriptionRequest {
  customerId: string;
  priceId: string;
  trialPeriodDays?: number;
  metadata?: Record<string, string>;
}

export interface UpdateSubscriptionRequest {
  subscriptionId: string;
  priceId?: string;
  cancelAtPeriodEnd?: boolean;
  metadata?: Record<string, string>;
}

// Webhook types
export interface WebhookEvent {
  id: string;
  type: string;
  data: any;
  processed: boolean;
  error?: string;
  createdAt: Date;
  processedAt?: Date;
}

// Billing types
export interface BillingPortalSession {
  url: string;
  returnUrl: string;
}

export interface CheckoutSession {
  id: string;
  url: string;
  customerId: string;
  mode: "payment" | "subscription" | "setup";
  status: Stripe.Checkout.Session.Status;
  metadata: Record<string, string>;
  createdAt: Date;
}

// Usage types
export interface UsageRecord {
  id: string;
  subscriptionItemId: string;
  quantity: number;
  timestamp: Date;
  action: "increment" | "set";
  metadata: Record<string, string>;
}

// Tax types
export interface TaxCalculation {
  id: string;
  amount: number;
  currency: string;
  taxAmount: number;
  taxRate: number;
  jurisdiction: string;
  metadata: Record<string, string>;
}

// Error types
export interface StripeError {
  type: string;
  code?: string;
  message: string;
  param?: string;
  statusCode?: number;
}
