{"name": "@nexus/realtime", "version": "0.1.0", "description": "Real-time collaboration service for Nexus SaaS", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "clean": "rm -rf dist", "test": "jest", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "@nexus/utils": "workspace:*", "@nexus/rbac": "workspace:*", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "socket.io-redis": "^6.1.1", "redis": "^4.6.12", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "jsonwebtoken": "^9.0.2", "yjs": "^13.6.10", "y-websocket": "^1.5.0", "y-redis": "^0.2.0", "uuid": "^9.0.1", "zod": "^4.0.5", "winston": "^3.11.0"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/jsonwebtoken": "^9.0.5", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.0", "@types/node": "^22.10.2", "jest": "^29.5.0", "tsx": "^4.6.2", "typescript": "^5.8.0", "eslint": "^8.57.0"}}