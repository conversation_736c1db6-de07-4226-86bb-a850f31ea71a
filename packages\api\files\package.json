{"name": "@nexus/files", "version": "0.1.0", "description": "Advanced file management service for Nexus SaaS", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "clean": "rm -rf dist", "test": "jest", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "@nexus/utils": "workspace:*", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "aws-sdk": "^2.1543.0", "@google-cloud/storage": "^7.7.0", "azure-storage": "^2.10.7", "sharp": "^0.33.1", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.2", "pdf-poppler": "^0.2.1", "mammoth": "^1.6.0", "xlsx": "^0.18.5", "archiver": "^6.0.1", "unzipper": "^0.10.14", "mime-types": "^2.1.35", "file-type": "^19.0.0", "crypto": "^1.0.1", "uuid": "^9.0.1", "zod": "^4.0.5", "winston": "^3.11.0", "bull": "^4.12.2", "redis": "^4.6.12", "prisma": "^5.7.1", "@prisma/client": "^5.7.1"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/multer": "^1.4.11", "@types/multer-s3": "^3.0.3", "@types/mime-types": "^2.1.4", "@types/fluent-ffmpeg": "^2.1.24", "@types/archiver": "^6.0.2", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.0", "@types/node": "^22.10.2", "jest": "^29.5.0", "tsx": "^4.6.2", "typescript": "^5.8.0", "eslint": "^8.57.0"}}