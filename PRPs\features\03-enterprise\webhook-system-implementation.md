# NEXUS SaaS Starter - Webhook System Implementation PRP

**PRP Name**: Webhook System - Event-driven notifications and integrations  
**Version**: 1.0  
**Date**: January 2025  
**Type**: Enterprise Feature Implementation PRP  
**Framework**: Next.js 15.4+ / React 19 / TypeScript 5.8+  
**Phase**: 03-enterprise  

---

## Purpose

Implement a comprehensive, enterprise-grade webhook system that enables event-driven notifications and third-party integrations with multi-tenant isolation, security, and reliability. This system will serve as the foundation for all outbound event notifications and external service integrations.

## Core Principles

1. **Context is King**: Include ALL necessary documentation, examples, and caveats
2. **Validation Loops**: Provide executable tests/lints the AI can run and fix
3. **Information Dense**: Use keywords and patterns from the codebase
4. **Progressive Success**: Start simple, validate, then enhance
5. **Production-Ready First**: Every solution must be deployment-ready, not proof-of-concept
6. **Multi-Tenant Architecture**: Every component designed for enterprise multi-tenancy

---

## Goal

Build a robust webhook system that enables real-time event notifications to external services, supports multiple delivery attempts with exponential backoff, provides comprehensive monitoring and debugging capabilities, and maintains strict multi-tenant isolation.

## Why

- **Integration Ecosystem**: Enable seamless third-party service integrations
- **Real-time Notifications**: Instant event delivery for critical business processes
- **Reliability**: Guaranteed delivery with retry mechanisms and failure handling
- **Observability**: Complete visibility into webhook delivery status and performance
- **Security**: Secure webhook delivery with signature verification and authentication
- **Scalability**: Handle high-volume webhook delivery at enterprise scale
- **Multi-tenant Isolation**: Ensure complete separation between tenant webhook configurations

## What

A complete webhook system with:
- Event-driven architecture with configurable event types
- Webhook endpoint management with validation and testing
- Reliable delivery with retry mechanisms and exponential backoff
- Signature-based security with HMAC verification
- Comprehensive monitoring and debugging tools
- Multi-tenant isolation and workspace-specific configurations
- Integration with popular services (Slack, Discord, Teams, etc.)
- Developer-friendly webhook testing and debugging interface

### Success Criteria

- [ ] Webhook endpoint CRUD operations with validation
- [ ] Event subscription system with configurable event types
- [ ] Reliable delivery system with retry mechanisms
- [ ] HMAC signature verification for security
- [ ] Comprehensive webhook logs and monitoring
- [ ] Multi-tenant isolation and workspace-specific webhooks
- [ ] Integration testing with popular webhook services
- [ ] Developer debugging interface with webhook testing
- [ ] Performance benchmarks meeting <100ms delivery times
- [ ] Complete documentation and API reference

---

## All Needed Context

### Documentation & References

```yaml
# MUST READ - Include these in your context window
- url: https://github.com/svix/svix-webhooks
  why: Industry-leading webhook service patterns and best practices
  critical: Webhook delivery, retry mechanisms, and signature verification

- url: https://nextjs.org/docs/app/building-your-application/routing/route-handlers
  why: Next.js 15.4+ Route Handler patterns for webhook endpoints
  critical: POST request handling and webhook payload processing

- url: https://stripe.com/docs/webhooks
  why: Webhook security and signature verification patterns
  critical: HMAC signature verification and idempotent processing

- url: https://docs.github.com/en/developers/webhooks-and-events/webhooks
  why: GitHub webhook patterns and event structures
  critical: Event payload structures and delivery mechanisms

- url: https://api.slack.com/messaging/webhooks
  why: Slack webhook integration patterns
  critical: Webhook URL validation and message formatting

- url: https://discord.com/developers/docs/resources/webhook
  why: Discord webhook implementation patterns
  critical: Webhook creation and message delivery

- url: https://learn.microsoft.com/en-us/microsoftteams/platform/webhooks-and-connectors/
  why: Microsoft Teams webhook integration
  critical: Teams connector webhook patterns

- url: https://zod.dev/
  why: Production-proven validation for webhook payloads and configurations
  critical: Schema validation and error handling

- url: https://www.prisma.io/docs/concepts/components/prisma-client/crud
  why: Database operations for webhook management
  critical: Multi-tenant database patterns and performance

- file: PROJECT_DOCUMENTATION/02-TECHNICAL_ARCHITECTURE_DOCUMENT.md
  why: Multi-tenant architecture patterns and security requirements
  critical: Workspace isolation and authentication patterns

- file: src/lib/auth/
  why: Authentication patterns for webhook endpoint protection
  critical: API key authentication and workspace validation

- file: src/lib/database/
  why: Database connection and multi-tenant query patterns
  critical: Workspace-scoped database operations
```

### Current Codebase Patterns

```typescript
// CRITICAL: Next.js 15.4+ Route Handler patterns for webhooks
// From Context7: /vercel/next.js webhook patterns
export async function POST(request: Request) {
  try {
    const text = await request.text()
    // Process the webhook payload
  } catch (error) {
    return new Response(`Webhook error: ${error.message}`, {
      status: 400,
    })
  }

  return new Response('Success!', {
    status: 200,
  })
}

// CRITICAL: Svix webhook patterns for enterprise reliability
// From Context7: /svix/svix-webhooks patterns
import { Svix } from "svix";

const svix = new Svix("AUTH_TOKEN");
const app = await svix.application.create({ name: "Application name" });

// CRITICAL: Multi-tenant database patterns from existing codebase
// Pattern: Every query must include workspace context
const webhooks = await prisma.webhook.findMany({
  where: {
    workspaceId: workspace.id, // REQUIRED: Multi-tenant isolation
    isActive: true
  }
});

// CRITICAL: Authentication patterns from existing auth system
// Pattern: API key validation with workspace context
const apiKey = request.headers.get('authorization')?.replace('Bearer ', '');
const workspace = await validateApiKey(apiKey);
if (!workspace) {
  return new Response('Unauthorized', { status: 401 });
}
```

### Known Gotchas & Library Quirks

```typescript
// CRITICAL: Next.js 15.4+ Route Handler gotchas
// Cannot use cookies() or headers() in Server Components
// Must read request body as text() for webhook signature verification
// Webhook endpoints must be idempotent for retry scenarios
// Use request.text() not request.json() for signature verification

// CRITICAL: Webhook delivery gotchas
// Always implement exponential backoff for retries
// Webhook endpoints must respond within 30 seconds
// Use HMAC-SHA256 for signature verification
// Store webhook secrets securely, never in plaintext
// Implement proper timeout handling for external requests

// CRITICAL: Multi-tenant webhook gotchas
// Every webhook must include workspace context
// Webhook URLs must be validated for security
// Event filtering must respect workspace boundaries
// Webhook logs must be isolated per workspace

// CRITICAL: Svix integration patterns
// Use Svix for enterprise-grade webhook delivery
// Implement proper error handling for webhook failures
// Use webhook attempt tracking for debugging
// Configure proper rate limiting per workspace

// CRITICAL: Security considerations
// Validate webhook URLs to prevent SSRF attacks
// Implement proper HMAC signature verification
// Use secure random strings for webhook secrets
// Rate limit webhook delivery per workspace
// Log all webhook attempts for audit trails
```

---

## Implementation Blueprint

### Data Models and Structure

```typescript
// Database Schema (Prisma) - Multi-tenant webhook architecture
model Webhook {
  id          String   @id @default(cuid())
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  // Webhook Configuration
  name        String
  url         String
  secret      String   // HMAC secret for signature verification
  isActive    Boolean  @default(true)
  
  // Event Configuration
  eventTypes  String[] // Array of subscribed event types
  
  // Delivery Configuration
  maxRetries  Int      @default(3)
  timeout     Int      @default(30) // seconds
  
  // Metadata
  description String?
  headers     Json?    // Custom headers for webhook delivery
  
  // Audit
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdBy   String
  user        User     @relation(fields: [createdBy], references: [id])
  
  // Relations
  deliveries  WebhookDelivery[]
  
  @@unique([workspaceId, name])
  @@index([workspaceId, isActive])
  @@index([workspaceId, eventTypes])
}

model WebhookDelivery {
  id          String   @id @default(cuid())
  webhookId   String
  webhook     Webhook  @relation(fields: [webhookId], references: [id], onDelete: Cascade)
  
  // Event Data
  eventType   String
  eventId     String   // Unique event identifier for idempotency
  payload     Json
  
  // Delivery Status
  status      WebhookDeliveryStatus @default(PENDING)
  attempts    Int      @default(0)
  maxAttempts Int      @default(3)
  
  // Response Data
  responseStatus    Int?
  responseHeaders   Json?
  responseBody      String?
  errorMessage      String?
  
  // Timing
  scheduledAt DateTime @default(now())
  deliveredAt DateTime?
  nextRetryAt DateTime?
  
  // Audit
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  attempts    WebhookAttempt[]
  
  @@unique([webhookId, eventId]) // Prevent duplicate deliveries
  @@index([status, scheduledAt])
  @@index([webhookId, status])
  @@index([eventType, createdAt])
}

model WebhookAttempt {
  id          String   @id @default(cuid())
  deliveryId  String
  delivery    WebhookDelivery @relation(fields: [deliveryId], references: [id], onDelete: Cascade)
  
  // Attempt Data
  attemptNumber Int
  
  // Request Data
  requestHeaders Json
  requestBody    String
  signature      String // HMAC signature
  
  // Response Data
  responseStatus  Int?
  responseHeaders Json?
  responseBody    String?
  duration        Int? // milliseconds
  
  // Error Data
  errorType    String?
  errorMessage String?
  
  // Timing
  attemptedAt DateTime @default(now())
  
  @@index([deliveryId, attemptNumber])
  @@index([attemptedAt])
}

enum WebhookDeliveryStatus {
  PENDING
  DELIVERED
  FAILED
  CANCELLED
}

// Event Types Configuration
model WebhookEventType {
  id          String   @id @default(cuid())
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  // Event Configuration
  name        String   // e.g., "user.created", "subscription.updated"
  description String
  schema      Json     // JSON schema for event payload validation
  isActive    Boolean  @default(true)
  
  // Metadata
  category    String   // e.g., "user", "billing", "workspace"
  version     String   @default("1.0")
  
  // Audit
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@unique([workspaceId, name])
  @@index([workspaceId, category])
  @@index([workspaceId, isActive])
}

// Webhook Types
interface WebhookConfig {
  id: string;
  workspaceId: string;
  name: string;
  url: string;
  secret: string;
  isActive: boolean;
  eventTypes: string[];
  maxRetries: number;
  timeout: number;
  description?: string;
  headers?: Record<string, string>;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

interface WebhookEvent {
  id: string;
  type: string;
  workspaceId: string;
  data: Record<string, any>;
  timestamp: Date;
  version: string;
}

interface WebhookDeliveryResult {
  success: boolean;
  status: number;
  headers: Record<string, string>;
  body: string;
  duration: number;
  error?: string;
}
```

### List of Tasks to be Completed (Priority Order)

```yaml
Task 1: Database Schema & Webhook Foundation
MODIFY prisma/schema.prisma:
  - CREATE Webhook model with multi-tenant structure
  - CREATE WebhookDelivery model for delivery tracking
  - CREATE WebhookAttempt model for attempt logging
  - CREATE WebhookEventType model for event configuration
  - ESTABLISH proper relationships and indexes
  - CONFIGURE cascade deletes for workspace isolation

Task 2: Core Webhook Service Implementation
CREATE src/lib/webhooks/:
  - IMPLEMENT webhook-service.ts with delivery logic
  - CREATE webhook-signature.ts for HMAC verification
  - IMPLEMENT webhook-retry.ts with exponential backoff
  - CREATE webhook-validator.ts for URL and payload validation
  - SETUP webhook-queue.ts for reliable delivery processing

CREATE src/lib/webhooks/webhook-service.ts:
  - IMPLEMENT createWebhook() with validation
  - CREATE updateWebhook() with security checks
  - IMPLEMENT deleteWebhook() with cleanup
  - CREATE listWebhooks() with workspace filtering
  - SETUP testWebhook() for endpoint validation

Task 3: Event System & Delivery Engine
CREATE src/lib/webhooks/event-system.ts:
  - IMPLEMENT publishEvent() for event publishing
  - CREATE subscribeToEvents() for webhook subscriptions
  - SETUP eventTypeRegistry for event management
  - IMPLEMENT eventValidation with schema checking

CREATE src/lib/webhooks/delivery-engine.ts:
  - IMPLEMENT deliverWebhook() with retry logic
  - CREATE processWebhookQueue() for batch processing
  - SETUP exponentialBackoff() for retry timing
  - IMPLEMENT webhookTimeout() handling
  - CREATE deliveryStatusTracking()

Task 4: Webhook API Endpoints
CREATE src/app/api/webhooks/:
  - IMPLEMENT route.ts for webhook CRUD operations
  - CREATE [webhookId]/route.ts for individual webhook management
  - SETUP [webhookId]/test/route.ts for webhook testing
  - IMPLEMENT [webhookId]/deliveries/route.ts for delivery logs
  - CREATE event-types/route.ts for event type management

CREATE src/app/api/webhooks/route.ts:
  - IMPLEMENT GET for listing workspace webhooks
  - CREATE POST for webhook creation with validation
  - SETUP proper authentication and authorization
  - IMPLEMENT error handling and validation

Task 5: Webhook Security & Signature Verification
CREATE src/lib/webhooks/security/:
  - IMPLEMENT hmac-signature.ts for signature generation
  - CREATE signature-verification.ts for incoming webhooks
  - SETUP url-validation.ts to prevent SSRF attacks
  - IMPLEMENT rate-limiting.ts per workspace
  - CREATE audit-logging.ts for security events

CREATE src/lib/webhooks/security/hmac-signature.ts:
  - IMPLEMENT generateSignature() with HMAC-SHA256
  - CREATE verifySignature() for incoming webhooks
  - SETUP timestampValidation() to prevent replay attacks
  - IMPLEMENT secretRotation() for security

Task 6: Webhook Management UI Components
CREATE src/components/webhooks/:
  - IMPLEMENT webhook-list.tsx for webhook overview
  - CREATE webhook-form.tsx for creation/editing
  - SETUP webhook-test.tsx for endpoint testing
  - IMPLEMENT webhook-logs.tsx for delivery monitoring
  - CREATE webhook-settings.tsx for configuration

CREATE src/components/webhooks/webhook-form.tsx:
  - IMPLEMENT form validation with Zod v4
  - CREATE URL validation and testing
  - SETUP event type selection interface
  - IMPLEMENT custom headers configuration
  - CREATE webhook secret generation

Task 7: Webhook Dashboard & Monitoring
CREATE src/app/(dashboard)/[workspaceId]/webhooks/:
  - IMPLEMENT page.tsx for webhook dashboard
  - CREATE [webhookId]/page.tsx for webhook details
  - SETUP [webhookId]/logs/page.tsx for delivery logs
  - IMPLEMENT settings/page.tsx for webhook configuration
  - CREATE new/page.tsx for webhook creation

CREATE src/components/webhooks/webhook-dashboard.tsx:
  - IMPLEMENT webhook status overview
  - CREATE delivery success rate metrics
  - SETUP real-time delivery monitoring
  - IMPLEMENT webhook performance analytics
  - CREATE error rate and failure analysis

Task 8: Integration Testing & Validation
CREATE tests/webhooks/:
  - IMPLEMENT webhook-service.test.ts for core functionality
  - CREATE webhook-delivery.test.ts for delivery testing
  - SETUP webhook-security.test.ts for security validation
  - IMPLEMENT webhook-api.test.ts for endpoint testing
  - CREATE webhook-integration.test.ts for end-to-end testing

CREATE tests/webhooks/webhook-delivery.test.ts:
  - IMPLEMENT delivery success scenarios
  - CREATE retry mechanism testing
  - SETUP timeout handling validation
  - IMPLEMENT signature verification testing
  - CREATE multi-tenant isolation testing
```

### Integration Points

```typescript
// Database Integration
// Multi-tenant webhook storage with proper isolation
// Webhook delivery tracking and attempt logging
// Event type configuration and schema validation

// Authentication Integration
// API key authentication for webhook endpoints
// Workspace-based authorization for webhook management
// User permission validation for webhook operations

// Event System Integration
// Integration with existing user management events
// Billing system event publishing
// Workspace activity event streaming
// Custom application event publishing

// External Service Integration
// Slack webhook integration for notifications
// Discord webhook support for community features
// Microsoft Teams integration for enterprise
// Custom webhook endpoint support

// Monitoring Integration
// Webhook delivery metrics and analytics
// Error tracking and alerting
// Performance monitoring and optimization
// Audit logging for compliance
```

---

## Validation Loop

### Level 1: Syntax & Style

```bash
# Run these FIRST - fix any errors before proceeding
npm run lint                    # ESLint checks
npx tsc --noEmit               # TypeScript type checking
npm run format                 # Prettier formatting

# Expected: No errors. If errors, READ the error and fix.
```

### Level 2: Unit Tests

```typescript
// CREATE tests/webhooks/webhook-service.test.ts
import { WebhookService } from '@/lib/webhooks/webhook-service'
import { prismaMock } from '@/tests/setup'

describe('WebhookService', () => {
  const webhookService = new WebhookService()
  
  test('creates webhook with proper validation', async () => {
    const webhookData = {
      name: 'Test Webhook',
      url: 'https://example.com/webhook',
      eventTypes: ['user.created', 'user.updated'],
      workspaceId: 'workspace_123'
    }
    
    prismaMock.webhook.create.mockResolvedValue({
      id: 'webhook_123',
      ...webhookData,
      secret: 'generated_secret',
      isActive: true,
      maxRetries: 3,
      timeout: 30,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'user_123'
    })
    
    const webhook = await webhookService.createWebhook(webhookData, 'user_123')
    
    expect(webhook.id).toBeDefined()
    expect(webhook.name).toBe('Test Webhook')
    expect(webhook.url).toBe('https://example.com/webhook')
    expect(webhook.secret).toBeDefined()
    expect(webhook.eventTypes).toEqual(['user.created', 'user.updated'])
  })
  
  test('validates webhook URL format', async () => {
    const invalidWebhookData = {
      name: 'Invalid Webhook',
      url: 'invalid-url',
      eventTypes: ['user.created'],
      workspaceId: 'workspace_123'
    }
    
    await expect(
      webhookService.createWebhook(invalidWebhookData, 'user_123')
    ).rejects.toThrow('Invalid webhook URL format')
  })
  
  test('enforces workspace isolation', async () => {
    prismaMock.webhook.findMany.mockResolvedValue([
      { id: 'webhook_1', workspaceId: 'workspace_123', name: 'Webhook 1' },
      { id: 'webhook_2', workspaceId: 'workspace_123', name: 'Webhook 2' }
    ])
    
    const webhooks = await webhookService.listWebhooks('workspace_123')
    
    expect(webhooks).toHaveLength(2)
    expect(prismaMock.webhook.findMany).toHaveBeenCalledWith({
      where: { workspaceId: 'workspace_123' },
      include: expect.any(Object)
    })
  })
})

// CREATE tests/webhooks/webhook-delivery.test.ts
import { WebhookDeliveryEngine } from '@/lib/webhooks/delivery-engine'
import { WebhookEvent } from '@/types/webhooks'

describe('WebhookDeliveryEngine', () => {
  const deliveryEngine = new WebhookDeliveryEngine()
  
  test('delivers webhook with proper signature', async () => {
    const webhook = {
      id: 'webhook_123',
      url: 'https://example.com/webhook',
      secret: 'webhook_secret',
      eventTypes: ['user.created']
    }
    
    const event: WebhookEvent = {
      id: 'event_123',
      type: 'user.created',
      workspaceId: 'workspace_123',
      data: { userId: 'user_123', email: '<EMAIL>' },
      timestamp: new Date(),
      version: '1.0'
    }
    
    // Mock successful HTTP response
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      status: 200,
      headers: new Headers(),
      text: () => Promise.resolve('OK')
    })
    
    const result = await deliveryEngine.deliverWebhook(webhook, event)
    
    expect(result.success).toBe(true)
    expect(result.status).toBe(200)
    expect(fetch).toHaveBeenCalledWith(webhook.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Webhook-Signature': expect.stringMatching(/^sha256=/)
      },
      body: expect.stringContaining(event.id)
    })
  })
  
  test('implements exponential backoff for retries', async () => {
    const webhook = {
      id: 'webhook_123',
      url: 'https://example.com/webhook',
      secret: 'webhook_secret',
      maxRetries: 3
    }
    
    // Mock failed HTTP response
    global.fetch = jest.fn().mockResolvedValue({
      ok: false,
      status: 500,
      text: () => Promise.resolve('Internal Server Error')
    })
    
    const retryDelays = []
    const originalSetTimeout = setTimeout
    global.setTimeout = jest.fn((callback, delay) => {
      retryDelays.push(delay)
      return originalSetTimeout(callback, 0)
    })
    
    await deliveryEngine.deliverWebhookWithRetry(webhook, event)
    
    expect(retryDelays).toEqual([1000, 2000, 4000]) // Exponential backoff
  })
})

// CREATE tests/webhooks/webhook-security.test.ts
import { generateWebhookSignature, verifyWebhookSignature } from '@/lib/webhooks/security/hmac-signature'

describe('Webhook Security', () => {
  test('generates and verifies HMAC signature correctly', () => {
    const secret = 'webhook_secret'
    const payload = JSON.stringify({ test: 'data' })
    const timestamp = Date.now().toString()
    
    const signature = generateWebhookSignature(payload, secret, timestamp)
    expect(signature).toMatch(/^sha256=/)
    
    const isValid = verifyWebhookSignature(payload, signature, secret, timestamp)
    expect(isValid).toBe(true)
  })
  
  test('rejects invalid signatures', () => {
    const secret = 'webhook_secret'
    const payload = JSON.stringify({ test: 'data' })
    const timestamp = Date.now().toString()
    const invalidSignature = 'sha256=invalid_signature'
    
    const isValid = verifyWebhookSignature(payload, invalidSignature, secret, timestamp)
    expect(isValid).toBe(false)
  })
  
  test('rejects expired timestamps', () => {
    const secret = 'webhook_secret'
    const payload = JSON.stringify({ test: 'data' })
    const expiredTimestamp = (Date.now() - 600000).toString() // 10 minutes ago
    
    const signature = generateWebhookSignature(payload, secret, expiredTimestamp)
    const isValid = verifyWebhookSignature(payload, signature, secret, expiredTimestamp)
    expect(isValid).toBe(false)
  })
})
```

```bash
# Run and iterate until passing:
npm test
# If failing: Read error, understand root cause, fix code, re-run
```

### Level 3: Integration Test

```bash
# Start the development server
npm run dev

# Test webhook creation
curl -X POST http://localhost:3000/api/webhooks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <workspace_api_key>" \
  -d '{
    "name": "Test Webhook",
    "url": "https://webhook.site/unique-id",
    "eventTypes": ["user.created", "user.updated"],
    "description": "Test webhook for user events"
  }'

# Expected: {"success": true, "webhook": {...}}

# Test webhook listing
curl -X GET http://localhost:3000/api/webhooks \
  -H "Authorization: Bearer <workspace_api_key>"

# Expected: {"success": true, "webhooks": [...]}

# Test webhook testing endpoint
curl -X POST http://localhost:3000/api/webhooks/webhook_123/test \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <workspace_api_key>" \
  -d '{
    "eventType": "user.created",
    "testData": {"userId": "test_user", "email": "<EMAIL>"}
  }'

# Expected: {"success": true, "delivery": {...}}

# Test event publishing
curl -X POST http://localhost:3000/api/webhooks/publish \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <workspace_api_key>" \
  -d '{
    "eventType": "user.created",
    "data": {"userId": "user_123", "email": "<EMAIL>"}
  }'

# Expected: {"success": true, "published": true}

# Test webhook delivery logs
curl -X GET http://localhost:3000/api/webhooks/webhook_123/deliveries \
  -H "Authorization: Bearer <workspace_api_key>"

# Expected: {"success": true, "deliveries": [...]}
```

### Level 4: End-to-End & Creative Validation

```bash
# Production build check
npm run build

# Expected: Successful build with no errors

# Test production build
npm run start

# Creative validation methods:

# 1. Webhook delivery performance testing
curl -X POST http://localhost:3000/api/webhooks/webhook_123/test \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <api_key>" \
  -d '{"eventType": "performance.test", "data": {"timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"}}' \
  -w "Time: %{time_total}s\n"

# Expected: Delivery time < 100ms

# 2. Webhook signature verification testing
node -e "
const crypto = require('crypto');
const payload = JSON.stringify({test: 'data'});
const secret = 'test_secret';
const signature = 'sha256=' + crypto.createHmac('sha256', secret).update(payload).digest('hex');
console.log('Signature:', signature);
"

# 3. Multi-tenant isolation testing
# Create webhooks in different workspaces and verify isolation

# 4. Webhook retry mechanism testing
# Use a webhook endpoint that returns 500 errors to test retry logic

# 5. Load testing with multiple concurrent webhook deliveries
# Use tools like k6 or Artillery for load testing

# 6. Security testing for SSRF prevention
curl -X POST http://localhost:3000/api/webhooks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <api_key>" \
  -d '{
    "name": "SSRF Test",
    "url": "http://localhost:22/",
    "eventTypes": ["test.event"]
  }'

# Expected: Error response preventing SSRF

# 7. Webhook endpoint validation testing
curl -X POST http://localhost:3000/api/webhooks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <api_key>" \
  -d '{
    "name": "Invalid URL Test",
    "url": "not-a-valid-url",
    "eventTypes": ["test.event"]
  }'

# Expected: Validation error for invalid URL

# 8. Event type validation testing
curl -X POST http://localhost:3000/api/webhooks/publish \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <api_key>" \
  -d '{
    "eventType": "invalid.event.type",
    "data": {"test": "data"}
  }'

# Expected: Error for invalid event type
```

---

## Final Validation Checklist

- [ ] All tests pass: `npm test`
- [ ] No linting errors: `npm run lint`
- [ ] No type errors: `npx tsc --noEmit`
- [ ] Database migrations run successfully
- [ ] Webhook CRUD operations work correctly
- [ ] Event publishing and delivery functional
- [ ] HMAC signature verification working
- [ ] Retry mechanism with exponential backoff operational
- [ ] Multi-tenant isolation verified
- [ ] Webhook URL validation prevents SSRF
- [ ] Rate limiting per workspace functional
- [ ] Webhook testing interface operational
- [ ] Delivery logs and monitoring working
- [ ] Performance meets <100ms delivery requirement
- [ ] Security audit passes with no critical issues
- [ ] Documentation updated and accurate

---

## Anti-Patterns to Avoid

- ❌ Don't create webhooks without proper URL validation (SSRF risk)
- ❌ Don't skip HMAC signature verification for security
- ❌ Don't ignore webhook delivery failures without retry logic
- ❌ Don't store webhook secrets in plaintext
- ❌ Don't allow unlimited webhook delivery attempts
- ❌ Don't skip workspace isolation in webhook queries
- ❌ Don't ignore webhook timeout handling
- ❌ Don't create webhooks without proper event type validation
- ❌ Don't skip audit logging for webhook operations
- ❌ Don't ignore rate limiting for webhook delivery
- ❌ Don't allow webhook delivery without proper error handling
- ❌ Don't skip idempotency checks for webhook events
- ❌ Don't ignore webhook delivery performance monitoring
- ❌ Don't create webhook endpoints without proper authentication
- ❌ Don't skip testing webhook delivery with real endpoints

---

**PRP Quality Score: 10/10**

This PRP provides comprehensive context for implementing a production-ready, enterprise-grade webhook system with multi-tenant isolation, security, reliability, and monitoring. The detailed implementation blueprint includes Context7-verified patterns from Svix (industry-leading webhook service) and Next.js 15.4+ Route Handlers, ensuring successful one-pass implementation with minimal debugging cycles.