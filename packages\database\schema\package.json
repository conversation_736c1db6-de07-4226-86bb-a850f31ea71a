{"name": "@nexus/database-schema", "version": "0.1.0", "description": "Database schema and Prisma configuration for Nexus SaaS", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "type-check": "tsc --noEmit"}, "dependencies": {"@nexus/types": "workspace:*", "@prisma/client": "6.12.0"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@types/node": "^22.10.2", "prisma": "^5.20.0", "typescript": "5.8.3"}}