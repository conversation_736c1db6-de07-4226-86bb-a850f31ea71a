import { User } from "@nexus/types";
export interface AuthUser extends User {
    tenantId: string;
    role: string;
    permissions: string[];
}
export interface AuthSession {
    user: AuthUser;
    token: string;
    expiresAt: Date;
}
export interface LoginCredentials {
    email: string;
    password: string;
    remember?: boolean;
}
export interface RegisterCredentials {
    email: string;
    password: string;
    confirmPassword: string;
    name: string;
}
export interface AuthState {
    user: AuthUser | null;
    session: AuthSession | null;
    isLoading: boolean;
    isAuthenticated: boolean;
}
export interface AuthContextType extends AuthState {
    login: (credentials: LoginCredentials) => Promise<void>;
    register: (credentials: RegisterCredentials) => Promise<void>;
    logout: () => Promise<void>;
    refreshSession: () => Promise<void>;
}
//# sourceMappingURL=auth-types.d.ts.map