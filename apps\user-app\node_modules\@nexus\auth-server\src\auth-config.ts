import { AUTH_CONFIG } from "@nexus/constants";
import { prisma } from "@nexus/database-service";
import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";

export const auth = betterAuth({
  database: prismaAdapter(prisma, {
    provider: "postgresql",
  }),
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false,
  },
  session: {
    expiresIn: AUTH_CONFIG.sessionDuration / 1000, // Convert to seconds
    updateAge: AUTH_CONFIG.refreshThreshold / 1000,
    cookieCache: {
      enabled: true,
      maxAge: AUTH_CONFIG.sessionDuration / 1000,
    },
  },
  user: {
    additionalFields: {
      tenantId: {
        type: "string",
        required: true,
      },
      role: {
        type: "string",
        required: true,
        defaultValue: "MEMBER",
      },
      status: {
        type: "string",
        required: true,
        defaultValue: "ACTIVE",
      },
    },
  },
  plugins: [],
  advanced: {
    generateId: () => {
      // Use cuid2 for consistent ID generation
      return require("@paralleldrive/cuid2").createId();
    },
  },
});

export type AuthSession = typeof auth.$Infer.Session;
export type AuthUser = any;
