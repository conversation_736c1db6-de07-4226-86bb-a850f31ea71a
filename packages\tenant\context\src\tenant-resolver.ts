import { TenantResolution } from "./tenant-types";

// Tenant resolution utilities
export class TenantResolver {
  // Resolve tenant from domain
  static fromDomain(hostname: string): TenantResolution | null {
    // Custom domain resolution (e.g., company.com)
    if (!hostname.includes("localhost") && !hostname.includes("nexus")) {
      return {
        method: "domain",
        value: hostname,
      };
    }
    return null;
  }

  // Resolve tenant from subdomain
  static fromSubdomain(hostname: string): TenantResolution | null {
    // Subdomain resolution (e.g., acme.nexus.com)
    const parts = hostname.split(".");
    if (parts.length >= 3 && parts[1] === "nexus") {
      return {
        method: "subdomain",
        value: parts[0],
      };
    }
    return null;
  }

  // Resolve tenant from path
  static fromPath(pathname: string): TenantResolution | null {
    // Path-based resolution (e.g., /tenant/acme)
    const match = pathname.match(/^\/tenant\/([^\/]+)/);
    if (match) {
      return {
        method: "path",
        value: match[1],
      };
    }
    return null;
  }

  // Resolve tenant from header
  static fromHeader(headers: Headers): TenantResolution | null {
    const tenantId = headers.get("x-tenant-id");
    const tenantSlug = headers.get("x-tenant-slug");
    
    if (tenantId) {
      return {
        method: "header",
        value: tenantId,
        tenantId,
      };
    }
    
    if (tenantSlug) {
      return {
        method: "header",
        value: tenantSlug,
      };
    }
    
    return null;
  }

  // Main resolution method
  static resolve(request: {
    hostname: string;
    pathname: string;
    headers: Headers;
  }): TenantResolution | null {
    // Try different resolution methods in order of priority
    
    // 1. Header-based (for API calls)
    const headerResolution = this.fromHeader(request.headers);
    if (headerResolution) return headerResolution;
    
    // 2. Custom domain
    const domainResolution = this.fromDomain(request.hostname);
    if (domainResolution) return domainResolution;
    
    // 3. Subdomain
    const subdomainResolution = this.fromSubdomain(request.hostname);
    if (subdomainResolution) return subdomainResolution;
    
    // 4. Path-based
    const pathResolution = this.fromPath(request.pathname);
    if (pathResolution) return pathResolution;
    
    return null;
  }

  // Validate tenant slug format
  static isValidSlug(slug: string): boolean {
    return /^[a-z0-9-]+$/.test(slug) && slug.length >= 3 && slug.length <= 50;
  }

  // Generate tenant URL
  static generateURL(slug: string, baseURL: string = "https://nexus.com"): string {
    return `https://${slug}.${new URL(baseURL).hostname}`;
  }

  // Extract tenant info from URL
  static parseURL(url: string): { slug?: string; domain?: string } | null {
    try {
      const parsed = new URL(url);
      
      // Check for subdomain
      const subdomainMatch = this.fromSubdomain(parsed.hostname);
      if (subdomainMatch) {
        return { slug: subdomainMatch.value };
      }
      
      // Check for custom domain
      const domainMatch = this.fromDomain(parsed.hostname);
      if (domainMatch) {
        return { domain: domainMatch.value };
      }
      
      return null;
    } catch {
      return null;
    }
  }
}

// Browser-specific utilities
export const getBrowserTenantResolution = (): TenantResolution | null => {
  if (typeof window === "undefined") return null;
  
  return TenantResolver.resolve({
    hostname: window.location.hostname,
    pathname: window.location.pathname,
    headers: new Headers(),
  });
};

// Next.js request utilities
export const getRequestTenantResolution = (request: Request): TenantResolution | null => {
  const url = new URL(request.url);
  
  return TenantResolver.resolve({
    hostname: url.hostname,
    pathname: url.pathname,
    headers: request.headers,
  });
};
