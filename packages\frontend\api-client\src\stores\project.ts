import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { restApiClient } from "../rest";
import { endpoints } from "../config";
import { ProjectStore, Project, CreateProjectInput, UpdateProjectInput, PaginationParams, SearchParams } from "../types";

interface ProjectState extends ProjectStore {
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  addProject: (project: Project) => void;
  updateProjectInList: (id: string, updates: Partial<Project>) => void;
  removeProject: (id: string) => void;
}

export const useProjectStore = create<ProjectState>()(
  immer((set, get) => ({
    projects: [],
    currentProject: null,
    isLoading: false,
    error: null,

    setLoading: (loading) =>
      set((state) => {
        state.isLoading = loading;
      }),

    setError: (error) =>
      set((state) => {
        state.error = error;
      }),

    addProject: (project) =>
      set((state) => {
        state.projects.push(project);
      }),

    updateProjectInList: (id, updates) =>
      set((state) => {
        const index = state.projects.findIndex(p => p.id === id);
        if (index !== -1) {
          state.projects[index] = { ...state.projects[index], ...updates };
        }
        if (state.currentProject?.id === id) {
          state.currentProject = { ...state.currentProject, ...updates };
        }
      }),

    removeProject: (id) =>
      set((state) => {
        state.projects = state.projects.filter(p => p.id !== id);
        if (state.currentProject?.id === id) {
          state.currentProject = null;
        }
      }),

    fetchProjects: async (workspaceId?: string, teamId?: string, pagination?: PaginationParams, search?: SearchParams) => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (workspaceId) params.append("workspaceId", workspaceId);
        if (teamId) params.append("teamId", teamId);
        if (pagination?.page) params.append("page", pagination.page.toString());
        if (pagination?.limit) params.append("limit", pagination.limit.toString());
        if (pagination?.sort) params.append("sort", pagination.sort);
        if (pagination?.order) params.append("order", pagination.order);
        if (search?.query) params.append("query", search.query);
        if (search?.filters) {
          Object.entries(search.filters).forEach(([key, value]) => {
            params.append(`filters[${key}]`, value.toString());
          });
        }

        const response = await restApiClient.get(
          `${endpoints.projects.list}?${params.toString()}`
        );

        set((state) => {
          state.projects = response.data.items || response.data;
        });
      } catch (error: any) {
        setError(error.message || "Failed to fetch projects");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    createProject: async (input: CreateProjectInput) => {
      const { setLoading, setError, addProject } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.post(endpoints.projects.create, input);
        const project = response.data;

        addProject(project);
        return project;
      } catch (error: any) {
        setError(error.message || "Failed to create project");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    updateProject: async (id: string, input: UpdateProjectInput) => {
      const { setLoading, setError, updateProjectInList } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.put(endpoints.projects.update(id), input);
        const project = response.data;

        updateProjectInList(id, project);
        return project;
      } catch (error: any) {
        setError(error.message || "Failed to update project");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    deleteProject: async (id: string) => {
      const { setLoading, setError, removeProject } = get();
      
      try {
        setLoading(true);
        setError(null);

        await restApiClient.delete(endpoints.projects.delete(id));
        removeProject(id);
      } catch (error: any) {
        setError(error.message || "Failed to delete project");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    setCurrentProject: (project) =>
      set((state) => {
        state.currentProject = project;
        
        // Update API client context
        if (project) {
          restApiClient.setContext({
            workspaceId: project.workspaceId,
            teamId: project.teamId,
          });
        }
      }),
  }))
);

// Project utilities
export const projectUtils = {
  // Get project by ID
  getProjectById: (id: string): Project | undefined => {
    const { projects } = useProjectStore.getState();
    return projects.find(p => p.id === id);
  },

  // Get current project
  getCurrentProject: (): Project | null => {
    return useProjectStore.getState().currentProject;
  },

  // Get projects by workspace
  getProjectsByWorkspace: (workspaceId: string): Project[] => {
    const { projects } = useProjectStore.getState();
    return projects.filter(p => p.workspaceId === workspaceId);
  },

  // Get projects by team
  getProjectsByTeam: (teamId: string): Project[] => {
    const { projects } = useProjectStore.getState();
    return projects.filter(p => p.teamId === teamId);
  },

  // Check if user is project owner
  isProjectOwner: (projectId: string, userId: string): boolean => {
    const project = projectUtils.getProjectById(projectId);
    return project?.ownerId === userId;
  },

  // Get project stats
  getProjectStats: (project: Project) => ({
    files: project.fileCount,
    storage: projectUtils.formatStorageSize(project.storageUsed),
    status: project.status,
    isActive: project.isActive,
    lastActivity: project.lastActivity,
  }),

  // Format storage size
  formatStorageSize: (bytes: number): string => {
    const units = ["B", "KB", "MB", "GB", "TB"];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  },

  // Get project status color
  getStatusColor: (status: Project["status"]): string => {
    switch (status) {
      case "ACTIVE":
        return "green";
      case "COMPLETED":
        return "blue";
      case "ON_HOLD":
        return "yellow";
      case "ARCHIVED":
        return "gray";
      default:
        return "gray";
    }
  },

  // Get project activity level
  getActivityLevel: (project: Project): "high" | "medium" | "low" => {
    if (!project.lastActivity) return "low";
    
    const daysSinceActivity = Math.floor(
      (Date.now() - new Date(project.lastActivity).getTime()) / (1000 * 60 * 60 * 24)
    );
    
    if (daysSinceActivity <= 1) return "high";
    if (daysSinceActivity <= 7) return "medium";
    return "low";
  },

  // Filter projects by status
  filterByStatus: (status: Project["status"]): Project[] => {
    const { projects } = useProjectStore.getState();
    return projects.filter(p => p.status === status);
  },

  // Search projects
  searchProjects: (query: string): Project[] => {
    const { projects } = useProjectStore.getState();
    const lowercaseQuery = query.toLowerCase();
    
    return projects.filter(p =>
      p.name.toLowerCase().includes(lowercaseQuery) ||
      p.description?.toLowerCase().includes(lowercaseQuery)
    );
  },
};
