export declare const APP_CONFIG: {
    readonly name: "Nexus SaaS Starter";
    readonly version: "1.0.0";
    readonly description: "Enterprise Multi-Tenant SaaS Foundation";
    readonly url: any;
};
export declare const API_CONFIG: {
    readonly baseUrl: any;
    readonly timeout: 30000;
    readonly retries: 3;
};
export declare const DATABASE_CONFIG: {
    readonly maxConnections: 100;
    readonly connectionTimeout: 30000;
    readonly idleTimeout: 600000;
};
export declare const AUTH_CONFIG: {
    readonly sessionDuration: number;
    readonly refreshThreshold: number;
    readonly maxLoginAttempts: 5;
    readonly lockoutDuration: number;
};
export declare const TENANT_CONFIG: {
    readonly maxWorkspaces: 10;
    readonly maxUsersPerWorkspace: 100;
    readonly defaultPlan: "starter";
};
export declare const VALIDATION_RULES: {
    readonly email: {
        readonly minLength: 5;
        readonly maxLength: 254;
        readonly pattern: RegExp;
    };
    readonly password: {
        readonly minLength: 8;
        readonly maxLength: 128;
        readonly requireUppercase: true;
        readonly requireLowercase: true;
        readonly requireNumbers: true;
        readonly requireSpecialChars: true;
    };
    readonly name: {
        readonly minLength: 2;
        readonly maxLength: 50;
        readonly pattern: RegExp;
    };
    readonly slug: {
        readonly minLength: 3;
        readonly maxLength: 50;
        readonly pattern: RegExp;
    };
};
export declare const HTTP_STATUS: {
    readonly OK: 200;
    readonly CREATED: 201;
    readonly NO_CONTENT: 204;
    readonly BAD_REQUEST: 400;
    readonly UNAUTHORIZED: 401;
    readonly FORBIDDEN: 403;
    readonly NOT_FOUND: 404;
    readonly CONFLICT: 409;
    readonly UNPROCESSABLE_ENTITY: 422;
    readonly INTERNAL_SERVER_ERROR: 500;
    readonly SERVICE_UNAVAILABLE: 503;
};
export declare const ROUTES: {
    readonly HOME: "/";
    readonly LOGIN: "/auth/login";
    readonly REGISTER: "/auth/register";
    readonly DASHBOARD: "/dashboard";
    readonly SETTINGS: "/settings";
    readonly PROFILE: "/profile";
    readonly WORKSPACES: "/workspaces";
    readonly ADMIN: "/admin";
};
export declare const PERMISSIONS: {
    readonly READ: "read";
    readonly WRITE: "write";
    readonly DELETE: "delete";
    readonly ADMIN: "admin";
};
export declare const ROLES: {
    readonly SUPER_ADMIN: "super_admin";
    readonly ADMIN: "admin";
    readonly USER: "user";
    readonly VIEWER: "viewer";
};
export declare const SUBSCRIPTION_PLANS: {
    readonly FREE: "free";
    readonly STARTER: "starter";
    readonly PRO: "pro";
    readonly ENTERPRISE: "enterprise";
};
export declare const EVENTS: {
    readonly USER_CREATED: "user.created";
    readonly USER_UPDATED: "user.updated";
    readonly USER_DELETED: "user.deleted";
    readonly WORKSPACE_CREATED: "workspace.created";
    readonly WORKSPACE_UPDATED: "workspace.updated";
    readonly WORKSPACE_DELETED: "workspace.deleted";
    readonly SUBSCRIPTION_CREATED: "subscription.created";
    readonly SUBSCRIPTION_UPDATED: "subscription.updated";
    readonly SUBSCRIPTION_CANCELLED: "subscription.cancelled";
};
//# sourceMappingURL=index.d.ts.map