import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { restApiClient } from "../rest";
import { endpoints } from "../config";
import { Role, Permission } from "../types";

interface RoleState {
  roles: Role[];
  permissions: Permission[];
  isLoading: boolean;
  error: string | null;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  addRole: (role: Role) => void;
  updateRoleInList: (id: string, updates: Partial<Role>) => void;
  removeRole: (id: string) => void;
  fetchRoles: () => Promise<void>;
  fetchPermissions: () => Promise<void>;
  createRole: (input: any) => Promise<Role>;
  updateRole: (id: string, input: any) => Promise<Role>;
  deleteRole: (id: string) => Promise<void>;
}

export const useRoleStore = create<RoleState>()(
  immer((set, get) => ({
    roles: [],
    permissions: [],
    isLoading: false,
    error: null,

    setLoading: (loading) =>
      set((state) => {
        state.isLoading = loading;
      }),

    setError: (error) =>
      set((state) => {
        state.error = error;
      }),

    addRole: (role) =>
      set((state) => {
        state.roles.push(role);
      }),

    updateRoleInList: (id, updates) =>
      set((state) => {
        const index = state.roles.findIndex(r => r.id === id);
        if (index !== -1) {
          state.roles[index] = { ...state.roles[index], ...updates };
        }
      }),

    removeRole: (id) =>
      set((state) => {
        state.roles = state.roles.filter(r => r.id !== id);
      }),

    fetchRoles: async () => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.get(endpoints.roles.list);

        set((state) => {
          state.roles = response.data.items || response.data;
        });
      } catch (error: any) {
        setError(error.message || "Failed to fetch roles");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    fetchPermissions: async () => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.get(endpoints.roles.permissions);

        set((state) => {
          state.permissions = response.data;
        });
      } catch (error: any) {
        setError(error.message || "Failed to fetch permissions");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    createRole: async (input: any) => {
      const { setLoading, setError, addRole } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.post(endpoints.roles.create, input);
        const role = response.data;

        addRole(role);
        return role;
      } catch (error: any) {
        setError(error.message || "Failed to create role");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    updateRole: async (id: string, input: any) => {
      const { setLoading, setError, updateRoleInList } = get();
      
      try {
        setLoading(true);
        setError(null);

        const response = await restApiClient.put(endpoints.roles.update(id), input);
        const role = response.data;

        updateRoleInList(id, role);
        return role;
      } catch (error: any) {
        setError(error.message || "Failed to update role");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    deleteRole: async (id: string) => {
      const { setLoading, setError, removeRole } = get();
      
      try {
        setLoading(true);
        setError(null);

        await restApiClient.delete(endpoints.roles.delete(id));
        removeRole(id);
      } catch (error: any) {
        setError(error.message || "Failed to delete role");
        throw error;
      } finally {
        setLoading(false);
      }
    },
  }))
);

// Role utilities
export const roleUtils = {
  // Get role by ID
  getRoleById: (id: string): Role | undefined => {
    const { roles } = useRoleStore.getState();
    return roles.find(r => r.id === id);
  },

  // Get role by slug
  getRoleBySlug: (slug: string): Role | undefined => {
    const { roles } = useRoleStore.getState();
    return roles.find(r => r.slug === slug);
  },

  // Get roles by level
  getRolesByLevel: (level: Role["level"]): Role[] => {
    const { roles } = useRoleStore.getState();
    return roles.filter(r => r.level === level);
  },

  // Get system roles
  getSystemRoles: (): Role[] => {
    const { roles } = useRoleStore.getState();
    return roles.filter(r => r.isSystem);
  },

  // Get custom roles
  getCustomRoles: (): Role[] => {
    const { roles } = useRoleStore.getState();
    return roles.filter(r => !r.isSystem);
  },

  // Get active roles
  getActiveRoles: (): Role[] => {
    const { roles } = useRoleStore.getState();
    return roles.filter(r => r.isActive);
  },

  // Check if role has permission
  roleHasPermission: (roleId: string, resource: string, action: string): boolean => {
    const role = roleUtils.getRoleById(roleId);
    if (!role) return false;
    
    return role.permissions.some(p => 
      p.resource === resource && p.action === action
    );
  },

  // Get role permissions by resource
  getRolePermissionsByResource: (roleId: string, resource: string): Permission[] => {
    const role = roleUtils.getRoleById(roleId);
    if (!role) return [];
    
    return role.permissions.filter(p => p.resource === resource);
  },

  // Get role level color
  getLevelColor: (level: Role["level"]): string => {
    switch (level) {
      case "SYSTEM":
        return "red";
      case "ORGANIZATION":
        return "purple";
      case "WORKSPACE":
        return "blue";
      case "TEAM":
        return "green";
      case "USER":
        return "gray";
      default:
        return "gray";
    }
  },

  // Get role level description
  getLevelDescription: (level: Role["level"]): string => {
    switch (level) {
      case "SYSTEM":
        return "System-wide access across all tenants";
      case "ORGANIZATION":
        return "Organization-wide access within tenant";
      case "WORKSPACE":
        return "Workspace-level access";
      case "TEAM":
        return "Team-level access";
      case "USER":
        return "User-level access";
      default:
        return "Unknown level";
    }
  },

  // Search roles
  searchRoles: (query: string): Role[] => {
    const { roles } = useRoleStore.getState();
    const lowercaseQuery = query.toLowerCase();
    
    return roles.filter(r =>
      r.name.toLowerCase().includes(lowercaseQuery) ||
      r.slug.toLowerCase().includes(lowercaseQuery) ||
      r.description?.toLowerCase().includes(lowercaseQuery)
    );
  },

  // Get permission by ID
  getPermissionById: (id: string): Permission | undefined => {
    const { permissions } = useRoleStore.getState();
    return permissions.find(p => p.id === id);
  },

  // Get permissions by resource
  getPermissionsByResource: (resource: string): Permission[] => {
    const { permissions } = useRoleStore.getState();
    return permissions.filter(p => p.resource === resource);
  },

  // Get unique resources
  getUniqueResources: (): string[] => {
    const { permissions } = useRoleStore.getState();
    return [...new Set(permissions.map(p => p.resource))];
  },

  // Get unique actions for resource
  getActionsForResource: (resource: string): string[] => {
    const { permissions } = useRoleStore.getState();
    return [...new Set(
      permissions
        .filter(p => p.resource === resource)
        .map(p => p.action)
    )];
  },

  // Format permission display name
  formatPermissionName: (permission: Permission): string => {
    return `${permission.action}:${permission.resource}`;
  },

  // Get permission scope color
  getScopeColor: (scope: Permission["scope"]): string => {
    switch (scope) {
      case "SYSTEM":
        return "red";
      case "ORGANIZATION":
        return "purple";
      case "WORKSPACE":
        return "blue";
      case "TEAM":
        return "green";
      case "OWN":
        return "gray";
      default:
        return "gray";
    }
  },
};
