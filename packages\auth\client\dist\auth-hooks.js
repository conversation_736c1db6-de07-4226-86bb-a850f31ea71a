import { useAuth } from "./auth-context";
// Main auth hook
export { useAuth };
// Convenience hooks
export function useUser() {
    const { user } = useAuth();
    return user;
}
export function useIsAuthenticated() {
    const { isAuthenticated } = useAuth();
    return isAuthenticated;
}
export function useIsLoading() {
    const { isLoading } = useAuth();
    return isLoading;
}
// Role-based hooks
export function useHasRole(role) {
    const { user } = useAuth();
    return user?.role === role;
}
export function useHasPermission(permission) {
    const { user } = useAuth();
    return user?.permissions?.includes(permission) ?? false;
}
export function useIsOwner() {
    return useHasRole("OWNER");
}
export function useIsAdmin() {
    const { user } = useAuth();
    return user?.role === "OWNER" || user?.role === "ADMIN";
}
export function useCanRead() {
    return useHasPermission("read");
}
export function useCanWrite() {
    return useHasPermission("write");
}
export function useCanDelete() {
    return useHasPermission("delete");
}
// Tenant-based hooks
export function useTenantId() {
    const { user } = useAuth();
    return user?.tenantId ?? null;
}
// Auth actions hooks
export function useLogin() {
    const { login } = useAuth();
    return login;
}
export function useRegister() {
    const { register } = useAuth();
    return register;
}
export function useLogout() {
    const { logout } = useAuth();
    return logout;
}
