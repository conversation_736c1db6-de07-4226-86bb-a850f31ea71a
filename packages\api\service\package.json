{"name": "@nexus/api-service", "version": "0.1.0", "description": "RESTful API service foundation for Nexus SaaS", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "clean": "rm -rf dist", "test": "jest", "type-check": "tsc --noEmit"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "@nexus/utils": "workspace:*", "@nexus/validation": "workspace:*", "@nexus/database-schema": "workspace:*", "@nexus/database-service": "workspace:*", "@nexus/auth-client": "workspace:*", "@nexus/rbac": "workspace:*", "fastify": "^5.1.0", "@fastify/cors": "^10.0.1", "@fastify/helmet": "^12.0.1", "@fastify/rate-limit": "^10.1.1", "@fastify/jwt": "^9.0.1", "@fastify/swagger": "^9.1.0", "@fastify/swagger-ui": "^5.0.1", "@fastify/multipart": "^9.0.1", "@fastify/static": "^8.0.1", "zod": "^4.0.5", "pino": "^9.5.0", "pino-pretty": "^13.0.0", "dotenv": "^16.4.7"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@types/jest": "^29.5.0", "@types/node": "^22.10.2", "jest": "^29.5.0", "tsx": "^4.19.2", "typescript": "^5.8.0"}}