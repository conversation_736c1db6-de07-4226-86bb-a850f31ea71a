{"name": "@nexus/api-service", "version": "0.1.0", "description": "RESTful API service foundation for Nexus SaaS", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "clean": "rm -rf dist", "test": "jest", "type-check": "tsc --noEmit"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "@nexus/utils": "workspace:*", "@nexus/validation": "workspace:*", "@nexus/database-schema": "workspace:*", "@nexus/database-service": "workspace:*", "@nexus/auth-client": "workspace:*", "@nexus/rbac": "workspace:*", "fastify": "5.4.0", "@fastify/cors": "11.0.1", "@fastify/helmet": "13.0.1", "@fastify/rate-limit": "10.3.0", "@fastify/jwt": "9.1.0", "@fastify/swagger": "9.5.1", "@fastify/swagger-ui": "5.2.3", "@fastify/multipart": "9.0.3", "@fastify/static": "8.2.0", "zod": "4.0.5", "pino": "9.7.0", "pino-pretty": "13.0.0", "dotenv": "17.2.0"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@types/jest": "30.0.0", "@types/node": "24.0.15", "jest": "30.0.4", "tsx": "4.20.3", "typescript": "5.8.3"}}