{"name": "nexus-saas-starter", "private": true, "packageManager": "pnpm@8.15.0", "engines": {"node": ">=18.18.0", "pnpm": ">=8.15.0"}, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "test": "turbo run test", "test:watch": "turbo run test:watch", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "type-check": "turbo run type-check", "clean": "turbo run clean", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md}\"", "prepare": "husky install"}, "devDependencies": {"@nexus/eslint-config": "workspace:*", "@nexus/prettier-config": "workspace:*", "@nexus/tsconfig": "workspace:*", "eslint": "^9.31.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.2.0", "turbo": "^1.13.4", "typescript": "^5.8.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}