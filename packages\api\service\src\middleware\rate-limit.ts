import { FastifyInstance, FastifyRequest } from "fastify";
import fp from "fastify-plugin";
import { config } from "../config";
import { RateLimitError } from "../types";

// Rate limit configuration
interface RateLimitConfig {
  max: number;
  timeWindow: string;
  skipOnError?: boolean;
  skipSuccessfulRequests?: boolean;
  keyGenerator?: (request: FastifyRequest) => string;
  errorResponseBuilder?: (request: FastifyRequest, context: any) => any;
}

// Default rate limit plugin
export const rateLimitPlugin = fp(async (fastify: FastifyInstance) => {
  if (!config.ENABLE_RATE_LIMIT) {
    return;
  }

  await fastify.register(require("@fastify/rate-limit"), {
    max: config.RATE_LIMIT_MAX,
    timeWindow: config.RATE_LIMIT_WINDOW,
    skipOnError: true,
    keyGenerator: (request: FastifyRequest) => {
      // Use user ID if authenticated, otherwise IP
      const authRequest = request as any;
      return authRequest.user?.id || request.ip;
    },
    errorResponseBuilder: (request: FastifyRequest, context: any) => {
      return {
        success: false,
        error: {
          code: "RATE_LIMIT_ERROR",
          message: `Rate limit exceeded. Try again in ${Math.round(context.ttl / 1000)} seconds.`,
          details: {
            limit: context.max,
            remaining: context.remaining,
            resetTime: new Date(Date.now() + context.ttl),
          },
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: process.env.npm_package_version || "unknown",
        },
      };
    },
  });
});

// Custom rate limit middleware factory
export const createRateLimit = (config: RateLimitConfig) => {
  return async (request: FastifyRequest, reply: any) => {
    // This would be implemented with a custom rate limiting solution
    // For now, we'll use the global rate limit
  };
};

// Strict rate limit for sensitive endpoints
export const strictRateLimit = createRateLimit({
  max: 10,
  timeWindow: "1m",
  keyGenerator: (request: FastifyRequest) => {
    const authRequest = request as any;
    return `strict:${authRequest.user?.id || request.ip}`;
  },
});

// Auth rate limit for login/register endpoints
export const authRateLimit = createRateLimit({
  max: 5,
  timeWindow: "15m",
  keyGenerator: (request: FastifyRequest) => {
    return `auth:${request.ip}`;
  },
});

// API key rate limit
export const apiKeyRateLimit = createRateLimit({
  max: 1000,
  timeWindow: "1h",
  keyGenerator: (request: FastifyRequest) => {
    const apiKey = request.headers["x-api-key"] as string;
    return `api:${apiKey}`;
  },
});

// Upload rate limit
export const uploadRateLimit = createRateLimit({
  max: 20,
  timeWindow: "1h",
  keyGenerator: (request: FastifyRequest) => {
    const authRequest = request as any;
    return `upload:${authRequest.user?.id || request.ip}`;
  },
});
