// Billing dashboard types

export interface Invoice {
  id: string;
  number: string;
  stripeInvoiceId?: string;
  customerId: string;
  subscriptionId?: string;
  status: "draft" | "open" | "paid" | "void" | "uncollectible";
  currency: string;
  
  // Amounts
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  total: number;
  amountPaid: number;
  amountDue: number;
  
  // Dates
  issueDate: Date;
  dueDate?: Date;
  paidAt?: Date;
  voidedAt?: Date;
  
  // Customer info
  customerName: string;
  customerEmail: string;
  billingAddress?: BillingAddress;
  
  // Items
  items: InvoiceItem[];
  
  // Files
  pdfUrl?: string;
  hostedInvoiceUrl?: string;
  
  // Metadata
  description?: string;
  footer?: string;
  metadata: Record<string, string>;
  
  createdAt: Date;
  updatedAt: Date;
}

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitAmount: number;
  amount: number;
  currency: string;
  period?: {
    start: Date;
    end: Date;
  };
  proration?: boolean;
  metadata: Record<string, string>;
}

export interface BillingAddress {
  line1: string;
  line2?: string;
  city: string;
  state?: string;
  postalCode?: string;
  country: string;
}

export interface PaymentRecord {
  id: string;
  invoiceId?: string;
  subscriptionId?: string;
  customerId: string;
  stripePaymentIntentId?: string;
  
  // Payment details
  amount: number;
  currency: string;
  status: "succeeded" | "pending" | "failed" | "canceled" | "requires_action";
  paymentMethod: PaymentMethodInfo;
  
  // Dates
  createdAt: Date;
  processedAt?: Date;
  failedAt?: Date;
  
  // Additional info
  description?: string;
  receiptUrl?: string;
  failureReason?: string;
  metadata: Record<string, string>;
}

export interface PaymentMethodInfo {
  id: string;
  type: "card" | "bank_account" | "paypal" | "apple_pay" | "google_pay";
  card?: {
    brand: string;
    last4: string;
    expMonth: number;
    expYear: number;
  };
  bankAccount?: {
    bankName: string;
    last4: string;
    accountType: string;
  };
}

export interface BillingPeriod {
  start: Date;
  end: Date;
  status: "current" | "upcoming" | "past";
}

export interface UsageMetrics {
  period: BillingPeriod;
  metrics: Array<{
    name: string;
    value: number;
    unit: string;
    limit?: number;
    cost?: number;
  }>;
  totalCost: number;
  projectedCost: number;
}

export interface BillingSettings {
  id: string;
  customerId: string;
  
  // Invoice settings
  invoicePrefix: string;
  invoiceFooter?: string;
  paymentTerms: number; // days
  
  // Notification settings
  emailInvoices: boolean;
  emailReceipts: boolean;
  emailFailures: boolean;
  
  // Tax settings
  taxExempt: boolean;
  taxIds: Array<{
    type: string;
    value: string;
  }>;
  
  // Billing address
  billingAddress?: BillingAddress;
  
  createdAt: Date;
  updatedAt: Date;
}

export interface BillingStats {
  totalRevenue: number;
  monthlyRecurringRevenue: number;
  averageRevenuePerUser: number;
  churnRate: number;
  lifetimeValue: number;
  
  // Current period
  currentPeriodRevenue: number;
  currentPeriodInvoices: number;
  currentPeriodPayments: number;
  
  // Trends
  revenueGrowth: number;
  customerGrowth: number;
  
  // Upcoming
  upcomingInvoices: number;
  upcomingRevenue: number;
}

export interface InvoiceFilters {
  status?: Invoice["status"][];
  dateRange?: {
    start: Date;
    end: Date;
  };
  customerId?: string;
  subscriptionId?: string;
  amountRange?: {
    min: number;
    max: number;
  };
  search?: string;
}

export interface PaymentFilters {
  status?: PaymentRecord["status"][];
  dateRange?: {
    start: Date;
    end: Date;
  };
  paymentMethod?: string[];
  amountRange?: {
    min: number;
    max: number;
  };
  search?: string;
}
