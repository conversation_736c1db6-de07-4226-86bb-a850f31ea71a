import { FastifyInstance } from "fastify";
import { z } from "zod";
import { validate, validatePagination, validateId } from "../middleware";
import { canRead, canCreate, canWrite, canDelete } from "../middleware/rbac";
import { ApiResponse } from "../types";

const createWorkspaceSchema = z.object({
  name: z.string().min(2),
  slug: z.string().min(2),
  description: z.string().optional(),
  settings: z.object({}).optional(),
});

const updateWorkspaceSchema = z.object({
  name: z.string().min(2).optional(),
  description: z.string().optional(),
  settings: z.object({}).optional(),
  isActive: z.boolean().optional(),
});

export const workspaceRoutes = async (fastify: FastifyInstance) => {
  // Get workspaces
  fastify.get("/", {
    schema: {
      tags: ["Workspaces"],
      summary: "List workspaces",
      description: "Get a paginated list of workspaces",
      security: [{ bearerAuth: [] }],
      querystring: {
        type: "object",
        properties: {
          page: { type: "integer", minimum: 1, default: 1 },
          limit: { type: "integer", minimum: 1, maximum: 100, default: 20 },
          search: { type: "string" },
          isActive: { type: "boolean" },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                workspaces: {
                  type: "array",
                  items: { $ref: "#/components/schemas/Workspace" },
                },
                pagination: { $ref: "#/components/schemas/Pagination" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
      },
    },
    preHandler: [fastify.authenticate, canRead("workspace"), validatePagination],
    handler: async (request, reply): Promise<ApiResponse> => {
      const query = request.query as any;
      
      // TODO: Implement actual workspace fetching
      const mockWorkspaces = [
        {
          id: "workspace_1",
          name: "Main Workspace",
          slug: "main-workspace",
          description: "Primary workspace for the organization",
          tenantId: "tenant_123",
          ownerId: "user_123",
          settings: {},
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      return {
        success: true,
        data: {
          workspaces: mockWorkspaces,
          pagination: {
            page: query.page || 1,
            limit: query.limit || 20,
            total: mockWorkspaces.length,
            totalPages: 1,
          },
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Get workspace by ID
  fastify.get("/:id", {
    schema: {
      tags: ["Workspaces"],
      summary: "Get workspace by ID",
      description: "Get a specific workspace by its ID",
      security: [{ bearerAuth: [] }],
      params: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
        },
        required: ["id"],
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                workspace: { $ref: "#/components/schemas/Workspace" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [fastify.authenticate, canRead("workspace"), validateId],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { id } = request.params as { id: string };
      
      // TODO: Implement actual workspace fetching
      const mockWorkspace = {
        id,
        name: "Main Workspace",
        slug: "main-workspace",
        description: "Primary workspace for the organization",
        tenantId: "tenant_123",
        ownerId: "user_123",
        settings: {},
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return {
        success: true,
        data: {
          workspace: mockWorkspace,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Create workspace
  fastify.post("/", {
    schema: {
      tags: ["Workspaces"],
      summary: "Create workspace",
      description: "Create a new workspace",
      security: [{ bearerAuth: [] }],
      body: {
        type: "object",
        properties: {
          name: { type: "string", minLength: 2 },
          slug: { type: "string", minLength: 2 },
          description: { type: "string" },
          settings: { type: "object" },
        },
        required: ["name", "slug"],
      },
      response: {
        201: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                workspace: { $ref: "#/components/schemas/Workspace" },
              },
            },
          },
        },
        400: { $ref: "#/components/responses/BadRequest" },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
      },
    },
    preHandler: [fastify.authenticate, canCreate("workspace"), validate({ body: createWorkspaceSchema })],
    handler: async (request, reply): Promise<ApiResponse> => {
      const workspaceData = request.body as z.infer<typeof createWorkspaceSchema>;
      
      // TODO: Implement actual workspace creation
      const newWorkspace = {
        id: `workspace_${Date.now()}`,
        name: workspaceData.name,
        slug: workspaceData.slug,
        description: workspaceData.description,
        tenantId: (request as any).user.tenantId,
        ownerId: (request as any).user.id,
        settings: workspaceData.settings || {},
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      reply.status(201);
      return {
        success: true,
        data: {
          workspace: newWorkspace,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Update workspace
  fastify.patch("/:id", {
    schema: {
      tags: ["Workspaces"],
      summary: "Update workspace",
      description: "Update an existing workspace",
      security: [{ bearerAuth: [] }],
      params: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
        },
        required: ["id"],
      },
      body: {
        type: "object",
        properties: {
          name: { type: "string", minLength: 2 },
          description: { type: "string" },
          settings: { type: "object" },
          isActive: { type: "boolean" },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                workspace: { $ref: "#/components/schemas/Workspace" },
              },
            },
          },
        },
        400: { $ref: "#/components/responses/BadRequest" },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [
      fastify.authenticate,
      canWrite("workspace"),
      validateId,
      validate({ body: updateWorkspaceSchema }),
    ],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { id } = request.params as { id: string };
      const updates = request.body as z.infer<typeof updateWorkspaceSchema>;
      
      // TODO: Implement actual workspace update
      const updatedWorkspace = {
        id,
        name: updates.name || "Workspace Name",
        slug: "workspace-slug",
        description: updates.description,
        tenantId: "tenant_123",
        ownerId: "user_123",
        settings: updates.settings || {},
        isActive: updates.isActive ?? true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return {
        success: true,
        data: {
          workspace: updatedWorkspace,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Delete workspace
  fastify.delete("/:id", {
    schema: {
      tags: ["Workspaces"],
      summary: "Delete workspace",
      description: "Delete a workspace",
      security: [{ bearerAuth: [] }],
      params: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
        },
        required: ["id"],
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                message: { type: "string" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [fastify.authenticate, canDelete("workspace"), validateId],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { id } = request.params as { id: string };
      
      // TODO: Implement actual workspace deletion
      
      return {
        success: true,
        data: {
          message: "Workspace deleted successfully",
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });
};
