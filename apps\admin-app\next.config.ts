import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  experimental: {
    turbo: {
      rules: {
        "*.svg": {
          loaders: ["@svgr/webpack"],
          as: "*.js",
        },
      },
    },
  },
  transpilePackages: [
    "@nexus/types",
    "@nexus/constants", 
    "@nexus/utils",
    "@nexus/validation",
    "@nexus/database-schema",
    "@nexus/database-service",
    "@nexus/auth-client",
    "@nexus/auth-server",
    "@nexus/tenant-context",
  ],
};

export default nextConfig;
