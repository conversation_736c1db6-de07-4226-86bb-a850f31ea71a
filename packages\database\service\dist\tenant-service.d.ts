import { CreateTenant } from "@nexus/validation";
export declare class TenantService {
    create(data: CreateTenant): Promise<any>;
    findById(id: string): Promise<any | null>;
    findBySlug(slug: string): Promise<any | null>;
    findByDomain(domain: string): Promise<any | null>;
    update(id: string, data: Partial<CreateTenant>): Promise<any>;
    updateStatus(id: string, status: any): Promise<any>;
    updatePlan(id: string, plan: any): Promise<any>;
    delete(id: string): Promise<any>;
    hardDelete(id: string): Promise<any>;
    list(page?: number, limit?: number): Promise<{
        data: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>;
    isSlugAvailable(slug: string): Promise<boolean>;
    isDomainAvailable(domain: string): Promise<boolean>;
}
export declare const tenantService: TenantService;
//# sourceMappingURL=tenant-service.d.ts.map