import { Tenant, TenantStatus, SubscriptionPlan } from "@nexus/database-schema";
import { CreateTenant } from "@nexus/validation";
export declare class TenantService {
    create(data: CreateTenant): Promise<Tenant>;
    findById(id: string): Promise<Tenant | null>;
    findBySlug(slug: string): Promise<Tenant | null>;
    findByDomain(domain: string): Promise<Tenant | null>;
    update(id: string, data: Partial<CreateTenant>): Promise<Tenant>;
    updateStatus(id: string, status: TenantStatus): Promise<Tenant>;
    updatePlan(id: string, plan: SubscriptionPlan): Promise<Tenant>;
    delete(id: string): Promise<Tenant>;
    hardDelete(id: string): Promise<Tenant>;
    list(page?: number, limit?: number): Promise<{
        data: any;
        pagination: {
            page: number;
            limit: number;
            total: any;
            totalPages: number;
        };
    }>;
    isSlugAvailable(slug: string): Promise<boolean>;
    isDomainAvailable(domain: string): Promise<boolean>;
}
export declare const tenantService: TenantService;
//# sourceMappingURL=tenant-service.d.ts.map