import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { restApiClient } from "../rest";
import { endpoints } from "../config";
import { DashboardMetrics } from "../types";

interface AnalyticsState {
  dashboardMetrics: DashboardMetrics | null;
  userActivity: any | null;
  projectAnalytics: any | null;
  apiUsage: any | null;
  isLoading: boolean;
  error: string | null;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  fetchDashboardMetrics: (input?: any) => Promise<void>;
  fetchUserActivity: (input?: any) => Promise<void>;
  fetchProjectAnalytics: (input?: any) => Promise<void>;
  fetchApiUsage: (input?: any) => Promise<void>;
}

export const useAnalyticsStore = create<AnalyticsState>()(
  immer((set, get) => ({
    dashboardMetrics: null,
    userActivity: null,
    projectAnalytics: null,
    apiUsage: null,
    isLoading: false,
    error: null,

    setLoading: (loading) =>
      set((state) => {
        state.isLoading = loading;
      }),

    setError: (error) =>
      set((state) => {
        state.error = error;
      }),

    fetchDashboardMetrics: async (input?: any) => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (input?.startDate) params.append("startDate", input.startDate);
        if (input?.endDate) params.append("endDate", input.endDate);
        if (input?.workspaceId) params.append("workspaceId", input.workspaceId);

        const response = await restApiClient.get(
          `${endpoints.analytics.dashboard}?${params.toString()}`
        );

        set((state) => {
          state.dashboardMetrics = response.data;
        });
      } catch (error: any) {
        setError(error.message || "Failed to fetch dashboard metrics");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    fetchUserActivity: async (input?: any) => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (input?.startDate) params.append("startDate", input.startDate);
        if (input?.endDate) params.append("endDate", input.endDate);
        if (input?.workspaceId) params.append("workspaceId", input.workspaceId);
        if (input?.groupBy) params.append("groupBy", input.groupBy);

        const response = await restApiClient.get(
          `${endpoints.analytics.userActivity}?${params.toString()}`
        );

        set((state) => {
          state.userActivity = response.data;
        });
      } catch (error: any) {
        setError(error.message || "Failed to fetch user activity");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    fetchProjectAnalytics: async (input?: any) => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (input?.startDate) params.append("startDate", input.startDate);
        if (input?.endDate) params.append("endDate", input.endDate);
        if (input?.workspaceId) params.append("workspaceId", input.workspaceId);
        if (input?.teamId) params.append("teamId", input.teamId);

        const response = await restApiClient.get(
          `${endpoints.analytics.projectAnalytics}?${params.toString()}`
        );

        set((state) => {
          state.projectAnalytics = response.data;
        });
      } catch (error: any) {
        setError(error.message || "Failed to fetch project analytics");
        throw error;
      } finally {
        setLoading(false);
      }
    },

    fetchApiUsage: async (input?: any) => {
      const { setLoading, setError } = get();
      
      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (input?.startDate) params.append("startDate", input.startDate);
        if (input?.endDate) params.append("endDate", input.endDate);
        if (input?.endpoint) params.append("endpoint", input.endpoint);
        if (input?.groupBy) params.append("groupBy", input.groupBy);

        const response = await restApiClient.get(
          `${endpoints.analytics.apiUsage}?${params.toString()}`
        );

        set((state) => {
          state.apiUsage = response.data;
        });
      } catch (error: any) {
        setError(error.message || "Failed to fetch API usage");
        throw error;
      } finally {
        setLoading(false);
      }
    },
  }))
);

// Analytics utilities
export const analyticsUtils = {
  // Format percentage
  formatPercentage: (value: number): string => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  },

  // Format large numbers
  formatNumber: (value: number): string => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    }
    if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  },

  // Format storage size
  formatStorageSize: (bytes: number): string => {
    const units = ["B", "KB", "MB", "GB", "TB"];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  },

  // Get trend color
  getTrendColor: (value: number): string => {
    if (value > 0) return "green";
    if (value < 0) return "red";
    return "gray";
  },

  // Get trend icon
  getTrendIcon: (value: number): string => {
    if (value > 0) return "↗️";
    if (value < 0) return "↘️";
    return "➡️";
  },

  // Calculate growth rate
  calculateGrowthRate: (current: number, previous: number): number => {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  },

  // Get date range options
  getDateRangeOptions: () => [
    { label: "Last 7 days", value: "7d" },
    { label: "Last 30 days", value: "30d" },
    { label: "Last 90 days", value: "90d" },
    { label: "Last 6 months", value: "6m" },
    { label: "Last year", value: "1y" },
    { label: "Custom", value: "custom" },
  ],

  // Convert date range to dates
  dateRangeToDate: (range: string): { startDate: Date; endDate: Date } => {
    const endDate = new Date();
    let startDate = new Date();

    switch (range) {
      case "7d":
        startDate.setDate(endDate.getDate() - 7);
        break;
      case "30d":
        startDate.setDate(endDate.getDate() - 30);
        break;
      case "90d":
        startDate.setDate(endDate.getDate() - 90);
        break;
      case "6m":
        startDate.setMonth(endDate.getMonth() - 6);
        break;
      case "1y":
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    return { startDate, endDate };
  },

  // Format date for display
  formatDate: (date: string | Date): string => {
    const d = new Date(date);
    return d.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  },

  // Format time for display
  formatTime: (date: string | Date): string => {
    const d = new Date(date);
    return d.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });
  },

  // Get current dashboard metrics
  getDashboardMetrics: (): DashboardMetrics | null => {
    return useAnalyticsStore.getState().dashboardMetrics;
  },

  // Check if data is loading
  isLoading: (): boolean => {
    return useAnalyticsStore.getState().isLoading;
  },

  // Get error message
  getError: (): string | null => {
    return useAnalyticsStore.getState().error;
  },
};
