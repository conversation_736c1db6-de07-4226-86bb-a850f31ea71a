import { FastifyInstance } from "fastify";
import fp from "fastify-plugin";
import { config, isDevelopment } from "../config";

export const swaggerPlugin = fp(async (fastify: FastifyInstance) => {
  if (!config.ENABLE_SWAGGER) {
    return;
  }

  // Register Swagger
  await fastify.register(require("@fastify/swagger"), {
    openapi: {
      openapi: "3.0.0",
      info: {
        title: "Nexus SaaS API",
        description: "RESTful API for Nexus SaaS platform with comprehensive RBAC, multi-tenancy, and enterprise features",
        version: "1.0.0",
        contact: {
          name: "Nexus API Support",
          email: "<EMAIL>",
        },
        license: {
          name: "MIT",
          url: "https://opensource.org/licenses/MIT",
        },
      },
      servers: [
        {
          url: isDevelopment ? `http://localhost:${config.PORT}` : "https://api.nexus.com",
          description: isDevelopment ? "Development server" : "Production server",
        },
      ],
      components: {
        securitySchemes: {
          bearerAuth: {
            type: "http",
            scheme: "bearer",
            bearerFormat: "JWT",
            description: "JWT token for authentication",
          },
          apiKey: {
            type: "apiKey",
            in: "header",
            name: "X-API-Key",
            description: "API key for service-to-service authentication",
          },
        },
        schemas: {
          Error: {
            type: "object",
            properties: {
              success: { type: "boolean", example: false },
              error: {
                type: "object",
                properties: {
                  code: { type: "string", example: "VALIDATION_ERROR" },
                  message: { type: "string", example: "Validation failed" },
                  details: { type: "object" },
                },
                required: ["code", "message"],
              },
              meta: {
                type: "object",
                properties: {
                  timestamp: { type: "string", format: "date-time" },
                  requestId: { type: "string" },
                  version: { type: "string" },
                },
              },
            },
            required: ["success", "error"],
          },
          Success: {
            type: "object",
            properties: {
              success: { type: "boolean", example: true },
              data: { type: "object" },
              meta: {
                type: "object",
                properties: {
                  timestamp: { type: "string", format: "date-time" },
                  requestId: { type: "string" },
                  version: { type: "string" },
                },
              },
            },
            required: ["success"],
          },
          Pagination: {
            type: "object",
            properties: {
              page: { type: "integer", minimum: 1, example: 1 },
              limit: { type: "integer", minimum: 1, maximum: 100, example: 20 },
              total: { type: "integer", example: 100 },
              totalPages: { type: "integer", example: 5 },
            },
          },
          User: {
            type: "object",
            properties: {
              id: { type: "string", format: "uuid" },
              email: { type: "string", format: "email" },
              name: { type: "string" },
              tenantId: { type: "string", format: "uuid" },
              roles: { type: "array", items: { type: "string" } },
              isActive: { type: "boolean" },
              createdAt: { type: "string", format: "date-time" },
              updatedAt: { type: "string", format: "date-time" },
            },
            required: ["id", "email", "name", "tenantId"],
          },
          Role: {
            type: "object",
            properties: {
              id: { type: "string", format: "uuid" },
              name: { type: "string" },
              slug: { type: "string" },
              description: { type: "string" },
              level: { type: "string", enum: ["system", "organization", "workspace", "team", "user"] },
              permissions: { type: "array", items: { $ref: "#/components/schemas/Permission" } },
              isSystem: { type: "boolean" },
              isActive: { type: "boolean" },
              createdAt: { type: "string", format: "date-time" },
              updatedAt: { type: "string", format: "date-time" },
            },
            required: ["id", "name", "slug", "level"],
          },
          Permission: {
            type: "object",
            properties: {
              id: { type: "string", format: "uuid" },
              resource: { type: "string" },
              action: { type: "string" },
              scope: { type: "string", enum: ["own", "team", "workspace", "organization", "system"] },
              conditions: { type: "object" },
              attributes: { type: "array", items: { type: "string" } },
            },
            required: ["id", "resource", "action", "scope"],
          },
          Workspace: {
            type: "object",
            properties: {
              id: { type: "string", format: "uuid" },
              name: { type: "string" },
              slug: { type: "string" },
              description: { type: "string" },
              tenantId: { type: "string", format: "uuid" },
              ownerId: { type: "string", format: "uuid" },
              settings: { type: "object" },
              isActive: { type: "boolean" },
              createdAt: { type: "string", format: "date-time" },
              updatedAt: { type: "string", format: "date-time" },
            },
            required: ["id", "name", "slug", "tenantId", "ownerId"],
          },
          Team: {
            type: "object",
            properties: {
              id: { type: "string", format: "uuid" },
              name: { type: "string" },
              description: { type: "string" },
              workspaceId: { type: "string", format: "uuid" },
              leadId: { type: "string", format: "uuid" },
              memberCount: { type: "integer" },
              isActive: { type: "boolean" },
              createdAt: { type: "string", format: "date-time" },
              updatedAt: { type: "string", format: "date-time" },
            },
            required: ["id", "name", "workspaceId"],
          },
        },
        parameters: {
          TenantId: {
            name: "X-Tenant-Id",
            in: "header",
            description: "Tenant ID for multi-tenant isolation",
            required: false,
            schema: { type: "string", format: "uuid" },
          },
          WorkspaceId: {
            name: "X-Workspace-Id",
            in: "header",
            description: "Workspace ID for workspace-scoped operations",
            required: false,
            schema: { type: "string", format: "uuid" },
          },
          TeamId: {
            name: "X-Team-Id",
            in: "header",
            description: "Team ID for team-scoped operations",
            required: false,
            schema: { type: "string", format: "uuid" },
          },
          Page: {
            name: "page",
            in: "query",
            description: "Page number for pagination",
            required: false,
            schema: { type: "integer", minimum: 1, default: 1 },
          },
          Limit: {
            name: "limit",
            in: "query",
            description: "Number of items per page",
            required: false,
            schema: { type: "integer", minimum: 1, maximum: 100, default: 20 },
          },
          Sort: {
            name: "sort",
            in: "query",
            description: "Field to sort by",
            required: false,
            schema: { type: "string" },
          },
          Order: {
            name: "order",
            in: "query",
            description: "Sort order",
            required: false,
            schema: { type: "string", enum: ["asc", "desc"], default: "asc" },
          },
          Search: {
            name: "q",
            in: "query",
            description: "Search query",
            required: false,
            schema: { type: "string" },
          },
        },
        responses: {
          BadRequest: {
            description: "Bad Request",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Error" },
                example: {
                  success: false,
                  error: {
                    code: "VALIDATION_ERROR",
                    message: "Validation failed",
                    details: [
                      {
                        field: "email",
                        message: "Invalid email format",
                        code: "invalid_string",
                      },
                    ],
                  },
                  meta: {
                    timestamp: "2024-01-01T00:00:00.000Z",
                    requestId: "req_123456789",
                    version: "1.0.0",
                  },
                },
              },
            },
          },
          Unauthorized: {
            description: "Unauthorized",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Error" },
                example: {
                  success: false,
                  error: {
                    code: "AUTHENTICATION_ERROR",
                    message: "Authentication required",
                  },
                },
              },
            },
          },
          Forbidden: {
            description: "Forbidden",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Error" },
                example: {
                  success: false,
                  error: {
                    code: "AUTHORIZATION_ERROR",
                    message: "Insufficient permissions",
                  },
                },
              },
            },
          },
          NotFound: {
            description: "Not Found",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Error" },
                example: {
                  success: false,
                  error: {
                    code: "NOT_FOUND",
                    message: "Resource not found",
                  },
                },
              },
            },
          },
          TooManyRequests: {
            description: "Too Many Requests",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Error" },
                example: {
                  success: false,
                  error: {
                    code: "RATE_LIMIT_ERROR",
                    message: "Rate limit exceeded. Try again in 60 seconds.",
                    details: {
                      limit: 100,
                      remaining: 0,
                      resetTime: "2024-01-01T00:01:00.000Z",
                    },
                  },
                },
              },
            },
          },
          InternalServerError: {
            description: "Internal Server Error",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Error" },
                example: {
                  success: false,
                  error: {
                    code: "INTERNAL_SERVER_ERROR",
                    message: "Internal server error",
                  },
                },
              },
            },
          },
        },
      },
      tags: [
        { name: "Authentication", description: "User authentication and authorization" },
        { name: "Users", description: "User management operations" },
        { name: "Roles", description: "Role and permission management" },
        { name: "Workspaces", description: "Workspace management" },
        { name: "Teams", description: "Team management" },
        { name: "Projects", description: "Project management" },
        { name: "Files", description: "File upload and management" },
        { name: "Analytics", description: "Analytics and reporting" },
        { name: "Billing", description: "Billing and subscription management" },
        { name: "Admin", description: "Administrative operations" },
      ],
    },
  });

  // Register Swagger UI
  await fastify.register(require("@fastify/swagger-ui"), {
    routePrefix: config.SWAGGER_PATH,
    uiConfig: {
      docExpansion: "list",
      deepLinking: false,
      defaultModelsExpandDepth: 1,
      defaultModelExpandDepth: 1,
    },
    uiHooks: {
      onRequest: function (request, reply, next) {
        next();
      },
      preHandler: function (request, reply, next) {
        next();
      },
    },
    staticCSP: true,
    transformStaticCSP: (header) => header,
    transformSpecification: (swaggerObject, request, reply) => {
      return swaggerObject;
    },
    transformSpecificationClone: true,
  });
});
