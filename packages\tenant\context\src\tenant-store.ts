import { create } from "zustand";
import { TenantData, TenantUser } from "./tenant-types";

interface TenantStore {
  // State
  tenant: TenantData | null;
  user: TenantUser | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setTenant: (tenant: TenantData | null) => void;
  setUser: (user: TenantUser | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
  
  // Computed
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
}

export const useTenantStore = create<TenantStore>((set, get) => ({
  // Initial state
  tenant: null,
  user: null,
  isLoading: false,
  error: null,
  
  // Actions
  setTenant: (tenant) => set({ tenant, error: null }),
  setUser: (user) => set({ user }),
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error, isLoading: false }),
  reset: () => set({ tenant: null, user: null, isLoading: false, error: null }),
  
  // Computed
  hasPermission: (permission) => {
    const { user } = get();
    if (!user) return false;
    
    // Basic role-based permissions
    switch (user.role) {
      case "OWNER":
        return true;
      case "ADMIN":
        return ["read", "write", "delete"].includes(permission);
      case "MEMBER":
        return ["read", "write"].includes(permission);
      case "VIEWER":
        return permission === "read";
      default:
        return false;
    }
  },
  
  hasRole: (role) => {
    const { user } = get();
    return user?.role === role;
  },
}));

// Selectors
export const selectTenant = (state: TenantStore) => state.tenant;
export const selectUser = (state: TenantStore) => state.user;
export const selectIsLoading = (state: TenantStore) => state.isLoading;
export const selectError = (state: TenantStore) => state.error;
export const selectTenantId = (state: TenantStore) => state.tenant?.id;
export const selectTenantSlug = (state: TenantStore) => state.tenant?.slug;
export const selectTenantName = (state: TenantStore) => state.tenant?.name;
export const selectUserRole = (state: TenantStore) => state.user?.role;
