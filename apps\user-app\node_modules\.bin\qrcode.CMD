@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Downloads\saas-starter-cursor-temp\node_modules\.pnpm\qrcode@1.5.4\node_modules\qrcode\bin\node_modules;C:\Users\<USER>\Downloads\saas-starter-cursor-temp\node_modules\.pnpm\qrcode@1.5.4\node_modules\qrcode\node_modules;C:\Users\<USER>\Downloads\saas-starter-cursor-temp\node_modules\.pnpm\qrcode@1.5.4\node_modules;C:\Users\<USER>\Downloads\saas-starter-cursor-temp\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Downloads\saas-starter-cursor-temp\node_modules\.pnpm\qrcode@1.5.4\node_modules\qrcode\bin\node_modules;C:\Users\<USER>\Downloads\saas-starter-cursor-temp\node_modules\.pnpm\qrcode@1.5.4\node_modules\qrcode\node_modules;C:\Users\<USER>\Downloads\saas-starter-cursor-temp\node_modules\.pnpm\qrcode@1.5.4\node_modules;C:\Users\<USER>\Downloads\saas-starter-cursor-temp\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\qrcode\bin\qrcode" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\qrcode\bin\qrcode" %*
)
