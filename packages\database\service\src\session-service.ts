import { Session } from "@nexus/database-schema";
import { createTenantClient } from "./client";

export class SessionService {
  private tenantId: string;

  constructor(tenantId: string) {
    this.tenantId = tenantId;
  }

  // Create a new session
  async create(userId: string, token: string, expiresAt: Date): Promise<Session> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.session.create({
      data: {
        userId,
        token,
        expiresAt,
      },
    });
  }

  // Find session by token
  async findByToken(token: string): Promise<Session | null> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.session.findUnique({
      where: { token },
      include: {
        user: true,
      },
    });
  }

  // Find sessions by user ID
  async findByUserId(userId: string): Promise<Session[]> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.session.findMany({
      where: { userId },
      orderBy: { createdAt: "desc" },
    });
  }

  // Update session expiration
  async updateExpiration(token: string, expiresAt: Date): Promise<Session> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.session.update({
      where: { token },
      data: { expiresAt },
    });
  }

  // Delete session
  async delete(token: string): Promise<Session> {
    const client = createTenantClient(this.tenantId);
    return client.tenant.session.delete({
      where: { token },
    });
  }

  // Delete all sessions for a user
  async deleteAllForUser(userId: string): Promise<number> {
    const client = createTenantClient(this.tenantId);
    const result = await client.tenant.session.deleteMany({
      where: { userId },
    });
    return result.count;
  }

  // Delete expired sessions
  async deleteExpired(): Promise<number> {
    const client = createTenantClient(this.tenantId);
    const result = await client.tenant.session.deleteMany({
      where: {
        expiresAt: {
          lt: new Date(),
        },
      },
    });
    return result.count;
  }

  // Check if session is valid
  async isValid(token: string): Promise<boolean> {
    const session = await this.findByToken(token);
    if (!session) return false;
    return session.expiresAt > new Date();
  }

  // Refresh session (extend expiration)
  async refresh(token: string, extensionMs: number = 24 * 60 * 60 * 1000): Promise<Session | null> {
    const session = await this.findByToken(token);
    if (!session || session.expiresAt <= new Date()) {
      return null;
    }

    const newExpiresAt = new Date(Date.now() + extensionMs);
    return this.updateExpiration(token, newExpiresAt);
  }
}

export const createSessionService = (tenantId: string): SessionService => {
  return new SessionService(tenantId);
};
