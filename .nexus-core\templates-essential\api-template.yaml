api_route_guidelines:
  id: nextjs-api-route-guidelines
  name: Next.js API Route Creation Guidelines
  version: 1.0
  framework: nexus
  purpose: Instructions for creating production-ready API routes

required_imports:
  - "NextRequest, NextResponse from next/server"
  - "createClient from @/lib/supabase/server"
  - "validation schemas from @/lib/schemas (when needed)"

route_structure:
  location: "app/api/[route-name]/route.ts"
  naming: "Use kebab-case for route names"
  methods: "Export named functions: GET, POST, PUT, DELETE, PATCH"

authentication_pattern:
  required_for: "All routes unless explicitly public"
  implementation:
    - "Create Supabase client"
    - "Call supabase.auth.getUser()"
    - "Return 401 if no user or auth error"
    - "Use user.id for data isolation"

validation_pattern:
  library: "Zod v4.0.5"
  implementation:
    - "Import schema from @/lib/schemas"
    - "Call schema.parse(requestBody)"
    - "Handle ZodError for validation failures"
    - "Use validated data in business logic"

error_handling:
  required_patterns:
    - "Wrap all logic in try-catch"
    - "Log errors with route context"
    - "Return appropriate HTTP status codes"
    - "Provide user-friendly error messages"
    - "Never expose internal errors to client"

response_patterns:
  success:
    - "GET: Return data with 200"
    - "POST: Return created resource with 201"
    - "PUT: Return updated resource with 200"
    - "DELETE: Return success message with 200"
  error:
    - "401: Unauthorized access"
    - "400: Validation or client errors"
    - "500: Internal server errors"

database_operations:
  security: "Always use Row Level Security (RLS)"
  filtering: "Filter by user.id for user-specific data"
  transactions: "Use database transactions for multi-step operations"
  performance: "Add appropriate indexes and optimize queries"

implementation_checklist:
  - "Route file in correct location"
  - "Authentication implemented"
  - "Input validation added"
  - "Error handling comprehensive"
  - "HTTP status codes correct"
  - "Database operations secure"
  - "TypeScript types defined"
  - "Business logic implemented"

examples:
  basic_pattern: "Standard CRUD operations with auth and validation"
  public_route: "Health checks or public data endpoints"
  protected_route: "User-specific data operations"
