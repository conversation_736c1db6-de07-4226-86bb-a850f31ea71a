import { PermissionCheck, Permission<PERSON><PERSON><PERSON>, Policy<PERSON>ule, AccessContext, UserRole, Role } from "./rbac-types";
import { permissionSystem } from "./permission-system";

export class PolicyEngine {
  private tenantId: string;

  constructor(tenantId: string) {
    this.tenantId = tenantId;
  }

  // Main permission check method
  async checkPermission(check: PermissionCheck): Promise<PermissionResult> {
    try {
      // Get user roles and context
      const context = await this.buildAccessContext(check);
      
      // Check system-level permissions first
      const systemResult = await this.checkSystemPermissions(check, context);
      if (systemResult.granted || systemResult.reason === "denied") {
        return systemResult;
      }

      // Check role-based permissions
      const roleResult = await this.checkRolePermissions(check, context);
      if (roleResult.granted) {
        return roleResult;
      }

      // Check policy rules
      const policyResult = await this.checkPolicyRules(check, context);
      if (policyResult.granted) {
        return policyResult;
      }

      // Default deny
      return {
        granted: false,
        reason: "No matching permissions found",
        metadata: { context },
      };
    } catch (error) {
      return {
        granted: false,
        reason: `Permission check failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  }

  // Build access context for permission check
  private async buildAccessContext(check: PermissionCheck): Promise<AccessContext> {
    const userRoles = await this.getUserRoles(check.userId, {
      tenantId: check.tenantId,
      workspaceId: check.workspaceId,
      teamId: check.teamId,
      resourceId: check.resourceId,
      resourceType: check.resource,
    });

    return {
      userId: check.userId,
      tenantId: check.tenantId,
      workspaceId: check.workspaceId,
      teamId: check.teamId,
      resourceId: check.resourceId,
      resourceType: check.resource,
      userRoles,
      requestData: check.context,
    };
  }

  // Check system-level permissions
  private async checkSystemPermissions(
    check: PermissionCheck,
    context: AccessContext
  ): Promise<PermissionResult> {
    // System admin has all permissions
    const hasSystemAdmin = context.userRoles.some(
      (role) => role.roleId === "system_admin" && role.isActive
    );

    if (hasSystemAdmin) {
      return {
        granted: true,
        reason: "System administrator access",
        role: "system_admin",
      };
    }

    // Check for system-level denials
    if (check.resource === "user" && check.action === "impersonate") {
      const hasImpersonatePermission = context.userRoles.some(
        (role) => role.roleId === "system_admin"
      );
      
      if (!hasImpersonatePermission) {
        return {
          granted: false,
          reason: "Impersonation requires system administrator role",
        };
      }
    }

    return { granted: false };
  }

  // Check role-based permissions
  private async checkRolePermissions(
    check: PermissionCheck,
    context: AccessContext
  ): Promise<PermissionResult> {
    for (const userRole of context.userRoles) {
      if (!userRole.isActive) continue;

      // Check if role has permission for this resource/action
      const hasPermission = permissionSystem.hasPermission(
        userRole.roleId,
        check.resource,
        check.action
      );

      if (hasPermission) {
        // Check scope constraints
        const scopeValid = await this.validateScope(userRole, check, context);
        
        if (scopeValid) {
          return {
            granted: true,
            reason: `Role-based access: ${userRole.roleId}`,
            role: userRole.roleId,
          };
        }
      }
    }

    return { granted: false };
  }

  // Validate scope constraints
  private async validateScope(
    userRole: UserRole,
    check: PermissionCheck,
    context: AccessContext
  ): Promise<boolean> {
    const allowedScopes = permissionSystem.getAllowedScopes(check.resource, check.action);

    // Check if user has appropriate scope
    for (const scope of allowedScopes) {
      switch (scope) {
        case "system":
          // System scope requires system admin
          if (userRole.roleId === "system_admin") return true;
          break;

        case "organization":
          // Organization scope requires organization-level role
          if (userRole.tenantId === check.tenantId && !userRole.workspaceId) return true;
          break;

        case "workspace":
          // Workspace scope requires workspace-level role or higher
          if (userRole.tenantId === check.tenantId) {
            if (!userRole.workspaceId || userRole.workspaceId === check.workspaceId) {
              return true;
            }
          }
          break;

        case "team":
          // Team scope requires team-level role or higher
          if (userRole.tenantId === check.tenantId) {
            if (userRole.workspaceId === check.workspaceId) {
              if (!userRole.teamId || userRole.teamId === check.teamId) {
                return true;
              }
            }
          }
          break;

        case "own":
          // Own scope requires resource ownership or higher permissions
          if (check.resourceId && context.userId === check.resourceId) return true;
          // Also allow if user has higher scope permissions
          if (userRole.tenantId === check.tenantId) return true;
          break;
      }
    }

    return false;
  }

  // Check policy rules
  private async checkPolicyRules(
    check: PermissionCheck,
    context: AccessContext
  ): Promise<PermissionResult> {
    const rules = await this.getPolicyRules(check.resource, check.action);

    for (const rule of rules) {
      if (!rule.isActive) continue;

      const conditionsMet = await this.evaluateConditions(rule.conditions, check, context);
      
      if (conditionsMet) {
        return {
          granted: rule.effect === "allow",
          reason: `Policy rule: ${rule.name}`,
          metadata: { ruleId: rule.id, effect: rule.effect },
        };
      }
    }

    return { granted: false };
  }

  // Evaluate policy conditions
  private async evaluateConditions(
    conditions: PolicyRule["conditions"],
    check: PermissionCheck,
    context: AccessContext
  ): Promise<boolean> {
    for (const condition of conditions) {
      const fieldValue = this.getFieldValue(condition.field, check, context);
      const conditionMet = this.evaluateCondition(fieldValue, condition.operator, condition.value);
      
      if (!conditionMet) {
        return false;
      }
    }

    return true;
  }

  // Get field value from context
  private getFieldValue(field: string, check: PermissionCheck, context: AccessContext): any {
    const fieldMap: Record<string, any> = {
      "user.id": context.userId,
      "user.roles": context.userRoles.map(r => r.roleId),
      "resource.type": check.resource,
      "resource.id": check.resourceId,
      "action": check.action,
      "tenant.id": check.tenantId,
      "workspace.id": check.workspaceId,
      "team.id": check.teamId,
      "request.ip": context.requestData?.ip,
      "request.userAgent": context.requestData?.userAgent,
      "time.hour": new Date().getHours(),
      "time.day": new Date().getDay(),
    };

    return fieldMap[field];
  }

  // Evaluate single condition
  private evaluateCondition(fieldValue: any, operator: string, expectedValue: any): boolean {
    switch (operator) {
      case "eq":
        return fieldValue === expectedValue;
      case "ne":
        return fieldValue !== expectedValue;
      case "gt":
        return fieldValue > expectedValue;
      case "gte":
        return fieldValue >= expectedValue;
      case "lt":
        return fieldValue < expectedValue;
      case "lte":
        return fieldValue <= expectedValue;
      case "in":
        return Array.isArray(expectedValue) && expectedValue.includes(fieldValue);
      case "nin":
        return Array.isArray(expectedValue) && !expectedValue.includes(fieldValue);
      case "contains":
        return Array.isArray(fieldValue) && fieldValue.includes(expectedValue);
      case "exists":
        return expectedValue ? fieldValue !== undefined : fieldValue === undefined;
      default:
        return false;
    }
  }

  // Get user roles
  private async getUserRoles(userId: string, context: {
    tenantId?: string;
    workspaceId?: string;
    teamId?: string;
    resourceId?: string;
    resourceType?: string;
  }): Promise<UserRole[]> {
    const params = new URLSearchParams({ userId });
    
    Object.entries(context).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await fetch(`/api/rbac/user-roles?${params}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to get user roles");
    }

    return response.json();
  }

  // Get policy rules
  private async getPolicyRules(resource: string, action: string): Promise<PolicyRule[]> {
    const params = new URLSearchParams({ resource, action });

    const response = await fetch(`/api/rbac/policy-rules?${params}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to get policy rules");
    }

    return response.json();
  }

  // Batch permission check
  async checkMultiplePermissions(checks: PermissionCheck[]): Promise<PermissionResult[]> {
    const results = await Promise.all(
      checks.map(check => this.checkPermission(check))
    );

    return results;
  }
}

export const createPolicyEngine = (tenantId: string): PolicyEngine => {
  return new PolicyEngine(tenantId);
};
