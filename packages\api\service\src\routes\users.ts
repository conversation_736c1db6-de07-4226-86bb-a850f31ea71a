import { FastifyInstance } from "fastify";
import { z } from "zod";
import { validate, validatePagination, validateId } from "../middleware";
import { canRead, canCreate, canWrite, canDelete } from "../middleware/rbac";
import { ApiResponse } from "../types";

const createUserSchema = z.object({
  email: z.string().email(),
  name: z.string().min(2),
  roles: z.array(z.string()).optional(),
});

const updateUserSchema = z.object({
  name: z.string().min(2).optional(),
  isActive: z.boolean().optional(),
});

export const userRoutes = async (fastify: FastifyInstance) => {
  // Get users
  fastify.get("/", {
    schema: {
      tags: ["Users"],
      summary: "List users",
      description: "Get a paginated list of users",
      security: [{ bearerAuth: [] }],
      querystring: {
        type: "object",
        properties: {
          page: { type: "integer", minimum: 1, default: 1 },
          limit: { type: "integer", minimum: 1, maximum: 100, default: 20 },
          search: { type: "string" },
          role: { type: "string" },
          isActive: { type: "boolean" },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                users: {
                  type: "array",
                  items: { $ref: "#/components/schemas/User" },
                },
                pagination: { $ref: "#/components/schemas/Pagination" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
      },
    },
    preHandler: [fastify.authenticate, canRead("user"), validatePagination],
    handler: async (request, reply): Promise<ApiResponse> => {
      const query = request.query as any;
      
      // TODO: Implement actual user fetching from database
      const mockUsers = [
        {
          id: "user_1",
          email: "<EMAIL>",
          name: "Admin User",
          tenantId: "tenant_123",
          roles: ["admin"],
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: "user_2",
          email: "<EMAIL>",
          name: "Regular User",
          tenantId: "tenant_123",
          roles: ["member"],
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      return {
        success: true,
        data: {
          users: mockUsers,
          pagination: {
            page: query.page || 1,
            limit: query.limit || 20,
            total: mockUsers.length,
            totalPages: 1,
          },
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Get user by ID
  fastify.get("/:id", {
    schema: {
      tags: ["Users"],
      summary: "Get user by ID",
      description: "Get a specific user by their ID",
      security: [{ bearerAuth: [] }],
      params: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
        },
        required: ["id"],
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                user: { $ref: "#/components/schemas/User" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [fastify.authenticate, canRead("user"), validateId],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { id } = request.params as { id: string };
      
      // TODO: Implement actual user fetching
      const mockUser = {
        id,
        email: "<EMAIL>",
        name: "User Name",
        tenantId: "tenant_123",
        roles: ["member"],
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return {
        success: true,
        data: {
          user: mockUser,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Create user
  fastify.post("/", {
    schema: {
      tags: ["Users"],
      summary: "Create user",
      description: "Create a new user",
      security: [{ bearerAuth: [] }],
      body: {
        type: "object",
        properties: {
          email: { type: "string", format: "email" },
          name: { type: "string", minLength: 2 },
          roles: {
            type: "array",
            items: { type: "string" },
          },
        },
        required: ["email", "name"],
      },
      response: {
        201: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                user: { $ref: "#/components/schemas/User" },
              },
            },
          },
        },
        400: { $ref: "#/components/responses/BadRequest" },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
      },
    },
    preHandler: [fastify.authenticate, canCreate("user"), validate({ body: createUserSchema })],
    handler: async (request, reply): Promise<ApiResponse> => {
      const userData = request.body as z.infer<typeof createUserSchema>;
      
      // TODO: Implement actual user creation
      const newUser = {
        id: `user_${Date.now()}`,
        email: userData.email,
        name: userData.name,
        tenantId: (request as any).user.tenantId,
        roles: userData.roles || ["member"],
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      reply.status(201);
      return {
        success: true,
        data: {
          user: newUser,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Update user
  fastify.patch("/:id", {
    schema: {
      tags: ["Users"],
      summary: "Update user",
      description: "Update an existing user",
      security: [{ bearerAuth: [] }],
      params: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
        },
        required: ["id"],
      },
      body: {
        type: "object",
        properties: {
          name: { type: "string", minLength: 2 },
          isActive: { type: "boolean" },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                user: { $ref: "#/components/schemas/User" },
              },
            },
          },
        },
        400: { $ref: "#/components/responses/BadRequest" },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [
      fastify.authenticate,
      canWrite("user"),
      validateId,
      validate({ body: updateUserSchema }),
    ],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { id } = request.params as { id: string };
      const updates = request.body as z.infer<typeof updateUserSchema>;
      
      // TODO: Implement actual user update
      const updatedUser = {
        id,
        email: "<EMAIL>",
        name: updates.name || "User Name",
        tenantId: "tenant_123",
        roles: ["member"],
        isActive: updates.isActive ?? true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return {
        success: true,
        data: {
          user: updatedUser,
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });

  // Delete user
  fastify.delete("/:id", {
    schema: {
      tags: ["Users"],
      summary: "Delete user",
      description: "Delete a user",
      security: [{ bearerAuth: [] }],
      params: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
        },
        required: ["id"],
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                message: { type: "string" },
              },
            },
          },
        },
        401: { $ref: "#/components/responses/Unauthorized" },
        403: { $ref: "#/components/responses/Forbidden" },
        404: { $ref: "#/components/responses/NotFound" },
      },
    },
    preHandler: [fastify.authenticate, canDelete("user"), validateId],
    handler: async (request, reply): Promise<ApiResponse> => {
      const { id } = request.params as { id: string };
      
      // TODO: Implement actual user deletion
      
      return {
        success: true,
        data: {
          message: "User deleted successfully",
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: request.id,
          version: "1.0.0",
        },
      };
    },
  });
};
