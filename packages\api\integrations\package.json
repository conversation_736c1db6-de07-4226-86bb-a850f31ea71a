{"name": "@nexus/integrations", "version": "0.1.0", "description": "Third-party integrations service for Nexus SaaS", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "clean": "rm -rf dist", "test": "jest", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "@nexus/utils": "workspace:*", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-github2": "^0.1.12", "passport-microsoft": "^1.0.0", "passport-slack-oauth2": "^1.0.8", "passport-oauth2": "^1.7.0", "oauth2-server": "^4.0.0", "@octokit/rest": "^20.0.2", "@slack/web-api": "^6.10.0", "@microsoft/microsoft-graph-client": "^3.0.7", "googleapis": "^131.0.0", "stripe": "^14.12.0", "twilio": "^4.19.3", "mailchimp-api-v3": "^1.15.0", "hubspot": "^9.1.0", "salesforce-api": "^1.0.0", "zapier-platform-core": "^15.0.0", "webhook-signature": "^1.0.0", "crypto": "^1.0.1", "uuid": "^9.0.1", "zod": "^4.0.5", "winston": "^3.11.0", "bull": "^4.12.2", "redis": "^4.6.12", "prisma": "^5.7.1", "@prisma/client": "^5.7.1"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/passport": "^1.0.16", "@types/passport-google-oauth20": "^2.0.14", "@types/passport-github2": "^1.2.9", "@types/passport-oauth2": "^1.4.15", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.0", "@types/node": "^22.10.2", "jest": "^29.5.0", "tsx": "^4.6.2", "typescript": "^5.8.0", "eslint": "^8.57.0"}}