import { Role, UserRole, RoleTemplate, RoleLevel, Permission } from "./rbac-types";
import { permissionSystem } from "./permission-system";

export class RoleManager {
  private tenantId: string;

  constructor(tenantId: string) {
    this.tenantId = tenantId;
  }

  // Create a new role
  async createRole(data: {
    name: string;
    slug: string;
    level: RoleLevel;
    description?: string;
    permissions: Permission[];
    inherits?: string[];
    workspaceId?: string;
    teamId?: string;
    metadata?: Record<string, any>;
  }): Promise<Role> {
    const response = await fetch("/api/rbac/roles", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to create role");
    }

    return response.json();
  }

  // Get role by ID
  async getRole(roleId: string): Promise<Role | null> {
    const response = await fetch(`/api/rbac/roles/${roleId}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (response.status === 404) {
      return null;
    }

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to get role");
    }

    return response.json();
  }

  // Get all roles
  async getRoles(filters?: {
    level?: RoleLevel;
    workspaceId?: string;
    teamId?: string;
    isActive?: boolean;
  }): Promise<Role[]> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`/api/rbac/roles?${params}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to get roles");
    }

    return response.json();
  }

  // Update role
  async updateRole(roleId: string, updates: Partial<Role>): Promise<Role> {
    const response = await fetch(`/api/rbac/roles/${roleId}`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify(updates),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to update role");
    }

    return response.json();
  }

  // Delete role
  async deleteRole(roleId: string): Promise<void> {
    const response = await fetch(`/api/rbac/roles/${roleId}`, {
      method: "DELETE",
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to delete role");
    }
  }

  // Assign role to user
  async assignRole(data: {
    userId: string;
    roleId: string;
    workspaceId?: string;
    teamId?: string;
    resourceId?: string;
    resourceType?: string;
    expiresAt?: Date;
    metadata?: Record<string, any>;
  }): Promise<UserRole> {
    const response = await fetch("/api/rbac/user-roles", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to assign role");
    }

    return response.json();
  }

  // Revoke role from user
  async revokeRole(userRoleId: string): Promise<void> {
    const response = await fetch(`/api/rbac/user-roles/${userRoleId}`, {
      method: "DELETE",
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to revoke role");
    }
  }

  // Get user roles
  async getUserRoles(userId: string, filters?: {
    workspaceId?: string;
    teamId?: string;
    isActive?: boolean;
  }): Promise<UserRole[]> {
    const params = new URLSearchParams({ userId });
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`/api/rbac/user-roles?${params}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to get user roles");
    }

    return response.json();
  }

  // Get role templates
  async getRoleTemplates(category?: string): Promise<RoleTemplate[]> {
    const params = new URLSearchParams();
    if (category) {
      params.append("category", category);
    }

    const response = await fetch(`/api/rbac/role-templates?${params}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to get role templates");
    }

    return response.json();
  }

  // Create role from template
  async createRoleFromTemplate(templateId: string, data: {
    name: string;
    slug: string;
    workspaceId?: string;
    teamId?: string;
    customizations?: Record<string, any>;
  }): Promise<Role> {
    const response = await fetch(`/api/rbac/role-templates/${templateId}/create-role`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to create role from template");
    }

    return response.json();
  }

  // Get effective permissions for a user
  async getUserEffectivePermissions(userId: string, context?: {
    workspaceId?: string;
    teamId?: string;
    resourceId?: string;
    resourceType?: string;
  }): Promise<Permission[]> {
    const params = new URLSearchParams({ userId });
    
    if (context) {
      Object.entries(context).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`/api/rbac/users/${userId}/effective-permissions?${params}`, {
      headers: {
        "x-tenant-id": this.tenantId,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to get effective permissions");
    }

    return response.json();
  }

  // Get default system roles
  getDefaultSystemRoles(): Array<Omit<Role, "id" | "createdAt" | "updatedAt">> {
    return [
      {
        name: "System Administrator",
        slug: "system_admin",
        level: "system",
        description: "Full system access with all permissions",
        color: "#DC2626",
        icon: "shield",
        isSystem: true,
        isActive: true,
        permissions: [],
        tenantId: this.tenantId,
        metadata: { systemRole: true },
      },
      {
        name: "Organization Owner",
        slug: "owner",
        level: "organization",
        description: "Full organization access and management",
        color: "#7C3AED",
        icon: "crown",
        isSystem: true,
        isActive: true,
        permissions: [],
        tenantId: this.tenantId,
        metadata: { systemRole: true },
      },
      {
        name: "Administrator",
        slug: "admin",
        level: "organization",
        description: "Administrative access with most permissions",
        color: "#2563EB",
        icon: "settings",
        isSystem: true,
        isActive: true,
        permissions: [],
        tenantId: this.tenantId,
        metadata: { systemRole: true },
      },
      {
        name: "Editor",
        slug: "editor",
        level: "workspace",
        description: "Can create and edit content",
        color: "#059669",
        icon: "edit",
        isSystem: true,
        isActive: true,
        permissions: [],
        tenantId: this.tenantId,
        metadata: { systemRole: true },
      },
      {
        name: "Member",
        slug: "member",
        level: "workspace",
        description: "Standard member access",
        color: "#0891B2",
        icon: "user",
        isSystem: true,
        isActive: true,
        permissions: [],
        tenantId: this.tenantId,
        metadata: { systemRole: true },
      },
      {
        name: "Viewer",
        slug: "viewer",
        level: "workspace",
        description: "Read-only access",
        color: "#6B7280",
        icon: "eye",
        isSystem: true,
        isActive: true,
        permissions: [],
        tenantId: this.tenantId,
        metadata: { systemRole: true },
      },
    ];
  }
}

export const createRoleManager = (tenantId: string): RoleManager => {
  return new RoleManager(tenantId);
};
