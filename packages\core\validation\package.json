{"name": "@nexus/validation", "version": "0.1.0", "description": "Shared validation schemas for Nexus SaaS", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "jest", "type-check": "tsc --noEmit"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "zod": "4.0.5"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@types/jest": "30.0.0", "jest": "30.0.4", "typescript": "5.8.3"}}