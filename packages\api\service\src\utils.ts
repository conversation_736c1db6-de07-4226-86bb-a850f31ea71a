import { ApiResponse } from "./types";

// Create success response
export const createSuccessResponse = <T>(
  data: T,
  requestId: string,
  statusCode: number = 200
): ApiResponse<T> => {
  return {
    success: true,
    data,
    meta: {
      timestamp: new Date().toISOString(),
      requestId,
      version: process.env.npm_package_version || "1.0.0",
    },
  };
};

// Create error response
export const createErrorResponse = (
  code: string,
  message: string,
  requestId: string,
  details?: any,
  statusCode: number = 500
): ApiResponse => {
  return {
    success: false,
    error: {
      code,
      message,
      ...(details && { details }),
    },
    meta: {
      timestamp: new Date().toISOString(),
      requestId,
      version: process.env.npm_package_version || "1.0.0",
    },
  };
};

// Format file size
export const formatFileSize = (bytes: number): string => {
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  if (bytes === 0) return "0 Bytes";
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + " " + sizes[i];
};

// Generate unique filename
export const generateUniqueFilename = (originalName: string): string => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  const extension = originalName.split(".").pop();
  const nameWithoutExt = originalName.replace(/\.[^/.]+$/, "");
  return `${nameWithoutExt}_${timestamp}_${random}.${extension}`;
};

// Validate UUID
export const isValidUUID = (uuid: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

// Sanitize filename
export const sanitizeFilename = (filename: string): string => {
  return filename
    .replace(/[^a-zA-Z0-9.-]/g, "_")
    .replace(/_{2,}/g, "_")
    .replace(/^_|_$/g, "");
};

// Parse sort parameter
export const parseSortParam = (sort?: string): { field: string; order: "asc" | "desc" } => {
  if (!sort) {
    return { field: "createdAt", order: "desc" };
  }

  const [field, order] = sort.split(":");
  return {
    field: field || "createdAt",
    order: (order === "asc" || order === "desc") ? order : "desc",
  };
};

// Calculate pagination
export const calculatePagination = (
  page: number,
  limit: number,
  total: number
) => {
  const totalPages = Math.ceil(total / limit);
  const offset = (page - 1) * limit;
  
  return {
    page,
    limit,
    total,
    totalPages,
    offset,
    hasNext: page < totalPages,
    hasPrev: page > 1,
  };
};

// Delay function for rate limiting
export const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Generate random string
export const generateRandomString = (length: number): string => {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// Hash password (placeholder - use proper hashing in production)
export const hashPassword = async (password: string): Promise<string> => {
  // TODO: Implement proper password hashing with bcrypt
  return `hashed_${password}`;
};

// Verify password (placeholder - use proper verification in production)
export const verifyPassword = async (password: string, hash: string): Promise<boolean> => {
  // TODO: Implement proper password verification with bcrypt
  return hash === `hashed_${password}`;
};

// Extract tenant ID from request
export const extractTenantId = (request: any): string | undefined => {
  return request.headers["x-tenant-id"] || request.user?.tenantId;
};

// Extract workspace ID from request
export const extractWorkspaceId = (request: any): string | undefined => {
  return request.headers["x-workspace-id"] || request.params?.workspaceId;
};

// Extract team ID from request
export const extractTeamId = (request: any): string | undefined => {
  return request.headers["x-team-id"] || request.params?.teamId;
};

// Check if user is admin
export const isAdmin = (user: any): boolean => {
  return user?.roles?.includes("admin") || 
         user?.roles?.includes("owner") || 
         user?.roles?.includes("system_admin");
};

// Check if user is owner
export const isOwner = (user: any): boolean => {
  return user?.roles?.includes("owner") || 
         user?.roles?.includes("system_admin");
};

// Check if user is system admin
export const isSystemAdmin = (user: any): boolean => {
  return user?.roles?.includes("system_admin");
};

// Format currency
export const formatCurrency = (amount: number, currency: string = "USD"): string => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency,
  }).format(amount);
};

// Format date
export const formatDate = (date: Date | string, locale: string = "en-US"): string => {
  const d = typeof date === "string" ? new Date(date) : date;
  return new Intl.DateTimeFormat(locale, {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(d);
};

// Truncate text
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + "...";
};

// Deep merge objects
export const deepMerge = (target: any, source: any): any => {
  const result = { ...target };
  
  for (const key in source) {
    if (source[key] && typeof source[key] === "object" && !Array.isArray(source[key])) {
      result[key] = deepMerge(result[key] || {}, source[key]);
    } else {
      result[key] = source[key];
    }
  }
  
  return result;
};

// Remove undefined values from object
export const removeUndefined = (obj: any): any => {
  const result: any = {};
  
  for (const key in obj) {
    if (obj[key] !== undefined) {
      if (typeof obj[key] === "object" && obj[key] !== null && !Array.isArray(obj[key])) {
        result[key] = removeUndefined(obj[key]);
      } else {
        result[key] = obj[key];
      }
    }
  }
  
  return result;
};
