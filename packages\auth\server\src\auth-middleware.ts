import { NextRequest, NextResponse } from "next/server";
import { getServerSession, hasRole, isAuthenticated } from "./auth-adapter";

// Authentication middleware
export async function authMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Public routes that don't require authentication
  const publicRoutes = ["/", "/auth/login", "/auth/register", "/api/auth"];
  
  if (publicRoutes.some(route => pathname.startsWith(route))) {
    return NextResponse.next();
  }
  
  // Check if user is authenticated
  const authenticated = await isAuthenticated(request);
  
  if (!authenticated) {
    const loginUrl = new URL("/auth/login", request.url);
    loginUrl.searchParams.set("redirect", pathname);
    return NextResponse.redirect(loginUrl);
  }
  
  return NextResponse.next();
}

// Role-based middleware
export function requireRole(role: string) {
  return async function(request: NextRequest) {
    const hasRequiredRole = await hasRole(request, role);
    
    if (!hasRequiredRole) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }
    
    return NextResponse.next();
  };
}

// Admin middleware
export async function adminMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  if (pathname.startsWith("/admin")) {
    const user = await getServerSession(request);

    if (!user?.user || ((user.user as any).role !== "OWNER" && (user.user as any).role !== "ADMIN")) {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      );
    }
  }
  
  return NextResponse.next();
}

// Tenant isolation middleware
export async function tenantMiddleware(request: NextRequest) {
  const session = await getServerSession(request);
  
  if (session?.user) {
    // Add tenant context to headers for downstream services
    const requestHeaders = new Headers(request.headers);
    requestHeaders.set("x-tenant-id", (session.user as any).tenantId);
    requestHeaders.set("x-user-id", (session.user as any).id);
    
    return NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });
  }
  
  return NextResponse.next();
}
