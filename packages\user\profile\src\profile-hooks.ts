import { useCallback, useEffect } from "react";
import { useAuth } from "@nexus/auth-client";
import { useTenantId } from "@nexus/tenant-context";
import { useProfileStore } from "./profile-store";
import { createProfileService } from "./profile-service";
import { ProfileUpdateData, PreferencesUpdateData, PrivacyUpdateData } from "./profile-types";

// Main profile hook
export function useProfile() {
  const { user } = useAuth();
  const tenantId = useTenantId();
  const {
    profile,
    stats,
    isLoading,
    error,
    setProfile,
    setStats,
    setLoading,
    setError,
    getProfileCompletion,
    getMissingFields,
    getDisplayName,
  } = useProfileStore();

  const profileService = user?.id && tenantId ? createProfileService(user.id, tenantId) : null;

  // Load profile data
  const loadProfile = useCallback(async () => {
    if (!profileService) return;
    
    setLoading(true);
    try {
      const [profileData, statsData] = await Promise.all([
        profileService.getProfile(),
        profileService.getProfileStats(),
      ]);
      
      setProfile(profileData);
      setStats(statsData);
      setError(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to load profile");
    } finally {
      setLoading(false);
    }
  }, [profileService, setProfile, setStats, setLoading, setError]);

  // Update profile
  const updateProfile = useCallback(async (data: ProfileUpdateData) => {
    if (!profileService) throw new Error("No profile service available");
    
    setLoading(true);
    try {
      const updatedProfile = await profileService.updateProfile(data);
      setProfile(updatedProfile);
      setError(null);
      return updatedProfile;
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to update profile";
      setError(message);
      throw new Error(message);
    } finally {
      setLoading(false);
    }
  }, [profileService, setProfile, setLoading, setError]);

  // Update preferences
  const updatePreferences = useCallback(async (data: PreferencesUpdateData) => {
    if (!profileService) throw new Error("No profile service available");
    
    setLoading(true);
    try {
      const updatedProfile = await profileService.updatePreferences(data);
      setProfile(updatedProfile);
      setError(null);
      return updatedProfile;
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to update preferences";
      setError(message);
      throw new Error(message);
    } finally {
      setLoading(false);
    }
  }, [profileService, setProfile, setLoading, setError]);

  // Update privacy settings
  const updatePrivacy = useCallback(async (data: PrivacyUpdateData) => {
    if (!profileService) throw new Error("No profile service available");
    
    setLoading(true);
    try {
      const updatedProfile = await profileService.updatePrivacy(data);
      setProfile(updatedProfile);
      setError(null);
      return updatedProfile;
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to update privacy settings";
      setError(message);
      throw new Error(message);
    } finally {
      setLoading(false);
    }
  }, [profileService, setProfile, setLoading, setError]);

  // Check username availability
  const checkUsernameAvailability = useCallback(async (username: string) => {
    if (!profileService) throw new Error("No profile service available");
    return profileService.checkUsernameAvailability(username);
  }, [profileService]);

  // Export profile data
  const exportProfileData = useCallback(async () => {
    if (!profileService) throw new Error("No profile service available");
    return profileService.exportProfileData();
  }, [profileService]);

  // Auto-load profile when user/tenant changes
  useEffect(() => {
    if (user?.id && tenantId) {
      loadProfile();
    }
  }, [user?.id, tenantId, loadProfile]);

  return {
    profile,
    stats,
    isLoading,
    error,
    profileCompletion: getProfileCompletion(),
    missingFields: getMissingFields(),
    displayName: getDisplayName(),
    loadProfile,
    updateProfile,
    updatePreferences,
    updatePrivacy,
    checkUsernameAvailability,
    exportProfileData,
  };
}

// Convenience hooks
export function useProfileData() {
  const { profile } = useProfileStore();
  return profile;
}

export function useProfileStats() {
  const { stats } = useProfileStore();
  return stats;
}

export function useProfileCompletion() {
  const { getProfileCompletion } = useProfileStore();
  return getProfileCompletion();
}

export function useDisplayName() {
  const { getDisplayName } = useProfileStore();
  return getDisplayName();
}

export function useProfileLoading() {
  const { isLoading } = useProfileStore();
  return isLoading;
}

export function useProfileError() {
  const { error } = useProfileStore();
  return error;
}
