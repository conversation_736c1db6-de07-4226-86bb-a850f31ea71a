import { useCallback } from "react";
import { useTenantId } from "@nexus/tenant-context";
import { useWorkspaceStore } from "./workspace-store";
import { createWorkspaceService } from "./workspace-service";
import { CreateWorkspaceData, UpdateWorkspaceData, WorkspaceData } from "./workspace-types";

// Main workspace hooks
export function useWorkspaces() {
  const tenantId = useTenantId();
  const {
    workspaces,
    currentWorkspace,
    isLoading,
    error,
    setWorkspaces,
    setCurrentWorkspace,
    addWorkspace,
    updateWorkspace,
    removeWorkspace,
    setLoading,
    setError,
  } = useWorkspaceStore();

  const workspaceService = tenantId ? createWorkspaceService(tenantId) : null;

  const loadWorkspaces = useCallback(async () => {
    if (!workspaceService) return;
    
    setLoading(true);
    try {
      const result = await workspaceService.list();
      setWorkspaces(result.data);
      setError(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to load workspaces");
    } finally {
      setLoading(false);
    }
  }, [workspaceService, setWorkspaces, setLoading, setError]);

  const createWorkspace = useCallback(async (data: CreateWorkspaceData) => {
    if (!workspaceService) throw new Error("No tenant context");
    
    setLoading(true);
    try {
      const workspace = await workspaceService.create(data);
      addWorkspace(workspace);
      setError(null);
      return workspace;
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to create workspace";
      setError(message);
      throw new Error(message);
    } finally {
      setLoading(false);
    }
  }, [workspaceService, addWorkspace, setLoading, setError]);

  const updateWorkspaceData = useCallback(async (id: string, data: UpdateWorkspaceData) => {
    if (!workspaceService) throw new Error("No tenant context");
    
    setLoading(true);
    try {
      const workspace = await workspaceService.update(id, data);
      updateWorkspace(id, workspace);
      setError(null);
      return workspace;
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to update workspace";
      setError(message);
      throw new Error(message);
    } finally {
      setLoading(false);
    }
  }, [workspaceService, updateWorkspace, setLoading, setError]);

  const deleteWorkspace = useCallback(async (id: string) => {
    if (!workspaceService) throw new Error("No tenant context");
    
    setLoading(true);
    try {
      await workspaceService.delete(id);
      removeWorkspace(id);
      setError(null);
    } catch (error) {
      const message = error instanceof Error ? error.message : "Failed to delete workspace";
      setError(message);
      throw new Error(message);
    } finally {
      setLoading(false);
    }
  }, [workspaceService, removeWorkspace, setLoading, setError]);

  const switchWorkspace = useCallback((workspace: WorkspaceData | null) => {
    setCurrentWorkspace(workspace);
  }, [setCurrentWorkspace]);

  return {
    workspaces,
    currentWorkspace,
    isLoading,
    error,
    loadWorkspaces,
    createWorkspace,
    updateWorkspace: updateWorkspaceData,
    deleteWorkspace,
    switchWorkspace,
  };
}

// Convenience hooks
export function useCurrentWorkspace() {
  const { currentWorkspace } = useWorkspaceStore();
  return currentWorkspace;
}

export function useWorkspaceById(id: string) {
  const { workspaces } = useWorkspaceStore();
  return workspaces.find(w => w.id === id) || null;
}

export function useWorkspaceBySlug(slug: string) {
  const { workspaces } = useWorkspaceStore();
  return workspaces.find(w => w.slug === slug) || null;
}

export function useWorkspaceMembers() {
  const { members, setMembers, addMember, updateMember, removeMember } = useWorkspaceStore();
  
  return {
    members,
    setMembers,
    addMember,
    updateMember,
    removeMember,
  };
}

export function useWorkspaceInvitations() {
  const { invitations, setInvitations, addInvitation, updateInvitation, removeInvitation } = useWorkspaceStore();
  
  return {
    invitations,
    setInvitations,
    addInvitation,
    updateInvitation,
    removeInvitation,
  };
}
