{"name": "@nexus/utils", "version": "0.1.0", "description": "Shared utility functions for Nexus SaaS", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "jest", "type-check": "tsc --noEmit"}, "dependencies": {"@nexus/types": "workspace:*", "clsx": "^2.1.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@types/jest": "^29.5.0", "jest": "^29.5.0", "typescript": "^5.8.0"}}