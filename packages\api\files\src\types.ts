// File management types

export interface FileItem {
  id: string;
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  path: string;
  url: string;
  downloadUrl: string;
  thumbnailUrl?: string;
  previewUrl?: string;
  tenantId: string;
  workspaceId: string;
  projectId?: string;
  uploadedById: string;
  description?: string;
  tags: string[];
  isPublic: boolean;
  metadata: FileMetadata;
  checksum: string;
  version: number;
  parentFileId?: string;
  status: FileStatus;
  processingStatus?: ProcessingStatus;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export type FileStatus = 
  | "uploading"
  | "processing"
  | "ready"
  | "failed"
  | "deleted"
  | "archived";

export type ProcessingStatus = 
  | "pending"
  | "processing"
  | "completed"
  | "failed";

export interface FileMetadata {
  width?: number;
  height?: number;
  duration?: number;
  pages?: number;
  format?: string;
  colorSpace?: string;
  compression?: string;
  quality?: number;
  bitrate?: number;
  frameRate?: number;
  channels?: number;
  sampleRate?: number;
  encoding?: string;
  language?: string;
  author?: string;
  title?: string;
  subject?: string;
  keywords?: string[];
  createdDate?: Date;
  modifiedDate?: Date;
  application?: string;
  producer?: string;
  creator?: string;
  exif?: Record<string, any>;
  iptc?: Record<string, any>;
  xmp?: Record<string, any>;
}

export interface FileVersion {
  id: string;
  fileId: string;
  version: number;
  filename: string;
  size: number;
  path: string;
  url: string;
  checksum: string;
  uploadedById: string;
  comment?: string;
  createdAt: Date;
}

export interface FileShare {
  id: string;
  fileId: string;
  sharedById: string;
  sharedWithId?: string;
  shareType: "public" | "private" | "workspace" | "team";
  permissions: FilePermission[];
  token: string;
  password?: string;
  expiresAt?: Date;
  downloadCount: number;
  maxDownloads?: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export type FilePermission = 
  | "view"
  | "download"
  | "comment"
  | "edit"
  | "delete"
  | "share";

export interface FileComment {
  id: string;
  fileId: string;
  userId: string;
  content: string;
  position?: {
    x: number;
    y: number;
    page?: number;
    timestamp?: number;
  };
  parentCommentId?: string;
  isResolved: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface FileActivity {
  id: string;
  fileId: string;
  userId: string;
  action: FileAction;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
}

export type FileAction = 
  | "upload"
  | "download"
  | "view"
  | "edit"
  | "delete"
  | "share"
  | "comment"
  | "version"
  | "move"
  | "copy"
  | "rename";

export interface Folder {
  id: string;
  name: string;
  path: string;
  parentFolderId?: string;
  tenantId: string;
  workspaceId: string;
  projectId?: string;
  createdById: string;
  description?: string;
  color?: string;
  isPublic: boolean;
  fileCount: number;
  totalSize: number;
  createdAt: Date;
  updatedAt: Date;
}

// Storage provider types
export interface StorageProvider {
  upload(file: Express.Multer.File, options: UploadOptions): Promise<UploadResult>;
  download(path: string): Promise<Buffer>;
  delete(path: string): Promise<void>;
  copy(sourcePath: string, destinationPath: string): Promise<void>;
  move(sourcePath: string, destinationPath: string): Promise<void>;
  getSignedUrl(path: string, operation: "read" | "write", expiresIn?: number): Promise<string>;
  exists(path: string): Promise<boolean>;
  getMetadata(path: string): Promise<any>;
}

export interface UploadOptions {
  path: string;
  filename: string;
  contentType: string;
  metadata?: Record<string, any>;
  acl?: "private" | "public-read" | "public-read-write";
  encryption?: boolean;
  storageClass?: string;
}

export interface UploadResult {
  path: string;
  url: string;
  size: number;
  etag?: string;
  versionId?: string;
}

// Processing types
export interface ProcessingJob {
  id: string;
  fileId: string;
  type: ProcessingType;
  status: ProcessingStatus;
  input: ProcessingInput;
  output?: ProcessingOutput;
  progress: number;
  error?: string;
  startedAt?: Date;
  completedAt?: Date;
  createdAt: Date;
}

export type ProcessingType = 
  | "thumbnail"
  | "preview"
  | "compress"
  | "convert"
  | "extract"
  | "analyze"
  | "ocr"
  | "transcode";

export interface ProcessingInput {
  sourcePath: string;
  options: Record<string, any>;
}

export interface ProcessingOutput {
  outputPath: string;
  metadata: Record<string, any>;
  artifacts?: Array<{
    type: string;
    path: string;
    metadata?: Record<string, any>;
  }>;
}

// Configuration types
export interface FileConfig {
  storage: {
    provider: "local" | "s3" | "gcs" | "azure";
    local?: {
      uploadPath: string;
      publicPath: string;
    };
    s3?: {
      region: string;
      bucket: string;
      accessKeyId: string;
      secretAccessKey: string;
      endpoint?: string;
      forcePathStyle?: boolean;
    };
    gcs?: {
      projectId: string;
      keyFilename: string;
      bucket: string;
    };
    azure?: {
      accountName: string;
      accountKey: string;
      containerName: string;
    };
  };
  upload: {
    maxFileSize: number;
    allowedMimeTypes: string[];
    blockedMimeTypes: string[];
    maxFilesPerUpload: number;
    chunkSize: number;
    enableVersioning: boolean;
    enableDeduplication: boolean;
  };
  processing: {
    enabled: boolean;
    concurrency: number;
    timeout: number;
    thumbnail: {
      enabled: boolean;
      sizes: Array<{ width: number; height: number; quality?: number }>;
      formats: string[];
    };
    preview: {
      enabled: boolean;
      maxPages: number;
      quality: number;
    };
    compression: {
      enabled: boolean;
      quality: number;
      progressive: boolean;
    };
    ocr: {
      enabled: boolean;
      languages: string[];
    };
  };
  security: {
    enableVirusScanning: boolean;
    enableContentAnalysis: boolean;
    quarantineSuspiciousFiles: boolean;
    encryptAtRest: boolean;
    encryptInTransit: boolean;
  };
  cdn: {
    enabled: boolean;
    provider: "cloudflare" | "cloudfront" | "fastly";
    domain: string;
    cacheTtl: number;
  };
  backup: {
    enabled: boolean;
    provider: "s3" | "gcs" | "azure";
    schedule: string;
    retention: number;
  };
}

// Search types
export interface FileSearchQuery {
  query?: string;
  mimeTypes?: string[];
  tags?: string[];
  workspaceId?: string;
  projectId?: string;
  uploadedById?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  sizeRange?: {
    min: number;
    max: number;
  };
  sortBy?: "name" | "size" | "created" | "modified" | "relevance";
  sortOrder?: "asc" | "desc";
  limit?: number;
  offset?: number;
}

export interface FileSearchResult {
  files: FileItem[];
  total: number;
  facets: {
    mimeTypes: Array<{ value: string; count: number }>;
    tags: Array<{ value: string; count: number }>;
    sizes: Array<{ range: string; count: number }>;
    dates: Array<{ range: string; count: number }>;
  };
}

// Analytics types
export interface FileAnalytics {
  totalFiles: number;
  totalSize: number;
  filesByType: Record<string, number>;
  sizeByType: Record<string, number>;
  uploadTrend: Array<{
    date: string;
    count: number;
    size: number;
  }>;
  topFiles: Array<{
    fileId: string;
    filename: string;
    downloads: number;
    views: number;
  }>;
  storageUsage: {
    used: number;
    limit: number;
    percentage: number;
  };
}

// Webhook types
export interface FileWebhook {
  id: string;
  tenantId: string;
  url: string;
  events: FileWebhookEvent[];
  secret?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export type FileWebhookEvent = 
  | "file.uploaded"
  | "file.processed"
  | "file.downloaded"
  | "file.shared"
  | "file.deleted"
  | "file.commented";

export interface FileWebhookPayload {
  event: FileWebhookEvent;
  fileId: string;
  file: FileItem;
  userId: string;
  tenantId: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}
