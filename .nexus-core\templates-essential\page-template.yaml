page_guidelines:
  id: nextjs-page-guidelines
  name: Next.js Page Creation Guidelines
  version: 1.0
  framework: nexus
  purpose: Instructions for creating production-ready Next.js pages

page_types:
  static_pages:
    description: "Static content pages"
    file_location: "app/[page-name]/page.tsx"
    characteristics:
      - "No dynamic parameters"
      - "Content known at build time"
      - "Optimal for landing pages, about pages"
  
  dynamic_pages:
    description: "Pages with dynamic routes"
    file_location: "app/[page-name]/[param]/page.tsx"
    characteristics:
      - "Uses dynamic segments like [id], [slug]"
      - "Generates pages based on data"
      - "Requires params handling"
  
  auth_protected_pages:
    description: "Pages requiring authentication"
    file_location: "app/[page-name]/page.tsx"
    characteristics:
      - "Redirects unauthenticated users"
      - "Access user data securely"
      - "Implements proper auth flow"

required_imports:
  all_pages:
    - "Metadata from next"
    - "Component dependencies as needed"
  
  auth_protected:
    - "redirect from next/navigation"
    - "createClient from @/lib/supabase/server"
  
  dynamic_pages:
    - "Params type definitions"
    - "Data fetching utilities"

metadata_patterns:
  basic_structure:
    - "export const metadata: Metadata"
    - "Include title and description"
    - "Add OpenGraph and Twitter meta"
    - "Consider robots and canonical URLs"
  
  dynamic_metadata:
    - "export async function generateMetadata()"
    - "Fetch data for dynamic titles/descriptions"
    - "Return Metadata object"

page_function_patterns:
  static: "export default function PageName()"
  dynamic: "export default async function PageName({ params }: { params: { [key]: string } })"
  auth_protected: "export default async function PageName()"

authentication_implementation:
  server_side_check:
    - "Create Supabase client"
    - "Call supabase.auth.getUser()"
    - "Redirect if no user: redirect('/login')"
    - "Use user data in page logic"
  
  error_handling:
    - "Handle auth errors gracefully"
    - "Provide fallback for auth failures"
    - "Log authentication issues"

dynamic_routing:
  parameter_handling:
    - "Destructure params from props"
    - "Validate parameter format"
    - "Handle missing or invalid params"
    - "Implement 404 for non-existent resources"
  
  data_fetching:
    - "Use async/await for server-side data"
    - "Implement proper error boundaries"
    - "Cache data appropriately"
    - "Handle loading states"

seo_optimization:
  metadata_requirements:
    - "Unique title for each page"
    - "Descriptive meta description"
    - "Relevant keywords naturally included"
    - "OpenGraph tags for social sharing"
  
  performance:
    - "Optimize images and assets"
    - "Minimize JavaScript bundle"
    - "Use proper heading hierarchy"
    - "Implement structured data when relevant"

implementation_checklist:
  - "Page file in correct location"
  - "Appropriate page type selected"
  - "Metadata properly configured"
  - "Authentication implemented (if required)"
  - "Dynamic parameters handled (if applicable)"
  - "Error boundaries in place"
  - "SEO optimization completed"
  - "Performance considerations applied"

examples:
  static_page: "About page, contact page, terms of service"
  dynamic_page: "Blog post page, product detail page"
  auth_protected: "Dashboard, user profile, settings"
