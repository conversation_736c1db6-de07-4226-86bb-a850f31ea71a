import { CreateUser } from "@nexus/validation";
export declare class UserService {
    private tenantId;
    constructor(tenantId: string);
    create(data: CreateUser): Promise<any>;
    findById(id: string): Promise<any | null>;
    findByEmail(email: string): Promise<any | null>;
    update(id: string, data: Partial<CreateUser>): Promise<any>;
    updateRole(id: string, role: any): Promise<any>;
    updateStatus(id: string, status: any): Promise<any>;
    delete(id: string): Promise<any>;
    list(page?: number, limit?: number): Promise<{
        data: unknown;
        pagination: {
            page: number;
            limit: number;
            total: unknown;
            totalPages: number;
        };
    }>;
    isEmailAvailable(email: string): Promise<boolean>;
    findByRole(role: any): Promise<any[]>;
    findByStatus(status: any): Promise<any[]>;
}
export declare const createUserService: (tenantId: string) => UserService;
//# sourceMappingURL=user-service.d.ts.map