import { stripeServer } from "./stripe-server";
import { Customer, PaymentIntent, Subscription, Product, Price } from "./stripe-types";

export class StripeService {
  private tenantId: string;

  constructor(tenantId: string) {
    this.tenantId = tenantId;
  }

  // Customer management
  async createOrGetCustomer(userId: string, email: string, name?: string): Promise<Customer> {
    // Check if customer already exists in database
    // If not, create new Stripe customer and store in database
    
    const stripeCustomer = await stripeServer.createCustomer({
      email,
      name,
      metadata: {
        userId,
        tenantId: this.tenantId,
      },
    });

    const customer: Customer = {
      id: userId,
      stripeCustomerId: stripeCustomer.id,
      email: stripeCustomer.email!,
      name: stripeCustomer.name || undefined,
      metadata: stripeCustomer.metadata,
      createdAt: new Date(stripeCustomer.created * 1000),
      updatedAt: new Date(),
    };

    return customer;
  }

  async updateCustomer(customerId: string, updates: {
    email?: string;
    name?: string;
    metadata?: Record<string, string>;
  }): Promise<Customer> {
    const stripeCustomer = await stripeServer.updateCustomer(customerId, updates);
    
    const customer: Customer = {
      id: customerId,
      stripeCustomerId: stripeCustomer.id,
      email: stripeCustomer.email!,
      name: stripeCustomer.name || undefined,
      metadata: stripeCustomer.metadata,
      createdAt: new Date(stripeCustomer.created * 1000),
      updatedAt: new Date(),
    };

    return customer;
  }

  // Payment processing
  async createPaymentIntent(params: {
    amount: number;
    currency: string;
    customerId: string;
    metadata?: Record<string, string>;
  }): Promise<PaymentIntent> {
    const stripePaymentIntent = await stripeServer.createPaymentIntent({
      amount: params.amount,
      currency: params.currency,
      customerId: params.customerId,
      metadata: {
        ...params.metadata,
        tenantId: this.tenantId,
      },
    });

    const paymentIntent: PaymentIntent = {
      id: stripePaymentIntent.id,
      amount: stripePaymentIntent.amount,
      currency: stripePaymentIntent.currency,
      status: stripePaymentIntent.status,
      clientSecret: stripePaymentIntent.client_secret!,
      customerId: stripePaymentIntent.customer as string,
      paymentMethodId: stripePaymentIntent.payment_method as string,
      metadata: stripePaymentIntent.metadata,
      createdAt: new Date(stripePaymentIntent.created * 1000),
      updatedAt: new Date(),
    };

    return paymentIntent;
  }

  // Subscription management
  async createSubscription(params: {
    customerId: string;
    priceId: string;
    trialPeriodDays?: number;
    metadata?: Record<string, string>;
  }): Promise<Subscription> {
    const stripeSubscription = await stripeServer.createSubscription({
      customerId: params.customerId,
      priceId: params.priceId,
      trialPeriodDays: params.trialPeriodDays,
      metadata: {
        ...params.metadata,
        tenantId: this.tenantId,
      },
    });

    const subscription: Subscription = {
      id: stripeSubscription.id,
      stripeSubscriptionId: stripeSubscription.id,
      customerId: stripeSubscription.customer as string,
      priceId: params.priceId,
      status: stripeSubscription.status,
      currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
      currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
      trialStart: stripeSubscription.trial_start ? new Date(stripeSubscription.trial_start * 1000) : undefined,
      trialEnd: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : undefined,
      cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
      canceledAt: stripeSubscription.canceled_at ? new Date(stripeSubscription.canceled_at * 1000) : undefined,
      metadata: stripeSubscription.metadata,
      createdAt: new Date(stripeSubscription.created * 1000),
      updatedAt: new Date(),
    };

    return subscription;
  }

  async updateSubscription(subscriptionId: string, params: {
    priceId?: string;
    cancelAtPeriodEnd?: boolean;
    metadata?: Record<string, string>;
  }): Promise<Subscription> {
    const updateParams: any = {};
    
    if (params.priceId) {
      updateParams.items = [{ price: params.priceId }];
    }
    
    if (params.cancelAtPeriodEnd !== undefined) {
      updateParams.cancel_at_period_end = params.cancelAtPeriodEnd;
    }
    
    if (params.metadata) {
      updateParams.metadata = params.metadata;
    }

    const stripeSubscription = await stripeServer.updateSubscription(subscriptionId, updateParams);

    const subscription: Subscription = {
      id: stripeSubscription.id,
      stripeSubscriptionId: stripeSubscription.id,
      customerId: stripeSubscription.customer as string,
      priceId: params.priceId || "",
      status: stripeSubscription.status,
      currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
      currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
      trialStart: stripeSubscription.trial_start ? new Date(stripeSubscription.trial_start * 1000) : undefined,
      trialEnd: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : undefined,
      cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
      canceledAt: stripeSubscription.canceled_at ? new Date(stripeSubscription.canceled_at * 1000) : undefined,
      metadata: stripeSubscription.metadata,
      createdAt: new Date(stripeSubscription.created * 1000),
      updatedAt: new Date(),
    };

    return subscription;
  }

  async cancelSubscription(subscriptionId: string, cancelAtPeriodEnd: boolean = true): Promise<Subscription> {
    const stripeSubscription = await stripeServer.cancelSubscription(subscriptionId, cancelAtPeriodEnd);

    const subscription: Subscription = {
      id: stripeSubscription.id,
      stripeSubscriptionId: stripeSubscription.id,
      customerId: stripeSubscription.customer as string,
      priceId: "",
      status: stripeSubscription.status,
      currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
      currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
      trialStart: stripeSubscription.trial_start ? new Date(stripeSubscription.trial_start * 1000) : undefined,
      trialEnd: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : undefined,
      cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
      canceledAt: stripeSubscription.canceled_at ? new Date(stripeSubscription.canceled_at * 1000) : undefined,
      metadata: stripeSubscription.metadata,
      createdAt: new Date(stripeSubscription.created * 1000),
      updatedAt: new Date(),
    };

    return subscription;
  }

  // Product and pricing
  async createProduct(params: {
    name: string;
    description?: string;
    metadata?: Record<string, string>;
  }): Promise<Product> {
    const stripeProduct = await stripeServer.createProduct(params);

    const product: Product = {
      id: stripeProduct.id,
      stripeProductId: stripeProduct.id,
      name: stripeProduct.name,
      description: stripeProduct.description || undefined,
      active: stripeProduct.active,
      metadata: stripeProduct.metadata,
      prices: [],
      createdAt: new Date(stripeProduct.created * 1000),
      updatedAt: new Date(stripeProduct.updated * 1000),
    };

    return product;
  }

  async createPrice(params: {
    productId: string;
    amount: number;
    currency: string;
    interval?: "month" | "year" | "week" | "day";
    intervalCount?: number;
    metadata?: Record<string, string>;
  }): Promise<Price> {
    const stripePrice = await stripeServer.createPrice(params);

    const price: Price = {
      id: stripePrice.id,
      stripePriceId: stripePrice.id,
      productId: stripePrice.product as string,
      amount: stripePrice.unit_amount || 0,
      currency: stripePrice.currency,
      interval: stripePrice.recurring?.interval,
      intervalCount: stripePrice.recurring?.interval_count,
      type: stripePrice.type === "recurring" ? "recurring" : "one_time",
      active: stripePrice.active,
      metadata: stripePrice.metadata,
      createdAt: new Date(stripePrice.created * 1000),
      updatedAt: new Date(),
    };

    return price;
  }

  // Billing portal
  async createBillingPortalSession(customerId: string, returnUrl: string): Promise<{ url: string }> {
    const session = await stripeServer.createBillingPortalSession({
      customerId,
      returnUrl,
    });

    return { url: session.url };
  }

  // Checkout session
  async createCheckoutSession(params: {
    customerId?: string;
    priceId: string;
    mode: "payment" | "subscription" | "setup";
    successUrl: string;
    cancelUrl: string;
    metadata?: Record<string, string>;
  }): Promise<{ url: string }> {
    const session = await stripeServer.createCheckoutSession({
      ...params,
      metadata: {
        ...params.metadata,
        tenantId: this.tenantId,
      },
    });

    return { url: session.url! };
  }
}

export const createStripeService = (tenantId: string): StripeService => {
  return new StripeService(tenantId);
};
