{"name": "@nexus/graphql-api", "version": "0.1.0", "description": "GraphQL API layer for Nexus SaaS with federation and subscriptions", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "clean": "rm -rf dist", "test": "jest", "type-check": "tsc --noEmit", "codegen": "graphql-codegen --config codegen.yml"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "@nexus/utils": "workspace:*", "@nexus/validation": "workspace:*", "@nexus/database-schema": "workspace:*", "@nexus/database-service": "workspace:*", "@nexus/auth-client": "workspace:*", "@nexus/rbac": "workspace:*", "@apollo/server": "5.0.0", "@apollo/subgraph": "2.11.2", "@apollo/gateway": "2.11.2", "@apollo/federation": "^0.38.1", "@graphql-tools/schema": "10.0.24", "@graphql-tools/utils": "10.9.0", "@graphql-tools/merge": "9.1.0", "@graphql-tools/load-files": "7.0.1", "graphql": "16.11.0", "graphql-subscriptions": "3.0.0", "graphql-ws": "6.0.6", "graphql-upload": "17.0.0", "graphql-scalars": "1.24.2", "graphql-depth-limit": "1.1.0", "graphql-query-complexity": "1.1.0", "graphql-rate-limit": "3.3.0", "ws": "8.18.3", "dataloader": "2.2.3", "zod": "4.0.5", "pino": "9.7.0", "dotenv": "17.2.0"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@graphql-codegen/cli": "5.0.7", "@graphql-codegen/typescript": "4.1.6", "@graphql-codegen/typescript-resolvers": "4.5.1", "@graphql-codegen/typescript-operations": "4.6.1", "@types/jest": "30.0.0", "@types/node": "24.0.15", "@types/ws": "8.18.1", "jest": "30.0.4", "tsx": "4.20.3", "typescript": "5.8.3"}}