{"name": "@nexus/graphql-api", "version": "0.1.0", "description": "GraphQL API layer for Nexus SaaS with federation and subscriptions", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "clean": "rm -rf dist", "test": "jest", "type-check": "tsc --noEmit", "codegen": "graphql-codegen --config codegen.yml"}, "dependencies": {"@nexus/types": "workspace:*", "@nexus/constants": "workspace:*", "@nexus/utils": "workspace:*", "@nexus/validation": "workspace:*", "@nexus/database-schema": "workspace:*", "@nexus/database-service": "workspace:*", "@nexus/auth-client": "workspace:*", "@nexus/rbac": "workspace:*", "@apollo/server": "^4.10.0", "@apollo/subgraph": "^2.6.1", "@apollo/gateway": "^2.6.1", "@apollo/federation": "^0.38.1", "@graphql-tools/schema": "^10.0.2", "@graphql-tools/utils": "^10.0.13", "@graphql-tools/merge": "^9.0.1", "@graphql-tools/load-files": "^7.0.0", "graphql": "^16.8.1", "graphql-subscriptions": "^2.0.0", "graphql-ws": "^5.14.3", "graphql-upload": "^16.0.2", "graphql-scalars": "^1.22.4", "graphql-depth-limit": "^1.1.0", "graphql-query-complexity": "^0.12.0", "graphql-rate-limit": "^3.3.0", "ws": "^8.16.0", "dataloader": "^2.2.2", "zod": "^4.0.5", "pino": "^9.5.0", "dotenv": "^16.4.7"}, "devDependencies": {"@nexus/tsconfig": "workspace:*", "@graphql-codegen/cli": "^5.0.0", "@graphql-codegen/typescript": "^4.0.1", "@graphql-codegen/typescript-resolvers": "^4.0.1", "@graphql-codegen/typescript-operations": "^4.0.1", "@types/jest": "^29.5.0", "@types/node": "^22.10.2", "@types/ws": "^8.5.10", "jest": "^29.5.0", "tsx": "^4.19.2", "typescript": "^5.8.0"}}