import { useCallback } from "react";
import { useWorkspaceStore, workspaceUtils } from "../stores/workspace";
import { useApi, useMutation } from "./useApi";
import { Workspace, CreateWorkspaceInput, UpdateWorkspaceInput, PaginationParams, SearchParams } from "../types";

export function useWorkspace() {
  const {
    workspaces,
    currentWorkspace,
    isLoading,
    error,
    fetchWorkspaces,
    createWorkspace,
    updateWorkspace,
    deleteWorkspace,
    setCurrentWorkspace,
  } = useWorkspaceStore();

  // Fetch workspaces with API hook
  const fetchWorkspacesApi = useApi(
    useCallback(
      (pagination?: PaginationParams, search?: SearchParams) =>
        fetchWorkspaces(pagination, search),
      [fetchWorkspaces]
    )
  );

  // Create workspace mutation
  const createWorkspaceMutation = useMutation(
    useCallback(
      (input: CreateWorkspaceInput) => createWorkspace(input),
      [createWorkspace]
    ),
    {
      onSuccess: (workspace) => {
        // Optionally set as current workspace
        setCurrentWorkspace(workspace);
      },
    }
  );

  // Update workspace mutation
  const updateWorkspaceMutation = useMutation(
    useCallback(
      ({ id, input }: { id: string; input: UpdateWorkspaceInput }) =>
        updateWorkspace(id, input),
      [updateWorkspace]
    )
  );

  // Delete workspace mutation
  const deleteWorkspaceMutation = useMutation(
    useCallback(
      (id: string) => deleteWorkspace(id),
      [deleteWorkspace]
    ),
    {
      onSuccess: () => {
        // Refresh workspaces list
        fetchWorkspacesApi.execute();
      },
    }
  );

  // Helper functions
  const getWorkspaceById = useCallback(
    (id: string): Workspace | undefined => {
      return workspaceUtils.getWorkspaceById(id);
    },
    [workspaces]
  );

  const isWorkspaceOwner = useCallback(
    (workspaceId: string, userId: string): boolean => {
      return workspaceUtils.isWorkspaceOwner(workspaceId, userId);
    },
    [workspaces]
  );

  const getWorkspaceStats = useCallback(
    (workspace: Workspace) => {
      return workspaceUtils.getWorkspaceStats(workspace);
    },
    []
  );

  const switchWorkspace = useCallback(
    (workspace: Workspace | null) => {
      setCurrentWorkspace(workspace);
    },
    [setCurrentWorkspace]
  );

  const refreshWorkspaces = useCallback(() => {
    fetchWorkspacesApi.execute();
  }, [fetchWorkspacesApi]);

  return {
    // State
    workspaces,
    currentWorkspace,
    isLoading: isLoading || fetchWorkspacesApi.loading,
    error: error || fetchWorkspacesApi.error,

    // Actions
    fetchWorkspaces: fetchWorkspacesApi.execute,
    createWorkspace: createWorkspaceMutation.execute,
    updateWorkspace: updateWorkspaceMutation.execute,
    deleteWorkspace: deleteWorkspaceMutation.execute,
    switchWorkspace,
    refreshWorkspaces,

    // Mutation states
    isCreating: createWorkspaceMutation.loading,
    isUpdating: updateWorkspaceMutation.loading,
    isDeleting: deleteWorkspaceMutation.loading,

    // Helper functions
    getWorkspaceById,
    isWorkspaceOwner,
    getWorkspaceStats,

    // Computed values
    hasWorkspaces: workspaces.length > 0,
    workspaceCount: workspaces.length,
  };
}

// Hook for current workspace context
export function useCurrentWorkspace() {
  const { currentWorkspace, switchWorkspace } = useWorkspace();

  return {
    workspace: currentWorkspace,
    setWorkspace: switchWorkspace,
    isSelected: !!currentWorkspace,
    workspaceId: currentWorkspace?.id || null,
    workspaceName: currentWorkspace?.name || null,
    workspaceSlug: currentWorkspace?.slug || null,
  };
}

// Hook for workspace selection
export function useWorkspaceSelector() {
  const { workspaces, currentWorkspace, switchWorkspace, isLoading } = useWorkspace();

  const selectWorkspace = useCallback(
    (workspaceId: string) => {
      const workspace = workspaces.find(w => w.id === workspaceId);
      if (workspace) {
        switchWorkspace(workspace);
      }
    },
    [workspaces, switchWorkspace]
  );

  const selectWorkspaceBySlug = useCallback(
    (slug: string) => {
      const workspace = workspaces.find(w => w.slug === slug);
      if (workspace) {
        switchWorkspace(workspace);
      }
    },
    [workspaces, switchWorkspace]
  );

  return {
    workspaces,
    currentWorkspace,
    isLoading,
    selectWorkspace,
    selectWorkspaceBySlug,
    clearSelection: () => switchWorkspace(null),
  };
}

// Hook for workspace management
export function useWorkspaceManagement() {
  const {
    createWorkspace,
    updateWorkspace,
    deleteWorkspace,
    isCreating,
    isUpdating,
    isDeleting,
  } = useWorkspace();

  const handleCreateWorkspace = useCallback(
    async (input: CreateWorkspaceInput) => {
      try {
        const workspace = await createWorkspace(input);
        return workspace;
      } catch (error) {
        throw error;
      }
    },
    [createWorkspace]
  );

  const handleUpdateWorkspace = useCallback(
    async (id: string, input: UpdateWorkspaceInput) => {
      try {
        const workspace = await updateWorkspace({ id, input });
        return workspace;
      } catch (error) {
        throw error;
      }
    },
    [updateWorkspace]
  );

  const handleDeleteWorkspace = useCallback(
    async (id: string) => {
      try {
        await deleteWorkspace(id);
      } catch (error) {
        throw error;
      }
    },
    [deleteWorkspace]
  );

  return {
    createWorkspace: handleCreateWorkspace,
    updateWorkspace: handleUpdateWorkspace,
    deleteWorkspace: handleDeleteWorkspace,
    isCreating,
    isUpdating,
    isDeleting,
    isBusy: isCreating || isUpdating || isDeleting,
  };
}
