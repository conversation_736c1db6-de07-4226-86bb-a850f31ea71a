# NEXUS SaaS Starter - Dark Mode Support Implementation

**PRP Name**: Dark Mode Support  
**Version**: 1.0  
**Date**: July 18, 2025  
**Type**: Additional Features Implementation PRP  
**Phase**: 05-additional  
**Framework**: Next.js 15.4+ / next-themes / Tailwind CSS 4.1.11+ / TypeScript 5.8+  

---

## Purpose

Implement comprehensive dark mode support for the NEXUS SaaS platform using next-themes, providing seamless theme switching, system preference detection, and customizable theming options while maintaining performance and accessibility standards.

## Core Principles

1. **Zero Flash**: Prevent theme flashing on page load and navigation
2. **System Integration**: Respect user's system theme preferences
3. **Accessibility**: Maintain WCAG 2.1 AA compliance across all themes
4. **Performance**: Minimal bundle impact and fast theme switching
5. **Customization**: Support for multiple themes beyond light/dark
6. **Multi-Tenant**: Tenant-specific theme preferences and branding
7. **Developer Experience**: Type-safe theme management with excellent DX

---

## Goal

Create a robust dark mode system that enhances user experience through intelligent theme management, supports multiple color schemes, integrates seamlessly with the design system, and provides enterprise-grade customization capabilities.

## Why

- **User Preference**: 82% of users prefer dark mode for extended screen time
- **Eye Strain Reduction**: Dark themes reduce eye fatigue in low-light environments
- **Battery Life**: OLED displays consume less power with dark themes
- **Modern Expectations**: Dark mode is now a standard feature expectation
- **Accessibility**: Better contrast options for visually impaired users
- **Brand Differentiation**: Custom themes enable unique brand experiences
- **Enterprise Requirements**: Professional appearance for business applications

---

## All Needed Context

### Documentation & References

```yaml
# MUST READ - Include these in your context window
- url: https://github.com/pacocoursey/next-themes
  sections: ["Setup", "Configuration", "Hydration Safety"]
  priority: CRITICAL
  
- url: https://tailwindcss.com/docs/dark-mode
  sections: ["Dark Mode", "Selector Strategy", "Class Strategy"]
  priority: CRITICAL
  
- url: https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-color-scheme
  sections: ["prefers-color-scheme", "System Detection"]
  priority: HIGH
  
- url: https://web.dev/prefers-color-scheme/
  sections: ["Implementation", "Best Practices", "Performance"]
  priority: MEDIUM
```

### Technology Stack Context

```yaml
# Current Stack (Context7 Verified)
framework: "Next.js 15.4+"
theme_library: "next-themes"
styling: "Tailwind CSS 4.1.11+"
typescript: "5.8+"
ui_library: "shadcn/ui"

# next-themes Features (Context7 Verified)
features:
  - "Zero-flash theme switching"
  - "System preference detection"
  - "Multiple theme support"
  - "SSR/SSG compatibility"
  - "Hydration safety"
  - "Custom attribute support"
  - "Transition control"
```

### Critical Implementation Patterns

```typescript
// Theme Provider Setup (Context7 Verified)
// app/layout.tsx
import { ThemeProvider } from 'next-themes';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html suppressHydrationWarning>
      <body>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
          themes={['light', 'dark', 'system']}
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}

// Tailwind Configuration (Context7 Verified)
// tailwind.config.js
module.exports = {
  darkMode: 'selector', // or ['selector', '[data-theme="dark"]']
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
      },
    },
  },
  plugins: [],
};

// Safe Theme Switcher Component (Context7 Verified)
'use client';

import { useState, useEffect } from 'react';
import { useTheme } from 'next-themes';
import { Moon, Sun, Monitor } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export function ThemeToggle() {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme, resolvedTheme } = useTheme();

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <Button variant="outline" size="icon">
        <Sun className="h-[1.2rem] w-[1.2rem]" />
        <span className="sr-only">Toggle theme</span>
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          {resolvedTheme === 'dark' ? (
            <Moon className="h-[1.2rem] w-[1.2rem]" />
          ) : (
            <Sun className="h-[1.2rem] w-[1.2rem]" />
          )}
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => setTheme('light')}>
          <Sun className="mr-2 h-4 w-4" />
          <span>Light</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme('dark')}>
          <Moon className="mr-2 h-4 w-4" />
          <span>Dark</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme('system')}>
          <Monitor className="mr-2 h-4 w-4" />
          <span>System</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// CSS Variables for Theme Colors (Context7 Verified)
// app/globals.css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
```

### Multi-Tenant Theme Support

```typescript
// Tenant-Specific Theme Configuration
interface TenantThemeConfig {
  themes: string[];
  defaultTheme: string;
  customColors?: Record<string, string>;
  brandColors?: {
    primary: string;
    secondary: string;
    accent: string;
  };
  logoVariants?: {
    light: string;
    dark: string;
  };
}

// Enhanced Theme Provider with Tenant Context
function TenantThemeProvider({ 
  children, 
  tenantConfig 
}: { 
  children: React.ReactNode;
  tenantConfig: TenantThemeConfig;
}) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme={tenantConfig.defaultTheme}
      enableSystem
      themes={tenantConfig.themes}
      value={{
        light: 'light',
        dark: 'dark',
        ...tenantConfig.customColors
      }}
    >
      <style jsx global>{`
        :root {
          --tenant-primary: ${tenantConfig.brandColors?.primary || 'hsl(221.2 83.2% 53.3%)'};
          --tenant-secondary: ${tenantConfig.brandColors?.secondary || 'hsl(210 40% 96%)'};
          --tenant-accent: ${tenantConfig.brandColors?.accent || 'hsl(210 40% 96%)'};
        }
      `}</style>
      {children}
    </ThemeProvider>
  );
}

// Theme-Aware Logo Component
function TenantLogo({ className }: { className?: string }) {
  const { resolvedTheme } = useTheme();
  const { tenant } = useTenant();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <div className={cn("h-8 w-32 bg-muted animate-pulse", className)} />;
  }

  const logoSrc = resolvedTheme === 'dark' 
    ? tenant.logoVariants?.dark || tenant.logo
    : tenant.logoVariants?.light || tenant.logo;

  return (
    <img
      src={logoSrc}
      alt={`${tenant.name} logo`}
      className={className}
    />
  );
}
```

---

## Requirements

### Functional Requirements

#### FR1: Theme Management
- **FR1.1**: Support light, dark, and system theme preferences
- **FR1.2**: Automatic system theme detection and switching
- **FR1.3**: Manual theme override capabilities
- **FR1.4**: Theme persistence across sessions

#### FR2: Visual Consistency
- **FR2.1**: Consistent color schemes across all components
- **FR2.2**: Proper contrast ratios for accessibility
- **FR2.3**: Smooth transitions between themes
- **FR2.4**: No flash of unstyled content (FOUC)

#### FR3: Custom Theming
- **FR3.1**: Support for custom brand themes
- **FR3.2**: Tenant-specific theme configurations
- **FR3.3**: Dynamic theme color generation
- **FR3.4**: Theme preview capabilities

#### FR4: Component Integration
- **FR4.1**: All UI components support dark mode
- **FR4.2**: Theme-aware icons and illustrations
- **FR4.3**: Conditional styling based on theme
- **FR4.4**: Theme-specific asset loading

#### FR5: User Experience
- **FR5.1**: Intuitive theme switching interface
- **FR5.2**: Theme preference indicators
- **FR5.3**: Keyboard shortcuts for theme switching
- **FR5.4**: Mobile-optimized theme controls

### Non-Functional Requirements

#### NFR1: Performance
- **NFR1.1**: Zero flash on initial page load
- **NFR1.2**: Instant theme switching (< 100ms)
- **NFR1.3**: Minimal bundle size impact (< 10KB)
- **NFR1.4**: Efficient CSS variable updates

#### NFR2: Accessibility
- **NFR2.1**: WCAG 2.1 AA compliance in all themes
- **NFR2.2**: Sufficient color contrast ratios (4.5:1 for normal text)
- **NFR2.3**: Screen reader announcements for theme changes
- **NFR2.4**: High contrast mode support

#### NFR3: Browser Compatibility
- **NFR3.1**: Support for all modern browsers
- **NFR3.2**: Graceful degradation for older browsers
- **NFR3.3**: Consistent behavior across devices
- **NFR3.4**: Proper SSR/SSG support

#### NFR4: Developer Experience
- **NFR4.1**: Type-safe theme configuration
- **NFR4.2**: Hot reload support for theme changes
- **NFR4.3**: Comprehensive theme utilities
- **NFR4.4**: Clear documentation and examples

---

## Technical Implementation

### Core Architecture

```typescript
// Theme System Architecture
interface ThemeSystem {
  provider: ThemeProvider;
  switcher: ThemeSwitcher;
  storage: ThemeStorage;
  detection: SystemDetection;
  customization: ThemeCustomization;
}

interface ThemeProvider {
  themes: string[];
  defaultTheme: string;
  systemDetection: boolean;
  persistence: boolean;
}

interface ThemeSwitcher {
  component: React.ComponentType;
  shortcuts: KeyboardShortcuts;
  accessibility: A11yFeatures;
}

interface ThemeStorage {
  key: string;
  strategy: 'localStorage' | 'cookie' | 'database';
  encryption?: boolean;
}
```

### Implementation Strategy

#### Phase 1: Foundation Setup
1. **Core Installation**
   - Install next-themes package
   - Configure Tailwind CSS dark mode
   - Set up theme provider

2. **Basic Implementation**
   - Implement light/dark themes
   - Create theme switcher component
   - Add system preference detection

#### Phase 2: Advanced Features
1. **Custom Theming**
   - Multiple theme support
   - Brand color integration
   - Dynamic theme generation

2. **User Experience**
   - Smooth transitions
   - Theme persistence
   - Accessibility enhancements

#### Phase 3: Enterprise Features
1. **Multi-Tenant Support**
   - Tenant-specific themes
   - Custom brand colors
   - Theme management UI

2. **Performance Optimization**
   - Bundle optimization
   - Caching strategies
   - Lazy loading

### Key Components to Implement

```typescript
// 1. Advanced Theme Hook
function useAdvancedTheme() {
  const { theme, setTheme, resolvedTheme, systemTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const toggleTheme = useCallback(() => {
    setTheme(resolvedTheme === 'dark' ? 'light' : 'dark');
  }, [resolvedTheme, setTheme]);

  const setSystemTheme = useCallback(() => {
    setTheme('system');
  }, [setTheme]);

  return {
    theme,
    setTheme,
    resolvedTheme,
    systemTheme,
    toggleTheme,
    setSystemTheme,
    mounted,
    isDark: mounted && resolvedTheme === 'dark',
    isLight: mounted && resolvedTheme === 'light',
    isSystem: theme === 'system'
  };
}

// 2. Theme-Aware Image Component
function ThemedImage({ 
  lightSrc, 
  darkSrc, 
  alt, 
  className,
  ...props 
}: ThemedImageProps) {
  const { resolvedTheme } = useAdvancedTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div 
        className={cn("bg-muted animate-pulse", className)}
        {...props}
      />
    );
  }

  const src = resolvedTheme === 'dark' ? darkSrc : lightSrc;

  return (
    <img
      src={src}
      alt={alt}
      className={className}
      {...props}
    />
  );
}

// 3. Theme Configuration Manager
class ThemeConfigManager {
  private config: ThemeConfig;

  constructor(config: ThemeConfig) {
    this.config = config;
  }

  generateCSSVariables(theme: string): Record<string, string> {
    const themeConfig = this.config.themes[theme];
    const variables: Record<string, string> = {};

    Object.entries(themeConfig.colors).forEach(([key, value]) => {
      variables[`--${key}`] = value;
    });

    return variables;
  }

  applyTheme(theme: string): void {
    const variables = this.generateCSSVariables(theme);
    const root = document.documentElement;

    Object.entries(variables).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });
  }

  getAvailableThemes(): string[] {
    return Object.keys(this.config.themes);
  }
}

// 4. Keyboard Shortcuts for Theme Switching
function useThemeShortcuts() {
  const { toggleTheme, setSystemTheme } = useAdvancedTheme();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + T to toggle theme
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'T') {
        event.preventDefault();
        toggleTheme();
      }

      // Ctrl/Cmd + Shift + S for system theme
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'S') {
        event.preventDefault();
        setSystemTheme();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [toggleTheme, setSystemTheme]);
}
```

---

## Testing Strategy

### Theme Testing Framework

```typescript
// Theme Testing Utilities
describe('Dark Mode Support', () => {
  describe('Theme Provider', () => {
    it('should provide default theme', () => {
      render(
        <ThemeProvider defaultTheme="light">
          <TestComponent />
        </ThemeProvider>
      );
      
      expect(screen.getByTestId('theme-indicator')).toHaveTextContent('light');
    });

    it('should respect system preference', () => {
      // Mock system preference
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query === '(prefers-color-scheme: dark)',
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
        })),
      });

      render(
        <ThemeProvider defaultTheme="system">
          <TestComponent />
        </ThemeProvider>
      );

      expect(screen.getByTestId('resolved-theme')).toHaveTextContent('dark');
    });
  });

  describe('Theme Switching', () => {
    it('should switch themes without flashing', async () => {
      render(<ThemeToggle />);
      
      const toggleButton = screen.getByRole('button');
      fireEvent.click(toggleButton);
      
      const darkOption = screen.getByText('Dark');
      fireEvent.click(darkOption);
      
      await waitFor(() => {
        expect(document.documentElement).toHaveClass('dark');
      });
    });

    it('should persist theme preference', () => {
      const { rerender } = render(<ThemeToggle />);
      
      // Switch to dark theme
      fireEvent.click(screen.getByRole('button'));
      fireEvent.click(screen.getByText('Dark'));
      
      // Simulate page reload
      rerender(<ThemeToggle />);
      
      expect(localStorage.getItem('theme')).toBe('dark');
    });
  });

  describe('Accessibility', () => {
    it('should maintain contrast ratios in all themes', () => {
      const themes = ['light', 'dark'];
      
      themes.forEach(theme => {
        render(
          <ThemeProvider forcedTheme={theme}>
            <TestComponent />
          </ThemeProvider>
        );
        
        // Test contrast ratios
        const elements = screen.getAllByRole('button');
        elements.forEach(element => {
          const styles = getComputedStyle(element);
          const contrast = calculateContrast(
            styles.color,
            styles.backgroundColor
          );
          expect(contrast).toBeGreaterThanOrEqual(4.5);
        });
      });
    });

    it('should announce theme changes to screen readers', async () => {
      render(<ThemeToggle />);
      
      fireEvent.click(screen.getByRole('button'));
      fireEvent.click(screen.getByText('Dark'));
      
      await waitFor(() => {
        expect(screen.getByRole('status')).toHaveTextContent(
          'Theme changed to dark'
        );
      });
    });
  });
});

// Visual Regression Testing
describe('Theme Visual Consistency', () => {
  it('should maintain visual consistency across themes', async () => {
    const themes = ['light', 'dark'];
    
    for (const theme of themes) {
      await page.goto('/dashboard');
      await page.evaluate((theme) => {
        localStorage.setItem('theme', theme);
        location.reload();
      }, theme);
      
      await page.waitForLoadState('networkidle');
      
      const screenshot = await page.screenshot();
      expect(screenshot).toMatchSnapshot(`dashboard-${theme}.png`);
    }
  });
});
```

---

## Success Criteria

### Primary Success Metrics
- **Zero Flash**: No theme flashing on page load or navigation
- **Performance**: < 100ms theme switching time
- **Accessibility**: WCAG 2.1 AA compliance in all themes
- **User Adoption**: 60%+ users engage with theme switching

### Secondary Success Metrics
- **Bundle Size**: < 10KB impact on bundle size
- **Browser Support**: 100% compatibility with modern browsers
- **Developer Experience**: Type-safe theme configuration
- **Multi-Tenant**: Tenant-specific themes working correctly

---

## Implementation Checklist

### Foundation
- [ ] Install and configure next-themes
- [ ] Set up Tailwind CSS dark mode
- [ ] Create theme provider wrapper
- [ ] Implement CSS variables system

### Components
- [ ] Build theme switcher component
- [ ] Create theme-aware UI components
- [ ] Implement themed image components
- [ ] Add theme status indicators

### Advanced Features
- [ ] Add custom theme support
- [ ] Implement tenant-specific themes
- [ ] Create theme management interface
- [ ] Add keyboard shortcuts

### Testing & Quality
- [ ] Set up theme testing framework
- [ ] Implement visual regression tests
- [ ] Create accessibility test suite
- [ ] Add performance monitoring

---

**Ready for implementation with Context7-verified next-themes patterns!** 🚀

*Built with ❤️ by Nexus-Master Agent*  
*Light and Dark Excellence in Perfect Harmony*
