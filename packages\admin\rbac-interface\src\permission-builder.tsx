"use client";

import React, { useState } from "react";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { ResourceType, ActionType, PermissionScope, Permission } from "@nexus/rbac";

// Permission builder component
export function PermissionBuilder({
  permissions,
  onChange,
}: {
  permissions: Permission[];
  onChange: (permissions: Permission[]) => void;
}) {
  const [selectedResource, setSelectedResource] = useState<ResourceType | "">("");
  const [selectedActions, setSelectedActions] = useState<ActionType[]>([]);
  const [selectedScope, setSelectedScope] = useState<PermissionScope>("workspace");

  const resources: ResourceType[] = [
    "organization", "workspace", "team", "member", "project", "document", 
    "file", "user", "subscription", "invoice", "analytics", "integration", "api"
  ];

  const actions: ActionType[] = [
    "create", "read", "update", "delete", "manage", "share", "invite", 
    "remove", "collaborate", "publish", "download", "upload", "export"
  ];

  const scopes: PermissionScope[] = ["own", "team", "workspace", "organization", "system"];

  const addPermission = () => {
    if (!selectedResource || selectedActions.length === 0) return;

    const newPermissions = selectedActions.map(action => ({
      id: `${selectedResource}-${action}-${Date.now()}`,
      resource: selectedResource,
      action,
      scope: selectedScope,
      createdAt: new Date(),
      updatedAt: new Date(),
    }));

    onChange([...permissions, ...newPermissions]);
    setSelectedActions([]);
  };

  const removePermission = (permissionId: string) => {
    onChange(permissions.filter(p => p.id !== permissionId));
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-4">Permission Builder</h3>
          <p className="text-gray-600 text-sm">Build custom permissions by selecting resources, actions, and scopes</p>
        </div>

        {/* Permission Form */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Resource</label>
              <select
                value={selectedResource}
                onChange={(e) => setSelectedResource(e.target.value as ResourceType)}
                className="w-full px-3 py-2 border rounded-md"
              >
                <option value="">Select resource</option>
                {resources.map((resource) => (
                  <option key={resource} value={resource}>
                    {resource.charAt(0).toUpperCase() + resource.slice(1)}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Actions</label>
              <div className="space-y-1 max-h-32 overflow-y-auto border rounded-md p-2 bg-white">
                {actions.map((action) => (
                  <label key={action} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={selectedActions.includes(action)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedActions([...selectedActions, action]);
                        } else {
                          setSelectedActions(selectedActions.filter(a => a !== action));
                        }
                      }}
                      className="rounded"
                    />
                    <span className="text-sm">{action}</span>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Scope</label>
              <select
                value={selectedScope}
                onChange={(e) => setSelectedScope(e.target.value as PermissionScope)}
                className="w-full px-3 py-2 border rounded-md"
              >
                {scopes.map((scope) => (
                  <option key={scope} value={scope}>
                    {scope.charAt(0).toUpperCase() + scope.slice(1)}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex items-end">
              <button
                onClick={addPermission}
                disabled={!selectedResource || selectedActions.length === 0}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add Permission
              </button>
            </div>
          </div>
        </div>

        {/* Permission List */}
        <div>
          <h4 className="text-md font-semibold mb-3">Current Permissions ({permissions.length})</h4>
          {permissions.length === 0 ? (
            <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
              No permissions added yet
            </div>
          ) : (
            <div className="space-y-2">
              {permissions.map((permission) => (
                <PermissionItem
                  key={permission.id}
                  permission={permission}
                  onRemove={() => removePermission(permission.id)}
                />
              ))}
            </div>
          )}
        </div>

        {/* Permission Templates */}
        <PermissionTemplates onApplyTemplate={(templatePermissions) => {
          onChange([...permissions, ...templatePermissions]);
        }} />
      </div>
    </DndProvider>
  );
}

// Permission item component
function PermissionItem({
  permission,
  onRemove,
}: {
  permission: Permission;
  onRemove: () => void;
}) {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: "permission",
    item: { permission },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  return (
    <div
      ref={drag}
      className={`flex items-center justify-between p-3 bg-white border rounded-lg cursor-move ${
        isDragging ? "opacity-50" : ""
      }`}
    >
      <div className="flex items-center space-x-3">
        <div className="text-gray-400">⋮⋮</div>
        <div>
          <span className="font-medium">{permission.resource}</span>
          <span className="mx-2 text-gray-400">→</span>
          <span className="text-blue-600">{permission.action}</span>
          <span className="mx-2 text-gray-400">@</span>
          <span className="text-green-600">{permission.scope}</span>
        </div>
      </div>
      
      <button
        onClick={onRemove}
        className="text-red-600 hover:text-red-800 text-sm"
      >
        Remove
      </button>
    </div>
  );
}

// Permission templates component
function PermissionTemplates({
  onApplyTemplate,
}: {
  onApplyTemplate: (permissions: Permission[]) => void;
}) {
  const templates = [
    {
      name: "Content Creator",
      permissions: [
        { resource: "document" as ResourceType, action: "create" as ActionType, scope: "workspace" as PermissionScope },
        { resource: "document" as ResourceType, action: "read" as ActionType, scope: "workspace" as PermissionScope },
        { resource: "document" as ResourceType, action: "update" as ActionType, scope: "own" as PermissionScope },
        { resource: "file" as ResourceType, action: "upload" as ActionType, scope: "workspace" as PermissionScope },
        { resource: "file" as ResourceType, action: "read" as ActionType, scope: "workspace" as PermissionScope },
      ],
    },
    {
      name: "Team Manager",
      permissions: [
        { resource: "team" as ResourceType, action: "read" as ActionType, scope: "team" as PermissionScope },
        { resource: "team" as ResourceType, action: "update" as ActionType, scope: "team" as PermissionScope },
        { resource: "member" as ResourceType, action: "invite" as ActionType, scope: "team" as PermissionScope },
        { resource: "member" as ResourceType, action: "remove" as ActionType, scope: "team" as PermissionScope },
        { resource: "project" as ResourceType, action: "manage" as ActionType, scope: "team" as PermissionScope },
      ],
    },
    {
      name: "Read Only",
      permissions: [
        { resource: "document" as ResourceType, action: "read" as ActionType, scope: "workspace" as PermissionScope },
        { resource: "file" as ResourceType, action: "read" as ActionType, scope: "workspace" as PermissionScope },
        { resource: "project" as ResourceType, action: "read" as ActionType, scope: "workspace" as PermissionScope },
        { resource: "analytics" as ResourceType, action: "read" as ActionType, scope: "workspace" as PermissionScope },
      ],
    },
  ];

  return (
    <div>
      <h4 className="text-md font-semibold mb-3">Quick Templates</h4>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {templates.map((template) => (
          <div key={template.name} className="border rounded-lg p-4">
            <h5 className="font-medium mb-2">{template.name}</h5>
            <div className="space-y-1 mb-3">
              {template.permissions.map((perm, index) => (
                <div key={index} className="text-xs text-gray-600">
                  {perm.resource}:{perm.action} ({perm.scope})
                </div>
              ))}
            </div>
            <button
              onClick={() => {
                const permissions = template.permissions.map((perm, index) => ({
                  id: `${template.name}-${index}-${Date.now()}`,
                  ...perm,
                  createdAt: new Date(),
                  updatedAt: new Date(),
                }));
                onApplyTemplate(permissions);
              }}
              className="w-full bg-gray-100 text-gray-700 py-1 px-3 rounded text-sm hover:bg-gray-200"
            >
              Apply Template
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
