# NEXUS Memory Enforcement System
# Mandatory memory creation and storage protocols

memory_enforcement:
  enabled: true
  enforcement_level: "absolute"
  
  # NEXUS MEMORY COMMANDS (Equivalent to ken-you-remember)
  nexus_memory_commands:
    nexus_remember:
      command: "nexus-remember:"
      usage: "nexus-remember: [critical_information_to_store]"
      purpose: "Store critical decisions, findings, patterns, failures"
      mandatory_before:
        - "major_decisions"
        - "implementation_changes"
        - "research_findings"
        - "failure_analysis"
        - "pattern_discoveries"
      
    nexus_recall:
      command: "nexus-recall:"
      usage: "nexus-recall: [what_to_retrieve]"
      purpose: "Retrieve stored memory for context validation"
      mandatory_before:
        - "starting_new_tasks"
        - "making_conflicting_decisions"
        - "context_validation"
        - "consistency_checks"

  # MANDATORY STORAGE TRIGGERS
  mandatory_storage_triggers:
    before_major_decisions:
      enforcement: "absolute"
      required_storage:
        - "nexus-remember: Current context and understanding"
        - "nexus-remember: Decision approach and reasoning"
        - "nexus-remember: Alternatives considered and why rejected"
        - "nexus-remember: Expected outcomes and success criteria"
    
    before_implementation:
      enforcement: "mandatory"
      required_storage:
        - "nexus-remember: Files being modified and why"
        - "nexus-remember: Implementation approach and patterns"
        - "nexus-remember: Rollback plan if implementation fails"
        - "nexus-remember: Dependencies and integration points"
    
    after_failures:
      enforcement: "immediate"
      required_storage:
        - "nexus-remember: What failed and exact error details"
        - "nexus-remember: Root cause analysis findings"
        - "nexus-remember: What was attempted and didn't work"
        - "nexus-remember: Lessons learned and pattern to avoid"
    
    after_successes:
      enforcement: "mandatory"
      required_storage:
        - "nexus-remember: What worked and exact solution details"
        - "nexus-remember: Why it worked and underlying principles"
        - "nexus-remember: When to use this solution again"
        - "nexus-remember: User guidance or breakthrough moment"
    
    breakthrough_moments:
      enforcement: "immediate"
      required_storage:
        - "nexus-remember: Problem that was causing error loops"
        - "nexus-remember: Solution that broke the cycle"
        - "nexus-remember: How the solution was discovered"
        - "nexus-remember: Pattern to remember for similar situations"
    
    context_checkpoints:
      frequency: "every_10_15_exchanges"
      enforcement: "automatic"
      required_storage:
        - "nexus-remember: Current session state summary"
        - "nexus-remember: Progress made and remaining tasks"
        - "nexus-remember: Key decisions and rationale"
        - "nexus-remember: Files modified and patterns used"

  # MEMORY STORAGE STRUCTURE
  storage_structure:
    location: ".nexus-core/context-memory.yaml"
    
    categories:
      decisions_memory:
        description: "Critical decisions and reasoning"
        format: "timestamp: decision - reasoning - outcome"
        
      pattern_memory:
        description: "Code patterns and architectural insights"
        format: "pattern_name: description - when_to_use - examples"
        
      failure_memory:
        description: "Failures and lessons learned"
        format: "failure_type: what_failed - cause - solution - prevention"
        
      success_memory:
        description: "Successes and why they worked"
        format: "success_type: what_worked - why_it_worked - when_to_use_again - user_guidance"
        
      breakthrough_memory:
        description: "Breakthrough moments that broke error loops"
        format: "breakthrough_type: problem_faced - solution_found - how_discovered - pattern_to_remember"
        
      file_memory:
        description: "Important file locations and structures"
        format: "file_path: purpose - key_patterns - modification_history"
        
      context_memory:
        description: "Session context and state"
        format: "session_id: current_task - progress - next_steps"

  # ENFORCEMENT MECHANISMS
  enforcement_mechanisms:
    pre_action_checks:
      enabled: true
      description: "Block actions until memory requirements met"
      triggers:
        - "before_file_modifications"
        - "before_major_decisions"
        - "before_implementation_start"
      
    automatic_reminders:
      enabled: true
      frequency: "every_5_exchanges"
      message: "🧠 MEMORY CHECK: Have you stored critical context with nexus-remember?"
      
    context_validation:
      enabled: true
      description: "Validate consistency with stored memory"
      before:
        - "contradicting_previous_decisions"
        - "changing_implementation_approach"
        - "modifying_established_patterns"

  # INTEGRATION WITH EXISTING SYSTEMS
  integration:
    with_zero_hallucination_policy:
      mechanism: "memory_verification_before_claims"
      requirement: "check_stored_memory_before_making_assertions"
      
    with_context_recall_protocol:
      mechanism: "automatic_memory_restoration"
      frequency: "every_20_interactions"
      action: "load_all_stored_context_and_decisions"
      
    with_self_validation_framework:
      mechanism: "memory_consistency_validation"
      questions:
        - "Does this contradict stored decisions?"
        - "Have I checked stored patterns?"
        - "Am I repeating stored failures?"

  # MEMORY QUALITY STANDARDS
  quality_standards:
    specificity: "Store specific details, not general statements"
    actionability: "Store actionable insights, not just facts"
    context: "Include enough context for future retrieval"
    timestamp: "Always include when the memory was created"
    reasoning: "Include why the decision was made"
    
  # FAILURE RECOVERY
  memory_failure_recovery:
    when_memory_lost:
      actions:
        - "Immediately notify user of memory loss"
        - "Request key context reconstruction"
        - "Review available files for context clues"
        - "Restart with explicit memory creation"
      
    when_memory_conflicts:
      actions:
        - "Identify conflicting memories"
        - "Present conflicts to user for resolution"
        - "Update memory with resolved decisions"
        - "Document conflict resolution approach"

# USAGE EXAMPLES
usage_examples:
  storing_decisions:
    example: "nexus-remember: Decided to use Zustand for state management because it's lightweight (2KB) and provides better TypeScript integration than Redux for this project size"
    
  storing_failures:
    example: "nexus-remember: API route /api/users failed with 500 error due to missing await on database query. Fixed by adding await and proper error handling. Pattern: always await async DB operations"
    
  storing_successes:
    example: "nexus-remember: Authentication middleware worked perfectly after adding proper token validation. Success because we followed JWT best practices with expiration checks. Use this pattern for all auth routes. User suggested checking token structure first."
    
  storing_breakthroughs:
    example: "nexus-remember: Error loop with infinite redirects broke when we discovered middleware order issue. Solution: moved auth middleware after CORS. Found by user suggestion to check middleware stack. Pattern: middleware sequence is critical."
    
  storing_patterns:
    example: "nexus-remember: File structure pattern found: components in /components/ui/ use shadcn/ui, custom components in /components/custom/. Follow this pattern for consistency"
    
  context_checkpoints:
    example: "nexus-remember: Session checkpoint - completed user authentication setup, currently working on dashboard components, next step is data fetching integration with Supabase"
