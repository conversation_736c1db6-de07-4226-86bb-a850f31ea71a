import { Socket } from "socket.io";
import { User } from "@nexus/types";

// Real-time event types
export interface RealtimeEvents {
  // Connection events
  "user:join": (data: { userId: string; workspaceId: string; metadata?: any }) => void;
  "user:leave": (data: { userId: string; workspaceId: string }) => void;
  "user:presence": (data: UserPresence) => void;
  
  // Collaboration events
  "document:join": (data: { documentId: string; userId: string }) => void;
  "document:leave": (data: { documentId: string; userId: string }) => void;
  "document:update": (data: DocumentUpdate) => void;
  "document:cursor": (data: CursorPosition) => void;
  "document:selection": (data: SelectionRange) => void;
  
  // Chat events
  "chat:message": (data: ChatMessage) => void;
  "chat:typing": (data: TypingIndicator) => void;
  "chat:read": (data: MessageRead) => void;
  
  // Notification events
  "notification:new": (data: RealtimeNotification) => void;
  "notification:read": (data: { notificationId: string; userId: string }) => void;
  
  // Activity events
  "activity:new": (data: ActivityEvent) => void;
  
  // System events
  "system:maintenance": (data: { message: string; scheduledAt?: Date }) => void;
  "system:broadcast": (data: { message: string; type: "info" | "warning" | "error" }) => void;
}

// User presence
export interface UserPresence {
  userId: string;
  user: Pick<User, "id" | "name" | "email" | "avatar">;
  status: "online" | "away" | "busy" | "offline";
  lastSeen: Date;
  currentLocation?: {
    workspaceId?: string;
    teamId?: string;
    projectId?: string;
    documentId?: string;
    route?: string;
  };
  metadata?: {
    device?: string;
    browser?: string;
    ip?: string;
  };
}

// Document collaboration
export interface DocumentUpdate {
  documentId: string;
  userId: string;
  operation: {
    type: "insert" | "delete" | "retain" | "format";
    position: number;
    content?: string;
    length?: number;
    attributes?: Record<string, any>;
  };
  timestamp: Date;
  version: number;
}

export interface CursorPosition {
  documentId: string;
  userId: string;
  user: Pick<User, "id" | "name" | "avatar">;
  position: {
    line: number;
    column: number;
    offset: number;
  };
  timestamp: Date;
}

export interface SelectionRange {
  documentId: string;
  userId: string;
  user: Pick<User, "id" | "name" | "avatar">;
  start: {
    line: number;
    column: number;
    offset: number;
  };
  end: {
    line: number;
    column: number;
    offset: number;
  };
  timestamp: Date;
}

// Chat system
export interface ChatMessage {
  id: string;
  channelId: string;
  channelType: "workspace" | "team" | "project" | "direct";
  userId: string;
  user: Pick<User, "id" | "name" | "avatar">;
  content: string;
  type: "text" | "file" | "image" | "system";
  metadata?: {
    fileId?: string;
    fileName?: string;
    fileSize?: number;
    mentions?: string[];
    reactions?: Array<{
      emoji: string;
      users: string[];
      count: number;
    }>;
  };
  replyTo?: string;
  editedAt?: Date;
  deletedAt?: Date;
  createdAt: Date;
}

export interface TypingIndicator {
  channelId: string;
  userId: string;
  user: Pick<User, "id" | "name">;
  isTyping: boolean;
  timestamp: Date;
}

export interface MessageRead {
  channelId: string;
  messageId: string;
  userId: string;
  readAt: Date;
}

// Notifications
export interface RealtimeNotification {
  id: string;
  userId: string;
  type: "mention" | "assignment" | "comment" | "system" | "invitation";
  title: string;
  message: string;
  data?: Record<string, any>;
  actionUrl?: string;
  priority: "low" | "normal" | "high" | "urgent";
  isRead: boolean;
  createdAt: Date;
}

// Activity tracking
export interface ActivityEvent {
  id: string;
  userId: string;
  user: Pick<User, "id" | "name" | "avatar">;
  type: "create" | "update" | "delete" | "view" | "share" | "comment";
  resource: "workspace" | "team" | "project" | "file" | "user";
  resourceId: string;
  resourceName: string;
  description: string;
  metadata?: Record<string, any>;
  workspaceId?: string;
  teamId?: string;
  projectId?: string;
  createdAt: Date;
}

// Socket context
export interface SocketContext {
  socket: Socket;
  user: User;
  tenantId: string;
  workspaceId?: string;
  teamId?: string;
  permissions: string[];
  joinedRooms: Set<string>;
  presence: UserPresence;
}

// Room types
export type RoomType = 
  | "tenant"
  | "workspace" 
  | "team"
  | "project"
  | "document"
  | "chat"
  | "user";

export interface Room {
  id: string;
  type: RoomType;
  name: string;
  participants: Map<string, SocketContext>;
  metadata?: Record<string, any>;
  createdAt: Date;
  lastActivity: Date;
}

// Collaboration document
export interface CollaborativeDocument {
  id: string;
  type: "text" | "code" | "markdown" | "json";
  content: string;
  version: number;
  participants: Map<string, {
    userId: string;
    user: Pick<User, "id" | "name" | "avatar">;
    cursor?: CursorPosition;
    selection?: SelectionRange;
    joinedAt: Date;
    lastActivity: Date;
  }>;
  operations: DocumentUpdate[];
  metadata?: {
    projectId?: string;
    fileId?: string;
    title?: string;
    language?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

// Rate limiting
export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

// Server configuration
export interface RealtimeConfig {
  port: number;
  cors: {
    origin: string | string[];
    credentials: boolean;
  };
  redis: {
    host: string;
    port: number;
    password?: string;
    db?: number;
  };
  jwt: {
    secret: string;
    expiresIn: string;
  };
  rateLimit: {
    connection: RateLimitConfig;
    message: RateLimitConfig;
    join: RateLimitConfig;
  };
  collaboration: {
    maxDocumentSize: number;
    maxParticipants: number;
    operationTimeout: number;
    snapshotInterval: number;
  };
  presence: {
    updateInterval: number;
    offlineTimeout: number;
    cleanupInterval: number;
  };
}

// Error types
export interface RealtimeError {
  code: string;
  message: string;
  details?: any;
}

// Metrics
export interface RealtimeMetrics {
  connections: {
    total: number;
    authenticated: number;
    anonymous: number;
  };
  rooms: {
    total: number;
    byType: Record<RoomType, number>;
  };
  documents: {
    active: number;
    totalOperations: number;
  };
  messages: {
    sent: number;
    received: number;
    errors: number;
  };
  performance: {
    averageLatency: number;
    memoryUsage: number;
    cpuUsage: number;
  };
}
