
> @nexus/database-service@0.1.0 type-check C:\Users\<USER>\Downloads\saas-starter-cursor-temp\packages\database\service
> tsc --noEmit

src/client.ts(15,14): error TS2580: Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.
src/client.ts(20,5): error TS2580: Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.
src/session-service.ts(12,73): error TS2304: Cannot find name 'Session'.
src/session-service.ts(24,45): error TS2304: Cannot find name 'Session'.
src/session-service.ts(35,47): error TS2304: Cannot find name 'Session'.
src/session-service.ts(37,5): error TS2322: Type 'unknown' is not assignable to type 'Session[]'.
src/session-service.ts(44,67): error TS2304: Cannot find name 'Session'.
src/session-service.ts(53,40): error TS2304: Cannot find name 'Session'.
src/session-service.ts(63,48): error TS2339: Property 'deleteMany' does not exist on type '{ findMany: (args?: any) => Promise<unknown>; findUnique: (args: any) => Promise<unknown>; create: (args: any) => Promise<unknown>; update: (args: any) => Promise<unknown>; delete: (args: any) => Promise<...>; }'.
src/session-service.ts(72,48): error TS2339: Property 'deleteMany' does not exist on type '{ findMany: (args?: any) => Promise<unknown>; findUnique: (args: any) => Promise<unknown>; create: (args: any) => Promise<unknown>; update: (args: any) => Promise<unknown>; delete: (args: any) => Promise<...>; }'.
src/session-service.ts(90,84): error TS2304: Cannot find name 'Session'.
src/tenant-service.ts(1,10): error TS2305: Module '"@nexus/database-schema"' has no exported member 'Tenant'.
src/tenant-service.ts(1,18): error TS2305: Module '"@nexus/database-schema"' has no exported member 'TenantStatus'.
src/tenant-service.ts(1,32): error TS2305: Module '"@nexus/database-schema"' has no exported member 'SubscriptionPlan'.
src/user-service.ts(1,10): error TS2305: Module '"@nexus/database-schema"' has no exported member 'User'.
src/user-service.ts(1,16): error TS2305: Module '"@nexus/database-schema"' has no exported member 'UserRole'.
src/user-service.ts(1,26): error TS2305: Module '"@nexus/database-schema"' has no exported member 'UserStatus'.
src/user-service.ts(91,26): error TS2339: Property 'count' does not exist on type '{ findMany: (args?: any) => Promise<unknown>; findUnique: (args: any) => Promise<unknown>; create: (args: any) => Promise<unknown>; update: (args: any) => Promise<unknown>; delete: (args: any) => Promise<...>; }'.
src/user-service.ts(114,5): error TS2322: Type 'unknown' is not assignable to type 'User[]'.
src/user-service.ts(123,5): error TS2322: Type 'unknown' is not assignable to type 'User[]'.
src/workspace-service.ts(1,10): error TS2305: Module '"@nexus/database-schema"' has no exported member 'Workspace'.
src/workspace-service.ts(69,31): error TS2339: Property 'count' does not exist on type '{ findMany: (args?: any) => Promise<unknown>; findUnique: (args: any) => Promise<unknown>; create: (args: any) => Promise<unknown>; update: (args: any) => Promise<unknown>; delete: (args: any) => Promise<...>; }'.
src/workspace-service.ts(92,5): error TS2322: Type 'unknown' is not assignable to type 'Workspace[]'.
 ELIFECYCLE  Command failed with exit code 2.
